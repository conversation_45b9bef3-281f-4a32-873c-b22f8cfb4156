@extends('admin.index')
@section('content')
<title>{{trans('admin.Default_Data')}}</title>
<link rel="stylesheet" media="screen, print" href="css/formplugins/summernote/summernote.css">
<main id="js-page-content" role="main" class="page-content">
   <ol class="breadcrumb page-breadcrumb">
      <li class="breadcrumb-item"><a href="javascript:void(0);">{{trans('admin.Settings')}}</a></li>
      <li class="breadcrumb-item active"> {{trans('admin.Default_Data')}}   </li>
      <li class="position-absolute pos-top pos-right d-none d-sm-block"><span
         class="js-get-date"></span></li>
   </ol>
   <!-- data entry -->
   <div class="row">
      <div class="col-lg-12">
         <div id="panel-2" class="panel">
            <div class="panel-hdr">
            </div>
            <div class="panel-container show">
               <span id="ex"> @include('admin.layouts.messages')</span>     
               <div class="panel-content">
                  <ul class="nav nav-tabs" role="tablist">
  
                     <li class="nav-item">
                        <a class="nav-link" data-toggle="tab" href="#tab_borders_icons-8" role="tab">   {{trans('admin.Company_Data')}}  </a>
                     </li>
                  </ul>
                  <div class="tab-content border border-top-0 p-3">
                 
                   
                     <div class="tab-pane fade show active" id="tab_borders_icons-8" role="tabpanel">
                        @if(!empty($Companies))            
                        <form action="{{url('AddDefaultCompany')}}" method="post" enctype="multipart/form-data" class="form-row">
                           {!! csrf_field() !!}
                    
                           <div class="col-md-12">
                              <div class="data-def">
                                 <div class="form-row">
                                     
                                    <div class="form-group col-md-6">
                                       <label class="form-label" for=""> {{trans('admin.Company_Arabic_Name')}}   </label>
                                       <input type="text" name="Name" value="{{$Companies->Name}}" class="form-control" required>
                                    </div>       
                                     
                                     <div class="form-group col-md-6">
                                       <label class="form-label" for=""> {{trans('admin.Company_English_Name')}}   </label>
                                       <input type="text" name="NameEn" value="{{$Companies->NameEn}}" class="form-control" required>
                                    </div>
                   
                   
                                 
                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="">    {{trans('admin.Logo')}}    </label>
                                       <input type="file" name="Logo">
                                    </div>
                                    <div class="form-group col-md-2">
                                       <img class="img-fluid" src="{{URL::to($Companies->Logo)}}" style="max-width: 50%">  
                                       <input type="hidden" name="Logos" value="{{$Companies->Logo}}">                   
                                    </div>
                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="">    {{trans('admin.Icon')}}    </label>
                                       <input type="file" name="Icon">
                                    </div>
                                    <div class="form-group col-md-2">
                                       <img class="img-fluid" src="{{URL::to($Companies->Icon)}}" style="max-width: 10%">  
                                       <input type="hidden" name="Icons" value="{{$Companies->Icon}}">                
                                    </div>
                                           <div class="form-group col-md-2">
                                       <label class="form-label" for="">    {{trans('admin.Logo_Store')}}    </label>
                                       <input type="file" name="Logo_Store">
                                    </div>
                                     
                                    <div class="form-group col-md-2">
                                       <img class="img-fluid" src="{{URL::to($Companies->Logo_Store)}}" style="max-width: 50%">  
                                       <input type="hidden" name="Logo_StoreS" value="{{$Companies->Logo_Store}}">
                                    </div> 
                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="">    {{trans('admin.Icon_Store')}}    </label>
                                       <input type="file" name="Icon_Store">
                                    </div>
                                    <div class="form-group col-md-2">
                                       <img class="img-fluid" src="{{URL::to($Companies->Icon_Store)}}" style="max-width: 10%">  
                                       <input type="hidden" name="Icon_StoreS" value="{{$Companies->Icon_Store}}">
                                    </div>  
                                     
                        
                               
                                 </div>
                              </div>
                           </div>
                           <div class="col-md-12">
                              <button type="submit" class="btn btn-primary">{{trans('admin.Save')}}</button>
                           </div>
                        </form>
                        @else
                        <div  class="form-row">
                           <div class="col-md-12">
                              <div class="data-def">
                                 <div class="form-row">
                                    <div class="form-group col-md-12">
                                       <a class="btn btn-primary" href="{{url('AddDefaultCompanyFirst')}}">
                                       {{trans('admin.AddNew')}}
                                       </a>   
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        @endif            
                     </div>
                 
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>
</main>
@endsection
@push('js')
<link rel="stylesheet" media="screen, print" href="{{asset('Admin/css/datagrid/datatables/datatables.bundle.css')}}">
<link rel="stylesheet" media="screen, print" href="{{asset('Admin/css/formplugins/summernote/summernote.css')}}">
<link rel="stylesheet" media="screen, print" href="{{asset('Admin/css/formplugins/select2/select2.bundle.css')}}">
<script src="{{asset('Admin/js/datagrid/datatables/datatables.bundle.js')}}"></script>
<script src="{{asset('Admin/js/datagrid/datatables/datatables.export.js')}}"></script>
<script src="{{asset('Admin/js/formplugins/summernote/summernote.js')}}"></script>
<script src="{{asset('Admin/js/formplugins/select2/select2.bundle.js')}}"></script>
<script>
   //_fnFeatureHtmlLength();
   $(document).ready(function () {
       // Setup - add a text input to each footer cell
       $('#dt-basic-example thead tr').clone(true).appendTo('#dt-basic-example thead');
       $('#dt-basic-example thead tr:eq(1) th').each(function (i) {
           var title = $(this).text();
           $(this).html('<input type="text" class="form-control form-control-sm" placeholder="Search ' + title + '" />');
   
           $('input', this).on('keyup change', function () {
               if (table.column(i).search() !== this.value) {
                   table
                       .column(i)
                       .search(this.value)
                       .draw();
               }
           });
       });
       var table = $('#dt-basic-example').DataTable(
           {
               responsive: true,
               orderCellsTop: true,
               fixedHeader: true,
               lengthChange: true,
   
               dom: "<'row mb-3'<'col-sm-12 col-md-3 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-9 d-flex align-items-center justify-content-end'B>>" +
                   "<'row'<'col-sm-12'tr>>" +
                   "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
   
               buttons: [
                   {
                       extend: 'pageLength',
                       className: 'btn-outline-default'
                   },
                   {
                       extend: 'colvis',
                       text: 'Column Visibility',
                       titleAttr: 'Col visibility',
                       className: 'btn-outline-default'
                   },
                   {
                       extend: 'pdfHtml5',
                       text: 'PDF',
                       titleAttr: 'Generate PDF',
                       className: 'btn-outline-danger btn-sm mr-1'
                   },
                   {
                       extend: 'excelHtml5',
                       text: 'Excel',
                       titleAttr: 'Generate Excel',
                       className: 'btn-outline-success btn-sm mr-1'
                   },
                   {
                       extend: 'csvHtml5',
                       text: 'CSV',
                       titleAttr: 'Generate CSV',
                       className: 'btn-outline-primary btn-sm mr-1'
                   },
                   {
                       extend: 'copyHtml5',
                       text: 'Copy',
                       titleAttr: 'Copy to clipboard',
                       className: 'btn-outline-primary btn-sm mr-1'
                   },
                   {
                       extend: 'print',
                       text: 'Print',
                       titleAttr: 'Print Table',
                       className: 'btn-outline-primary btn-sm'
                   }
               ],
           });
       $('.js-thead-colors a').on('click', function () {
           var theadColor = $(this).attr("data-bg");
           console.log(theadColor);
           $('#dt-basic-example thead').removeClassPrefix('bg-').addClass(theadColor);
       });
   
       $('.js-tbody-colors a').on('click', function () {
           var theadColor = $(this).attr("data-bg");
           console.log(theadColor);
           $('#dt-basic-example').removeClassPrefix('bg-').addClass(theadColor);
       });
   
   });
   
</script>
<!-- Search Selecet -->
<script>
   $(document).ready(function()
   {
       $(function()
       {
           $('.select2').select2();
   
           $(".select2-placeholder-multiple").select2(
           {
               placeholder: "Select State"
           });
           $(".js-hide-search").select2(
           {
               minimumResultsForSearch: 1 / 0
           });
           $(".js-max-length").select2(
           {
               maximumSelectionLength: 2,
               placeholder: "Select maximum 2 items"
           });
           $(".select2-placeholder").select2(
           {
               placeholder: "Select a state",
               allowClear: true
           });
   
           $(".js-select2-icons").select2(
           {
               minimumResultsForSearch: 1 / 0,
               templateResult: icon,
               templateSelection: icon,
               escapeMarkup: function(elm)
               {
                   return elm
               }
           });
   
           function icon(elm)
           {
               elm.element;
               return elm.id ? "<i class='" + $(elm.element).data("icon") + " mr-2'></i>" + elm.text : elm.text
           }
   
   
   $('#AccountDificit').select2({
   placeholder: "select...",
   ajax: {
   type: "GET",
   dataType: 'json',
   url: 'AllSubAccounts',
   processResults: function (data) {
     return {
       results: $.map(data, function(obj, index) {
         return { id: index, text: obj };
       })
     };
       
       	console.log(data);
       
   },
   data: function (params) {  
     var query = {
       search: params.term
     };
     if (params.term == "*") query.items = [];
     return { json: JSON.stringify( query ) }
   }
   }
   });
   
   
   $('#AccountDificit').on('select2:select', function (e) {
   console.log("select done", e.params.data);
   });
      
           
   $('#AccountExcess').select2({
   placeholder: "select...",
   ajax: {
   type: "GET",
   dataType: 'json',
   url: 'AllSubAccounts',
   processResults: function (data) {
     return {
       results: $.map(data, function(obj, index) {
         return { id: index, text: obj };
       })
     };
       
       	console.log(data);
       
   },
   data: function (params) {  
     var query = {
       search: params.term
     };
     if (params.term == "*") query.items = [];
     return { json: JSON.stringify( query ) }
   }
   }
   });
   
   
   $('#AccountExcess').on('select2:select', function (e) {
   console.log("select done", e.params.data);
   });
                      
   
           
         $("#client").select2({
           placeholder: "select...",
           ajax: {
               type: "GET",
               dataType: "json",
               url: "AllClientsFilter",
               processResults: function (data) {
                   return {
                       results: $.map(data, function (obj, index) {
   
                           return { id: index, text: obj };
                       }),
                   };
   
                   console.log(data);
               },
             data: function (params) {  
   
       
              var query = {
                       search: params.term,
                   };
                           
       
         $.ajax({
                         url: 'AllClientsFilterJS/'+params.term,
                         type:"GET",
                         dataType:"json",
                         beforeSend: function(){
                             $('#loader').css("visibility", "visible");
                         },
   
                         success:function(data) {
                                     $('#client').empty();  
                             $.each(data, function(key, value){
   
                    $('#client').append('<option value="'+ key +'">' + value + '</option>');
              
                             });
                         },
                         complete: function(){
                             $('#loader').css("visibility", "hidden");
                         }
                     });
       
       
       
   }
           },
       });
   
       $("#client").on("select2:select", function (e) {
           console.log("select done", e.params.data);
       });
       
                   
           
       });
   });
</script>
<script src="{{asset('Admin/js/formplugins/summernote/summernote.js')}}"></script>
<script>
   var autoSave = $('#autoSave');
   var interval;
   var timer = function()
   {
       interval = setInterval(function()
       {
           //start slide...
           if (autoSave.prop('checked'))
               saveToLocal();
   
           clearInterval(interval);
       }, 3000);
   };
   
   //save
   var saveToLocal = function()
   {
       localStorage.setItem('summernoteData', $('#saveToLocal').summernote("code"));
       console.log("saved");
   }
   
   //delete 
   var removeFromLocal = function()
   {
       localStorage.removeItem("summernoteData");
       $('#saveToLocal').summernote('reset');
   }
   
   $(document).ready(function()
   {
       //init default
       $('.js-summernote').summernote(
       {
           height: 200,
           tabsize: 2,
           placeholder: "Type here...",
           dialogsFade: true,
           toolbar: [
               ['style', ['style']],
               ['font', ['strikethrough', 'superscript', 'subscript']],
               ['font', ['bold', 'italic', 'underline', 'clear']],
               ['fontsize', ['fontsize']],
               ['fontname', ['fontname']],
               ['color', ['color']],
               ['para', ['ul', 'ol', 'paragraph']],
               ['height', ['height']]
               ['table', ['table']],
               ['insert', ['link', 'picture', 'video']],
               ['view', ['fullscreen', 'codeview', 'help']]
           ],
           callbacks:
           {
               //restore from localStorage
               onInit: function(e)
               {
                   $('.js-summernote').summernote("code", localStorage.getItem("summernoteData"));
               },
               onChange: function(contents, $editable)
               {
                   clearInterval(interval);
                   timer();
               }
           }
       });
   
       //load emojis
       $.ajax(
       {
           url: 'https://api.github.com/emojis',
           async: false
       }).then(function(data)
       {
           window.emojis = Object.keys(data);
           window.emojiUrls = data;
       });
   
       //init emoji example
       $(".js-hint2emoji").summernote(
       {
           height: 100,
           toolbar: false,
           placeholder: 'type starting with : and any alphabet',
           hint:
           {
               match: /:([\-+\w]+)$/,
               search: function(keyword, callback)
               {
                   callback($.grep(emojis, function(item)
                   {
                       return item.indexOf(keyword) === 0;
                   }));
               },
               template: function(item)
               {
                   var content = emojiUrls[item];
                   return '<img src="' + content + '" width="20" /> :' + item + ':';
               },
               content: function(item)
               {
                   var url = emojiUrls[item];
                   if (url)
                   {
                       return $('<img />').attr('src', url).css('width', 20)[0];
                   }
                   return '';
               }
           }
       });
   
       //init mentions example
       $(".js-hint2mention").summernote(
       {
           height: 100,
           toolbar: false,
           placeholder: "type starting with @",
           hint:
           {
               mentions: ['jayden', 'sam', 'alvin', 'david'],
               match: /\B@(\w*)$/,
               search: function(keyword, callback)
               {
                   callback($.grep(this.mentions, function(item)
                   {
                       return item.indexOf(keyword) == 0;
                   }));
               },
               content: function(item)
               {
                   return '@' + item;
               }
           }
       });
   
   });
   
</script>

@endpush