{"version": 3, "sources": ["cust-theme-10.css", "../_imports/_theme-modules-import.scss", "../_mixins/mixins.scss", "_modules/variables.scss", "_modules/_placeholders.scss", "../../../node_modules/bootstrap/scss/_variables.scss", "_modules/_body.scss", "../../../node_modules/bootstrap/scss/mixins/_hover.scss", "_modules/_page-header.scss", "_modules/_page-search.scss", "_modules/_dropdown-notification.scss", "_modules/_dropdown-app-list.scss", "../../../node_modules/bootstrap/scss/mixins/_breakpoints.scss", "_modules/_header-function-fixed.scss", "_modules/_nav.scss", "_modules/_nav-listfilter.scss", "_modules/_nav-info-card.scss", "_modules/_nav-function-top.scss", "_modules/_nav-function-minify.scss", "_modules/_nav-footer.scss", "_modules/_page-wrapper.scss", "_modules/_page-heading.scss", "_modules/_page-footer.scss", "_modules/_page-components-accordion.scss", "_modules/_page-components-alerts.scss", "_modules/_page-components-badge.scss", "_modules/_page-components-buttons.scss", "_modules/_page-components-navspills.scss", "_modules/_page-components-cards.scss", "_modules/_page-components-carousel.scss", "_modules/_page-components-dropdowns.scss", "../../../node_modules/bootstrap/scss/mixins/_gradients.scss", "_modules/_page-components-messanger.scss", "_modules/_page-components-modal.scss", "_modules/_page-components-panels.scss", "_modules/_page-components-popovers.scss", "_modules/_page-components-shortcut.scss", "_modules/_page-components-tabs.scss", "_modules/_page-components-custom-forms.scss", "../../../node_modules/bootstrap/scss/_functions.scss", "_modules/_page-components-pagination.scss", "_modules/_page-components-listgroup.scss", "_modules/_helpers.scss", "_modules/_misc.scss", "_modules/_hack.scss", "_modules/_responsive.scss", "_modules/_forms.scss", "_modules/_settings-demo.scss", "_modules/_settings-demo-incompatiblity-list.scss", "_modules/_mod-nav-accessibility.scss", "_modules/_colors.scss", "../../../node_modules/bootstrap/scss/mixins/_buttons.scss", "_modules/_custom-bootstrap-varients.scss", "_plugins/_plugins-chartist.scss", "_plugins/_plugins-select2.scss", "_plugins/_plugins-datatables.scss", "_plugins/_plugins-datepicker.scss", "_plugins/_plugins-daterangepicker.scss", "_plugins/_plugins-ion.rangeslider.scss", "_plugins/_plugins-pace.scss"], "names": [], "mappings": "AAAA;4EAC4E;AAO5E;4EAL4E;ACH5E;4EDK4E;AEL5E;;;;;sDFWsD;AEqNtD;;;;;;;yBF7MyB;AE2NzB;;;;;;;;;;;;yBF9MyB;AE0OzB;;;yBFtOyB;AE0QzB;;;;;;;;;;;yBF9PyB;AEwRzB;;;yBFpRyB;AEsSzB;;;yBFlSyB;AE4UzB,wBAAA;AAQA,0BAAA;AC5YA;4EH4D4E;AG1D5E,+CAAA;AAQA,+FAAA;AAQA;;;;;;kFHmDkF;AG3ClF;4EH6C4E;AGvC5E;4EHyC4E;AGvC5E,cAAA;AAYA,kBAAA;AAYA,iBAAA;AAYA,kBAAA;AAYA,cAAA;AAYA,eAAA;AAYA,kBAAA;AAmFA;4EH3G4E;AG+G5E;4EH7G4E;AG4HR,kGAAA;AACG,2EAAA;AAavE,+BAAA;AAgBA,6BAAA;AACA,wFAAA;AAQA;4EH5J4E;AGqL5E,oCAAA;AAYA,UAAA;AACA,wIAAA;AASA,UAAA;AAIA,aAAA;AAMA,qDAAA;AAGA,mCAAA;AAGA,oBAAA;AAgBA,iBAAA;AASA,gBAAA;AAGA,UAAA;AAIA,UAAA;AAOA,gBAAA;AAMA,UAAA;AAKA,UAAA;AAKA,eAAA;AAIA,iBAAA;AAUA,aAAA;AAIA,qBAAA;AAKA,WAAA;AASA,cAAA;AASA,oBAAA;AAOA,aAAA;AAcA,aAAA;AAYA,UAAA;AAUA;;;;;;;;;;;;;;;;;;;;;;;;;;;CHjTC;AG8UD,UAAA;AAuBA,aAAA;AAIA;4EHrW4E;AG6W5E,6EAAA;AAEiC,WAAA;AACD,WAAA;AACA,WAAA;AACA,WAAA;AACA,WAAA;AACA,WAAA;AACC,WAAA;AAEjC;4EH7W4E;AG+WlE,mFAAA;AAOV;4EHnX4E;AGqXG,mEAAA;AAE/E;4EHpX4E;AG0X5E,oEAAA;AAUA;4EHjY4E;AGqY5E;4EHnY4E;AGqY5B,0BAAA;AACH,iBAAA;AAG7C;4EHrY4E;AG0Y5E;4EHxY4E;AG8Y5E;4EH5Y4E;AGgZ5E;4EH9Y4E;AGiZ5E,WAAA;AAOA,WAAA;AAMA,SAAA;AAEoD,6DAAA;AACC,8DAAA;AACC,qDAAA;AAEtD,gCAAA;AAGA,qBAAA;AAC4D,uBAAA;AAO5D,QAAA;AAYA,uBAAA;AASA,UAAA;AAKA,sBAAA;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4EHja4E;AGgc5E,oBAAA;AACA,eAAA;AAMA,eAAA;AAGA,uBAAA;AAOA,mBAAA;AAOA,kBAAA;AAIA,cAAA;AAIA,cAAA;AAKA,eAAA;AAIA,gCAAA;AAGA,qBAAA;AACA,mCAAA;AAGA,mBAAA;AAQA,2CAAA;AAK6C,kBAAA;AAE7C,gCAAA;AAKyE,+CAAA;AAEzE;4EHnf4E;AGqf5E,eAAA;AAIA;4EHtf4E;AG6f5E;4EH3f4E;AG+f5E;4EH7f4E;AG0gB5E;4EHxgB4E;AG+gB5E;4EH7gB4E;AGqhB5E;4EHnhB4E;AG2hB5E;4EHzhB4E;AG8hB5E,oBAAA;AC9xBA;;;;;;;;;;;;;;;;;;;;;;;;;;CJ4RC;AIhQD;EAEE,gHAA6E;EAA7E,gFAA6E;EAC7E,yBDioBgD,EAAA;;AC9nBlD;;;;;;;;;CJyQC;AI/PD;EFpCE,yBEqCqC;EFnCrC,6FAAsD;EAAtD,2DAAsD;EEoCtD,WAAW,EAAA;EAEX;IACE,yBAAyB,EAAA;EAG3B;IACE,gCAAqC,EAAA;EAGvC;IACE,mBDP2C;ICQ3C,WCnDW,EAAA;;AH6WX;EEtSF,WCvEa;EDwEb,8BAAoC,EAAA;EFqSlC;IEnSA,2BAA4B;IAC5B,yBAA+B,EAAA;EFkS/B;IE/RA,cJnFmB,EAAA;;AI0FnB;EACE,cD+oB8B,EAAA;;AC7oBhC;EACE,cD6oB6B,EAAA;;AC9nBnC;EACE,yBAA0C;EAC1C,cDmf6C,EAAA;ECjf7C;IACE,qBJlHmB;IImHnB,mBDnE2C;ICoE3C,WC/GW,EAAA;;ADoIf;;;;EACE,mBDzD4C;EC0D5C,yBDnD4C;ECoD5C,cD1B0C,EAAA;;AF1G5C;4ED+U4E;AC7U5E,qCAAA;AAEA;;4ED+U4E;AC5U5E,2DAAA;AACA,8BAAA;AKlBA;EAEE,sBDKa,EAAA;ECPf;IAKG,cNHoB;IMIpB,6BAA6B,EAAA;ICM9B;MDHE,cH0C0C,EAAA;;AKnD9C;EACC,cL8fwC,EAAA;EK/fzC;;IAQI,cRNmB,EAAA;EQFvB;;IAcK,cL2jB+C,EAAA;EKzkBpD;IAgCG,qBAAA;IACA,8DAAA;IAKA,yBAAA,EAA0B;IAtC7B;MAuBI,cLkjBgD,EAAA;MKzkBpD;;QA2BK,yBAA0C,EAAA;IA3B/C;MAmCI,kBLmiB0B,EAAA;EKtkB9B;IA6CE,cL4hBkD,EAAA;;AKxhBpD;EACC,sBH3Cc,EAAA;;AIPf;EACC,uBN2lBkC;EM1lBlC,6BNylByC,EAAA;;AO3lB1C;EAGG,mBAAoC,EAAA;;AAHvC;EAOG,4CLUY,EAAA;EKjBf;IASI,2HAA+E;IAA/E,iFAA+E,EAAA;;AATnF;EAeE,cAA6B,EAAA;;AAf/B;;EAoBE,cAA6B,EAAA;;AApB/B;EAyBG,mBPUoB,EAAA;EOnCvB;IA2BI,gBLpBW,EAAA;EKPf;IA8BI,4CLbW,EAAA;;AKjBf;EAmCG,eAAe,EAAA;;ACnClB;EACC,cR8fwC,EAAA;EQ/fzC;IAIE,yBAAoD,EAAA;EAJtD;IAQE,qBXNqB,EAAA;;AY0DnB;EC3DH;IAGG,2DVmkByD;YUnkBzD,mDVmkByD,EAAA,EUlkBzD;;ACHJ;EACC,cX4rBoD,EAAA;;AWzrBrD;EZ0TI,YAA0B,EAAA;;AY1T9B;EAiCG,qDAAA,EAAsD;EAjCzD;IZ0TI,YAA0B;IY3S1B,2CTdW;IH6HX,yCFlImB;YEkInB,iCFlImB,EAAA;IcIvB;;MAsBS,cXgqB+D,EAAA;EWtrBxE;IA8BI,cAAc,EAAA;EA9BlB;IAmCI,cAAc,EAAA;;AAnClB;EAwCG,cX2nBiD,EAAA;EWnqBpD;IA2CK,+BAA4B,EAAA;EA3CjC;;IAiDO,cXooBwD,EAAA;EWrrB/D;IZ4LE,iCAKwC;IAJhC,yBAIgC,EAAA;EYjM1C;IZ0TI,YAA0B;IY7P1B,oCTlDW,EAAA;ISXf;MAgEK,WT/DU,EAAA;ISDf;;MAqEK,cXinBmE,EAAA;IWtrBxE;MAyEK,oDAAuD;cAAvD,4CAAuD;MACvD,wCAAmD,EAAA;EA1ExD;IZ0TI,YAA0B,EAAA;IY1T9B;MAmFK,WTlFU,EAAA;;ASDf;EAyFG,cX5C2C,EAAA;;AW7C9C;EA8FG,oCTnFY,EAAA;ESXf;IAmGK,cAAkC,EAAA;IAnGvC;;MAuGM,cX8kByD,EAAA;IWrrB/D;MA2GM,WT1GS;MS2GT,yBd5GgB,EAAA;IcAtB;MZ0TI,YAA0B;MYzMxB,oCTtGS,EAAA;MSXf;;QAsHW,cXgkB6D,EAAA;EWtrBxE;IZ0TI,YAA0B;IY1LxB,6BAA4B,EAAA;IAhIlC;;MZ0TI,YAA0B,EAAA;IY1T9B;;MA6IY,cXyiB4D,EAAA;EWtrBxE;IZ0TI,YAA0B,EAAA;EY1T9B;IAgKO,cAAkC,EAAA;IAhKzC;MZ0TI,YAA0B,EAAA;IY1T9B;MAuKQ,WTtKO;MSuKP,yBdxKc;McyKd,yBAA2C,EAAA;;AAanD,uBAAA;AACA;EACC,gBTvLc,EAAA;ESsLf;IAKG,yBAA6B,EAAA;IALhC;MAOI,yBAA6B,EAAA;IAPjC;MAWI,oCAAoC,EAAA;;AAOxC,0BAAA;AACA;EACC,qCXyI2C,EAAA;EW1I5C;IAIE,4CXsI0C,EAAA;;AY1V5C;EAEE,8BVea;EUdb,WVIa,EAAA;EUPf;IAMG,gCAAgC,EAAA;EANnC;IAUG,qBAA2C,EAAA;;ACV9C;EACC,WXMc,EAAA;EWPf;IAGE,uBAAyB,EAAA;;AJyDvB;EKxDH;IAEC,+BAAA,EAAgC;IAFjC;MAIE,WZDY,EAAA;IYHd;MAUG,kBAAiB,EAAA;MAVpB;QAaI,cd2tB+B,EAAA;McxuBnC;QAgBI,cdytB8B,EAAA;MczuBlC;QAmBI,mBd4DyC;Qc3DzC,yBdkEyC;QcjEzC,cd2FuC,EAAA;IchH3C;MA6BE,gHAA6E;MAA7E,gFAA6E;MAC7E,yBd8nB+C;Mc7nB/C,2DdoiB0D;ccpiB1D,mDdoiB0D,EAAA;McnkB5D;;QAmCG,cAAwC,EAAA;QAnC3C;;UAsCI,cAAwC,EAAA;MAtC5C;QA6CG,qCdIyC;gBcJzC,6BdIyC,EAAA;IcjD5C;MAmDE,gBZhDY;MYkDZ,2Dd8gB0D;cc9gB1D,mDd8gB0D,EAAA;McnkB5D;QAiEQ,cAAc,EAAA;MAjEtB;;QA6EO,cAAc,EAAA;MA7ErB;QAiFO,cAAoC,EAAA;MAjF3C;QAwFM,cdokB2C,EAAA;Mc5pBjD;QA4FM,mBdkkB2C,EAAA;Qc9pBjD;UAiGQ,cdokB2C,EAAA;QcrqBnD;UAqGQ,mBdyjByC,EAAA;Qc9pBjD;UA0GS,8BZ7FK;UY8FL,WZxGK,EAAA;QYHd;UAiHO,uBAAuB,EAAA;QAjH9B;UAsHO,cdwiB0C,EAAA;Mc9pBjD;QA8HO,cjBhIe;QiBiIf,uBAAuB,EAAA,EACvB;;ALxEJ;EM1DH;IAYO,cAAc,EAAA;EAZrB;IAyBO,sBAAsB,EAAA;EAzB7B;IA8BO,yBfgoB0C,EAAA;Ie9pBjD;MAkCQ,cf4nByC,EAAA;Ee9pBjD;IA6CI,iBAAiB,EAAA;IA7CrB;MAkDM,mBfgpB8C;Me/oB9C,Wb9CQ,EAAA;MaLd;QAsDQ,mBfwmByC,EAAA;Qe9pBjD;UAyDQ,cfqmByC,EAAA;Ee9pBjD;IAoEG,mBf9D2E;Ie+D3E,gCAA2D;IAC3D,sBAAyC,EAAA,EACzC;;ACzEJ;EAMI,chBgqBqD,EAAA;;AgB1pBzD;EAIE,mBhBgpBgD,EAAA;EgBppBlD;IAOG,oChB6qB2D;IgB1qB3D,yIAAqJ;IAArJ,yFAAqJ,EAAA;;APsCpJ;EO7BH;IAIE,yBAA6C,EAAA;IAJ/C;MAOG,chBqpB2D,EAAA;IgB5pB9D;MAWG,yBAA8C,EAAA;MAXjD;QAcI,chB+oBmE,EAAA,EgB9oBnE;;AC9CL;EACC,yBjBojB4D,EAAA;;AkBrjB7D;EACC,clB2rBuE,EAAA;;AkBzrBxE;EACC,clBI8E;EkBH9E,uBAAyB,EAAA;EAF1B;IAIE,clBqG2C,EAAA;;AmB5G7C;EACC,gBjBMc;EiBLX,cnBkxBkD,EAAA;;AoBpxBtD;EAGG,yBpB4jB6B,EAAA;EoB/jBhC;IAKI,cvBHmB,EAAA;IuBFvB;MAQK,cpBoGwC,EAAA;;AoB5G7C;EAeG,gBlBRY,EAAA;;AkBPf;EAqBG,gBlBdY,EAAA;EkBPf;IAwBK,WlBjBU;IkBkBV,yBpByB0C,EAAA;;AoBlD/C;EA8BG,WlBvBY;EkBwBZ,yBvB7BoB,EAAA;;AwBFvB;;;;;CxB4qBC;AwBrqBD;EACC,cAAoC;EACpC,yBAA6D;EAC7D,qBAAyD,EAAA;;AAG1D;EACC,cAAqC;EACrC,yBAA6D;EAC7D,qBAAyD,EAAA;;AAG1D;EACC,crBmF2C;EqBlF3C,yBAA2C;EAC3C,qBAAuC,EAAA;;AAIxC;EACC,cAA+B;EAC/B,yBAA4C;EAC5C,qBAAuC,EAAA;;AAIxC;EACC,crB6CwC;EqB5CxC,uBAAyC;EACzC,qBAAqC,EAAA;;AAItC;EACC,crBjC8E;EqBkC9E,yBAA0C;EAC1C,qBAAuC,EAAA;;AC3CxC;EACC,yBzBKqB;EyBJrB,WpBKc;EH6HX,kCG7HW;UH6HX,0BG7HW,EAAA;;AqBPf,eAAA;AACA;EACC,mBvB4G4C;EDkNzC,YAA0B,EAAA;EwB/T9B;IxB+TI,YAA0B,EAAA;EwB/T9B;IxB+TI,YAA0B,EAAA;EwB/T9B;IAaE,WrBPa;IqBQb,mB1BbqB,EAAA;I0BDvB;MxB+TI,YAA0B,EAAA;IwB/T9B;MAoBG,gBrBdY;MqBeZ,c1BpBoB,EAAA;;A0B0BvB,kDAAA;AACA;EACC,WrBvBc,EAAA;;AqB0Bf,mDAAA;AACA;EAIE,qBvBgE0C;ED9F1C,yBC6FyC;ED3FzC,6FAAsD;EAAtD,2DAAsD;EwB8BtD,WrBjCa,EAAA;;AqBuCf,iBAAA;AACA,sCAAA;AACA;EACC,8BrBhCc;EqBiCd,oCrBjCc;EqBkCd,sBAAwB;EACxB,wD1BlDsB;U0BkDtB,gD1BlDsB,EAAA;E0B8CvB;IAOE,mB1BrDqB;I0BsDrB,qBvBH2C,EAAA;;AuBO7C,aAAA;AAKA;EACC,crBlDiB;EqBmDjB,qBvB2N+B,EAAA;EuB7NhC;;IAQE,crBzDgB;IqB0DhB,yBAAkD;IAClD,qBvBmN8B,EAAA;EuB7NhC;IAeE,crBhEgB,EAAA;;AqBsElB,gBAAA;AAKE;EAHA,wD1BtFqB;U0BsFrB,gD1BtFqB,EAAA;;A0ByFrB;EAHA,wDrB3EgB;UqB2EhB,gDrB3EgB,EAAA;;AqB8EhB;EAHA,uD1BrFqB;U0BqFrB,+C1BrFqB,EAAA;;A0BwFrB;EAHA,uD1BpFkB;U0BoFlB,+C1BpFkB,EAAA;;A0BuFlB;EAHA,uD1BnFqB;U0BmFrB,+C1BnFqB,EAAA;;A0BsFrB;EAHA,uD1BlFoB;U0BkFpB,+C1BlFoB,EAAA;;A0BqFpB;EAHA,wDrBjFa;UqBiFb,gDrBjFa,EAAA;;AqBoFb;EAHA,qDvBhF6E;UuBgF7E,6CvBhF6E,EAAA;;AuBwF/E;EAEC,0CAAmC;EACnC,oCAAoC,EAAA;EAHrC;IAME,0CAAmC,EAAA;EANrC;IAUE,sBAAwB;IACxB,qDAA6C,EAAA;;AAK/C,sBAAA;AACA;EACE,c1BhHqB,EAAA;EOUrB;ImByGE,cvB/D0C,EAAA;;AwBtD9C;;EAGI,WtBIW;EsBHX,yB3BFmB,EAAA;;A4BFvB;EACC,yBzB8jB+B,EAAA;;A0B/jBhC;EACC,uHAA4E;EAA5E,mFAA4E,EAAA;;AAG7E;EACC,uHAA2E;EAA3E,kFAA2E,EAAA;;ACL5E,8BAAA;AACA;EAEE,gBzBIa,EAAA;;AyBNf;EAOI,mBzBAc;EyBCd,c3B6C0C,EAAA;;AItC5C;EuBEE,c3BoC0C;E4BhD1C,yBDa4B,EAAA;;AAHhC;EAQI,c3B+B0C;E4BjD1C,yBDmB8C,EAAA;;AEzBlD;EAEE,mB7B2wB+B,EAAA;;A6BvwBjC;EAEE,mBhCLqB,EAAA;;AiCHvB,sBAAA;AACA;EAEE,wD9BqD4C;U8BrD5C,gD9BqD4C,EAAA;;A8BvD9C;EAKE,kCAA2D,EAAA;;ACN7D;EAEC,sB7BKc;E6BFd,gCAAgC;EAEhC,qBAAA;EAUA,iBAAA,EAAkB;EAjBnB;IAUE,6BAAA,EAA8B;IAVhC;MAYG,wEAA0E;cAA1E,gEAA0E,EAAA;EAZ7E;IAwBK,clClBiB,EAAA;;AkC2BtB,mCAAA;AACA;EACC,mBAAmB,EAAA;;AAGpB,iBAAA;AACA;EACC,gB7BjCc,EAAA;;A6BoCf,wBAAA;AACA;EAGG,0C/BG4C;E+BF5C,2ClC9CoB;EkC+CpB,4ClC/CoB,EAAA;EkC0CvB;IASI,2E/BAyC,EAAA;;A+BM7C;;;ElC2zBE;AkCtzBF;EAGG,yB/B+B0C;E+B9B1C,0B7B5Bc;E6B6Bd,2B7B7Bc,EAAA;E6BwBjB;IAQI,yC7BhCa,EAAA;;A6BsCjB,6CAAA;AACA;EAIE,0CAAA,EAA2C;EAJ7C;IAOG,mBlCnFoB,EAAA;EkC4EvB;IAYG,mBlCtFoB,EAAA;EkC0EvB;IAiBG,mBlC1FmB,EAAA;;AkCiGtB,gBAAA;AACA;EACC,yB/BupBsE,EAAA;E+BxpBvE;IAIE,mB/BopBqE,EAAA;;A+BhpBvE;EAGE,gB7B5Ga;E6B6Gb,0FAA4D;EAA5D,2DAA4D,EAAA;;AAM9D;EACC,0CAAA;EACA;IAEE,cAAc,EAAA,EACd;;AC/HH;EAEE,qBhCmaoC,EAAA;;AiCratC;;EACE,mBpCCqB;EoCArB,sBAAuB,EAAA;EAErB;;IACE,mBjCiDwC,EAAA;;AiCxC9C;EACE,mBAAmB;EACnB,cAAc,EAAA;;AAGhB;EACE,mBjCZ6E,EAAA;;AkCR/E,mBAAA;AACA;EAOI,gCrCNmB;EqCOnB,crCPmB,EAAA;;AqCDvB;EAYI,crCXmB,EAAA;;AqCiBvB;;EAEC,crCnBsB,EAAA;;AsCQvB;EAGI,WjCNW;EiCOX,qBnCwC0C;E4BhD1C,yB5B+CyC,EAAA;;AmC3C7C;EASI,qBnCgC0C,EAAA;;AmCzC9C;EAaI,cnCyB2C;EmCxB3C,yBnCwB2C;EmCvB3C,qBnCuB2C,EAAA;;AmCtC/C;EAsBM,cjCnBY,EAAA;EiCHlB;IAyBQ,yBjC1BU,EAAA;;AiCoClB;EAKI,sBjC3CW;EiC4CX,yBnC0PyC,EAAA;;AmCjP7C;EAIM,qBnCXuC;E4B/CzC,yB/BJmB,EAAA;;AsC0DvB;EAQM,+KCV4E,EAAA;;ADElF;EAcM,yBnC1ByC,EAAA;;AmCY/C;EAiBM,yBnC7ByC,EAAA;;AmCsC/C;EAGM,yBnCzCyC,EAAA;;AmCmD/C;EAKM,yBjC5FY,EAAA;;AiCuFlB;EAWM,sBjCvGS,EAAA;;AiC4Ff;EAiBM,yBnCpEyC,EAAA;;AmCgF/C;EACE,cjCnHgB;EiCoHhB,uOjC4c+I;EiC3c/I,yBjCxHgB,EAAA;EiCqHlB;IAOI,qBtCrImB,EAAA;IsC8HvB;MAeM,cjCjIY;MiCkIZ,sBjCzIS,EAAA;EiCyHf;IAqBI,cjCxIc;IiCyId,yBjC7Ic,EAAA;;AiCsJlB;EAEI,qBtC/JmB,EAAA;;AsC6JvB;;EAQI,yBjC9Jc,EAAA;;AiCsJlB;EAYI,0BAA0B,EAAA;;AAI9B;EACE,cjClKgB;EiCmKhB,sBjC1Ka;EiC2Kb,yBjCvKgB,EAAA;EiCoKlB;IAMI,cjCvKc;I0BRd,yB1BGc,EAAA;;AiCuLlB;EACE,6BAA6B,EAAA;EAD/B;IP1LI,yB/BJmB;IsCmMnB,SjC4a0C,EAAA;IiCjb9C;MP1LI,yB5B4C2C,EAAA;EmC8I/C;IAaI,yBjCnMc,EAAA;EiCsLlB;IP1LI,yB/BJmB;IsCgNnB,SjC+Z0C,EAAA;IiCjb9C;MP1LI,yB5B4C2C,EAAA;EmC8I/C;IA0BI,yBjChNc,EAAA;EiCsLlB;IP1LI,yB/BJmB;IsC6NnB,SjCkZ0C,EAAA;IiCjb9C;MP1LI,yB5B4C2C,EAAA;EmC8I/C;IAwCI,yBjC9Nc,EAAA;EiCsLlB;IA4CI,yBjClOc,EAAA;EiCsLlB;IAiDM,yBjCrOY,EAAA;EiCoLlB;IAsDM,yBjC1OY,EAAA;EiCoLlB;IA2DM,yBjC/OY,EAAA;;AmCVlB;EACE,cxCDqB;EwCErB,sBnCGa;EmCFb,yBnCKgB;EmCGhB;;IxC4hCE,EwC1hCC;EAbL;IAMI,cnC8K8D;ImC7K9D,yBnCAc;ImCCd,qBnCAc,EAAA;;AmCQlB;EAEI,WnCbW;EmCcX,yBxCnBmB,EAAA;;AwCgBvB;EAOI,cnCZc;EmCad,sBnCnBW,EAAA;;AmCuBf;;;EAMG,mBAAqC,EAAA;;AANxC;EAYG,oCAAyC;EACzC,WnCpCY,EAAA;;AoCPf;EACE,sDAA2E,EAAA;EAD7E;IAGI,yBzCDmB;IyCEnB,qBzCFmB,EAAA;;A0CFvB,gBAAA;AACA;EAAY,sBrCMG;EqCNuB,cvC8fG,EAAA;;AuC7fzC;EAAY,yBvC6jBoB,EAAA;;AuC5jBhC;ExCKE,sBGDa;EHGb,0FAAsD;EAAtD,wDAAsD,EAAA;;AwCNxD;EAAkB,yBAA8C,EAAA;;AAChE;ExCGE,sBGDa;EHGb,0FAAsD;EAAtD,wDAAsD,EAAA;;AwCJxD;EAAgB,yBAA0C,EAAA;;AAE1D;EAAe,yBvC2BQ,EAAA;;AuC1BvB;EAAe,yBrCDG,EAAA;;AqCElB;EAAe,yBrCDG,EAAA;;AqCElB;EAAe,yBrCDG,EAAA;;AqCElB;EAAe,yBrCDG,EAAA;;AqCElB;EAAe,yBrCDG,EAAA;;AqCElB;EAAe,yBrCDG,EAAA;;AqCElB;EAAe,yBrCDG,EAAA;;AqCElB;EAAe,yBrCDG,EAAA;;AqCElB;EAAe,yBrCDG,EAAA;;AqCGlB,YAAA;AACA;EAAgB,wCvCgG4B,EAAA;;AuC7F5C,iBAAA;AACA,4CAAA;AACA;EACC,gBrCnBc,EAAA;;AqCsBf,WAAA;AACA;EACC,4BAA8C,EAAA;;AAG/C,gBAAA;AACA;EACC,kDAAuC;UAAvC,0CAAuC,EAAA;EADxC;IAIE,mBAAmB,EAAA;EAJrB;;;IAUE,gBAAgB,EAAA;;AC7ClB;EAEC,qDAA8F,EAAA;;AAO/F;EACC,mBAAmB,EAAA;;AAGpB,qBAAA;AACA;EACC,cAAc,EAAA;;AAIf,kBAAA;AACA;EACC,kBAAkB,EAAA;EADnB;IAIE,mBxChB6E;IwCiB7E,sBAAsB,EAAA;EALxB;IASE,mB3C1BqB,EAAA;E2CiBvB;IAaE,mB3C3BoB,EAAA;E2CctB;IAiBE,mB3ChCqB,EAAA;;A2CqCvB,kBAAA;AACA;EACC,cAA8B,EAAA;;AAE/B;EACC,iBAAiB,EAAA;;AAGlB,eAAA;AACA;EACC,cxCyD4C,EAAA;;AwCtD7C,iBAAA;A3CgoCA;E2C9nCC,WtClDc,EAAA;ELkrCb;I2C9nCA,8BtC1Ca,EAAA;;AsC8Cf,SAAA;AACA;EACC,mBAAuD,EAAA;;ACjExD,sBAAA;AACA;EACE,mBzCM6E;EyCL7E,WvCIa,EAAA;AuCNf;EACE,mBzCM6E;EyCL7E,WvCIa,EAAA;;AuCFf;EACE,mBzCE6E;EyCD7E,WvCAa,EAAA;;AwCNf;EAGC;IACC,gBxCEa,EAAA;IwCHd;MAIE,4CxCSY,EAAA;IwCbd;MAUE,WAAW,EAAA;MAVb;QAwBG,e1C0L4B,EAAA;I0ClN/B;MA6BE,yCxChBY,EAAA;EwCqBd,eAAA;EACC;IAKE,mB1CygByD,EAAA;E0CpgB5D,kCAAA;EACA,YAAA;EACC;IAGC,2CAAuC;I3C8EtC,sD2C7EmD;Y3C6EnD,8C2C7EmD,EAAA;EAJrD;IAQC,+BxC1CY,EAAA,EwC2CZ;;AAMH;EAEC,oDAAA;EACA;;G7C8pCE;E6C1pCF,sBAAA;EACA;IAOI,gBxC1EU,EAAA;IwCmEd;MAWK,qB7CnFiB,EAAA,E6CoFjB;;ACtFN,cAAA;A9C2uCA;E8CvuCE,+BAA+B;EAC/B,c3CsG2C,EAAA;;AHooC7C;E8CtuCE,kCAAiC;EACjC,WzCOa,EAAA;;AL8tCf;E8CtuCE,kCAAiC;EACjC,WzCOa,EAAA;;ALkuCf;E8CruCE,kCAAiC;EACjC,WzCEa,EAAA;;AyCCf,uDAAA;AACA;EACC,mB9ClBsB,EAAA;;A8CoBvB;EAEE,qB9CtBqB,EAAA;E8CoBvB;IAIG,yCzCTY,EAAA;;AyCKf;EAQE,mB9C5BqB;E8C6BrB,sBAAwB,EAAA;;AAK1B,4CAAA;AACA;EACC,c3CqE4C,EAAA;;A2ClE7C;EACC,czC5BiB;EyC6BjB,sBzCpCc;EyCqCd,qB9C1CsB,EAAA;;A+CFvB;EAIE,c5CI6E,EAAA;;A4CR/E;EASE,cAAyB,EAAA;EAT3B;IAYG,cAAyB;IACzB,oC1CNY,EAAA;;A0CPf;EAqBG,4B5C0FyC,EAAA;;A6CxF5C;EAEC;;;;;IAMO,kBAAiB,EAAA;IANxB;;;;;MASO,yBAA0C,EAAA;IATjD;;;;;MAYO,yBAAyC,EAAA;IAZhD;;;;;MAeO,mB7C2CsC;M6C1CtC,yB7CiDsC;M6ChDtC,c7C0EoC,EAAA,E6CzErC;;AC3CP,4BAAA;AAEA;EAQI,0CAAA,EAA2C;EAR/C;IAKK,8BAAmD,EAAA;EALxD;IAYO,yB9C6qBwD,EAAA;;ADzV3D;EACE,yBCpTwC;EDqR1C,yBAAsB,EAAA;EA8BtB;IA9BA,yBAAsB,EAAA;;AA8BtB;EACE,yBCnTyC;EDoR3C,yBAAsB,EAAA;EA8BtB;IA9BA,yBAAsB,EAAA;;AA8BtB;EACE,yBClTyC;EDmR3C,yBAAsB,EAAA;EA8BtB;IA9BA,yBAAsB,EAAA;;AA8BtB;EACE,yBCjTyC;EDkR3C,yBAAsB,EAAA;EA8BtB;IA9BA,yBAAsB,EAAA;;AA8BtB;EACE,yBChTwC;ED6Q1C,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AAkC1B;EACE,yBFjWiB;EE8TnB,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AAkC1B;EACE,yBC9SuC;ED2QzC,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AAkC1B;EACE,yBC7SwC;ED0Q1C,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AAkC1B;EACE,yBC5SwC;EDyQ1C,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AAkC1B;EACE,yBC3SwC;EDwQ1C,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AA4C1B;EACE,cC9TwC,EAAA;;AD6T1C;EACE,cC7TyC,EAAA;;AD4T3C;EACE,cC5TyC,EAAA;;AD2T3C;EACE,cC3TyC,EAAA;;AD0T3C;EACE,cC1TwC,EAAA;;ADyT1C;EACE,cF3WiB,EAAA;;AE0WnB;EACE,cCxTuC,EAAA;;ADuTzC;EACE,cCvTwC,EAAA;;ADsT1C;EACE,cCtTwC,EAAA;;ADqT1C;EACE,cCrTwC,EAAA;;AD0S1C;EACE,yBCxSwC;EDyQ1C,yBAAsB,EAAA;EA8BtB;IA9BA,yBAAsB,EAAA;;AA8BtB;EACE,yBCvSyC;EDwQ3C,yBAAsB,EAAA;EA8BtB;IA9BA,yBAAsB,EAAA;;AA8BtB;EACE,yBCtSyC;EDuQ3C,yBAAsB,EAAA;EA8BtB;IA9BA,yBAAsB,EAAA;;AA8BtB;EACE,yBCrSyC;EDsQ3C,yBAAsB,EAAA;EA8BtB;IA9BA,yBAAsB,EAAA;;AA8BtB;EACE,yBCpSwC;EDqQ1C,yBAAsB,EAAA;EA8BtB;IA9BA,yBAAsB,EAAA;;AA8BtB;EACE,yBFhWiB;EE6TnB,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AAkC1B;EACE,yBClSuC;ED+PzC,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AAkC1B;EACE,yBCjSwC;ED8P1C,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AAkC1B;EACE,yBChSwC;ED6P1C,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AAkC1B;EACE,yBC/RwC;ED4P1C,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AA4C1B;EACE,cClTwC,EAAA;;ADiT1C;EACE,cCjTyC,EAAA;;ADgT3C;EACE,cChTyC,EAAA;;AD+S3C;EACE,cC/SyC,EAAA;;AD8S3C;EACE,cC9SwC,EAAA;;AD6S1C;EACE,cF1WiB,EAAA;;AEyWnB;EACE,cC5SuC,EAAA;;AD2SzC;EACE,cC3SwC,EAAA;;AD0S1C;EACE,cC1SwC,EAAA;;ADyS1C;EACE,cCzSwC,EAAA;;AD8R1C;EACE,yBC5RmC;ED6PrC,yBAAsB,EAAA;EA8BtB;IA9BA,yBAAsB,EAAA;;AA8BtB;EACE,yBC3RoC;ED4PtC,yBAAsB,EAAA;EA8BtB;IA9BA,yBAAsB,EAAA;;AA8BtB;EACE,yBC1RoC;ED2PtC,yBAAsB,EAAA;EA8BtB;IA9BA,yBAAsB,EAAA;;AA8BtB;EACE,yBCzRoC;EDsPtC,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AAkC1B;EACE,yBCxRmC;EDqPrC,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AAkC1B;EACE,yBF/Vc;EE4ThB,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AAkC1B;EACE,yBCtRkC;EDmPpC,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AAkC1B;EACE,yBCrRmC;EDkPrC,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AAkC1B;EACE,yBCpRmC;EDiPrC,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AAkC1B;EACE,yBCnRmC;EDgPrC,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AA4C1B;EACE,cCtSmC,EAAA;;ADqSrC;EACE,cCrSoC,EAAA;;ADoStC;EACE,cCpSoC,EAAA;;ADmStC;EACE,cCnSoC,EAAA;;ADkStC;EACE,cClSmC,EAAA;;ADiSrC;EACE,cFzWc,EAAA;;AEwWhB;EACE,cChSkC,EAAA;;AD+RpC;EACE,cC/RmC,EAAA;;AD8RrC;EACE,cC9RmC,EAAA;;AD6RrC;EACE,cC7RmC,EAAA;;ADkRrC;EACE,yBChRwC;EDiP1C,yBAAsB,EAAA;EA8BtB;IA9BA,yBAAsB,EAAA;;AA8BtB;EACE,yBC/QyC;EDgP3C,yBAAsB,EAAA;EA8BtB;IA9BA,yBAAsB,EAAA;;AA8BtB;EACE,yBC9QyC;ED+O3C,yBAAsB,EAAA;EA8BtB;IA9BA,yBAAsB,EAAA;;AA8BtB;EACE,yBC7QyC;ED8O3C,yBAAsB,EAAA;EA8BtB;IA9BA,yBAAsB,EAAA;;AA8BtB;EACE,yBC5QwC;ED6O1C,yBAAsB,EAAA;EA8BtB;IA9BA,yBAAsB,EAAA;;AA8BtB;EACE,yBF9ViB;EE+TnB,yBAAsB,EAAA;EA8BtB;IA9BA,yBAAsB,EAAA;;AA8BtB;EACE,yBC1QuC;ED2OzC,yBAAsB,EAAA;EA8BtB;IA9BA,yBAAsB,EAAA;;AA8BtB;EACE,yBCzQwC;ED0O1C,yBAAsB,EAAA;EA8BtB;IA9BA,yBAAsB,EAAA;;AA8BtB;EACE,yBCxQwC;EDqO1C,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AAkC1B;EACE,yBCvQwC;EDoO1C,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AA4C1B;EACE,cC1RwC,EAAA;;ADyR1C;EACE,cCzRyC,EAAA;;ADwR3C;EACE,cCxRyC,EAAA;;ADuR3C;EACE,cCvRyC,EAAA;;ADsR3C;EACE,cCtRwC,EAAA;;ADqR1C;EACE,cFxWiB,EAAA;;AEuWnB;EACE,cCpRuC,EAAA;;ADmRzC;EACE,cCnRwC,EAAA;;ADkR1C;EACE,cClRwC,EAAA;;ADiR1C;EACE,cCjRwC,EAAA;;ADsQ1C;EACE,yBCpQuC;EDqOzC,yBAAsB,EAAA;EA8BtB;IA9BA,yBAAsB,EAAA;;AA8BtB;EACE,yBCnQuC;EDoOzC,yBAAsB,EAAA;EA8BtB;IA9BA,yBAAsB,EAAA;;AA8BtB;EACE,yBClQuC;EDmOzC,yBAAsB,EAAA;EA8BtB;IA9BA,yBAAsB,EAAA;;AA8BtB;EACE,yBCjQuC;ED8NzC,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AAkC1B;EACE,yBChQsC;ED6NxC,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AAkC1B;EACE,yBF7VgB;EE0TlB,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AAkC1B;EACE,yBC9PqC;ED2NvC,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AAkC1B;EACE,yBC7PsC;ED0NxC,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AAkC1B;EACE,yBC5PsC;EDyNxC,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AAkC1B;EACE,yBC3PsC;EDwNxC,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AA4C1B;EACE,cC9QuC,EAAA;;AD6QzC;EACE,cC7QuC,EAAA;;AD4QzC;EACE,cC5QuC,EAAA;;AD2QzC;EACE,cC3QuC,EAAA;;AD0QzC;EACE,cC1QsC,EAAA;;ADyQxC;EACE,cFvWgB,EAAA;;AEsWlB;EACE,cCxQqC,EAAA;;ADuQvC;EACE,cCvQsC,EAAA;;ADsQxC;EACE,cCtQsC,EAAA;;ADqQxC;EACE,cCrQsC,EAAA;;AD0PxC;EACE,yBCxPuC;EDqNzC,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AAkC1B;EACE,yBCvPuC;EDoNzC,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AAkC1B;EACE,yBCtPuC;EDmNzC,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AAkC1B;EACE,yBCrPuC;EDkNzC,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AAkC1B;EACE,yBCpPsC;EDiNxC,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AAkC1B;EACE,yBC3VyE;EDwT3E,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AAkC1B;EACE,yBClPqC;ED+MvC,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AAkC1B;EACE,yBCjPsC;ED8MxC,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AAkC1B;EACE,yBChPsC;ED6MxC,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AAkC1B;EACE,yBC/OsC;ED4MxC,YAA0B,EAAA;EAkC1B;IAlCA,YAA0B,EAAA;;AA4C1B;EACE,cClQuC,EAAA;;ADiQzC;EACE,cCjQuC,EAAA;;ADgQzC;EACE,cChQuC,EAAA;;AD+PzC;EACE,cC/PuC,EAAA;;AD8PzC;EACE,cC9PsC,EAAA;;AD6PxC;EACE,cCrWyE,EAAA;;ADoW3E;EACE,cC5PqC,EAAA;;AD2PvC;EACE,cC3PsC,EAAA;;AD0PxC;EACE,cC1PsC,EAAA;;ADyPxC;EACE,cCzPsC,EAAA;;ADwPxC;EACE,WGtWS,EAAA;;AHqWX;EACE,cgDjO+B,EAAA;;AhDiPjC;EAEE,6EAAyE,EAAA;;AAF3E;EAEE,8EAAyE,EAAA;;AAF3E;EAEE,6EAAyE,EAAA;;AAF3E;EAEE,+EAAyE,EAAA;;AAF3E;EAEE,8EAAyE,EAAA;;AAF3E;EAEE,6EAAyE,EAAA;;AwBpS7E;EyBrFA,W9CCa;E0BDX,yB/BJmB;EmDMrB,qBnDNqB;EEkInB,2FGnHW;UHmHX,mFGnHW,EAAA;EELb;I4CAE,W9CLW;I0BDX,yBoBDoF;IASpF,qBATyH,EAAA;EAY3H;IAEE,W9CZW;I0BDX,yBoBDoF;IAgBpF,qBAhByH;IAqBvH,yDAAiF;YAAjF,iDAAiF,EAAA;EAKrF;IAEE,W9C1BW;I8C2BX,yBnDhCmB;ImDiCnB,qBnDjCmB,EAAA;EmDwCrB;;IAGE,W9CtCW;I8CuCX,yBAzCuK;IA6CvK,qBA7C+M,EAAA;IA+C/M;;MAKI,yDAAiF;cAAjF,iDAAiF,EAAA;;AzBkCvF;EyBrFA,W9CCa;E0BDX,yB1BOc;E8CLhB,qB9CKgB;EHuHd,2FGnHW;UHmHX,mFGnHW,EAAA;EELb;I4CAE,W9CLW;I0BDX,yBoBDoF;IASpF,qBATyH,EAAA;EAY3H;IAEE,W9CZW;I0BDX,yBoBDoF;IAgBpF,qBAhByH;IAqBvH,yDAAiF;YAAjF,iDAAiF,EAAA;EAKrF;IAEE,W9C1BW;I8C2BX,yB9CrBc;I8CsBd,qB9CtBc,EAAA;E8C6BhB;;IAGE,W9CtCW;I8CuCX,yBAzCuK;IA6CvK,qBA7C+M,EAAA;IA+C/M;;MAKI,yDAAiF;cAAjF,iDAAiF,EAAA;;AzBkCvF;EyBrFA,W9CCa;E0BDX,yB/BHmB;EmDKrB,qBnDLqB;EEiInB,2FGnHW;UHmHX,mFGnHW,EAAA;EELb;I4CAE,W9CLW;I0BDX,yBoBDoF;IASpF,qBATyH,EAAA;EAY3H;IAEE,W9CZW;I0BDX,yBoBDoF;IAgBpF,qBAhByH;IAqBvH,wDAAiF;YAAjF,gDAAiF,EAAA;EAKrF;IAEE,W9C1BW;I8C2BX,yBnD/BmB;ImDgCnB,qBnDhCmB,EAAA;EmDuCrB;;IAGE,W9CtCW;I8CuCX,yBAzCuK;IA6CvK,qBA7C+M,EAAA;IA+C/M;;MAKI,wDAAiF;cAAjF,gDAAiF,EAAA;;AzBkCvF;EyBrFA,W9CCa;E0BDX,yB/BFgB;EmDIlB,qBnDJkB;EEgIhB,2FGnHW;UHmHX,mFGnHW,EAAA;EELb;I4CAE,W9CLW;I0BDX,yBoBDoF;IASpF,qBATyH,EAAA;EAY3H;IAEE,W9CZW;I0BDX,yBoBDoF;IAgBpF,qBAhByH;IAqBvH,wDAAiF;YAAjF,gDAAiF,EAAA;EAKrF;IAEE,W9C1BW;I8C2BX,yBnD9BgB;ImD+BhB,qBnD/BgB,EAAA;EmDsClB;;IAGE,W9CtCW;I8CuCX,yBAzCuK;IA6CvK,qBA7C+M,EAAA;IA+C/M;;MAKI,wDAAiF;cAAjF,gDAAiF,EAAA;;AzBkCvF;EyBrFA,c9CUgB;E0BVd,yB/BDmB;EmDGrB,qBnDHqB;EE+HnB,2FGnHW;UHmHX,mFGnHW,EAAA;EELb;I4CAE,c9CIc;I0BVd,yBoBDoF;IASpF,qBATyH,EAAA;EAY3H;IAEE,c9CHc;I0BVd,yBoBDoF;IAgBpF,qBAhByH;IAqBvH,wDAAiF;YAAjF,gDAAiF,EAAA;EAKrF;IAEE,c9CjBc;I8CkBd,yBnD7BmB;ImD8BnB,qBnD9BmB,EAAA;EmDqCrB;;IAGE,c9C7Bc;I8C8Bd,yBAzCuK;IA6CvK,qBA7C+M,EAAA;IA+C/M;;MAKI,wDAAiF;cAAjF,gDAAiF,EAAA;;AzBkCvF;EyBrFA,W9CCa;E0BDX,yB/BAkB;EmDEpB,qBnDFoB;EE8HlB,2FGnHW;UHmHX,mFGnHW,EAAA;EELb;I4CAE,W9CLW;I0BDX,yBoBDoF;IASpF,qBATyH,EAAA;EAY3H;IAEE,W9CZW;I0BDX,yBoBDoF;IAgBpF,qBAhByH;IAqBvH,wDAAiF;YAAjF,gDAAiF,EAAA;EAKrF;IAEE,W9C1BW;I8C2BX,yBnD5BkB;ImD6BlB,qBnD7BkB,EAAA;EmDoCpB;;IAGE,W9CtCW;I8CuCX,yBAzCuK;IA6CvK,qBA7C+M,EAAA;IA+C/M;;MAKI,wDAAiF;cAAjF,gDAAiF,EAAA;;AzBkCvF;EyBrFA,c9CUgB;E0BVd,sB1BCW;E8CCb,kB9CDa;EH6HX,2FGnHW;UHmHX,mFGnHW,EAAA;EELb;I4CAE,c9CIc;I0BVd,yBoBDoF;IASpF,qBATyH,EAAA;EAY3H;IAEE,c9CHc;I0BVd,yBoBDoF;IAgBpF,qBAhByH;IAqBvH,yDAAiF;YAAjF,iDAAiF,EAAA;EAKrF;IAEE,c9CjBc;I8CkBd,sB9C3BW;I8C4BX,kB9C5BW,EAAA;E8CmCb;;IAGE,c9C7Bc;I8C8Bd,yBAzCuK;IA6CvK,qBA7C+M,EAAA;IA+C/M;;MAKI,yDAAiF;cAAjF,iDAAiF,EAAA;;AzBkCvF;EyBrFA,W9CCa;E0BDX,yB5BE2E;EgDA7E,qBhDA6E;ED4H3E,2FGnHW;UHmHX,mFGnHW,EAAA;EELb;I4CAE,W9CLW;I0BDX,yBoBDoF;IASpF,qBATyH,EAAA;EAY3H;IAEE,W9CZW;I0BDX,yBoBDoF;IAgBpF,qBAhByH;IAqBvH,sDAAiF;YAAjF,8CAAiF,EAAA;EAKrF;IAEE,W9C1BW;I8C2BX,yBhD1B2E;IgD2B3E,qBhD3B2E,EAAA;EgDkC7E;;IAGE,W9CtCW;I8CuCX,yBAzCuK;IA6CvK,qBA7C+M,EAAA;IA+C/M;;MAKI,sDAAiF;cAAjF,8CAAiF,EAAA;;ACfxF;EDsBC,cnD9DqB;EmD+DrB,qBnD/DqB,EAAA;EOUrB;I4CwDE,W9C7DW;I8C8DX,yBnDnEmB;ImDoEnB,qBnDpEmB,EAAA;EmDuErB;IAEE,yDnDzEmB;YmDyEnB,iDnDzEmB,EAAA;EmD4ErB;IAEE,cnD9EmB;ImD+EnB,6BAA6B,EAAA;EAG/B;;IAGE,W9ChFW;I8CiFX,yBnDtFmB;ImDuFnB,qBnDvFmB,EAAA;ImDyFnB;;MAKI,yDnD9Fe;cmD8Ff,iDnD9Fe,EAAA;;AoDwCtB;EDsBC,c9CnDgB;E8CoDhB,qB9CpDgB,EAAA;EEDhB;I4CwDE,W9C7DW;I8C8DX,yB9CxDc;I8CyDd,qB9CzDc,EAAA;E8C4DhB;IAEE,yD9C9Dc;Y8C8Dd,iD9C9Dc,EAAA;E8CiEhB;IAEE,c9CnEc;I8CoEd,6BAA6B,EAAA;EAG/B;;IAGE,W9ChFW;I8CiFX,yB9C3Ec;I8C4Ed,qB9C5Ec,EAAA;I8C8Ed;;MAKI,yD9CnFU;c8CmFV,iD9CnFU,EAAA;;A+C6BjB;EDsBC,cnD7DqB;EmD8DrB,qBnD9DqB,EAAA;EOSrB;I4CwDE,W9C7DW;I8C8DX,yBnDlEmB;ImDmEnB,qBnDnEmB,EAAA;EmDsErB;IAEE,wDnDxEmB;YmDwEnB,gDnDxEmB,EAAA;EmD2ErB;IAEE,cnD7EmB;ImD8EnB,6BAA6B,EAAA;EAG/B;;IAGE,W9ChFW;I8CiFX,yBnDrFmB;ImDsFnB,qBnDtFmB,EAAA;ImDwFnB;;MAKI,wDnD7Fe;cmD6Ff,gDnD7Fe,EAAA;;AoDuCtB;EDsBC,cnD5DkB;EmD6DlB,qBnD7DkB,EAAA;EOQlB;I4CwDE,W9C7DW;I8C8DX,yBnDjEgB;ImDkEhB,qBnDlEgB,EAAA;EmDqElB;IAEE,wDnDvEgB;YmDuEhB,gDnDvEgB,EAAA;EmD0ElB;IAEE,cnD5EgB;ImD6EhB,6BAA6B,EAAA;EAG/B;;IAGE,W9ChFW;I8CiFX,yBnDpFgB;ImDqFhB,qBnDrFgB,EAAA;ImDuFhB;;MAKI,wDnD5FY;cmD4FZ,gDnD5FY,EAAA;;AoDsCnB;EDsBC,cnD3DqB;EmD4DrB,qBnD5DqB,EAAA;EOOrB;I4CwDE,c9CpDc;I8CqDd,yBnDhEmB;ImDiEnB,qBnDjEmB,EAAA;EmDoErB;IAEE,wDnDtEmB;YmDsEnB,gDnDtEmB,EAAA;EmDyErB;IAEE,cnD3EmB;ImD4EnB,6BAA6B,EAAA;EAG/B;;IAGE,c9CvEc;I8CwEd,yBnDnFmB;ImDoFnB,qBnDpFmB,EAAA;ImDsFnB;;MAKI,wDnD3Fe;cmD2Ff,gDnD3Fe,EAAA;;AoDqCtB;EDsBC,cnD1DoB;EmD2DpB,qBnD3DoB,EAAA;EOMpB;I4CwDE,W9C7DW;I8C8DX,yBnD/DkB;ImDgElB,qBnDhEkB,EAAA;EmDmEpB;IAEE,wDnDrEkB;YmDqElB,gDnDrEkB,EAAA;EmDwEpB;IAEE,cnD1EkB;ImD2ElB,6BAA6B,EAAA;EAG/B;;IAGE,W9ChFW;I8CiFX,yBnDlFkB;ImDmFlB,qBnDnFkB,EAAA;ImDqFlB;;MAKI,wDnD1Fc;cmD0Fd,gDnD1Fc,EAAA;;AoDoCrB;EDsBC,W9CzDa;E8C0Db,kB9C1Da,EAAA;EEKb;I4CwDE,c9CpDc;I8CqDd,sB9C9DW;I8C+DX,kB9C/DW,EAAA;E8CkEb;IAEE,yD9CpEW;Y8CoEX,iD9CpEW,EAAA;E8CuEb;IAEE,W9CzEW;I8C0EX,6BAA6B,EAAA;EAG/B;;IAGE,c9CvEc;I8CwEd,sB9CjFW;I8CkFX,kB9ClFW,EAAA;I8CoFX;;MAKI,yD9CzFO;c8CyFP,iD9CzFO,EAAA;;A+CmCd;EDsBC,chDxD6E;EgDyD7E,qBhDzD6E,EAAA;EII7E;I4CwDE,W9C7DW;I8C8DX,yBhD7D2E;IgD8D3E,qBhD9D2E,EAAA;EgDiE7E;IAEE,sDhDnE2E;YgDmE3E,8ChDnE2E,EAAA;EgDsE7E;IAEE,chDxE2E;IgDyE3E,6BAA6B,EAAA;EAG/B;;IAGE,W9ChFW;I8CiFX,yBhDhF2E;IgDiF3E,qBhDjF2E,EAAA;IgDmF3E;;MAKI,sDhDxFuE;cgDwFvE,8ChDxFuE,EAAA;;AiDyC9E;EACC,gCAA+B,EAAA;;AADhC;EACC,gCAA+B,EAAA;;AADhC;EACC,gCAA+B,EAAA;;AADhC;EACC,gCAA+B,EAAA;;AADhC;EACC,gCAA+B,EAAA;;AADhC;EACC,gCAA+B,EAAA;;AADhC;EACC,6BAA+B,EAAA;;AADhC;EACC,gCAA+B,EAAA;;AA7BhC;EACC,yBAAwB,EAAA;;A7CNxB;E6CWE,yBAA0E,EAAA;;AAN7E;EACC,yBAAwB,EAAA;;A7CNxB;E6CWE,yBAA0E,EAAA;;AAN7E;EACC,yBAAwB,EAAA;;A7CNxB;E6CWE,yBAA0E,EAAA;;AAN7E;EACC,yBAAwB,EAAA;;A7CNxB;E6CWE,yBAA0E,EAAA;;AAN7E;EACC,yBAAwB,EAAA;;A7CNxB;E6CWE,yBAA0E,EAAA;;AAN7E;EACC,yBAAwB,EAAA;;A7CNxB;E6CWE,yBAA0E,EAAA;;AAN7E;EACC,sBAAwB,EAAA;;A7CNxB;E6CWE,yBAA0E,EAAA;;AAN7E;EACC,yBAAwB,EAAA;;A7CNxB;E6CWE,yBAA0E,EAAA;;AAzB7E;EACC,oCAAmC,EAAA;;A7CanC;;;E6CRC,oCAAgD,EAAA;;AANlD;EACC,oCAAmC,EAAA;;A7CanC;;;E6CRC,oCAAgD,EAAA;;AANlD;EACC,oCAAmC,EAAA;;A7CanC;;;E6CRC,oCAAgD,EAAA;;AANlD;EACC,oCAAmC,EAAA;;A7CanC;;;E6CRC,oCAAgD,EAAA;;AANlD;EACC,oCAAmC,EAAA;;A7CanC;;;E6CRC,oCAAgD,EAAA;;AANlD;EACC,oCAAmC,EAAA;;A7CanC;;;E6CRC,oCAAgD,EAAA;;AANlD;EACC,iCAAmC,EAAA;;A7CanC;;;E6CRC,oCAAgD,EAAA;;AANlD;EACC,oCAAmC,EAAA;;A7CanC;;;E6CRC,oCAAgD,EAAA;;AA+DnD;EAEE,wBAAiC;EAAjC,0BAAiC;EAAjC,wBAAiC;EAAjC,qBAAiC;EAAjC,wBAAiC;EAAjC,uBAAiC;EAAjC,mBAAiC;EAAjC,qBAAiC;EAIjC,gCAA0C;EAA1C,+BAA0C;EAA1C,4BAA0C;EAA1C,+BAA0C;EAA1C,8BAA0C;EAA1C,qCAA0C;EAA1C,2BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,2BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,wBAA0C;EAA1C,yBAA0C;EAA1C,yBAA0C;EAA1C,yBAA0C;EAA1C,yBAA0C;EAA1C,yBAA0C;EAA1C,yBAA0C;EAA1C,yBAA0C;EAA1C,yBAA0C;EAA1C,yBAA0C;EAA1C,2BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,0BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C;EAA1C,0BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C,EAAA;;AC9C1C;EACE,erD9BmB,EAAA;;AqDgCrB;EACE,arDjCmB,EAAA;;AqD6BrB;EACE,erD1BkB,EAAA;;AqD4BpB;EACE,arD7BkB,EAAA;;AqDyBpB;EACE,erD3BmB,EAAA;;AqD6BrB;EACE,arD9BmB,EAAA;;AqD0BrB;EACE,erD5BgB,EAAA;;AqD8BlB;EACE,arD/BgB,EAAA;;AqD2BlB;EACE,elDxB2E,EAAA;;AkD0B7E;EACE,alD3B2E,EAAA;;AkDuB7E;EACE,erD7BmB,EAAA;;AqD+BrB;EACE,arDhCmB,EAAA;;AqD4BrB;EACE,erD5BgB,EAAA;;AqD8BlB;EACE,arD/BgB,EAAA;;AqD2BlB;EACE,elDwB0C,EAAA;;AkDtB5C;EACE,alDqB0C,EAAA;;AkDzB5C;EACE,elDgEyC,EAAA;;AkD9D3C;EACE,alD6DyC,EAAA;;AkDjE3C;EACE,elDqD2C,EAAA;;AkDnD7C;EACE,alDkD2C,EAAA;;AkDtD7C;EACE,elDwEwC,EAAA;;AkDtE1C;EACE,alDqEwC,EAAA;;AkDzE1C;EACE,elD8EyC,EAAA;;AkD5E3C;EACE,alD2EyC,EAAA;;AkD/E3C;EACE,elD8B2C,EAAA;;AkD5B7C;EACE,alD2B2C,EAAA;;AkD/B7C;EACE,elD0CsC,EAAA;;AkDxCxC;EACE,alDuCsC,EAAA;;AkD3CxC;EACE,elDkB2C,EAAA;;AkDhB7C;EACE,alDe2C,EAAA;;AmDjD/C;;EAEC,qBnD2R+B,EAAA;;AmDzRhC;EACC,qBnDwR+B,EAAA;;AmDrRhC;EAGE,ctDVqB,EAAA;;AsDcvB;EACC,yBAA8B,EAAA;;AAG/B;EACC,qBtDnBsB,EAAA;;AsDsBvB;EACC,qBtDvBsB,EAAA;;AsD2BvB;EACE,cjDhBgB;EiDiBhB,sBjDxBa;EiDyBb,qBnD8P8B;EmD7P9B,sEAA2D;UAA3D,8DAA2D,EAAA;EAJ7D;IAOG,qBAA8C,EAAA;;AAIjD;EACI,iBAAiB;EACjB,cAAc,EAAA;;AAGlB;EACC,mBnDoWqD;EmDnWrD,cnDS6C,EAAA;;AmDN9C;EACC,yBtDjDsB;EsDkDtB,WjD7Cc,EAAA;;AiDgDf;;;;EAIC,qBtDzDsB,EAAA;;AsD4DvB;;EAEC,qBtD9DsB,EAAA;;AsDiEvB;EACC,mBnD8UqD;EmD7UrD,qBnDlB6C;EmDmB7C,cnDd6C,EAAA;EmDW9C;IAME,cAAiC,EAAA;IANnC;MASG,ctD1EoB,EAAA;IsDiEvB;MAaG,uDAA+C;cAA/C,+CAA+C,EAAA;;AAKlD;EACC,ctDhFqB,EAAA;EsD+EtB;IAIE,cnDYyC,EAAA;;AmDrF3C;EA6EC,ctDvFqB,EAAA;;AuDNtB;;;EAGC,2CvDDsB,EAAA;;AuDIvB;EACC,2CvDLsB,EAAA;;AuDQvB;;;EAIE,0CvDTqB,EAAA;;AuDKvB;EAQE,0CvDbqB,EAAA;;AuDiBvB;;;;EAIC,yBvDxBsB,EAAA;;AuD2BvB;;;;EAIC,yBvD9BsB,EAAA;;AuDkCvB;EACC,cvDhCqB,EAAA;;AuDmCtB;EAKG,cpDiB2C,EAAA;;AoDtB9C;EASG,mBpDW4C,EAAA;;AoDJ/C;EACC,YAAY;EACZ,sBlD1Cc,EAAA;;AkD6Cf;EACC,mBvD7DsB,EAAA;;AuDgEvB;EACE,yBvDjEqB,EAAA;;AuDoEvB,iBAAA;AACA;;;EAKE,mBvDtEoB,EAAA;;AuD2EtB,aAAA;AACA;;EAEI,6CvDlFmB;UuDkFnB,qCvDlFmB;EuDmFnB,oCvDnFmB,EAAA;;AuDsFvB;EACC,gBlDlFc,EAAA;;AkDqFf;EACE,sBvD1FqB,EAAA;;AuD6FvB;EACE,sBvD/FqB,EAAA;;AuDmGvB,WAAA;AACA;EAKI,gClD1FW,EAAA;;AkDqFf;EASG,gClD9FY,EAAA;;AkDqFf;;EAgBG,6CvDpHoB;UuDoHpB,qCvDpHoB;EuDqHpB,oCvDrHoB,EAAA;;AwDFvB;;EAEC,cAA+B,EAAA;;AAGhC;;;;;;;;EAQC,yBrDsC6C;EqDrC7C,qBxDZsB;EwDatB,WnDRc,EAAA;;AmDYf;;;;;;;;;;;;;;;;;;;;;;;;;;EA0BC,yBrDQ4C;EqDP5C,qBrDQ6C;EqDP7C,WnDxCc,EAAA;;AmD2Cf;EACI,yBAAyB,EAAA;;AAG7B;EACI,cAAc,EAAA;;ACvDlB;;EAEC,cAA+B,EAAA;;AAGhC;;;;;;;;EAQC,yBtDsC6C;EsDrC7C,WpDPc,EAAA;;AoDWf;;;;;;;;;;;;;;;;;;;;;;;;;;EA0BC,yBtDS4C;EsDR5C,WpDtCc,EAAA;;AoD0Cf;;EAIK,qBAAqB,EAAA;;AAI1B;EACC,yBtD0B8C,EAAA;;AsDvB/C;EACC,yBtDqB6C,EAAA;;AsDjB9C;;EAEC,yBtDkB8C,EAAA;;AsDd/C;EAGE,cAAc,EAAA;;AAIhB;EAGK,yBzDhFkB,EAAA;;A0DFvB;;;;;EAKC,yB1DHsB,EAAA;;A0DMvB;;;EAGC,yB1DTsB,EAAA;;A0DYvB;;EAEC,yBvDqC4C,EAAA;;AuDjC7C;EACC,yBvD6B8C;EuD5B3C,qB1DpBmB;E0DqBtB,iHAAuF;EAAvF,6EAAuF,EAAA;;AAGxF;;;EAGC,mB1D3BsB,EAAA;;A0D+BvB;EACC,mBvD+B4C;EuD9B5C,uFAA0E;EAA1E,gEAA0E,EAAA;;AAG3E;;;EAGC,yB1DtCsB,EAAA;;A0DyCvB;;;EAGC,yB1D5CsB,EAAA;;A0D+CvB;;;;;EAKC,yB1DjDqB,EAAA;;A0DoDtB;;;;EAIC,yB1DxDqB,EAAA;;A0D2DtB;;EAEC,yBvDoC2C,EAAA;;AuDjC5C;EACC,qB1DnEmB,EAAA;;A0DsEpB;;;;EAIC,yB1D1EmB,EAAA;;A0D6EpB;;;EAGC,yB1DhFmB,EAAA;;A2DJpB;EAGG,mB3DDoB,EAAA;;A2DOvB;EAGE,gBtDLa,EAAA;EsDEf;IAMG,yB3DboB;I2DcpB,iJAC8G,EAAA;;AARjH;EAgBI,yBxD4hByD,EAAA;;AHziB7D;4EAq3F4E;AAn3F5E;EACC,mCAA2B;UAA3B,2BAA2B,EAAA;;AAE5B;EACC,qCAA6B;UAA7B,6BAA6B,EAAA", "file": "cust-theme-10.css", "sourcesContent": ["/* #THEME COLOR (variable overrides)\r\n========================================================================== */\r\n$color-primary: #778c85; \r\n$color-success: #2bc381; \r\n$color-info: #3787ff; \r\n$color-warning: #d0d725; \r\n$color-danger: #af37ff;\r\n\r\n/* #GLOBAL IMPORTS\r\n========================================================================== */\r\n@import './src/scss/_imports/_theme-modules-import';\r\n\r\n/* #Reset userselect\r\n========================================================================== */\r\n#myapp-0 {\r\n\tbox-shadow: none !important;\r\n}\r\n#myapp-10 {\r\n\tbox-shadow: 0 0 0 3px #000000;\r\n}", "/* #IMPORTS ~~\r\n========================================================================== */\r\n@import './node_modules/bootstrap/scss/functions';\r\n@import './node_modules/bootstrap/scss/variables'; \r\n@import './node_modules/bootstrap/scss/mixins';\r\n@import './src/scss/_mixins/mixins'; \r\n@import './src/scss/_themes/_modules/variables';\r\n@import './src/scss/_themes/_modules/_placeholders';\r\n\r\n\r\n/* #GLOBAL IMPORTS\r\n========================================================================== */\r\n/*@import '_imports/_global-import';*/\r\n\r\n/* #FRAMEWORK - Structure and layout files. (**DO NOT** change order)\r\n                DOC: you can disable unused _modules\r\n========================================================================== */\r\n/* contains root variables to be used with css (see docs) */\r\n/* html and body base styles */\r\n@import './src/scss/_themes/_modules/_body';\r\n\r\n@import './src/scss/_themes/_modules/_page-header';\r\n@import './src/scss/_themes/_modules/_page-logo';\r\n@import './src/scss/_themes/_modules/_page-search';\r\n@import './src/scss/_themes/_modules/_dropdown-icon-menu';\r\n@import './src/scss/_themes/_modules/_dropdown-notification';\r\n@import './src/scss/_themes/_modules/_dropdown-app-list';\r\n@import './src/scss/_themes/_modules/_header-function-fixed';\r\n@import './src/scss/_themes/_modules/_left-panel';\r\n@import './src/scss/_themes/_modules/_nav';\r\n@import './src/scss/_themes/_modules/_nav-listfilter';\r\n@import './src/scss/_themes/_modules/_nav-info-card';\r\n@import './src/scss/_themes/_modules/_nav-function-top';\r\n@import './src/scss/_themes/_modules/_nav-function-minify';\r\n@import './src/scss/_themes/_modules/_nav-footer';\r\n@import './src/scss/_themes/_modules/_page-wrapper';\r\n@import './src/scss/_themes/_modules/_page-heading';\r\n@import './src/scss/_themes/_modules/_page-footer';\r\n@import './src/scss/_themes/_modules/_page-components-accordion';\r\n@import './src/scss/_themes/_modules/_page-components-alerts';\r\n@import './src/scss/_themes/_modules/_page-components-badge';\r\n//@import './src/scss/_themes/_modules/_page-components-breadcrumb';\r\n@import './src/scss/_themes/_modules/_page-components-buttons';\r\n@import './src/scss/_themes/_modules/_page-components-navspills';\r\n@import './src/scss/_themes/_modules/_page-components-cards';\r\n@import './src/scss/_themes/_modules/_page-components-carousel';\r\n@import './src/scss/_themes/_modules/_page-components-dropdowns';\r\n@import './src/scss/_themes/_modules/_page-components-messanger';\r\n@import './src/scss/_themes/_modules/_page-components-modal';\r\n@import './src/scss/_themes/_modules/_page-components-panels';\r\n@import './src/scss/_themes/_modules/_page-components-popovers';\r\n@import './src/scss/_themes/_modules/_page-components-shortcut';\r\n@import './src/scss/_themes/_modules/_page-components-tabs';\r\n@import './src/scss/_themes/_modules/_page-components-custom-forms';\r\n@import './src/scss/_themes/_modules/_page-components-pagination';\r\n@import './src/scss/_themes/_modules/_page-components-listgroup';\r\n\r\n\r\n@import './src/scss/_themes/_modules/_helpers';\r\n@import './src/scss/_themes/_modules/_misc';\r\n@import './src/scss/_themes/_modules/_hack';\r\n@import './src/scss/_themes/_modules/_responsive';\r\n@import './src/scss/_themes/_modules/_forms';\r\n@import './src/scss/_themes/_modules/_settings-demo';\r\n@import './src/scss/_themes/_modules/_settings-demo-incompatiblity-list';\r\n@import './src/scss/_themes/_modules/_settings-demo-theme-colors';\r\n\r\n\r\n@import './src/scss/_themes/_modules/_mod-nav-accessibility';\r\n@import './src/scss/_themes/_modules/_colors';\r\n\r\n@import './src/scss/_themes/_modules/_custom-bootstrap-varients';\r\n\r\n//we import plugins here\r\n@import './src/scss/_themes/_plugins/_plugins-chartist';\r\n@import './src/scss/_themes/_plugins/_plugins-select2';\r\n@import './src/scss/_themes/_plugins/_plugins-datatables';\r\n@import './src/scss/_themes/_plugins/_plugins-datepicker';\r\n@import './src/scss/_themes/_plugins/_plugins-daterangepicker';\r\n@import './src/scss/_themes/_plugins/_plugins-ion.rangeslider';\r\n@import './src/scss/_themes/_plugins/_plugins-pace';", "/*---------------------------------------------------\r\n    SASS ELements (based on LESS Elements 0.9 http://lesselements.com) \r\n  -------------------------------- -------------------\r\n    LESS ELEMENTS made by <PERSON> (http://fadeyev.net)\r\n    SASS port by <PERSON> (http://samuelbeek.com) \r\n  ---------------------------------------------------*/\r\n \r\n@mixin gradient-img($start: #EEE,$stop: #FFF) {\r\n  background-color: $start;\r\n  background-image: -webkit-linear-gradient(top,$start,$stop);\r\n  background-image: linear-gradient(to top,$start,$stop);\r\n}\r\n\r\n@mixin gradient($color: #F5F5F5,$start: #EEE,$stop: #FFF) {\r\n    background:$color;\r\n    background:-webkit-gradient(linear,left bottom,left top,color-stop(0,$start),color-stop(1,$stop));\r\n    background:-ms-linear-gradient(bottom,$start,$stop);\r\n    background:-moz-linear-gradient(center bottom,$start 0%,$stop 100%);\r\n    background:-o-linear-gradient($stop,$start);\r\n    filter:progid:DXImageTransform.Microsoft.gradient(startColorstr=$start,endColorstr=$stop)\r\n}\r\n\r\n@mixin bw-gradient($color: #F5F5F5,$start: 0,$stop: 255) {\r\n    background:$color;\r\n    background:-webkit-gradient(linear,left bottom,left top,color-stop(0,#000),color-stop(1,#000));\r\n    background:-ms-linear-gradient(bottom,#000 0%,#000 100%);\r\n    background:-moz-linear-gradient(center bottom,#000 0%,#000 100%);\r\n    background:-o-linear-gradient(#000,#000);\r\n    filter:progid:DXImageTransform.Microsoft.gradient(startColorstr=rgb($start,$start,$start),endColorstr=rgb($stop,$stop,$stop))\r\n}\r\n\r\n@mixin bordered($top-color: #EEE,$right-color: #EEE,$bottom-color: #EEE,$left-color: #EEE) {\r\n    border-top:solid 1px $top-color;\r\n    border-left:solid 1px $left-color;\r\n    border-right:solid 1px $right-color;\r\n    border-bottom:solid 1px $bottom-color\r\n}\r\n\r\n@mixin drop-shadow($x-axis: 0,$y-axis: 1px,$blur: 2px,$alpha: 0.1) {\r\n    //-webkit-box-shadow:$x-axis $y-axis $blur rgba(0,0,0,$alpha);\r\n    //-moz-box-shadow:$x-axis $y-axis $blur rgba(0,0,0,$alpha);\r\n    box-shadow:$x-axis $y-axis $blur rgba(0,0,0,$alpha)\r\n}\r\n\r\n@mixin rounded($radius: 2px) {\r\n    border-radius:$radius\r\n}\r\n\r\n@mixin border-radius($topright: 0,$bottomright: 0,$bottomleft: 0,$topleft: 0) {\r\n    border-top-right-radius:$topright;\r\n    border-bottom-right-radius:$bottomright;\r\n    border-bottom-left-radius:$bottomleft;\r\n    border-top-left-radius:$topleft\r\n}\r\n\r\n@mixin opacity($opacity: 0.5) {\r\n    -moz-opacity:$opacity;\r\n    -khtml-opacity:$opacity;\r\n    -webkit-opacity:$opacity;\r\n    opacity:$opacity;\r\n    $opperc:$opacity * 100\r\n/*\r\n  -ms-filter: ~\"progid:DXImageTransform.Microsoft.Alpha(opacity=${opperc})\";\r\n  filter: ~\"alpha(opacity=${opperc})\";\r\n*/\r\n}\r\n\r\n@mixin transition-duration($duration: 0.2s) {\r\n    -moz-transition-duration:$duration;\r\n    -webkit-transition-duration:$duration;\r\n    -o-transition-duration:$duration;\r\n    transition-duration:$duration\r\n}\r\n\r\n@mixin transform($arguments) {\r\n    -webkit-transform:$arguments;\r\n    -moz-transform:$arguments;\r\n    -o-transform:$arguments;\r\n    -ms-transform:$arguments;\r\n    transform:$arguments\r\n}\r\n\r\n@mixin rotation($deg:5deg) {\r\n}\r\n\r\n@mixin scale($ratio:1.5) {\r\n}\r\n\r\n@mixin transition($duration:0.2s,$ease:ease-out) {\r\n    -webkit-transition:all $duration $ease;\r\n    -moz-transition:all $duration $ease;\r\n    -o-transition:all $duration $ease;\r\n    transition:all $duration $ease\r\n}\r\n\r\n@mixin transition-color($duration:0.2s,$ease:ease-out) {\r\n    -webkit-transition:color $duration $ease;\r\n    -moz-transition:color $duration $ease;\r\n    -o-transition:color $duration $ease;\r\n    transition:color $duration $ease\r\n}\r\n\r\n@mixin transition-border($duration:0.2s,$ease:ease-out) {\r\n    -webkit-transition:border $duration $ease;\r\n    -moz-transition:border $duration $ease;\r\n    -o-transition:border $duration $ease;\r\n    transition:border $duration $ease\r\n}\r\n\r\n@mixin transition-background-color($duration:0.2s,$ease:ease) {\r\n    -webkit-transition:background-color $duration $ease;\r\n    -moz-transition:background-color $duration $ease;\r\n    -o-transition:background-color $duration $ease;\r\n    transition:background-color $duration $ease\r\n}\r\n\r\n@mixin transition-fill($duration:0.2s,$ease:ease) {\r\n    -webkit-transition:fill $duration $ease;\r\n    -moz-transition:fill $duration $ease;\r\n    -o-transition:fill $duration $ease;\r\n    transition:fill $duration $ease\r\n}\r\n\r\n@mixin inner-shadow($horizontal:0,$vertical:1px,$blur:2px,$alpha: 0.4) {\r\n    -webkit-box-shadow:inset $horizontal $vertical $blur rgba(0,0,0,$alpha);\r\n    -moz-box-shadow:inset $horizontal $vertical $blur rgba(0,0,0,$alpha);\r\n    box-shadow:inset $horizontal $vertical $blur rgba(0,0,0,$alpha)\r\n}\r\n\r\n@mixin box-shadow($arguments) {\r\n    //-webkit-box-shadow:$arguments;\r\n    //-moz-box-shadow:$arguments;\r\n    box-shadow:$arguments\r\n}\r\n\r\n@mixin box-sizing($sizing: border-box) {\r\n    //-ms-box-sizing:$sizing;\r\n    //-moz-box-sizing:$sizing;\r\n    //-webkit-box-sizing:$sizing;\r\n    box-sizing:$sizing\r\n}\r\n\r\n@mixin user-select($argument: none) {\r\n    -webkit-user-select:$argument;\r\n    -moz-user-select:$argument;\r\n    -ms-user-select:$argument;\r\n    user-select:$argument\r\n}\r\n\r\n@mixin columns($colwidth: 250px,$colcount: 0,$colgap: 50px,$columnRuleColor: #EEE,$columnRuleStyle: solid,$columnRuleWidth: 1px) {\r\n    -moz-column-width:$colwidth;\r\n    -moz-column-count:$colcount;\r\n    -moz-column-gap:$colgap;\r\n    -moz-column-rule-color:$columnRuleColor;\r\n    -moz-column-rule-style:$columnRuleStyle;\r\n    -moz-column-rule-width:$columnRuleWidth;\r\n    -webkit-column-width:$colwidth;\r\n    -webkit-column-count:$colcount;\r\n    -webkit-column-gap:$colgap;\r\n    -webkit-column-rule-color:$columnRuleColor;\r\n    -webkit-column-rule-style:$columnRuleStyle;\r\n    -webkit-column-rule-width:$columnRuleWidth;\r\n    column-width:$colwidth;\r\n    column-count:$colcount;\r\n    column-gap:$colgap;\r\n    column-rule-color:$columnRuleColor;\r\n    column-rule-style:$columnRuleStyle;\r\n    column-rule-width:$columnRuleWidth\r\n}\r\n\r\n@mixin translate($x:0,$y:0) {\r\n    -webkit-transform: translate($x,$y);\r\n    -moz-transform: translate($x,$y);\r\n    -ms-transform: translate($x,$y);\r\n    -o-transform: translate($x,$y);\r\n    transform: translate($x,$y);\r\n}\r\n\r\n@mixin translate3d($x:0,$y:0,$z:0) {\r\n  -webkit-transform: translate3d($x, $y, $z);\r\n      -ms-transform: translate3d($x, $y, $z); \r\n          transform: translate3d($x, $y, $z); \r\n}\r\n\r\n@mixin background-clip($argument: padding-box) {\r\n    -moz-background-clip:$argument;\r\n    -webkit-background-clip:$argument;\r\n    background-clip:$argument\r\n}\r\n\r\n@mixin transform($transforms) {\r\n     -moz-transform: $transforms;\r\n       -o-transform: $transforms;\r\n      -ms-transform: $transforms;\r\n  -webkit-transform: $transforms;\r\n          transform: $transforms;\r\n}\r\n// rotate\r\n@mixin rotate ($deg) {\r\n  @include transform(rotate(#{$deg}deg));\r\n}\r\n \r\n// scale\r\n@mixin scale($scale) {\r\n   @include transform(scale($scale));\r\n} \r\n// translate\r\n@mixin translate ($x, $y) {\r\n   @include transform(translate($x, $y));\r\n}\r\n// skew\r\n@mixin skew ($x, $y) {\r\n   @include transform(skew(#{$x}deg, #{$y}deg));\r\n}\r\n//transform origin\r\n@mixin transform-origin ($origin) {\r\n    -moz-transform-origin: $origin;\r\n       -o-transform-origin: $origin;\r\n      -ms-transform-origin: $origin;\r\n  -webkit-transform-origin: $origin;\r\n          transform-origin: $origin;\r\n}\r\n\r\n//return rgb value \r\n/*------------------------\r\n    Usage\r\n\r\n  $color-white: hexToRGBString(#fff) => \"255,255,255\"\r\n  $color-white: hexToRGBString(rgb(255,255,255)) => \"255,255,255\"\r\n  $color-white: hexToRGBString(rgba(#fff,1)) => \"255,255,255\"\r\n  \r\n------------------------*/\r\n@function hexToRGBString($hexColor) {\r\n  @return \"#{red($hexColor)},#{green($hexColor)},#{blue($hexColor)}\";\r\n}\r\n\r\n//Rem size support\r\n\r\n/*------------------------\r\n    Usage\r\n\r\n    h1 {\r\n      font-size: rem(32);\r\n    }\r\n\r\n    OR:\r\n\r\n    h1 {\r\n      font-size: rem(32px);\r\n    }\r\n------------------------*/\r\n\r\n$browser-context: 16;\r\n\r\n@function rem($pixels, $context: $browser-context) {\r\n  @if (unitless($pixels)) {\r\n    $pixels: $pixels * 1px;\r\n  }\r\n\r\n  @if (unitless($context)) {\r\n    $context: $context * 1px;\r\n  }\r\n\r\n  @return $pixels / $context * 1rem;\r\n}\r\n\r\n/*------------------------\r\n  FADE IN\r\n  e.g. @include fadeIn( 2s );\r\n------------------------*/\r\n\r\n//$prefix:'-moz-', '-webkit-', '-o-', '-ms-', '';\r\n//\r\n//@mixin keyframe-fadeIn {\r\n//  0%   { opacity:0; }\r\n//  100% { opacity:1; }\r\n//}\r\n//\r\n//@-moz-keyframes fadeIn {\r\n//  @include keyframe-fadeIn;\r\n//}\r\n//@-webkit-keyframes fadeIn {\r\n//  @include keyframe-fadeIn;\r\n//}\r\n//@-o-keyframes fadeIn {\r\n//  @include keyframe-fadeIn;\r\n//}\r\n//@-ms-keyframes fadeIn {\r\n//  @include keyframe-fadeIn;\r\n//}\r\n//@keyframes fadeIn {\r\n//  @include keyframe-fadeIn;\r\n//}\r\n//\r\n//@mixin fadeIn( $arg ) {\r\n//  $keyframe-name:fadeIn;\r\n//  $duration:$arg;\r\n//  @each $p in $prefix {\r\n//    #{$p}animation:$keyframe-name $duration;\r\n//  }\r\n//}\r\n\r\n/*------------------------\r\nmixin that calculates if text needs to be light or dark\r\ndepending on the background color passed.\r\n\r\nFrom this W3C document: http://www.webmasterworld.com/r.cgi?f=88&d=9769&url=http://www.w3.org/TR/AERT#color-contrast\r\n\r\nusage:\r\n@include text-contrast($bgcolor)\r\n      \r\nColor brightness is determined by the following formula: \r\n((Red value X 299) + (Green value X 587) + (Blue value X 114)) / 1000\r\n------------------------*/\r\n\r\n@mixin text-contrast($n:#333) {\r\n  $color-brightness: round((red($n) * 299) + (green($n) * 587) + (blue($n) * 114) / 1000);\r\n  $light-color: round((red(#ffffff) * 299) + (green(#ffffff) * 587) + (blue(#ffffff) * 114) / 1000);\r\n  \r\n  @if abs($color-brightness) < ($light-color/1.70){\r\n    color: rgba(255,255,255,1);\r\n  }\r\n\r\n  @else {\r\n    color: rgba(0,0,0,0.8);\r\n  }\r\n}\r\n\r\n/*------------------------\r\n color factory \r\n  eg: contrast-ink($contrastvalue)\r\n------------------------*/\r\n\r\n@function contrast($n:#333) {\r\n  $color-brightness: round((red($n) * 299) + (green($n) * 587) + (blue($n) * 114) / 1000);\r\n  $light-color: round((red(#ffffff) * 299) + (green(#ffffff) * 587) + (blue(#ffffff) * 114) / 1000);\r\n  \r\n  @if abs($color-brightness) < ($light-color/1.70){\r\n    @return #ffffff;\r\n  }\r\n\r\n  @else {\r\n    @return #000000;\r\n  }\r\n}\r\n\r\n/*------------------------\r\n color factory \r\n  eg: @include paint($blue-grey-50, bg-blue-grey-50);\r\n------------------------*/\r\n\r\n\r\n@mixin paint($paint:#333333,$make:bg-blue-grey-50) {\r\n\r\n    .#{$make} {\r\n      background-color: $paint;\r\n      @include text-contrast($paint)\r\n      &:hover {\r\n        @include text-contrast($paint)\r\n      }\r\n    }\r\n}\r\n\r\n@mixin brush($brush: #333,$make: red-50) {\r\n    .#{$make} {\r\n      color: $brush;\r\n    }\r\n}\r\n\r\n//mixen for settings side buttons\r\n@mixin set-settings($class-element: nav-function-fixed) {\r\n\r\n    .#{$class-element} .btn-switch[data-class=\"#{$class-element}\"] {\r\n      @extend %set-settings;\r\n    }\r\n\r\n}\r\n\r\n//mixen for settings side buttons\r\n@mixin paint-gradient($paint: $fusion-500, $make:bg-fusion-gradient) {\r\n\r\n    .#{$make} {\r\n      background-image: -webkit-linear-gradient(250deg, rgba($paint, 0.7), transparent);\r\n      background-image: linear-gradient(250deg, rgba($paint, 0.7), transparent);\r\n    }\r\n\r\n}\r\n\r\n/* backface visibility */\r\n@mixin backface-visibility($argument: none) {\r\n  -webkit-backface-visibility: hidden;\r\n  -moz-backface-visibility:    hidden;\r\n  -ms-backface-visibility:     hidden;\r\n   backface-visibility:        hidden;\r\n}\r\n\r\n/* generate theme button */\r\n@mixin theme-button-color ($theme-fusion:none, $theme-primary:none, $theme-info:none, $theme-success:none, $theme-warning:none, $theme-danger:none) {\r\n  background-image: -webkit-linear-gradient(left, #{$theme-fusion}, #{$theme-fusion} 70%, #{$theme-primary} 70%, #{$theme-primary} 76%, #{$theme-info} 76%, #{$theme-info} 82%, #{$theme-success} 82%, #{$theme-success} 88%, #{$theme-warning} 88%, #{$theme-warning} 94%, #{$theme-danger} 94%, #{$theme-danger} 94%, #{$theme-danger} 100%);\r\n  background-image: -moz-linear-gradient(left, #{$theme-fusion}, #{$theme-fusion} 70%, #{$theme-primary} 70%, #{$theme-primary} 76%, #{$theme-info} 76%, #{$theme-info} 82%, #{$theme-success} 82%, #{$theme-success} 88%, #{$theme-warning} 88%, #{$theme-warning} 94%, #{$theme-danger} 94%, #{$theme-danger} 94%, #{$theme-danger} 100%);\r\n  background-image: -ms-linear-gradient(left, #{$theme-fusion}, #{$theme-fusion} 70%, #{$theme-primary} 70%, #{$theme-primary} 76%, #{$theme-info} 76%, #{$theme-info} 82%, #{$theme-success} 82%, #{$theme-success} 88%, #{$theme-warning} 88%, #{$theme-warning} 94%, #{$theme-danger} 94%, #{$theme-danger} 94%, #{$theme-danger} 100%);\r\n  background-image: linear-gradient(to right, #{$theme-fusion}, #{$theme-fusion} 70%, #{$theme-primary} 70%, #{$theme-primary} 76%, #{$theme-info} 76%, #{$theme-info} 82%, #{$theme-success} 82%, #{$theme-success} 88%, #{$theme-warning} 88%, #{$theme-warning} 94%, #{$theme-danger} 94%, #{$theme-danger} 94%, #{$theme-danger} 100%);\r\n}\r\n\r\n// IE flexbox details:\r\n//\r\n// - Flexbox in IE 10:\r\n//   https://msdn.microsoft.com/en-us/library/hh673531(v=vs.85).aspx\r\n//\r\n// - IE 11 flexbox changes (includes property/value names for IE 10)\r\n//   https://msdn.microsoft.com/library/dn265027(v=vs.85).aspx\r\n\r\n@mixin flexbox ($important: false) {\r\n  display: unquote(\"-ms-flexbox #{if($important, '!important', null)}\");\r\n  display: unquote(\"flex #{if($important, '!important', null)}\");\r\n}\r\n\r\n@mixin inline-flexbox ($important: false) {\r\n  display: unquote(\"-ms-inline-flexbox #{if($important, '!important', null)}\");\r\n  display: unquote(\"inline-flex #{if($important, '!important', null)}\");\r\n}\r\n\r\n@mixin align-content ($value) {\r\n  $ms-map: (\r\n    flex-start: start,\r\n    flex-end: end\r\n  );\r\n  -ms-flex-line-pack: map-get($ms-map, $value) or $value;\r\n  align-content: $value;\r\n}\r\n\r\n@mixin align-items ($value) {\r\n  $ms-map: (\r\n    flex-start: start,\r\n    flex-end: end\r\n  );\r\n  -ms-flex-align: map-get($ms-map, $value) or $value;\r\n  align-items: $value;\r\n}\r\n\r\n@mixin align-self ($value) {\r\n  $ms-map: (\r\n    flex-start: start,\r\n    flex-end: end\r\n  );\r\n  -ms-flex-item-align: map-get($ms-map, $value) or $value;\r\n  align-self: $value;\r\n}\r\n\r\n@mixin flex ($value) {\r\n  -ms-flex: $value;\r\n  flex: $value;\r\n}\r\n\r\n@mixin flex-direction ($value) {\r\n  -ms-flex-direction: $value;\r\n  flex-direction: $value;\r\n}\r\n\r\n@mixin flex-wrap ($value) {\r\n  $ms-map: (\r\n    nowrap: none\r\n  );\r\n  -ms-flex-wrap: map-get($ms-map, $value) or $value;\r\n  flex-wrap: $value;\r\n}\r\n\r\n@mixin justify-content ($value) {\r\n  $ms-map: (\r\n    flex-start: start,\r\n    flex-end: end,\r\n    space-around: distribute,\r\n    space-between: justify\r\n  );\r\n  -ms-flex-pack: map-get($ms-map, $value) or $value;\r\n  justify-content: $value;\r\n}\r\n\r\n@mixin order ($value) {\r\n  -ms-flex-order: $value;\r\n  order: $value;\r\n}", "/*  THEME COLORs\r\n========================================================================== */\r\n/* Looks good on chrome default color profile */\r\n$color-primary:\t\t\t\t\t\t#886ab5 !default;\r\n$color-success:\t\t\t\t\t\t#1dc9b7 !default;\r\n$color-info:\t\t\t\t\t\t#2196F3 !default;\r\n$color-warning:\t\t\t\t\t\t#ffc241 !default;\r\n$color-danger:\t\t\t\t\t\t#fd3995 !default;\r\n$color-fusion:\t\t\t\t\t\tdarken(desaturate(adjust-hue($color-primary, 5), 80%), 25%) !default; \r\n\r\n/* We will manually convert these primary colors to rgb for the dark mode option of the theme */\r\n$rgb-primary:\t\t\t\t\t\thexToRGBString($color-primary) !default;\r\n$rgb-success:\t\t\t\t\t\thexToRGBString($color-success) !default;\r\n$rgb-info:\t\t\t\t\t\t\thexToRGBString($color-info) !default;\r\n$rgb-warning:\t\t\t\t\t\thexToRGBString($color-warning) !default;\r\n$rgb-danger:\t\t\t\t\t\thexToRGBString($color-danger) !default;\r\n$rgb-fusion:\t\t\t\t\t\thexToRGBString($color-fusion) !default; \r\n\r\n/* looks good in sRGB but washed up on chrome default \r\n$color-primary:\t\t\t\t\t\t#826bb0;\r\n$color-success:\t\t\t\t\t\t#31cb55;\r\n$color-info:\t\t\t\t\t\t#5e93ec;\r\n$color-warning:\t\t\t\t\t\t#eec559;\r\n$color-danger:\t\t\t\t\t\t#dc4b92;\r\n$color-fusion:\t\t\t\t\t\tdarken(desaturate(adjust-hue($color-primary, 5), 80%), 25%); */\r\n\r\n/*  Color Polarity\r\n========================================================================== */\r\n$white:\t\t\t\t\t\t\t\t#fff !default;\r\n$black:\t\t\t\t\t\t\t\t#000 !default;\r\n$disabled:\t\t\t\t\t\t\tdarken($white, 20%) !default;\r\n\r\n/*  PAINTBUCKET MIXER\r\n========================================================================== */\r\n/* the grays */ \r\n$gray-50:\t\t\t\t\t\t\t#f9f9f9 !default;\r\n$gray-100:\t\t\t\t\t\t\t#f8f9fa !default;\r\n$gray-200:\t\t\t\t\t\t\t#f3f3f3 !default;\r\n$gray-300:\t\t\t\t\t\t\t#dee2e6 !default;\r\n$gray-400:\t\t\t\t\t\t\t#ced4da !default;\r\n$gray-500:\t\t\t\t\t\t\t#adb5bd !default;\r\n$gray-600:\t\t\t\t\t\t\t#868e96 !default;\r\n$gray-700:\t\t\t\t\t\t\t#495057 !default;\r\n$gray-800:\t\t\t\t\t\t\t#343a40 !default;\r\n$gray-900:\t\t\t\t\t\t\t#212529 !default;\r\n\r\n/* the sapphires */\r\n$primary-50:\t\t\t\t\t\tlighten($color-primary, 25%) !default;\t\r\n$primary-100:\t\t\t\t\t\tlighten($color-primary, 20%) !default;\t\r\n$primary-200:\t\t\t\t\t\tlighten($color-primary, 15%) !default;\t\r\n$primary-300:\t\t\t\t\t\tlighten($color-primary, 10%) !default;\t\r\n$primary-400:\t\t\t\t\t\tlighten($color-primary, 5%) !default;\r\n$primary-500:\t\t\t\t\t\t$color-primary !default;\r\n$primary-600:\t\t\t\t\t\tdarken($color-primary, 5%) !default;\r\n$primary-700:\t\t\t\t\t\tdarken($color-primary, 10%) !default;\r\n$primary-800:\t\t\t\t\t\tdarken($color-primary, 15%) !default;\r\n$primary-900:\t\t\t\t\t\tdarken($color-primary, 20%) !default;\r\n\r\n/* the emeralds */\r\n$success-50:\t\t\t\t\t\tlighten($color-success, 25%) !default;\t\r\n$success-100:\t\t\t\t\t\tlighten($color-success, 20%) !default;\t\r\n$success-200:\t\t\t\t\t\tlighten($color-success, 15%) !default;\t\r\n$success-300:\t\t\t\t\t\tlighten($color-success, 10%) !default;\t\r\n$success-400:\t\t\t\t\t\tlighten($color-success, 5%) !default;\r\n$success-500:\t\t\t\t\t\t$color-success !default;\r\n$success-600:\t\t\t\t\t\tdarken($color-success, 5%) !default;\r\n$success-700:\t\t\t\t\t\tdarken($color-success, 10%) !default;\r\n$success-800:\t\t\t\t\t\tdarken($color-success, 15%) !default;\r\n$success-900:\t\t\t\t\t\tdarken($color-success, 20%) !default;\r\n\r\n/* the amethyths */\r\n$info-50:\t\t\t\t\t\t\tlighten($color-info, 25%) !default;\t\r\n$info-100:\t\t\t\t\t\t\tlighten($color-info, 20%) !default;\t\r\n$info-200:\t\t\t\t\t\t\tlighten($color-info, 15%) !default;\t\r\n$info-300:\t\t\t\t\t\t\tlighten($color-info, 10%) !default;\t\r\n$info-400:\t\t\t\t\t\t\tlighten($color-info, 5%) !default;\r\n$info-500:\t\t\t\t\t\t\t$color-info !default;\r\n$info-600:\t\t\t\t\t\t\tdarken($color-info, 5%) !default;\r\n$info-700:\t\t\t\t\t\t\tdarken($color-info, 10%) !default;\r\n$info-800:\t\t\t\t\t\t\tdarken($color-info, 15%) !default;\r\n$info-900:\t\t\t\t\t\t\tdarken($color-info, 20%) !default;\r\n\r\n/* the topaz */\r\n$warning-50:\t\t\t\t\t\tlighten($color-warning, 25%) !default;\t\r\n$warning-100:\t\t\t\t\t\tlighten($color-warning, 20%) !default;\t\r\n$warning-200:\t\t\t\t\t\tlighten($color-warning, 15%) !default;\t\r\n$warning-300:\t\t\t\t\t\tlighten($color-warning, 10%) !default;\t\r\n$warning-400:\t\t\t\t\t\tlighten($color-warning, 5%) !default;\r\n$warning-500:\t\t\t\t\t\t$color-warning !default;\r\n$warning-600:\t\t\t\t\t\tdarken($color-warning, 5%) !default;\r\n$warning-700:\t\t\t\t\t\tdarken($color-warning, 10%) !default;\r\n$warning-800:\t\t\t\t\t\tdarken($color-warning, 15%) !default;\r\n$warning-900:\t\t\t\t\t\tdarken($color-warning, 20%) !default;\r\n\r\n/* the rubies */\r\n$danger-50:\t\t\t\t\t\t\tlighten($color-danger, 25%) !default;\t\r\n$danger-100:\t\t\t\t\t\tlighten($color-danger, 20%) !default;\t\r\n$danger-200:\t\t\t\t\t\tlighten($color-danger, 15%) !default;\t\r\n$danger-300:\t\t\t\t\t\tlighten($color-danger, 10%) !default;\t\r\n$danger-400:\t\t\t\t\t\tlighten($color-danger, 5%) !default;\r\n$danger-500:\t\t\t\t\t\t$color-danger !default;\r\n$danger-600:\t\t\t\t\t\tdarken($color-danger, 5%) !default;\r\n$danger-700:\t\t\t\t\t\tdarken($color-danger, 10%) !default;\r\n$danger-800:\t\t\t\t\t\tdarken($color-danger, 15%) !default;\r\n$danger-900:\t\t\t\t\t\tdarken($color-danger, 20%) !default;\r\n\r\n/* the graphites */\r\n$fusion-50:\t\t\t\t\t\t\tlighten($color-fusion, 25%) !default;\t\r\n$fusion-100:\t\t\t\t\t\tlighten($color-fusion, 20%) !default;\t\r\n$fusion-200:\t\t\t\t\t\tlighten($color-fusion, 15%) !default;\t\r\n$fusion-300:\t\t\t\t\t\tlighten($color-fusion, 10%) !default;\t\r\n$fusion-400:\t\t\t\t\t\tlighten($color-fusion, 5%) !default;\r\n$fusion-500:\t\t\t\t\t\t$color-fusion !default;\r\n$fusion-600:\t\t\t\t\t\tdarken($color-fusion, 5%) !default;\r\n$fusion-700:\t\t\t\t\t\tdarken($color-fusion, 10%) !default;\r\n$fusion-800:\t\t\t\t\t\tdarken($color-fusion, 15%) !default;\r\n$fusion-900:\t\t\t\t\t\tdarken($color-fusion, 20%) !default;\r\n\r\n$theme-colors-extended: () !default;\r\n$theme-colors-extended: map-merge((\r\n\t\"rgb-primary\":\t\t\t\t\t$rgb-primary,\r\n\t\"rgb-success\":\t\t\t\t\t$rgb-success,\r\n\t\"rgb-info\":\t\t\t\t\t\t$rgb-info,\r\n\t\"rgb-warning\":\t\t\t\t\t$rgb-warning,\r\n\t\"rgb-danger\":\t\t\t\t\t$rgb-danger,\r\n\t\"rgb-fusion\":\t\t\t\t\t$rgb-fusion,\r\n\t\"primary-50\":\t\t\t\t\t$primary-50,\r\n\t\"primary-100\":\t\t\t\t\t$primary-100,\r\n\t\"primary-200\":\t\t\t\t\t$primary-200,\r\n\t\"primary-300\":\t\t\t\t\t$primary-300,\r\n\t\"primary-400\":\t\t\t\t\t$primary-400,\r\n\t\"primary-500\":\t\t\t\t\t$primary-500,\r\n\t\"primary-600\":\t\t\t\t\t$primary-600,\r\n\t\"primary-700\":\t\t\t\t\t$primary-700,\r\n\t\"primary-800\":\t\t\t\t\t$primary-800,\r\n\t\"primary-900\":\t\t\t\t\t$primary-900,\r\n\t\"success-50\":\t\t\t\t\t$success-50,\r\n\t\"success-100\":\t\t\t\t\t$success-100,\r\n\t\"success-200\":\t\t\t\t\t$success-200,\r\n\t\"success-300\":\t\t\t\t\t$success-300,\r\n\t\"success-400\":\t\t\t\t\t$success-400,\r\n\t\"success-500\":\t\t\t\t\t$success-500,\r\n\t\"success-600\":\t\t\t\t\t$success-600,\r\n\t\"success-700\":\t\t\t\t\t$success-700,\r\n\t\"success-800\":\t\t\t\t\t$success-800,\r\n\t\"success-900\":\t\t\t\t\t$success-900,\r\n\t\"info-50\":\t\t\t\t\t\t$info-50,\r\n\t\"info-100\":\t\t\t\t\t\t$info-100,\r\n\t\"info-200\":\t\t\t\t\t\t$info-200,\r\n\t\"info-300\":\t\t\t\t\t\t$info-300,\r\n\t\"info-400\":\t\t\t\t\t\t$info-400,\r\n\t\"info-500\":\t\t\t\t\t\t$info-500,\r\n\t\"info-600\":\t\t\t\t\t\t$info-600,\r\n\t\"info-700\":\t\t\t\t\t\t$info-700,\r\n\t\"info-800\":\t\t\t\t\t\t$info-800,\r\n\t\"info-900\":\t\t\t\t\t\t$info-900,\r\n\t\"warning-50\":\t\t\t\t\t$warning-50,\r\n\t\"warning-100\":\t\t\t\t\t$warning-100,\r\n\t\"warning-200\":\t\t\t\t\t$warning-200,\r\n\t\"warning-300\":\t\t\t\t\t$warning-300,\r\n\t\"warning-400\":\t\t\t\t\t$warning-400,\r\n\t\"warning-500\":\t\t\t\t\t$warning-500,\r\n\t\"warning-600\":\t\t\t\t\t$warning-600,\r\n\t\"warning-700\":\t\t\t\t\t$warning-700,\r\n\t\"warning-800\":\t\t\t\t\t$warning-800,\r\n\t\"warning-900\":\t\t\t\t\t$warning-900,  \r\n\t\"danger-50\":\t\t\t\t\t$danger-50,\r\n\t\"danger-100\":\t\t\t\t\t$danger-100,\r\n\t\"danger-200\":\t\t\t\t\t$danger-200,\r\n\t\"danger-300\":\t\t\t\t\t$danger-300,\r\n\t\"danger-400\":\t\t\t\t\t$danger-400,\r\n\t\"danger-500\":\t\t\t\t\t$danger-500,\r\n\t\"danger-600\":\t\t\t\t\t$danger-600,\r\n\t\"danger-700\":\t\t\t\t\t$danger-700,\r\n\t\"danger-800\":\t\t\t\t\t$danger-800,\r\n\t\"danger-900\":\t\t\t\t\t$danger-900,\r\n\t\"fusion-50\":\t\t\t\t\t$fusion-50,\r\n\t\"fusion-100\":\t\t\t\t\t$fusion-100,\r\n\t\"fusion-200\":\t\t\t\t\t$fusion-200,\r\n\t\"fusion-300\":\t\t\t\t\t$fusion-300,\r\n\t\"fusion-400\":\t\t\t\t\t$fusion-400,\r\n\t\"fusion-500\":\t\t\t\t\t$fusion-500,\r\n\t\"fusion-600\":\t\t\t\t\t$fusion-600,\r\n\t\"fusion-700\":\t\t\t\t\t$fusion-700,\r\n\t\"fusion-800\":\t\t\t\t\t$fusion-800,\r\n\t\"fusion-900\":\t\t\t\t\t$fusion-900\r\n\r\n), $theme-colors-extended);\r\n\r\n/*  Define universal border difition (div outlines, etc)\r\n========================================================================== */\r\n$theme-border-utility-size:\t\t\t\t0px;\r\n\r\n/*  MOBILE BREAKPOINT & GUTTERS (contains some bootstrap responsive overrides)\r\n========================================================================== */\r\n$grid-breakpoints: (\r\n\t// Extra small screen / phone\r\n\txs: 0,\r\n\t// Small screen / phone\r\n\tsm: 576px,\r\n\t// Medium screen / tablet\r\n\tmd: 768px,\r\n\t// Large screen / desktop\r\n\tlg: 992px, // also change 'mobileResolutionTrigger' in app.config.js\r\n\t// Decently size screen / wide laptop\r\n\txl: 1399px \r\n);\r\n\r\n$mobile-breakpoint:\t\t\t\t\t\tlg !default;                               /* define when mobile menu activates, here we are declearing (lg) so it targets the one after it */\r\n$mobile-breakpoint-size:\t\t\t\tmap-get($grid-breakpoints, lg) !default;   /* bootstrap reference xs: 0,  sm: 544px, md: 768px, lg: 992px, xl: 1200px*/\r\n$grid-gutter-width-base:\t\t\t\t3rem;\r\n$grid-gutter-width:\t\t\t\t\t\t1.5rem;\r\n\r\n$grid-gutter-widths: (\r\n\txs: $grid-gutter-width-base / 2,         \r\n\tsm: $grid-gutter-width-base / 2,          \r\n\tmd: $grid-gutter-width-base / 2,        \r\n\tlg: $grid-gutter-width-base / 2,        \r\n\txl: $grid-gutter-width-base / 2        \r\n);\r\n\r\n\r\n/* global var used for spacing*/\r\n$spacer:                  1rem;\r\n$spacers: () ;\r\n$spacers: map-merge(\r\n\t(\r\n\t\t0: 0,\r\n\t\t1: ($spacer * .25),\r\n\t\t2: ($spacer * .5),\r\n\t\t3: $spacer,\r\n\t\t4: ($spacer * 1.5),\r\n\t\t5: ($spacer * 2),\r\n\t\t6: ($spacer * 2.5)\r\n\t),\r\n\t$spacers\r\n);\r\n\r\n/* Uniform Padding variable */\r\n/* Heads up! This is a global scoped variable - changing may impact the whole template */\r\n$p-1:\t\t\t\t\t\t\t\t\t0.25rem;\r\n$p-2:\t\t\t\t\t\t\t\t\t0.5rem;\r\n$p-3:\t\t\t\t\t\t\t\t\t1rem;\r\n$p-4:\t\t\t\t\t\t\t\t\t1.5rem;\r\n$p-5:\t\t\t\t\t\t\t\t\t2rem;\r\n\r\n\r\n/*   BOOTSTRAP OVERRIDES (bootstrap variables)\r\n========================================================================== */ \r\n$grays: (\r\n\t\"100\": $gray-100,\r\n\t\"200\": $gray-200,\r\n\t\"300\": $gray-300,\r\n\t\"400\": $gray-400,\r\n\t\"500\": $gray-500,\r\n\t\"600\": $gray-600,\r\n\t\"700\": $gray-700,\r\n\t\"800\": $gray-800,\r\n\t\"900\": $gray-900\r\n);\r\n\r\n$colors: (\r\n\t\"blue\": $color-primary,\r\n\t\"red\": $color-danger,\r\n\t\"orange\": $color-warning,\r\n\t\"yellow\": $color-warning,\r\n\t\"green\": $color-success,\r\n\t\"white\": $white,\r\n\t\"gray\": $gray-600,\r\n\t\"gray-dark\": $gray-700\r\n);\r\n\r\n/* usage: theme-colors(\"primary\"); */\r\n$theme-colors: (\r\n\t\"primary\": $color-primary,\r\n\t\"secondary\": $gray-600,\r\n\t\"success\": $color-success,\r\n\t\"info\": $color-info,\r\n\t\"warning\": $color-warning,\r\n\t\"danger\": $color-danger,\r\n\t\"light\": $white,\r\n\t\"dark\": $fusion-500\r\n);\r\n\r\n/* forms */\r\n/*$input-height:\t\t\t\t\t\t\tcalc(2.25rem + 1px); //I had to add this because the input gruops was having improper height for some reason... */\r\n$input-border-color:\t\t\t\t\t#E5E5E5;\r\n$input-focus-border-color:\t\t\t\t$color-primary;\r\n$input-btn-focus-color:\t\t\t\t\ttransparent;\r\n$input-padding-y:\t\t\t\t\t\t.5rem;  \r\n$input-padding-x:\t\t\t\t\t\t.875rem;\r\n$label-margin-bottom:\t\t\t\t\t.3rem;\r\n$form-group-margin-bottom:\t\t\t\t1.5rem;\r\n\r\n/* links */\r\n$link-color:\t\t\t\t\t\t\t$primary-500;\r\n$link-hover-color:\t\t\t\t\t\t$primary-400;\r\n\r\n/* checkbox */ \r\n$custom-control-indicator-size:\t\t\t\t\t1.125rem;\r\n$custom-checkbox-indicator-border-radius:\t\t2px;\r\n$custom-control-indicator-border-width: \t\t2px;\r\n$custom-control-indicator-bg-size:\t\t\t\t0.5rem;\r\n\r\n/*$custom-file-height-inner:\t\t\t\tcalc(2.25rem - 1px);*/\r\n//$custom-file-padding-y:\t\t\t\t\t$input-padding-y;\r\n\r\n/* not part of bootstrap variable */\r\n$custom-control-indicator-bg-size-checkbox:  50% 50% !default;\r\n\r\n/* custom checkbox */\r\n// the checkbox needs to be a little darker for input groups\r\n$custom-control-indicator-checked-bg:\t\t\t\t$primary-600;\r\n$custom-control-indicator-checked-border-color: \t$primary-700;\r\n$custom-control-indicator-checked-disabled-bg:\t\t$primary-100;\r\n\r\n$custom-control-indicator-active-bg:\t\t\t\t$primary-100; \r\n$custom-control-indicator-active-border-color:\t\t$primary-100;\r\n$custom-control-indicator-active-color:\t\t\t\t$primary-100;\r\n\r\n$custom-control-indicator-focus-border-color:\t\t$primary-400;\r\n$custom-select-focus-border-color:\t\t\t\t\t$primary-500;\r\n\r\n$custom-checkbox-indicator-indeterminate-border-color: $primary-600;\r\n$custom-checkbox-indicator-indeterminate-bg: $primary-500;\r\n\r\n/* custom range */\r\n$custom-range-thumb-width:\t\t\t\t1rem;\r\n$custom-range-thumb-border-radius:\t\t50%;\r\n$custom-range-track-height:\t\t\t\t0.325rem;\r\n$custom-range-thumb-bg:\t\t\t\t\t$primary-500;\r\n$custom-range-thumb-active-bg:\t\t\t$primary-300;\r\n$custom-range-thumb-focus-box-shadow:\t0 0 0 1px $white, 0 0 0 0.2rem rgba($primary-500, 0.25);\r\n\r\n\r\n/* custom file */\r\n$custom-file-focus-border-color:\t\t$primary-500;\r\n\r\n/* badge */\r\n$badge-font-size:\t\t\t\t\t\t85%;\r\n$badge-font-weight:\t\t\t\t\t\t500;\r\n\r\n/* cards */\r\n$card-spacer-y:\t\t\t\t\t\t\t1rem;\r\n$card-spacer-x:\t\t\t\t\t\t\t1rem;\r\n$card-cap-bg:\t\t\t\t\t\t\tinherit;\r\n$card-border-color:\t\t\t\t\t\trgba(0, 0, 0, 0.08);\r\n$list-group-border-color:\t\t\t\t$card-border-color;\r\n\r\n/*border radius*/\r\n$border-radius:\t\t\t\t\t\t\t4px;\r\n$border-radius-lg:\t\t\t\t\t\t$border-radius;\r\n$border-radius-sm:\t\t\t\t\t\t$border-radius;\r\n$border-radius-plus:\t\t\t\t\t10px;\r\n\r\n/* alert */\r\n$alert-padding-y:\t\t\t\t\t\t1rem;\r\n$alert-padding-x:\t\t\t\t\t\t1.25rem;\r\n$alert-margin-bottom:\t\t\t\t\t$grid-gutter-width + 0.5rem;\r\n\r\n/* toast */\r\n$toast-padding-y:\t\t\t\t\t\t0.5rem;\r\n$toast-padding-x:\t\t\t\t\t\t0.75rem;\r\n$toast-header-color:\t\t\t\t\t$fusion-500;\r\n\r\n/* breadcrumb */\r\n$breadcrumb-bg:\t\t\t\t\t\t\tlighten($fusion-50, 40%);\r\n$breadcrumb-divider-color:\t\t\t\tinherit;\r\n\r\n/* input button */\r\n$input-btn-padding-y-sm:\t\t\t\t.375rem;\r\n$input-btn-padding-x-sm:\t\t\t\t.844rem;\r\n\r\n$input-btn-padding-y:\t\t\t\t\t.5rem;\r\n$input-btn-padding-x:\t\t\t\t\t1.125rem;\r\n\r\n$input-btn-padding-y-lg:\t\t\t\t.75rem;\r\n$input-btn-padding-x-lg:\t\t\t\t1.5rem;\r\n\r\n/* nav link */\r\n$nav-link-padding-y:\t\t\t\t\t$input-btn-padding-y;\r\n$nav-link-padding-x:\t\t\t\t\t$input-btn-padding-x;\r\n\r\n/* nav, tabs, pills */\r\n$nav-tabs-border-color:\t\t\t\t\trgba($black, 0.1);\r\n$nav-tabs-link-active-border-color:\t\trgba($black, 0.1) rgba($black, 0.1) $white;\r\n$nav-tabs-link-hover-border-color:\t\trgba($black, 0.07) rgba($black, 0.07) transparent;\r\n\r\n/* tables */\r\n$table-border-color:\t\t\t\t\tlighten(desaturate($primary-500, 60%), 35%); //rgba($black, 0.09);\r\n$table-hover-bg:\t\t\t\t\t\tlighten(desaturate($primary-900, 70%), 63%);\r\n$table-accent-bg:\t\t\t\t\t\trgba($fusion-500,.02);\r\n$table-dark-bg:\t\t\t\t\t\t\t$fusion-300;\r\n$table-dark-border-color:\t\t\t\t$fusion-400;\r\n$table-dark-accent-bg:\t\t\t\t\trgba($white, .05);\r\n$table-dark-hover-bg:\t\t\t\t\t$color-primary;\r\n\r\n/* dropdowns */\r\n$dropdown-border-width:\t\t\t\t\t$theme-border-utility-size; \r\n$dropdown-padding-y:\t\t\t\t\t.3125rem;\r\n$dropdown-item-padding-y:\t\t\t\t.75rem;\r\n$dropdown-item-padding-x:\t\t\t\t1.5rem; \r\n$dropdown-link-active-bg:\t\t\t\tlighten($primary-50, 13%);  \r\n$dropdown-link-active-color:\t\t\t$primary-900;\r\n$dropdown-link-hover-color:\t\t\t\t$primary-700;\r\n\r\n/* dropdowns sizes */\r\n$dropdown-xl-width:\t\t\t\t\t\t21.875rem !default;\r\n$dropdown-lg-width:\t\t\t\t\t\t17.5rem !default;\r\n$dropdown-md-width:\t\t\t\t\t\t14rem !default;\r\n$dropdown-sm-width:\t\t\t\t\t\t8rem !default;\r\n$dropdown-shadow:\t\t\t\t\t\t0 0 15px 1px rgba(desaturate($primary-900, 20%), (20/100));   \r\n\r\n/* popovers */\r\n$popover-border-color:\t\t\t\t\trgba(0, 0, 0, 0.2);\r\n$popover-header-padding-y:\t\t\t\t1rem;\r\n$popover-header-padding-x:\t\t\t\t1rem;\r\n$popover-header-bg:\t\t\t\t\t\ttransparent;\r\n$popover-border-width:\t\t\t\t\t3px;\r\n$popover-arrow-width:\t\t\t\t\t15px;\r\n$popover-arrow-height:\t\t\t\t\t7px;\r\n$popover-arrow-outer-color:\t\t\t\tinherit;\r\n$popover-arrow-color:\t\t\t\t\ttransparent;\r\n$popover-font-size:\t\t\t\t\t\t14px;\r\n$popover-box-shadow:\t\t\t\t\t1px 0 13px rgba(90, 80, 105, 0.2);\r\n$popover-border-radius:\t\t\t\t\t0.5rem;\r\n\r\n/* tooltips */\r\n$tooltip-max-width:\t\t\t\t\t\t200px;\r\n$tooltip-color:\t\t\t\t\t\t\t$white;\r\n$tooltip-bg:\t\t\t\t\t\t\trgba($fusion-700, 0.9);\r\n$tooltip-border-radius:\t\t\t\t\t5px;\r\n$tooltip-opacity:\t\t\t\t\t\t1;\r\n$tooltip-padding-y:\t\t\t\t\t\t.3rem;\r\n$tooltip-padding-x:\t\t\t\t\t\t.6rem;\r\n$tooltip-margin:\t\t\t\t\t\t2px;\r\n$tooltip-arrow-width:\t\t\t\t\t8px;\r\n$tooltip-arrow-height:\t\t\t\t\t5px;\r\n\r\n/* modal */\r\n$modal-header-padding-y:\t\t\t\t1.25rem;\r\n$modal-header-padding-x:\t\t\t\t1.25rem;\r\n$modal-header-padding:\t\t\t\t\t$modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\r\n$modal-inner-padding:\t\t\t\t\t1.25rem;\r\n$modal-backdrop-opacity:\t\t\t\t0.2;\r\n$modal-content-border-color:\t\t\ttransparent;\r\n$modal-header-border-width:\t\t\t\t0px;\r\n$modal-footer-border-width:\t\t\t\t0px;\r\n\r\n/* reference guide\r\nhttp://www.standardista.com/px-to-rem-conversion-if-root-font-size-is-16px/\r\n8px = 0.5rem\r\n9px = 0.5625rem\r\n10px = 0.625rem\r\n11px = 0.6875rem\r\n12px = 0.75rem\r\n13px = 0.8125rem\r\n14px = 0.875rem\r\n15px = 0.9375rem\r\n16px = 1rem (base)\r\n17px = 1.0625rem\r\n18px = 1.125rem\r\n19px = 1.1875rem\r\n20px = 1.25rem\r\n21px = 1.3125rem\r\n22px = 1.375rem\r\n24px = 1.5rem\r\n25px = 1.5625rem\r\n26px = 1.625rem\r\n28px = 1.75rem\r\n30px = 1.875rem\r\n32px = 2rem\r\n34px = 2.125rem\r\n36px = 2.25rem\r\n38px = 2.375rem\r\n40px = 2.5rem\r\n*/\r\n\r\n/* Fonts */\r\n$font-size-base:\t\t\t\t\t\t0.8125rem;\r\n$font-size-lg:\t\t\t\t\t\t\t1rem;\r\n$font-size-sm:\t\t\t\t\t\t\t0.75rem;\r\n$line-height-base:\t\t\t\t\t\t1.47;\r\n$headings-line-height:\t\t\t\t\t1.57;\r\n\r\n$h1-font-size:\t\t\t\t\t\t\t1.5rem;\r\n$h2-font-size:\t\t\t\t\t\t\t1.375rem;\r\n$h3-font-size:\t\t\t\t\t\t\t1.1875rem;\r\n$h4-font-size:\t\t\t\t\t\t\t1.0625rem;\r\n$h5-font-size:\t\t\t\t\t\t\t0.9375rem;\r\n$h6-font-size:\t\t\t\t\t\t\t0.875rem;\r\n\r\n$display1-size:\t\t\t\t\t\t\t5rem;\r\n$display2-size:\t\t\t\t\t\t\t4.5rem;\r\n$display3-size:\t\t\t\t\t\t\t3.5rem;\r\n$display4-size:\t\t\t\t\t\t\t2.5rem;\r\n\r\n$navbar-toggler-font-size:\t\t\t\t21px;\r\n$navbar-toggler-padding-y:\t\t\t\t7.5px; \r\n$navbar-toggler-padding-x:\t\t\t\t18px;\r\n\r\n/* carousel */\r\n$carousel-indicator-height:\t\t\t\t13px;\r\n$carousel-indicator-width:\t\t\t\t13px;\r\n\r\n/*  BASE VARS\r\n========================================================================== */\r\n// usage: background-image: url(\"#{$baseURL}img/bg.png\"); \r\n\r\n$baseURL:\t\t\t\t\t\t\t\t\"../\" !default;\r\n$webfontsURL:\t\t\t\t\t\t\t\"../webfonts\" !default;\r\n$base-text-color:\t\t\t\t\t\tdarken($white,60%) !default;\r\n\r\n/* font vars below will auto change to rem values using function rem($value)*/\r\n$fs-base:\t\t\t\t\t\t\t\t13px !default;\r\n$fs-nano:\t\t\t\t\t\t\t\t$fs-base - 2;   /* 11px   */\r\n$fs-xs: \t\t\t\t\t\t\t\t$fs-base - 1;   /* 12px   */\r\n$fs-sm: \t\t\t\t\t\t\t\t$fs-base - 0.5; /* 12.5px */\r\n$fs-md: \t\t\t\t\t\t\t\t$fs-base + 1;   /* 14px   */\r\n$fs-lg: \t\t\t\t\t\t\t\t$fs-base + 2;   /* 15px   */\r\n$fs-xl: \t\t\t\t\t\t\t\t$fs-base + 3;   /* 16px   */\r\n$fs-xxl: \t\t\t\t\t\t\t\t$fs-base + 15;  /* 28px   */\r\n\r\n/*  Font Family\r\n========================================================================== */\r\n\t\t\t\t\t\t\t\t\t\t/*hint: you can also try the font called 'Poppins' by replacing the font 'Roboto' */\r\n$font-import:\t\t\t\t\t\t\t\"https://fonts.googleapis.com/css?family=Roboto:300,400,500,700,900\" !default;\r\n$page-font:\t\t\t\t\t\t\t\t\"Roboto\", 'Helvetica Neue', Helvetica, Arial !default;\r\n$nav-font:\t\t\t\t\t\t\t\t$page-font !default;\r\n$heading-font-family:\t\t\t\t\t$page-font !default; \r\n$mobile-page-font:\t\t\t\t\t\t'HelveticaNeue-Light','Helvetica Neue Light','Helvetica Neue',Helvetica,Arial,sans-serif;\r\n\r\n/*  ANIMATIONS\r\n========================================================================== */\r\n$nav-hide-animate: \t\t\t\t\t\tall 470ms cubic-bezier(0.34, 1.25, 0.3, 1) !default;\t\t/* this addresses all animation related to nav hide to nav minify */\r\n\r\n/*  Z-INDEX declearation\r\n========================================================================== */\r\n$space:\t\t\t\t\t\t\t\t\t1000 !default;\r\n$cloud:\t\t\t\t\t\t\t\t\t950 !default;\r\n$ground:\t\t\t\t\t\t\t\t0 !default;\r\n$water:\t\t\t\t\t\t\t\t\t-99 !default;\r\n/* we adjust bootstrap z-index to be higher than our higest z-index*/\r\n$zindex-dropdown:\t\t\t\t\t\t$space + 1000;\r\n$zindex-sticky:\t\t\t\t\t\t\t$space + 1020;\r\n$zindex-fixed:\t\t\t\t\t\t\t$space + 1030;\r\n$zindex-modal-backdrop:\t\t\t\t\t$space + 1040;\r\n$zindex-modal:\t\t\t\t\t\t\t$space + 1050;\r\n$zindex-panel-fullscreen:\t\t\t\t$space + 1055;\r\n$zindex-popover:\t\t\t\t\t\t$space + 1060;\r\n$zindex-tooltip:\t\t\t\t\t\t$space + 1070;\r\n\r\n/*  CUSTOM ICON PREFIX \r\n========================================================================== */\r\n$cust-icon-prefix:\t\t\t\t\t\tni;\r\n\r\n/*  PRINT CSS (landscape or portrait)\r\n========================================================================== */\r\n$print-page-type: \t\t\t\t\t\tportrait; \t\t\t\t\t\t\t\t\t\t\t\t  /* landscape or portrait */\r\n$print-page-size:\t\t\t\t\t\tletter;\t\t\t\t\t\t\t\t\t\t\t\t\t  /* auto, letter */\r\n$print-page-margin:\t\t\t\t\t\t1.0cm;\r\n\r\n/*  Common Element Variables\r\n========================================================================== */\r\n$body-background-color:\t\t\t\t\t$white !default;\r\n$page-bg:\t\t\t\t\t\t\t\tdesaturate(lighten($primary-500, 41.7%), 5%)  !default; //#f9f9fc\r\n\r\n/* Z-index decleartion \"birds eye view\"\r\n========================================================================== */\r\n$depth:\t\t\t\t\t\t\t\t\t999 !default;\r\n$depth-header:\t\t\t\t\t\t\t$depth + 1 !default;\r\n$depth-nav:\t\t\t\t\t\t\t\t$depth-header + 2 !default;\r\n\r\n/*  Components\r\n========================================================================== */\r\n$frame-border-color:\t\t\t\t\t#f7f9fa !default;\r\n\r\n/*  PAGE HEADER STUFF\r\n========================================================================== */\r\n\r\n/* colors */\r\n$header-bg:\t\t\t\t\t\t\t\t$white !default;\r\n$header-border-color:\t\t\t\t\t#ccc !default;\r\n$header-border-bottom-color:\t\t\trgba(darken($primary-700, 10%), (13/100)) !default;\t\t\r\n$header-link-color:\t\t\t\t\t\t$primary-500 !default;\r\n$header-link-hover-color:\t\t\t\tdarken($header-bg, 75%) !default;\r\n\r\n/* height */\r\n$header-height:\t\t\t\t\t\t\t4.125rem !default;\r\n$header-height-nav-top:\t\t\t\t\t4.125rem !default;\r\n$header-inner-padding-x:\t\t\t\t2rem !default;\r\n$header-inner-padding-y:\t\t\t\t0 !default;\r\n\r\n/* logo */\r\n$header-logo-border-bottom:\t\t\t\trgba(darken($primary-700, 10%), (30/100)) !default;\r\n$header-logo-width:\t\t\t\t\t\tauto !default; \t\t\t\t\t\t\t\t\t\t  /* try not to go beywond the width of $main_nav_width value */\r\n$header-logo-height:\t\t\t\t\tauto !default \t\t\t\t\t\t\t\t\t\t    /* you may need to change this depending on your logo design */\r\n$header-logo-text-align:\t\t\t\tcenter; \t\t\t\t\t\t\t\t\t\t\t\t      /* adjust this as you see fit : left, right, center */\r\n\r\n/* icon font size (not button) */\r\n$header-icon-size:\t\t\t\t\t\t21px;\r\n\r\n/* search input box */\r\n$header-search-border-color:\t\t\ttransparent !default;\t\t\t\t\t\t\t\t/* suggestion: #ccced0*/\r\n$header-search-bg:\t\t\t\t\t\ttransparent !default;\r\n$header-search-width:\t\t\t\t\t25rem !default;\r\n$header-search-height:\t\t\t\t\t$header-height - 1.5rem !default; \r\n$header-search-font-size:\t\t\t\t$fs-base + 2;\r\n$header-search-padding:\t\t\t\t\t$spacer * 0.38;\r\n\r\n/* btn */\r\n$header-btn-active-bg:\t\t\t\t\t$fusion-500 !default;\r\n$header-btn-color:\t\t\t\t\t\tdarken($header-bg, 35%) !default;\r\n$header-btn-hover-color:\t\t\t\t$header-link-hover-color !default;\r\n$header-btn-active-color:\t\t\t\t$white !default;\r\n$header-btn-height: \t\t\t\t\t$header-height/2 + 0.1875rem !default;\r\n$header-btn-width: \t\t\t\t\t\t3.25rem !default;\r\n$header-btn-font-size:\t\t\t\t\t21px !default; //works only for font icons\r\n$header-btn-border-radius:\t\t\t\t$border-radius !default;\r\n$header-non-btn-width:\t\t\t\t\t3.125rem !default;\r\n$header-dropdown-arrow-color:\t\t\t$primary-700 !default;\r\n\r\n/* dropdown: app list */\r\n$header-applist-link-block-height:\t\t5.9375rem;\r\n$header-applist-link-block-width:\t\t6.25rem;\r\n$header-applist-rows-width:\t\t\t\t21.875rem;\r\n$header-applist-rows-height:\t\t\t22.5rem; \r\n$header-applist-box-padding-x:\t\t\t$p-2;\r\n$header-applist-box-padding-y:\t\t\t$p-3;\r\n$header-applist-icon-size:\t\t\t\t3.125rem;\r\n\r\n/* badge */\r\n$header-badge-min-width:\t\t\t\t1.25rem !default;\r\n$header-badge-left:\t\t\t\t\t\t1.5625rem !default;\r\n$header-badge-top:\t\t\t\t\t\t($header-height / 2 - $header-badge-min-width) + 0.28125rem !default; \r\n\r\n/* COMPONENTS & MODS */\r\n$nav-tabs-clean-link-height:\t\t\t45px !default;\r\n\r\n/*  NAVIGATION STUFF\r\n\r\nGuide:\r\n\r\naside.page-sidebar ($nav-width, $nav-background)\r\n\t.page-logo\r\n\t.primary-nav\r\n\t\t.info-card\r\n\t\tul.nav-menu\r\n\t\t\tli\r\n\t\t\t\ta (parent level-0..., $nav-link-color, $nav-link-hover-color, $nav-link-hover-bg-color, $nav-link-hover-left-border-color)\r\n\t\t\t\t\ticon \r\n\t\t\t\t\tspan\r\n\t\t\t\t\tcollapse-sign \r\n\t\t\t\t\t\r\n\t\t\t\tul.nav-menu-sub-one  \r\n\t\t\t\t\tli\r\n\t\t\t\t\t\ta ($nav-level-1... $nav-sub-link-height)\r\n\t\t\t\t\t\t\tspan\r\n\t\t\t\t\t\t\tcollapse-sign\r\n\r\n\t\t\t\t\t\tul.nav-menu-sub-two\r\n\t\t\t\t\t\t\tli\r\n\t\t\t\t\t\t\t\ta ($nav-level-2... $nav-sub-link-height)\r\n\t\t\t\t\t\t\t\t\tspan\r\n\r\n\t\tp.nav-title ($nav-title-*...)\r\n\r\n\r\n========================================================================== */\r\n\r\n/* main navigation */\r\n/* left panel */\r\n$nav-background:\t\t\t\t\t\tdesaturate($primary-900, 7%) !default;\r\n$nav-background-shade:\t\t\t\t\trgba(desaturate($info-500, 15%), 0.18) !default;                  \r\n$nav-base-color:\t\t\t\t\t\tlighten($nav-background, 7%) !default;\r\n$nav-width:\t\t\t\t\t\t\t\t16.875rem !default; \r\n\r\n/* nav footer */\r\n$nav-footer-link-color:\t\t\t\t\tlighten($nav-background, 25%) !default;\r\n\r\n/* nav parent level-0 */\r\n$nav-link-color: \t\t\t\t\t\tlighten($nav-base-color, 32%) !default;\r\n$nav-font-link-size: \t\t\t\t\t$fs-base + 1 !default;\r\n$nav-collapse-sign-font-size:\t\t\tinherit !default;\t\r\n$nav-padding-x:\t\t\t\t\t\t\t2rem !default; \r\n$nav-padding-y:\t\t\t\t\t\t\t0.8125rem !default;\r\n\r\n/* nav icon sizes */\r\n$nav-font-icon-size:\t\t\t\t\t1.125rem !default; //23px for Fontawesome & 20px for NextGen icons\r\n$nav-font-icon-size-sub:\t\t\t\t1.125rem !default;\r\n\r\n$nav-icon-width:\t\t\t\t\t\t1.75rem !default;\r\n$nav-icon-margin-right:\t\t\t\t\t0.25rem !default;\r\n\r\n/* badge default */\r\n$nav-badge-color: \t\t\t\t\t\t$white !default;\r\n$nav-badge-bg-color: \t\t\t\t\t$danger-500 !default;\r\n\r\n/* all child */\r\n$nav-icon-color:\t\t\t\t\t\tlighten(darken($nav-base-color, 15%),27%) !default;\r\n$nav-icon-hover-color:\t\t\t\t\tlighten(desaturate($color-primary, 30%), 10%) !default;\r\n\r\n/* nav title */\r\n$nav-title-color: \t\t\t\t\t\tlighten($nav-base-color, 10%) !default;\r\n$nav-title-border-bottom-color: \t\tlighten($nav-base-color, 3%) !default;\r\n$nav-title-font-size: \t\t\t\t\t$fs-base - 1.8px;\r\n\r\n/* nav Minify */\r\n$nav-minify-hover-bg:\t\t\t\t\tdarken($nav-base-color, 3%) !default;\r\n$nav-minify-hover-text:\t\t\t\t\t$white !default;\r\n$nav-minify-width:\t\t\t\t\t\t4.6875rem !default;\r\n/* when the menu pops on hover */\r\n$nav-minify-sub-width:\t\t\t\t\t$nav-width - ($nav-minify-width - 1.5625rem) !default; \t\t\t\t\r\n\r\n/* navigation Width */\r\n/* partial visibility of the menu */\r\n$nav-hidden-visiblity:\t\t\t\t\t0.625rem !default; \t\t\t\t\t\t\t\t\t\t\t\r\n\r\n/* top navigation */\r\n$nav-top-height:\t\t\t\t\t\t3.5rem !default;\r\n$nav-top-drowndown-width:\t\t\t\t13rem !default;\r\n$nav-top-drowndown-background:\t\t\t$nav-base-color;\r\n$nav-top-drowndown-hover:\t\t\t\trgba($black, 0.1);;\r\n$nav-top-drowndown-color:\t\t\t\t$nav-link-color;\r\n$nav-top-drowndown-hover-color:\t\t\t$white;\r\n\r\n/* nav Info Card (appears below the logo) */\r\n$nav-infocard-height:\t\t\t\t\t9.530rem !default;\r\n$profile-image-width:\t\t\t\t\t3.125rem !default; \r\n$profile-image-width-md:\t\t\t\t2rem !default;\r\n$profile-image-width-sm:\t\t\t\t1.5625rem !default;\r\n$image-share-height:\t\t\t\t\t2.8125rem !default; /* width is auto */\r\n\r\n/* nav DL labels for all child */\r\n$nav-dl-font-size:\t\t\t\t\t\t0.625rem !default;\r\n$nav-dl-width:\t\t\t\t\t\t\t1.25rem !default;\r\n$nav-dl-height:\t\t\t\t\t\t\t1rem !default;\r\n$nav-dl-margin-right:\t\t\t\t\t0.9375rem !default;\r\n$nav-dl-margin-left:\t\t\t\t\t$nav-dl-width + $nav-dl-margin-right !default; \t/* will be pulled to left as a negative value */\r\n\r\n/*   MISC Settings\r\n========================================================================== */\r\n/* List Table */\r\n$list-table-padding-x:\t\t\t\t\t11px !default;\r\n$list-table-padding-y:\t\t\t\t\t0 !default;\r\n\r\n/*   PAGE SETTINGS\r\n========================================================================== */\r\n$settings-incompat-title:\t\t\t\t#d58100 !default;\r\n$settings-incompat-desc:\t\t\t\t#ec9f28 !default;\r\n$settings-incompat-bg:\t\t\t\t\t$warning-50 !default;\r\n$settings-incompat-border:\t\t\t\t$warning-700 !default;\r\n\r\n/*   PAGE BREADCRUMB \r\n========================================================================== */\r\n$page-breadcrumb-maxwidth:\t\t\t\t200px;\r\n\r\n/*   PAGE COMPONENT PANELS \r\n========================================================================== */\r\n$panel-spacer-y:\t\t\t\t\t\t1rem;\r\n$panel-spacer-x:\t\t\t\t\t\t1rem;\r\n$panel-hdr-font-size:\t\t\t\t\t14px;\r\n$panel-hdr-height:\t\t\t\t\t\t3rem;\r\n$panel-btn-size:\t\t\t\t\t\t1rem;\r\n$panel-btn-spacing:\t\t\t\t\t\t0.3rem;\r\n$panel-toolbar-icon:\t\t\t\t\t1.5625rem;\r\n$panel-hdr-background:\t\t\t\t\t$white; //#fafafa;\r\n$panel-edge-radius:\t\t\t\t\t\t$border-radius;\r\n$panel-placeholder-color:\t\t\t\tlighten(desaturate($primary-50, 20%), 10%);\r\n\r\n/*   PAGE COMPONENT PROGRESSBARS \r\n========================================================================== */\r\n$progress-height:\t\t\t\t\t\t.75rem;\r\n$progress-font-size:\t\t\t\t\t.625rem;\r\n$progress-bg:\t\t\t\t\t\t\tlighten($fusion-50, 40%);\r\n$progress-border-radius:\t\t\t\t10rem;\r\n\r\n/*   PAGE COMPONENT MESSENGER \r\n========================================================================== */\r\n$msgr-list-width:\t\t\t\t\t\t14.563rem;\r\n$msgr-list-width-collapsed:\t\t\t\t3.125rem;\r\n$msgr-get-background:\t\t\t\t\t#f1f0f0;\r\n$msgr-sent-background:\t\t\t\t\t$success-500;\r\n$msgr-animation-delay:\t\t\t\t\t100ms;\r\n\r\n/*   FOOTER\r\n========================================================================== */\r\n$footer-bg:\t\t\t\t\t\t\t\t$white !default;\r\n$footer-text-color:\t\t\t\t\t\tdarken($base-text-color, 10%);\r\n$footer-height:\t\t\t\t\t\t\t2.8125rem !default;\r\n$footer-font-size:\t\t\t\t\t\t$fs-base !default;\r\n$footer-zindex:\t\t\t\t\t\t\t$cloud - 20 !default;\r\n\r\n/*   GLOBALS\r\n========================================================================== */\r\n$mod-main-boxed-width:\t\t\t\t\tmap-get($grid-breakpoints, xl);\r\n$slider-width:\t\t\t\t\t\t\t15rem;\r\n\r\n/* ACCESSIBILITIES */\r\n$enable-prefers-reduced-motion-media-query:   false;\r\n", "/* PLACEHOLDER \r\n============================================= \r\n\r\nEXAMPLE:\r\n\r\n%bg-image {\r\n    width: 100%;\r\n    background-position: center center;\r\n    background-size: cover;\r\n    background-repeat: no-repeat;\r\n}\r\n\r\n.image-one {\r\n    @extend %bg-image;\r\n    background-image:url(/img/image-one.jpg\");\r\n}\r\n\r\nRESULT:\r\n\r\n.image-one, .image-two {\r\n    width: 100%;\r\n    background-position: center center;\r\n    background-size: cover;\r\n    background-repeat: no-repeat;\r\n}\r\n\r\n*/\r\n\r\n%nav-bg {\r\n  background-image: -webkit-linear-gradient(270deg, $nav-background-shade, transparent);\r\n  background-image: linear-gradient(270deg, $nav-background-shade, transparent); \r\n  background-color: $nav-background;\r\n}\r\n\r\n/*\r\n%shadow-hover {\r\n  box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 0 2px rgba(0,0,0,0.24);\r\n  transition: all 0.2s ease-in-out;\r\n\r\n  &:hover {\r\n    box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 -1px 6px rgba(0,0,0,0.23);\r\n  }\r\n}\r\n*/\r\n%btn-default {\r\n  @include gradient-img($start: #f5f5f5,$stop: #f1f1f1);\r\n  color: #444;\r\n\r\n  &:hover {\r\n    border: 1px solid #c6c6c6;\r\n  }\r\n\r\n  &:focus {\r\n    border-color: $primary-200 !important;\r\n  }\r\n\r\n  &.active {\r\n    background: $primary-300;\r\n    color: $white;\r\n  }\r\n}\r\n\r\n%custom-scroll {\r\n\r\n &::-webkit-scrollbar-thumb:vertical {\r\n    background-color: #666;\r\n  }\r\n\r\n\r\n}\r\n\r\n\r\n%font-smoothing {\r\n     -webkit-font-smoothing: antialiased;\r\n    -moz-osx-font-smoothing: grayscale;\r\n}\r\n\r\n%set-settings {\r\n  color:$white;\r\n  background:$color-primary !important;\r\n  &:after {\r\n    background:$white !important;\r\n    color:$color-primary !important;\r\n  }\r\n  + .onoffswitch-title {\r\n    color: $primary-500;\r\n  }\r\n}\r\n\r\n\r\n%not-compatible {\r\n\r\n    .onoffswitch-title {\r\n      color: $settings-incompat-title;\r\n    }\r\n    .onoffswitch-title-desc {\r\n      color: $settings-incompat-desc;\r\n    }\r\n    &:after {\r\n      @extend %incompatible;\r\n    }\r\n}\r\n\r\n\r\n%ping-badge {\r\n  background-color: $nav-badge-bg-color;\r\n  color: $nav-badge-color;\r\n  border: 1px solid $nav-background;\r\n}\r\n\r\n\r\n%header-btn {\r\n  border: 1px solid lighten($fusion-50, 30%);\r\n  color:$header-btn-color;\r\n\r\n  &:hover {\r\n    border-color: $primary-500;\r\n    background: $primary-300;\r\n    color:$white;\r\n\r\n  }\r\n\r\n}\r\n\r\n%expanded-box {\r\n  background: $white;\r\n}\r\n\r\n%header-btn-active {\r\n  background: $header-btn-active-bg;\r\n  border-color: darken($header-btn-active-bg, 10%) !important;\r\n  color:$header-btn-active-color !important;\r\n}\r\n\r\n\r\n%spin-loader {\r\n  border: 2px solid $color-primary;\r\n}\r\n\r\n%incompatible {\r\n  background: $settings-incompat-bg;\r\n  border: 1px solid $settings-incompat-border;\r\n  color:$fusion-900;\r\n}\r\n", "// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n// Color system\n\n$white:    #fff !default;\n$gray-100: #f8f9fa !default;\n$gray-200: #e9ecef !default;\n$gray-300: #dee2e6 !default;\n$gray-400: #ced4da !default;\n$gray-500: #adb5bd !default;\n$gray-600: #6c757d !default;\n$gray-700: #495057 !default;\n$gray-800: #343a40 !default;\n$gray-900: #212529 !default;\n$black:    #000 !default;\n\n$grays: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$grays: map-merge(\n  (\n    \"100\": $gray-100,\n    \"200\": $gray-200,\n    \"300\": $gray-300,\n    \"400\": $gray-400,\n    \"500\": $gray-500,\n    \"600\": $gray-600,\n    \"700\": $gray-700,\n    \"800\": $gray-800,\n    \"900\": $gray-900\n  ),\n  $grays\n);\n\n$blue:    #007bff !default;\n$indigo:  #6610f2 !default;\n$purple:  #6f42c1 !default;\n$pink:    #e83e8c !default;\n$red:     #dc3545 !default;\n$orange:  #fd7e14 !default;\n$yellow:  #ffc107 !default;\n$green:   #28a745 !default;\n$teal:    #20c997 !default;\n$cyan:    #17a2b8 !default;\n\n$colors: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$colors: map-merge(\n  (\n    \"blue\":       $blue,\n    \"indigo\":     $indigo,\n    \"purple\":     $purple,\n    \"pink\":       $pink,\n    \"red\":        $red,\n    \"orange\":     $orange,\n    \"yellow\":     $yellow,\n    \"green\":      $green,\n    \"teal\":       $teal,\n    \"cyan\":       $cyan,\n    \"white\":      $white,\n    \"gray\":       $gray-600,\n    \"gray-dark\":  $gray-800\n  ),\n  $colors\n);\n\n$primary:       $blue !default;\n$secondary:     $gray-600 !default;\n$success:       $green !default;\n$info:          $cyan !default;\n$warning:       $yellow !default;\n$danger:        $red !default;\n$light:         $gray-100 !default;\n$dark:          $gray-800 !default;\n\n$theme-colors: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$theme-colors: map-merge(\n  (\n    \"primary\":    $primary,\n    \"secondary\":  $secondary,\n    \"success\":    $success,\n    \"info\":       $info,\n    \"warning\":    $warning,\n    \"danger\":     $danger,\n    \"light\":      $light,\n    \"dark\":       $dark\n  ),\n  $theme-colors\n);\n\n// Set a specific jump point for requesting color jumps\n$theme-color-interval:      8% !default;\n\n// The yiq lightness value that determines when the lightness of color changes from \"dark\" to \"light\". Acceptable values are between 0 and 255.\n$yiq-contrasted-threshold:  150 !default;\n\n// Customize the light and dark text colors for use in our YIQ color contrast function.\n$yiq-text-dark:             $gray-900 !default;\n$yiq-text-light:            $white !default;\n\n// Characters which are escaped by the escape-svg function\n$escaped-characters: (\n  (\"<\",\"%3c\"),\n  (\">\",\"%3e\"),\n  (\"#\",\"%23\"),\n  (\"(\",\"%28\"),\n  (\")\",\"%29\"),\n) !default;\n\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret:                                true !default;\n$enable-rounded:                              true !default;\n$enable-shadows:                              false !default;\n$enable-gradients:                            false !default;\n$enable-transitions:                          true !default;\n$enable-prefers-reduced-motion-media-query:   true !default;\n$enable-hover-media-query:                    false !default; // Deprecated, no longer affects any compiled CSS\n$enable-grid-classes:                         true !default;\n$enable-pointer-cursor-for-buttons:           true !default;\n$enable-print-styles:                         true !default;\n$enable-responsive-font-sizes:                false !default;\n$enable-validation-icons:                     true !default;\n$enable-deprecation-messages:                 true !default;\n\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n$spacer: 1rem !default;\n$spacers: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$spacers: map-merge(\n  (\n    0: 0,\n    1: ($spacer * .25),\n    2: ($spacer * .5),\n    3: $spacer,\n    4: ($spacer * 1.5),\n    5: ($spacer * 3)\n  ),\n  $spacers\n);\n\n// This variable affects the `.h-*` and `.w-*` classes.\n$sizes: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$sizes: map-merge(\n  (\n    25: 25%,\n    50: 50%,\n    75: 75%,\n    100: 100%,\n    auto: auto\n  ),\n  $sizes\n);\n\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-bg:                   $white !default;\n$body-color:                $gray-900 !default;\n\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                              theme-color(\"primary\") !default;\n$link-decoration:                         none !default;\n$link-hover-color:                        darken($link-color, 15%) !default;\n$link-hover-decoration:                   underline !default;\n// Darken percentage for links with `.text-*` class (e.g. `.text-success`)\n$emphasized-link-hover-darken-percentage: 15% !default;\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom:   1rem !default;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n$grid-breakpoints: (\n  xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px\n) !default;\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n$container-max-widths: (\n  sm: 540px,\n  md: 720px,\n  lg: 960px,\n  xl: 1140px\n) !default;\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12 !default;\n$grid-gutter-width:           30px !default;\n$grid-row-columns:            6 !default;\n\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n$line-height-lg:              1.5 !default;\n$line-height-sm:              1.5 !default;\n\n$border-width:                1px !default;\n$border-color:                $gray-300 !default;\n\n$border-radius:               .25rem !default;\n$border-radius-lg:            .3rem !default;\n$border-radius-sm:            .2rem !default;\n\n$rounded-pill:                50rem !default;\n\n$box-shadow-sm:               0 .125rem .25rem rgba($black, .075) !default;\n$box-shadow:                  0 .5rem 1rem rgba($black, .15) !default;\n$box-shadow-lg:               0 1rem 3rem rgba($black, .175) !default;\n\n$component-active-color:      $white !default;\n$component-active-bg:         theme-color(\"primary\") !default;\n\n$caret-width:                 .3em !default;\n$caret-vertical-align:        $caret-width * .85 !default;\n$caret-spacing:               $caret-width * .85 !default;\n\n$transition-base:             all .2s ease-in-out !default;\n$transition-fade:             opacity .15s linear !default;\n$transition-collapse:         height .35s ease !default;\n\n$embed-responsive-aspect-ratios: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$embed-responsive-aspect-ratios: join(\n  (\n    (21 9),\n    (16 9),\n    (4 3),\n    (1 1),\n  ),\n  $embed-responsive-aspect-ratios\n);\n\n// Typography\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\" !default;\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\n$font-family-base:            $font-family-sans-serif !default;\n// stylelint-enable value-keyword-case\n\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\n$font-size-lg:                $font-size-base * 1.25 !default;\n$font-size-sm:                $font-size-base * .875 !default;\n\n$font-weight-lighter:         lighter !default;\n$font-weight-light:           300 !default;\n$font-weight-normal:          400 !default;\n$font-weight-bold:            700 !default;\n$font-weight-bolder:          bolder !default;\n\n$font-weight-base:            $font-weight-normal !default;\n$line-height-base:            1.5 !default;\n\n$h1-font-size:                $font-size-base * 2.5 !default;\n$h2-font-size:                $font-size-base * 2 !default;\n$h3-font-size:                $font-size-base * 1.75 !default;\n$h4-font-size:                $font-size-base * 1.5 !default;\n$h5-font-size:                $font-size-base * 1.25 !default;\n$h6-font-size:                $font-size-base !default;\n\n$headings-margin-bottom:      $spacer / 2 !default;\n$headings-font-family:        null !default;\n$headings-font-weight:        500 !default;\n$headings-line-height:        1.2 !default;\n$headings-color:              null !default;\n\n$display1-size:               6rem !default;\n$display2-size:               5.5rem !default;\n$display3-size:               4.5rem !default;\n$display4-size:               3.5rem !default;\n\n$display1-weight:             300 !default;\n$display2-weight:             300 !default;\n$display3-weight:             300 !default;\n$display4-weight:             300 !default;\n$display-line-height:         $headings-line-height !default;\n\n$lead-font-size:              $font-size-base * 1.25 !default;\n$lead-font-weight:            300 !default;\n\n$small-font-size:             80% !default;\n\n$text-muted:                  $gray-600 !default;\n\n$blockquote-small-color:      $gray-600 !default;\n$blockquote-small-font-size:  $small-font-size !default;\n$blockquote-font-size:        $font-size-base * 1.25 !default;\n\n$hr-border-color:             rgba($black, .1) !default;\n$hr-border-width:             $border-width !default;\n\n$mark-padding:                .2em !default;\n\n$dt-font-weight:              $font-weight-bold !default;\n\n$kbd-box-shadow:              inset 0 -.1rem 0 rgba($black, .25) !default;\n$nested-kbd-font-weight:      $font-weight-bold !default;\n\n$list-inline-padding:         .5rem !default;\n\n$mark-bg:                     #fcf8e3 !default;\n\n$hr-margin-y:                 $spacer !default;\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n$table-cell-padding:          .75rem !default;\n$table-cell-padding-sm:       .3rem !default;\n\n$table-color:                 $body-color !default;\n$table-bg:                    null !default;\n$table-accent-bg:             rgba($black, .05) !default;\n$table-hover-color:           $table-color !default;\n$table-hover-bg:              rgba($black, .075) !default;\n$table-active-bg:             $table-hover-bg !default;\n\n$table-border-width:          $border-width !default;\n$table-border-color:          $border-color !default;\n\n$table-head-bg:               $gray-200 !default;\n$table-head-color:            $gray-700 !default;\n\n$table-dark-color:            $white !default;\n$table-dark-bg:               $gray-800 !default;\n$table-dark-accent-bg:        rgba($white, .05) !default;\n$table-dark-hover-color:      $table-dark-color !default;\n$table-dark-hover-bg:         rgba($white, .075) !default;\n$table-dark-border-color:     lighten($table-dark-bg, 7.5%) !default;\n\n$table-striped-order:         odd !default;\n\n$table-caption-color:         $text-muted !default;\n\n$table-bg-level:              -9 !default;\n$table-border-level:          -6 !default;\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n$input-btn-padding-y:         .375rem !default;\n$input-btn-padding-x:         .75rem !default;\n$input-btn-font-family:       null !default;\n$input-btn-font-size:         $font-size-base !default;\n$input-btn-line-height:       $line-height-base !default;\n\n$input-btn-focus-width:       .2rem !default;\n$input-btn-focus-color:       rgba($component-active-bg, .25) !default;\n$input-btn-focus-box-shadow:  0 0 0 $input-btn-focus-width $input-btn-focus-color !default;\n\n$input-btn-padding-y-sm:      .25rem !default;\n$input-btn-padding-x-sm:      .5rem !default;\n$input-btn-font-size-sm:      $font-size-sm !default;\n$input-btn-line-height-sm:    $line-height-sm !default;\n\n$input-btn-padding-y-lg:      .5rem !default;\n$input-btn-padding-x-lg:      1rem !default;\n$input-btn-font-size-lg:      $font-size-lg !default;\n$input-btn-line-height-lg:    $line-height-lg !default;\n\n$input-btn-border-width:      $border-width !default;\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n$btn-padding-y:               $input-btn-padding-y !default;\n$btn-padding-x:               $input-btn-padding-x !default;\n$btn-font-family:             $input-btn-font-family !default;\n$btn-font-size:               $input-btn-font-size !default;\n$btn-line-height:             $input-btn-line-height !default;\n$btn-white-space:             null !default; // Set to `nowrap` to prevent text wrapping\n\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\n$btn-font-size-sm:            $input-btn-font-size-sm !default;\n$btn-line-height-sm:          $input-btn-line-height-sm !default;\n\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\n$btn-font-size-lg:            $input-btn-font-size-lg !default;\n$btn-line-height-lg:          $input-btn-line-height-lg !default;\n\n$btn-border-width:            $input-btn-border-width !default;\n\n$btn-font-weight:             $font-weight-normal !default;\n$btn-box-shadow:              inset 0 1px 0 rgba($white, .15), 0 1px 1px rgba($black, .075) !default;\n$btn-focus-width:             $input-btn-focus-width !default;\n$btn-focus-box-shadow:        $input-btn-focus-box-shadow !default;\n$btn-disabled-opacity:        .65 !default;\n$btn-active-box-shadow:       inset 0 3px 5px rgba($black, .125) !default;\n\n$btn-link-disabled-color:     $gray-600 !default;\n\n$btn-block-spacing-y:         .5rem !default;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:           $border-radius !default;\n$btn-border-radius-lg:        $border-radius-lg !default;\n$btn-border-radius-sm:        $border-radius-sm !default;\n\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n\n// Forms\n\n$label-margin-bottom:                   .5rem !default;\n\n$input-padding-y:                       $input-btn-padding-y !default;\n$input-padding-x:                       $input-btn-padding-x !default;\n$input-font-family:                     $input-btn-font-family !default;\n$input-font-size:                       $input-btn-font-size !default;\n$input-font-weight:                     $font-weight-base !default;\n$input-line-height:                     $input-btn-line-height !default;\n\n$input-padding-y-sm:                    $input-btn-padding-y-sm !default;\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\n$input-font-size-sm:                    $input-btn-font-size-sm !default;\n$input-line-height-sm:                  $input-btn-line-height-sm !default;\n\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\n$input-font-size-lg:                    $input-btn-font-size-lg !default;\n$input-line-height-lg:                  $input-btn-line-height-lg !default;\n\n$input-bg:                              $white !default;\n$input-disabled-bg:                     $gray-200 !default;\n\n$input-color:                           $gray-700 !default;\n$input-border-color:                    $gray-400 !default;\n$input-border-width:                    $input-btn-border-width !default;\n$input-box-shadow:                      inset 0 1px 1px rgba($black, .075) !default;\n\n$input-border-radius:                   $border-radius !default;\n$input-border-radius-lg:                $border-radius-lg !default;\n$input-border-radius-sm:                $border-radius-sm !default;\n\n$input-focus-bg:                        $input-bg !default;\n$input-focus-border-color:              lighten($component-active-bg, 25%) !default;\n$input-focus-color:                     $input-color !default;\n$input-focus-width:                     $input-btn-focus-width !default;\n$input-focus-box-shadow:                $input-btn-focus-box-shadow !default;\n\n$input-placeholder-color:               $gray-600 !default;\n$input-plaintext-color:                 $body-color !default;\n\n$input-height-border:                   $input-border-width * 2 !default;\n\n$input-height-inner:                    add($input-line-height * 1em, $input-padding-y * 2) !default;\n$input-height-inner-half:               add($input-line-height * .5em, $input-padding-y) !default;\n$input-height-inner-quarter:            add($input-line-height * .25em, $input-padding-y / 2) !default;\n\n$input-height:                          add($input-line-height * 1em, add($input-padding-y * 2, $input-height-border, false)) !default;\n$input-height-sm:                       add($input-line-height-sm * 1em, add($input-padding-y-sm * 2, $input-height-border, false)) !default;\n$input-height-lg:                       add($input-line-height-lg * 1em, add($input-padding-y-lg * 2, $input-height-border, false)) !default;\n\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$form-text-margin-top:                  .25rem !default;\n\n$form-check-input-gutter:               1.25rem !default;\n$form-check-input-margin-y:             .3rem !default;\n$form-check-input-margin-x:             .25rem !default;\n\n$form-check-inline-margin-x:            .75rem !default;\n$form-check-inline-input-margin-x:      .3125rem !default;\n\n$form-grid-gutter-width:                10px !default;\n$form-group-margin-bottom:              1rem !default;\n\n$input-group-addon-color:               $input-color !default;\n$input-group-addon-bg:                  $gray-200 !default;\n$input-group-addon-border-color:        $input-border-color !default;\n\n$custom-forms-transition:               background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$custom-control-gutter:                 .5rem !default;\n$custom-control-spacer-x:               1rem !default;\n$custom-control-cursor:                 null !default;\n\n$custom-control-indicator-size:         1rem !default;\n$custom-control-indicator-bg:           $input-bg !default;\n\n$custom-control-indicator-bg-size:      50% 50% !default;\n$custom-control-indicator-box-shadow:   $input-box-shadow !default;\n$custom-control-indicator-border-color: $gray-500 !default;\n$custom-control-indicator-border-width: $input-border-width !default;\n\n$custom-control-label-color:            null !default;\n\n$custom-control-indicator-disabled-bg:          $input-disabled-bg !default;\n$custom-control-label-disabled-color:           $gray-600 !default;\n\n$custom-control-indicator-checked-color:        $component-active-color !default;\n$custom-control-indicator-checked-bg:           $component-active-bg !default;\n$custom-control-indicator-checked-disabled-bg:  rgba(theme-color(\"primary\"), .5) !default;\n$custom-control-indicator-checked-box-shadow:   none !default;\n$custom-control-indicator-checked-border-color: $custom-control-indicator-checked-bg !default;\n\n$custom-control-indicator-focus-box-shadow:     $input-focus-box-shadow !default;\n$custom-control-indicator-focus-border-color:   $input-focus-border-color !default;\n\n$custom-control-indicator-active-color:         $component-active-color !default;\n$custom-control-indicator-active-bg:            lighten($component-active-bg, 35%) !default;\n$custom-control-indicator-active-box-shadow:    none !default;\n$custom-control-indicator-active-border-color:  $custom-control-indicator-active-bg !default;\n\n$custom-checkbox-indicator-border-radius:       $border-radius !default;\n$custom-checkbox-indicator-icon-checked:        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'><path fill='#{$custom-control-indicator-checked-color}' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/></svg>\") !default;\n\n$custom-checkbox-indicator-indeterminate-bg:           $component-active-bg !default;\n$custom-checkbox-indicator-indeterminate-color:        $custom-control-indicator-checked-color !default;\n$custom-checkbox-indicator-icon-indeterminate:         url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='4' height='4' viewBox='0 0 4 4'><path stroke='#{$custom-checkbox-indicator-indeterminate-color}' d='M0 2h4'/></svg>\") !default;\n$custom-checkbox-indicator-indeterminate-box-shadow:   none !default;\n$custom-checkbox-indicator-indeterminate-border-color: $custom-checkbox-indicator-indeterminate-bg !default;\n\n$custom-radio-indicator-border-radius:          50% !default;\n$custom-radio-indicator-icon-checked:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='-4 -4 8 8'><circle r='3' fill='#{$custom-control-indicator-checked-color}'/></svg>\") !default;\n\n$custom-switch-width:                           $custom-control-indicator-size * 1.75 !default;\n$custom-switch-indicator-border-radius:         $custom-control-indicator-size / 2 !default;\n$custom-switch-indicator-size:                  subtract($custom-control-indicator-size, $custom-control-indicator-border-width * 4) !default;\n\n$custom-select-padding-y:           $input-padding-y !default;\n$custom-select-padding-x:           $input-padding-x !default;\n$custom-select-font-family:         $input-font-family !default;\n$custom-select-font-size:           $input-font-size !default;\n$custom-select-height:              $input-height !default;\n$custom-select-indicator-padding:   1rem !default; // Extra padding to account for the presence of the background-image based indicator\n$custom-select-font-weight:         $input-font-weight !default;\n$custom-select-line-height:         $input-line-height !default;\n$custom-select-color:               $input-color !default;\n$custom-select-disabled-color:      $gray-600 !default;\n$custom-select-bg:                  $input-bg !default;\n$custom-select-disabled-bg:         $gray-200 !default;\n$custom-select-bg-size:             8px 10px !default; // In pixels because image dimensions\n$custom-select-indicator-color:     $gray-800 !default;\n$custom-select-indicator:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'><path fill='#{$custom-select-indicator-color}' d='M2 0L0 2h4zm0 5L0 3h4z'/></svg>\") !default;\n$custom-select-background:          escape-svg($custom-select-indicator) no-repeat right $custom-select-padding-x center / $custom-select-bg-size !default; // Used so we can have multiple background elements (e.g., arrow and feedback icon)\n\n$custom-select-feedback-icon-padding-right: add(1em * .75, (2 * $custom-select-padding-y * .75) + $custom-select-padding-x + $custom-select-indicator-padding) !default;\n$custom-select-feedback-icon-position:      center right ($custom-select-padding-x + $custom-select-indicator-padding) !default;\n$custom-select-feedback-icon-size:          $input-height-inner-half $input-height-inner-half !default;\n\n$custom-select-border-width:        $input-border-width !default;\n$custom-select-border-color:        $input-border-color !default;\n$custom-select-border-radius:       $border-radius !default;\n$custom-select-box-shadow:          inset 0 1px 2px rgba($black, .075) !default;\n\n$custom-select-focus-border-color:  $input-focus-border-color !default;\n$custom-select-focus-width:         $input-focus-width !default;\n$custom-select-focus-box-shadow:    0 0 0 $custom-select-focus-width $input-btn-focus-color !default;\n\n$custom-select-padding-y-sm:        $input-padding-y-sm !default;\n$custom-select-padding-x-sm:        $input-padding-x-sm !default;\n$custom-select-font-size-sm:        $input-font-size-sm !default;\n$custom-select-height-sm:           $input-height-sm !default;\n\n$custom-select-padding-y-lg:        $input-padding-y-lg !default;\n$custom-select-padding-x-lg:        $input-padding-x-lg !default;\n$custom-select-font-size-lg:        $input-font-size-lg !default;\n$custom-select-height-lg:           $input-height-lg !default;\n\n$custom-range-track-width:          100% !default;\n$custom-range-track-height:         .5rem !default;\n$custom-range-track-cursor:         pointer !default;\n$custom-range-track-bg:             $gray-300 !default;\n$custom-range-track-border-radius:  1rem !default;\n$custom-range-track-box-shadow:     inset 0 .25rem .25rem rgba($black, .1) !default;\n\n$custom-range-thumb-width:                   1rem !default;\n$custom-range-thumb-height:                  $custom-range-thumb-width !default;\n$custom-range-thumb-bg:                      $component-active-bg !default;\n$custom-range-thumb-border:                  0 !default;\n$custom-range-thumb-border-radius:           1rem !default;\n$custom-range-thumb-box-shadow:              0 .1rem .25rem rgba($black, .1) !default;\n$custom-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-focus-box-shadow !default;\n$custom-range-thumb-focus-box-shadow-width:  $input-focus-width !default; // For focus box shadow issue in IE/Edge\n$custom-range-thumb-active-bg:               lighten($component-active-bg, 35%) !default;\n$custom-range-thumb-disabled-bg:             $gray-500 !default;\n\n$custom-file-height:                $input-height !default;\n$custom-file-height-inner:          $input-height-inner !default;\n$custom-file-focus-border-color:    $input-focus-border-color !default;\n$custom-file-focus-box-shadow:      $input-focus-box-shadow !default;\n$custom-file-disabled-bg:           $input-disabled-bg !default;\n\n$custom-file-padding-y:             $input-padding-y !default;\n$custom-file-padding-x:             $input-padding-x !default;\n$custom-file-line-height:           $input-line-height !default;\n$custom-file-font-family:           $input-font-family !default;\n$custom-file-font-weight:           $input-font-weight !default;\n$custom-file-color:                 $input-color !default;\n$custom-file-bg:                    $input-bg !default;\n$custom-file-border-width:          $input-border-width !default;\n$custom-file-border-color:          $input-border-color !default;\n$custom-file-border-radius:         $input-border-radius !default;\n$custom-file-box-shadow:            $input-box-shadow !default;\n$custom-file-button-color:          $custom-file-color !default;\n$custom-file-button-bg:             $input-group-addon-bg !default;\n$custom-file-text: (\n  en: \"Browse\"\n) !default;\n\n\n// Form validation\n\n$form-feedback-margin-top:          $form-text-margin-top !default;\n$form-feedback-font-size:           $small-font-size !default;\n$form-feedback-valid-color:         theme-color(\"success\") !default;\n$form-feedback-invalid-color:       theme-color(\"danger\") !default;\n\n$form-feedback-icon-valid-color:    $form-feedback-valid-color !default;\n$form-feedback-icon-valid:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'><path fill='#{$form-feedback-icon-valid-color}' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/></svg>\") !default;\n$form-feedback-icon-invalid-color:  $form-feedback-invalid-color !default;\n$form-feedback-icon-invalid:        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='#{$form-feedback-icon-invalid-color}' viewBox='0 0 12 12'><circle cx='6' cy='6' r='4.5'/><path stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/><circle cx='6' cy='8.2' r='.6' fill='#{$form-feedback-icon-invalid-color}' stroke='none'/></svg>\") !default;\n\n$form-validation-states: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$form-validation-states: map-merge(\n  (\n    \"valid\": (\n      \"color\": $form-feedback-valid-color,\n      \"icon\": $form-feedback-icon-valid\n    ),\n    \"invalid\": (\n      \"color\": $form-feedback-invalid-color,\n      \"icon\": $form-feedback-icon-invalid\n    ),\n  ),\n  $form-validation-states\n);\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n$zindex-dropdown:                   1000 !default;\n$zindex-sticky:                     1020 !default;\n$zindex-fixed:                      1030 !default;\n$zindex-modal-backdrop:             1040 !default;\n$zindex-modal:                      1050 !default;\n$zindex-popover:                    1060 !default;\n$zindex-tooltip:                    1070 !default;\n\n\n// Navs\n\n$nav-link-padding-y:                .5rem !default;\n$nav-link-padding-x:                1rem !default;\n$nav-link-disabled-color:           $gray-600 !default;\n\n$nav-tabs-border-color:             $gray-300 !default;\n$nav-tabs-border-width:             $border-width !default;\n$nav-tabs-border-radius:            $border-radius !default;\n$nav-tabs-link-hover-border-color:  $gray-200 $gray-200 $nav-tabs-border-color !default;\n$nav-tabs-link-active-color:        $gray-700 !default;\n$nav-tabs-link-active-bg:           $body-bg !default;\n$nav-tabs-link-active-border-color: $gray-300 $gray-300 $nav-tabs-link-active-bg !default;\n\n$nav-pills-border-radius:           $border-radius !default;\n$nav-pills-link-active-color:       $component-active-color !default;\n$nav-pills-link-active-bg:          $component-active-bg !default;\n\n$nav-divider-color:                 $gray-200 !default;\n$nav-divider-margin-y:              $spacer / 2 !default;\n\n\n// Navbar\n\n$navbar-padding-y:                  $spacer / 2 !default;\n$navbar-padding-x:                  $spacer !default;\n\n$navbar-nav-link-padding-x:         .5rem !default;\n\n$navbar-brand-font-size:            $font-size-lg !default;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height:                   $font-size-base * $line-height-base + $nav-link-padding-y * 2 !default;\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base !default;\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) / 2 !default;\n\n$navbar-toggler-padding-y:          .25rem !default;\n$navbar-toggler-padding-x:          .75rem !default;\n$navbar-toggler-font-size:          $font-size-lg !default;\n$navbar-toggler-border-radius:      $btn-border-radius !default;\n\n$navbar-dark-color:                 rgba($white, .5) !default;\n$navbar-dark-hover-color:           rgba($white, .75) !default;\n$navbar-dark-active-color:          $white !default;\n$navbar-dark-disabled-color:        rgba($white, .25) !default;\n$navbar-dark-toggler-icon-bg:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'><path stroke='#{$navbar-dark-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-dark-toggler-border-color:  rgba($white, .1) !default;\n\n$navbar-light-color:                rgba($black, .5) !default;\n$navbar-light-hover-color:          rgba($black, .7) !default;\n$navbar-light-active-color:         rgba($black, .9) !default;\n$navbar-light-disabled-color:       rgba($black, .3) !default;\n$navbar-light-toggler-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'><path stroke='#{$navbar-light-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-light-toggler-border-color: rgba($black, .1) !default;\n\n$navbar-light-brand-color:                $navbar-light-active-color !default;\n$navbar-light-brand-hover-color:          $navbar-light-active-color !default;\n$navbar-dark-brand-color:                 $navbar-dark-active-color !default;\n$navbar-dark-brand-hover-color:           $navbar-dark-active-color !default;\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n$dropdown-min-width:                10rem !default;\n$dropdown-padding-y:                .5rem !default;\n$dropdown-spacer:                   .125rem !default;\n$dropdown-font-size:                $font-size-base !default;\n$dropdown-color:                    $body-color !default;\n$dropdown-bg:                       $white !default;\n$dropdown-border-color:             rgba($black, .15) !default;\n$dropdown-border-radius:            $border-radius !default;\n$dropdown-border-width:             $border-width !default;\n$dropdown-inner-border-radius:      subtract($dropdown-border-radius, $dropdown-border-width) !default;\n$dropdown-divider-bg:               $gray-200 !default;\n$dropdown-divider-margin-y:         $nav-divider-margin-y !default;\n$dropdown-box-shadow:               0 .5rem 1rem rgba($black, .175) !default;\n\n$dropdown-link-color:               $gray-900 !default;\n$dropdown-link-hover-color:         darken($gray-900, 5%) !default;\n$dropdown-link-hover-bg:            $gray-100 !default;\n\n$dropdown-link-active-color:        $component-active-color !default;\n$dropdown-link-active-bg:           $component-active-bg !default;\n\n$dropdown-link-disabled-color:      $gray-600 !default;\n\n$dropdown-item-padding-y:           .25rem !default;\n$dropdown-item-padding-x:           1.5rem !default;\n\n$dropdown-header-color:             $gray-600 !default;\n$dropdown-header-padding:           $dropdown-padding-y $dropdown-item-padding-x !default;\n\n\n// Pagination\n\n$pagination-padding-y:              .5rem !default;\n$pagination-padding-x:              .75rem !default;\n$pagination-padding-y-sm:           .25rem !default;\n$pagination-padding-x-sm:           .5rem !default;\n$pagination-padding-y-lg:           .75rem !default;\n$pagination-padding-x-lg:           1.5rem !default;\n$pagination-line-height:            1.25 !default;\n\n$pagination-color:                  $link-color !default;\n$pagination-bg:                     $white !default;\n$pagination-border-width:           $border-width !default;\n$pagination-border-color:           $gray-300 !default;\n\n$pagination-focus-box-shadow:       $input-btn-focus-box-shadow !default;\n$pagination-focus-outline:          0 !default;\n\n$pagination-hover-color:            $link-hover-color !default;\n$pagination-hover-bg:               $gray-200 !default;\n$pagination-hover-border-color:     $gray-300 !default;\n\n$pagination-active-color:           $component-active-color !default;\n$pagination-active-bg:              $component-active-bg !default;\n$pagination-active-border-color:    $pagination-active-bg !default;\n\n$pagination-disabled-color:         $gray-600 !default;\n$pagination-disabled-bg:            $white !default;\n$pagination-disabled-border-color:  $gray-300 !default;\n\n\n// Jumbotron\n\n$jumbotron-padding:                 2rem !default;\n$jumbotron-color:                   null !default;\n$jumbotron-bg:                      $gray-200 !default;\n\n\n// Cards\n\n$card-spacer-y:                     .75rem !default;\n$card-spacer-x:                     1.25rem !default;\n$card-border-width:                 $border-width !default;\n$card-border-radius:                $border-radius !default;\n$card-border-color:                 rgba($black, .125) !default;\n$card-inner-border-radius:          subtract($card-border-radius, $card-border-width) !default;\n$card-cap-bg:                       rgba($black, .03) !default;\n$card-cap-color:                    null !default;\n$card-height:                       null !default;\n$card-color:                        null !default;\n$card-bg:                           $white !default;\n\n$card-img-overlay-padding:          1.25rem !default;\n\n$card-group-margin:                 $grid-gutter-width / 2 !default;\n$card-deck-margin:                  $card-group-margin !default;\n\n$card-columns-count:                3 !default;\n$card-columns-gap:                  1.25rem !default;\n$card-columns-margin:               $card-spacer-y !default;\n\n\n// Tooltips\n\n$tooltip-font-size:                 $font-size-sm !default;\n$tooltip-max-width:                 200px !default;\n$tooltip-color:                     $white !default;\n$tooltip-bg:                        $black !default;\n$tooltip-border-radius:             $border-radius !default;\n$tooltip-opacity:                   .9 !default;\n$tooltip-padding-y:                 .25rem !default;\n$tooltip-padding-x:                 .5rem !default;\n$tooltip-margin:                    0 !default;\n\n$tooltip-arrow-width:               .8rem !default;\n$tooltip-arrow-height:              .4rem !default;\n$tooltip-arrow-color:               $tooltip-bg !default;\n\n// Form tooltips must come after regular tooltips\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y !default;\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x !default;\n$form-feedback-tooltip-font-size:     $tooltip-font-size !default;\n$form-feedback-tooltip-line-height:   $line-height-base !default;\n$form-feedback-tooltip-opacity:       $tooltip-opacity !default;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\n\n\n// Popovers\n\n$popover-font-size:                 $font-size-sm !default;\n$popover-bg:                        $white !default;\n$popover-max-width:                 276px !default;\n$popover-border-width:              $border-width !default;\n$popover-border-color:              rgba($black, .2) !default;\n$popover-border-radius:             $border-radius-lg !default;\n$popover-inner-border-radius:       subtract($popover-border-radius, $popover-border-width) !default;\n$popover-box-shadow:                0 .25rem .5rem rgba($black, .2) !default;\n\n$popover-header-bg:                 darken($popover-bg, 3%) !default;\n$popover-header-color:              $headings-color !default;\n$popover-header-padding-y:          .5rem !default;\n$popover-header-padding-x:          .75rem !default;\n\n$popover-body-color:                $body-color !default;\n$popover-body-padding-y:            $popover-header-padding-y !default;\n$popover-body-padding-x:            $popover-header-padding-x !default;\n\n$popover-arrow-width:               1rem !default;\n$popover-arrow-height:              .5rem !default;\n$popover-arrow-color:               $popover-bg !default;\n\n$popover-arrow-outer-color:         fade-in($popover-border-color, .05) !default;\n\n\n// Toasts\n\n$toast-max-width:                   350px !default;\n$toast-padding-x:                   .75rem !default;\n$toast-padding-y:                   .25rem !default;\n$toast-font-size:                   .875rem !default;\n$toast-color:                       null !default;\n$toast-background-color:            rgba($white, .85) !default;\n$toast-border-width:                1px !default;\n$toast-border-color:                rgba(0, 0, 0, .1) !default;\n$toast-border-radius:               .25rem !default;\n$toast-box-shadow:                  0 .25rem .75rem rgba($black, .1) !default;\n\n$toast-header-color:                $gray-600 !default;\n$toast-header-background-color:     rgba($white, .85) !default;\n$toast-header-border-color:         rgba(0, 0, 0, .05) !default;\n\n\n// Badges\n\n$badge-font-size:                   75% !default;\n$badge-font-weight:                 $font-weight-bold !default;\n$badge-padding-y:                   .25em !default;\n$badge-padding-x:                   .4em !default;\n$badge-border-radius:               $border-radius !default;\n\n$badge-transition:                  $btn-transition !default;\n$badge-focus-width:                 $input-btn-focus-width !default;\n\n$badge-pill-padding-x:              .6em !default;\n// Use a higher than normal value to ensure completely rounded edges when\n// customizing padding or font-size on labels.\n$badge-pill-border-radius:          10rem !default;\n\n\n// Modals\n\n// Padding applied to the modal body\n$modal-inner-padding:               1rem !default;\n\n// Margin between elements in footer, must be lower than or equal to 2 * $modal-inner-padding\n$modal-footer-margin-between:       .5rem !default;\n\n$modal-dialog-margin:               .5rem !default;\n$modal-dialog-margin-y-sm-up:       1.75rem !default;\n\n$modal-title-line-height:           $line-height-base !default;\n\n$modal-content-color:               null !default;\n$modal-content-bg:                  $white !default;\n$modal-content-border-color:        rgba($black, .2) !default;\n$modal-content-border-width:        $border-width !default;\n$modal-content-border-radius:       $border-radius-lg !default;\n$modal-content-inner-border-radius: subtract($modal-content-border-radius, $modal-content-border-width) !default;\n$modal-content-box-shadow-xs:       0 .25rem .5rem rgba($black, .5) !default;\n$modal-content-box-shadow-sm-up:    0 .5rem 1rem rgba($black, .5) !default;\n\n$modal-backdrop-bg:                 $black !default;\n$modal-backdrop-opacity:            .5 !default;\n$modal-header-border-color:         $border-color !default;\n$modal-footer-border-color:         $modal-header-border-color !default;\n$modal-header-border-width:         $modal-content-border-width !default;\n$modal-footer-border-width:         $modal-header-border-width !default;\n$modal-header-padding-y:            1rem !default;\n$modal-header-padding-x:            1rem !default;\n$modal-header-padding:              $modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\n\n$modal-xl:                          1140px !default;\n$modal-lg:                          800px !default;\n$modal-md:                          500px !default;\n$modal-sm:                          300px !default;\n\n$modal-fade-transform:              translate(0, -50px) !default;\n$modal-show-transform:              none !default;\n$modal-transition:                  transform .3s ease-out !default;\n$modal-scale-transform:             scale(1.02) !default;\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n$alert-padding-y:                   .75rem !default;\n$alert-padding-x:                   1.25rem !default;\n$alert-margin-bottom:               1rem !default;\n$alert-border-radius:               $border-radius !default;\n$alert-link-font-weight:            $font-weight-bold !default;\n$alert-border-width:                $border-width !default;\n\n$alert-bg-level:                    -10 !default;\n$alert-border-level:                -9 !default;\n$alert-color-level:                 6 !default;\n\n\n// Progress bars\n\n$progress-height:                   1rem !default;\n$progress-font-size:                $font-size-base * .75 !default;\n$progress-bg:                       $gray-200 !default;\n$progress-border-radius:            $border-radius !default;\n$progress-box-shadow:               inset 0 .1rem .1rem rgba($black, .1) !default;\n$progress-bar-color:                $white !default;\n$progress-bar-bg:                   theme-color(\"primary\") !default;\n$progress-bar-animation-timing:     1s linear infinite !default;\n$progress-bar-transition:           width .6s ease !default;\n\n\n// List group\n\n$list-group-color:                  null !default;\n$list-group-bg:                     $white !default;\n$list-group-border-color:           rgba($black, .125) !default;\n$list-group-border-width:           $border-width !default;\n$list-group-border-radius:          $border-radius !default;\n\n$list-group-item-padding-y:         .75rem !default;\n$list-group-item-padding-x:         1.25rem !default;\n\n$list-group-hover-bg:               $gray-100 !default;\n$list-group-active-color:           $component-active-color !default;\n$list-group-active-bg:              $component-active-bg !default;\n$list-group-active-border-color:    $list-group-active-bg !default;\n\n$list-group-disabled-color:         $gray-600 !default;\n$list-group-disabled-bg:            $list-group-bg !default;\n\n$list-group-action-color:           $gray-700 !default;\n$list-group-action-hover-color:     $list-group-action-color !default;\n\n$list-group-action-active-color:    $body-color !default;\n$list-group-action-active-bg:       $gray-200 !default;\n\n\n// Image thumbnails\n\n$thumbnail-padding:                 .25rem !default;\n$thumbnail-bg:                      $body-bg !default;\n$thumbnail-border-width:            $border-width !default;\n$thumbnail-border-color:            $gray-300 !default;\n$thumbnail-border-radius:           $border-radius !default;\n$thumbnail-box-shadow:              0 1px 2px rgba($black, .075) !default;\n\n\n// Figures\n\n$figure-caption-font-size:          90% !default;\n$figure-caption-color:              $gray-600 !default;\n\n\n// Breadcrumbs\n\n$breadcrumb-font-size:              null !default;\n\n$breadcrumb-padding-y:              .75rem !default;\n$breadcrumb-padding-x:              1rem !default;\n$breadcrumb-item-padding:           .5rem !default;\n\n$breadcrumb-margin-bottom:          1rem !default;\n\n$breadcrumb-bg:                     $gray-200 !default;\n$breadcrumb-divider-color:          $gray-600 !default;\n$breadcrumb-active-color:           $gray-600 !default;\n$breadcrumb-divider:                quote(\"/\") !default;\n\n$breadcrumb-border-radius:          $border-radius !default;\n\n\n// Carousel\n\n$carousel-control-color:             $white !default;\n$carousel-control-width:             15% !default;\n$carousel-control-opacity:           .5 !default;\n$carousel-control-hover-opacity:     .9 !default;\n$carousel-control-transition:        opacity .15s ease !default;\n\n$carousel-indicator-width:           30px !default;\n$carousel-indicator-height:          3px !default;\n$carousel-indicator-hit-area-height: 10px !default;\n$carousel-indicator-spacer:          3px !default;\n$carousel-indicator-active-bg:       $white !default;\n$carousel-indicator-transition:      opacity .6s ease !default;\n\n$carousel-caption-width:             70% !default;\n$carousel-caption-color:             $white !default;\n\n$carousel-control-icon-width:        20px !default;\n\n$carousel-control-prev-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' width='8' height='8' viewBox='0 0 8 8'><path d='M5.25 0l-4 4 4 4 1.5-1.5L4.25 4l2.5-2.5L5.25 0z'/></svg>\") !default;\n$carousel-control-next-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' width='8' height='8' viewBox='0 0 8 8'><path d='M2.75 0l-1.5 1.5L3.75 4l-2.5 2.5L2.75 8l4-4-4-4z'/></svg>\") !default;\n\n$carousel-transition-duration:       .6s !default;\n$carousel-transition:                transform $carousel-transition-duration ease-in-out !default; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\n\n\n// Spinners\n\n$spinner-width:         2rem !default;\n$spinner-height:        $spinner-width !default;\n$spinner-border-width:  .25em !default;\n\n$spinner-width-sm:        1rem !default;\n$spinner-height-sm:       $spinner-width-sm !default;\n$spinner-border-width-sm: .2em !default;\n\n\n// Close\n\n$close-font-size:                   $font-size-base * 1.5 !default;\n$close-font-weight:                 $font-weight-bold !default;\n$close-color:                       $black !default;\n$close-text-shadow:                 0 1px 0 $white !default;\n\n\n// Code\n\n$code-font-size:                    87.5% !default;\n$code-color:                        $pink !default;\n\n$kbd-padding-y:                     .2rem !default;\n$kbd-padding-x:                     .4rem !default;\n$kbd-font-size:                     $code-font-size !default;\n$kbd-color:                         $white !default;\n$kbd-bg:                            $gray-900 !default;\n\n$pre-color:                         $gray-900 !default;\n$pre-scrollable-max-height:         340px !default;\n\n\n// Utilities\n\n$displays: none, inline, inline-block, block, table, table-row, table-cell, flex, inline-flex !default;\n$overflows: auto, hidden !default;\n$positions: static, relative, absolute, fixed, sticky !default;\n$user-selects: all, auto, none !default;\n\n\n// Printing\n\n$print-page-size:                   a3 !default;\n$print-body-min-width:              map-get($grid-breakpoints, \"lg\") !default;\n", "html {\r\n\tbody {\t\r\n\t\tbackground-color: $body-background-color;\r\n\r\n\t\ta {\r\n\t\t\tcolor: $link-color;\r\n\t\t\tbackground-color: transparent; // Remove the gray background on active links in IE 10.\r\n\r\n\t\t  \t@include hover() {\r\n\t\t\t\tcolor: $link-hover-color;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}", "// Hover mixin and `$enable-hover-media-query` are deprecated.\n//\n// Originally added during our alphas and maintained during betas, this mixin was\n// designed to prevent `:hover` stickiness on iOS-an issue where hover styles\n// would persist after initial touch.\n//\n// For backward compatibility, we've kept these mixins and updated them to\n// always return their regular pseudo-classes instead of a shimmed media query.\n//\n// Issue: https://github.com/twbs/bootstrap/issues/25195\n\n@mixin hover() {\n  &:hover { @content; }\n}\n\n@mixin hover-focus() {\n  &:hover,\n  &:focus {\n    @content;\n  }\n}\n\n@mixin plain-hover-focus() {\n  &,\n  &:hover,\n  &:focus {\n    @content;\n  }\n}\n\n@mixin hover-focus-active() {\n  &:hover,\n  &:focus,\n  &:active {\n    @content;\n  }\n}\n", ".header-icon {\t\t\r\n\tcolor: $base-text-color;\r\n\r\n\t&:not(.btn) {\r\n\r\n\r\n\t\t>[class*='fa-']:first-child,\r\n\t\t>.#{$cust-icon-prefix}:first-child {\r\n\t\t\t\tcolor: $header-link-color;\r\n\t\t\t}\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\t&>[class*='fa-']:only-child,\r\n\t\t\t\t&>.#{$cust-icon-prefix} {\r\n\t\t\t\t\tcolor: $header-link-hover-color;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\r\n\t\t&[data-toggle=\"dropdown\"] {\r\n\r\n\r\n\t\t\t&[aria-expanded=\"true\"] {\r\n\t\t\t\tcolor: $header-link-hover-color;\r\n\t\t\t\t\r\n\t\t\t\t>[class*='fa-']:first-child,\r\n\t\t\t\t>.#{$cust-icon-prefix}:first-child {\r\n\t\t\t\t\tcolor: $header-link-hover-color !important;\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\r\n\t\t\t/* header dropdowns */\r\n\t\t\t/* note: important rules to override popper's inline classes */\r\n\t\t\t& + .dropdown-menu {\r\n\t\t\t\tborder-color: $header-border-color;\r\n\t\t\t}\r\n\r\n\t\t\t/* end header dropdowns */\r\n\t\t\t\r\n\t\t}\r\n\r\n\t}\r\n\r\n\t&:hover{\r\n\t\tcolor:$header-link-hover-color;\r\n\t}\r\n}\r\n\r\n.page-header {\r\n\tbackground-color: $header-bg;\r\n}", "#search-field {\r\n\tbackground: $header-search-bg;\r\n\tborder: 1px solid $header-search-border-color;\r\n}", ".notification {\r\n\tli {\r\n\t\t&.unread {\r\n\t\t\tbackground: lighten($warning-50, 9%);\r\n\t\t}\r\n\r\n\t\t> :first-child {\r\n\t\t\tborder-bottom: 1px solid rgba($black, 0.06);\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground-image: linear-gradient(rgba(29, 33, 41, .03), rgba(29, 33, 41, .04));\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.name {\r\n\t\tcolor: lighten($black, 13.5%);\r\n\t}\r\n\r\n\t.msg-a,\r\n\t.msg-b {\r\n\t\tcolor: lighten($black, 33.5%);\r\n\t}\r\n\r\n\t&.notification-layout-2 {\r\n\t\tli {\r\n\t\t\tbackground: $gray-50;\r\n\t\t\t&.unread {\r\n\t\t\t\tbackground: $white;\r\n\t\t\t}\r\n\t\t\t> :first-child {\r\n\t\t\t\tborder-bottom: 1px solid rgba($black, 0.04);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&:hover {\r\n\t\t\tcursor: pointer;\r\n\t\t}\r\n\t}\r\n\r\n}", ".app-list-item {\r\n\tcolor:$base-text-color;\r\n\r\n\t&:hover {\r\n\t\tborder: 1px solid lighten(lighten($black, 75%), 14%);\r\n\t}\r\n\r\n\t&:active {\r\n\t\tborder-color: $primary-500;\r\n\t}\r\n}", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n != null and $n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width. Null for the largest (last) breakpoint.\n// The maximum value is calculated as the minimum of the next one less 0.02px\n// to work around the limitations of `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $next: breakpoint-next($name, $breakpoints);\n  @return if($next, breakpoint-min($next, $breakpoints) - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  $max: breakpoint-max($name, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($name, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "@include media-breakpoint-up($mobile-breakpoint) {\r\n\t.header-function-fixed {\r\n\t\t&.nav-function-top {\r\n\t\t\t.page-header {\r\n\t\t\t\tbox-shadow: 0px 0px 28px 2px $header-border-bottom-color;\r\n\t\t\t}\t\r\n\t\t}\r\n\t}\r\n}", "\r\n\r\n.nav-title {\r\n\tcolor: $nav-title-color;\r\n}\r\n\r\n.nav-menu {\r\n\r\n\tli {\r\n\t\r\n\r\n\t\t&.open {\r\n\t\t\t> a {\r\n\t\t\t\t@include text-contrast($nav-background);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&.active {\r\n\r\n\t\t\t> a {\r\n\t\t\t\t@include text-contrast($nav-background);\r\n\t\t\t\tbackground-color: rgba($white,0.04);\r\n\t\t\t\t@include box-shadow(inset 3px 0 0 $color-primary);\r\n\r\n\t\t\t\t&:hover {\r\n\r\n\t\t\t\t\t>[class*='fa-'],\r\n\t\t\t\t\t>.#{$cust-icon-prefix} {\r\n\t\t\t\t\t    color: $nav-icon-hover-color;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\r\n\t\t\t> ul {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t}\r\n\r\n\t\t\t/* arrow that appears next to active/selected items */\r\n\t\t\t&:not(.open) > a:before {\r\n\t\t\t\tcolor: #24b3a4;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\ta {\r\n\t\t\tcolor: $nav-link-color;\r\n\t\t\t.dl-ref {\r\n\t\t\t\t&.label {\r\n\t\t\t\t\tcolor: rgba(255,255,255,0.7);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t>[class*='fa-'],\r\n\t\t\t>.#{$cust-icon-prefix} {\r\n\t\t\t    color:$nav-icon-color;\r\n\t\t\t} \r\n\r\n\r\n\t\t\t&.collapsed {\r\n\t\t\t\t.nav-menu-btn-sub-collapse {\r\n\t\t\t\t\t@include rotate(180);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\t@include text-contrast(rgba(lighten($nav-base-color, 1%), (80/100)))\r\n\t\t\t\tbackground-color: rgba($black, 0.1);\r\n\r\n\t\t\t\t.badge {\r\n\t\t\t\t\tcolor: $nav-badge-color;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t>[class*='fa-'],\r\n\t\t\t\t>.#{$cust-icon-prefix} {\r\n\t\t\t\t\tcolor:$nav-icon-hover-color;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t> .badge {\r\n\t\t\t\t\tbox-shadow: 0 0 0 1px rgba(lighten($nav-base-color, 1%), (80/100));\r\n\t\t\t\t\tborder: 1px solid rgba(lighten($nav-base-color, 1%), (80/100));\r\n\t\t\t\t}\r\n\t\t\t\r\n\t\t\t}\r\n\r\n\t\t\t&:focus {\r\n\t\t\t\t@include text-contrast( rgba(darken($nav-base-color, 5% ), (50/100)) );\r\n\r\n\t\t\t\t.badge {\r\n\t\t\t\t\tcolor: $nav-badge-color;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t} \r\n\r\n\t\tb.collapse-sign {\r\n\t\t\tcolor: $primary-400;\r\n\t\t}\r\n\r\n\t\t// Sub nav level 1\r\n\t\t> ul {\r\n\t\t\tbackground-color: rgba($black,0.1);\r\n\r\n\t\t\tli {\r\n\r\n\t\t\t\ta {\r\n\t\t\t\t\tcolor: darken($nav-link-color, 5%);\r\n\r\n\t\t\t\t\t>[class*='fa-'],\r\n\t\t\t\t\t>.#{$cust-icon-prefix} {\r\n\t\t\t\t\t\tcolor: $nav-icon-color;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t> .badge {\r\n\t\t\t\t\t\tcolor: $nav-badge-color;\r\n\t\t\t\t\t\tbackground-color: $nav-badge-bg-color;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t&:hover {\r\n\t\t\t\t\t\t@include text-contrast(rgba(($black), (10/100)));\r\n\t\t\t\t\t\tbackground-color: rgba(($black), (10/100));\r\n\r\n\t\t\t\t\t\t> .nav-link-text {\r\n\t\t\t\t\t\t\t>[class*='fa-'],\r\n\t\t\t\t\t\t\t>.#{$cust-icon-prefix} {\r\n\t\t\t\t\t\t\t    color:$nav-icon-hover-color;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&.active {\r\n\t\t\t\t\t\r\n\t\t\t\t\t> a {\r\n\t\t\t\t\t\t@include text-contrast(rgba(lighten(darken($nav-base-color, 11%), 5%), (45/100)))\r\n\t\t\t\t\t\tbackground-color:transparent;\r\n\r\n\t\t\t\t\t\t> .nav-link-text {\r\n\t\t\t\t\t\t\t>[class*='fa-'],\r\n\t\t\t\t\t\t\t>.#{$cust-icon-prefix} {\r\n\t\t\t\t\t\t\t    @include text-contrast(rgba(lighten(darken($nav-base-color, 11%), 5%), (45/100)));\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\t\r\n\r\n\t\t\t\t\t\t&:hover {\r\n\t\t\t\t\t\t\t> .nav-link-text {\r\n\t\t\t\t\t\t\t\t>[class*='fa-'],\r\n\t\t\t\t\t\t\t\t>.#{$cust-icon-prefix} {\r\n\t\t\t\t\t\t\t\t    color:$nav-icon-hover-color;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\t\t\t\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Sub nav level 2\r\n\t\t\t\t> ul {\r\n\r\n\t\t\t\t\tli {\r\n\r\n\t\t\t\t\t\t&.active {\r\n\t\t\t\t\t\t\t> a {\r\n\t\t\t\t\t\t\t\t@include text-contrast( rgba(lighten(darken($nav-base-color, 11%), 5%), (20/100)) )\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\ta {\r\n\t\t\t\t\t\t\tcolor: darken($nav-link-color, 7%);\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t&:hover {\r\n\t\t\t\t\t\t\t\t@include text-contrast( rgba(lighten(darken($nav-base-color, 11%), 5%), (45/100)) )\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t> .badge {\r\n\t\t\t\t\t\t\t\tcolor: $nav-badge-color;\r\n\t\t\t\t\t\t\t\tbackground-color: $nav-badge-bg-color;\r\n\t\t\t\t\t\t\t\tborder: 1px solid darken($color-fusion, 0%);\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t} \r\n\r\n\t}\r\n\r\n} \r\n\r\n/* nav clean elements */\r\n.nav-menu-clean {\r\n\tbackground:$white;\r\n\t\r\n\tli {\r\n\t\ta {\r\n\t\t\tcolor: $fusion-500 !important;\r\n\t\t\tspan {\r\n\t\t\t\tcolor: $fusion-500 !important;\r\n\t\t\t}\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground-color: #f4f4f4 !important;\r\n\t\t\t}\r\n\t\t}\t\r\n\t}\r\n\r\n}\r\n\r\n/* nav bordered elements */\r\n.nav-menu-bordered {\r\n\tborder: 1px solid $card-border-color;\r\n\r\n\tli a {\r\n\t\tborder-bottom: 1px solid $card-border-color;\r\n\t}\r\n\r\n}", ".nav-filter {\r\n\tinput[type=\"text\"] {\r\n\t\tbackground: rgba($black, 0.4); \r\n\t\tcolor: $white;\r\n\r\n\t\t&:not(:focus) {\r\n\t\t\tborder-color: rgba(0, 0, 0, 0.1);\r\n\t\t}\r\n\r\n\t\t&:focus {\r\n\t\t\tborder-color: lighten($nav-background, 13%);\r\n\t\t}\r\n\t\t\r\n\t}\r\n}", ".info-card {\r\n\tcolor:$white;\r\n\t.info-card-text {\r\n\t\ttext-shadow: $black 0 1px;\r\n\t}\r\n\t\r\n}", "$nav-function-top-menu-item-bg: $primary-500;\r\n\r\n@include media-breakpoint-up($mobile-breakpoint) {\r\n\r\n\t.nav-function-top {\r\n\r\n\t\t/* correct search field color */\r\n\t\t#search-field {\r\n\t\t\tcolor: $white;\r\n\t\t}\r\n\r\n\t\t&:not(.header-function-fixed) {\r\n\t\t\t\t\r\n\t\t\t#nff {\r\n\t\t\t\tposition:relative;\r\n\r\n\t\t\t\t.onoffswitch-title {\r\n\t\t\t\t\tcolor: $settings-incompat-title;\r\n\t\t\t\t}\r\n\t\t\t\t.onoffswitch-title-desc {\r\n\t\t\t\t\tcolor: $settings-incompat-desc;\r\n\t\t\t\t}\r\n\t\t\t\t&:after {\r\n\t\t\t\t\tbackground: $settings-incompat-bg;\r\n\t\t\t\t\tborder: 1px solid $settings-incompat-border;\r\n\t\t\t\t\tcolor:$fusion-900;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t}\r\n\r\n\t\t.page-header {\r\n\t\t\tbackground-image: -webkit-linear-gradient(270deg, $nav-background-shade, transparent);\r\n\t\t\tbackground-image: linear-gradient(270deg, $nav-background-shade, transparent); \r\n\t\t\tbackground-color: $nav-background; \r\n\t\t\tbox-shadow: 0px 0px 14px 0px $header-border-bottom-color;\r\n\r\n\t\t\t.header-icon:not(.btn) > [class*='fa-']:first-child, \r\n\t\t\t.header-icon:not(.btn) > .ni:first-child {\r\n\t\t\t\tcolor:  lighten($header-link-color, 10%);\r\n\r\n\t\t\t\t&:hover {\r\n\t\t\t\t\tcolor:  lighten($header-link-color, 20%);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\r\n\r\n\t\t\t.badge.badge-icon {\r\n\t\t\t\tbox-shadow: 0 0 0 1px $primary-600;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t}\r\n\r\n\t\t.page-sidebar {\r\n\t\t\tbackground: $white;\r\n\r\n\t\t\tbox-shadow: 0px 0px 14px 0px $header-border-bottom-color;\r\n\r\n\t\t\t.primary-nav {\r\n\r\n\t\t\t\t.nav-menu {\r\n\r\n\t\t\t\t\t> li {\r\n\r\n\t\t\t\t\t\t&.active {\r\n\t\t\t\t\t\t\t> a {\r\n\r\n\t\t\t\t\t\t\t\t&:before {\r\n\t\t\t\t\t\t\t\t\tcolor: #24b3a4;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\r\n\r\n\t\t\t\t\t\t> a {\r\n\r\n\r\n\t\t\t\t\t\t\t>.#{$cust-icon-prefix},\r\n\t\t\t\t\t\t\t>[class*='fa-'] {\r\n\t\t\t\t\t\t\t\tcolor: inherit;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t>.collapse-sign {\r\n\t\t\t\t\t\t\t\tcolor: lighten($nav-background, 30%);\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t}\t\r\n\r\n\t\t\t\t\t\t// all children\r\n\t\t\t\t\t\ta {\r\n\t\t\t\t\t\t\tcolor: $nav-background;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t> ul {\r\n\t\t\t\t\t\t\tbackground: $nav-top-drowndown-background;\r\n\r\n\t\t\t\t\t\t\tli {\r\n\r\n\t\t\t\t\t\t\t\ta {\r\n\t\t\t\t\t\t\t\t\tcolor: $nav-top-drowndown-color;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tul {\r\n\t\t\t\t\t\t\t\t\tbackground: $nav-top-drowndown-background;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t&:hover {\r\n\t\t\t\t\t\t\t\t\t> a {\r\n\t\t\t\t\t\t\t\t\t\tbackground: $nav-top-drowndown-hover;\r\n\t\t\t\t\t\t\t\t\t\tcolor: $nav-top-drowndown-hover-color;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t&:after {\r\n\t\t\t\t\t\t\t\tbackground: transparent;\r\n\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t&:before {\r\n\t\t\t\t\t\t\t\tcolor: $nav-top-drowndown-background;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t//first child hover\r\n\t\t\t\t\t\t&:hover {\r\n\r\n\t\t\t\t\t\t\t> a {\r\n\t\t\t\t\t\t\t\tcolor: $primary-500;\r\n\t\t\t\t\t\t\t\tbackground: transparent;\r\n\t\t\t\t\t\t\t}\t\t\t\t\t\t\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\t\r\n}", "@include media-breakpoint-up($mobile-breakpoint) {\r\n\r\n\t.nav-function-minify:not(.nav-function-top) {\r\n\r\n\t\t.page-sidebar {\r\n\t\t\t\t\r\n\t\t\t.primary-nav {\r\n\t\r\n\t\t\t\t.nav-menu {\r\n\t\t\t\t\r\n\t\t\t\t\tli {\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t&.active {\r\n\t\t\t\t\t\t\t&.open > a:before {\r\n\t\t\t\t\t\t\t\tcolor: #24b3a4;\r\n\t\t\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// first level\r\n\t\t\t\t\t> li {\r\n\r\n\t\t\t\t\t\t> a {\r\n\r\n\t\t\t\t\t\t\t> .nav-link-text {\r\n\t\t\t\t\t\t\t\tbackground: trasparent;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t// sub 1\r\n\t\t\t\t\t\t\t& + ul {\r\n\t\t\t\t\t\t\t\tbackground-color: $nav-background;\r\n\r\n\t\t\t\t\t\t\t\t//arrow\r\n\t\t\t\t\t\t\t    &:before {\r\n\t\t\t\t\t\t\t\t\tcolor: $nav-background;\r\n\t\t\t\t\t\t\t    }\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\r\n\t\t\t\t&:hover {\r\n\t\t\t\t\toverflow: visible;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.nav-menu > li:hover {\r\n\r\n\t\t\t\t\t\t> a {\r\n\t\t\t\t\t\t\tbackground: $nav-minify-hover-bg;\r\n\t\t\t\t\t\t\tcolor: $white;\r\n\r\n\t\t\t\t\t\t\t>.nav-link-text:last-child {\r\n\t\t\t\t\t\t\t\t\tbackground: $nav-background;\r\n\r\n\t\t\t\t\t\t\t\t&:before {\r\n\t\t\t\t\t\t\t\t\tcolor: $nav-background;\r\n\t\t\t\t\t\t\t    }\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} \r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.page-header {\r\n\t\t\t[data-class=\"nav-function-minify\"] {\r\n\t\t\t\tbackground: $header-btn-active-bg;\r\n\t\t\t\tborder-color: darken($header-btn-active-bg, 10%) !important;\r\n\t\t\t\tcolor:$header-btn-active-color !important;\r\n\t\t\t}\r\n\t\t}\t\r\n\t}\r\n}\r\n", ".nav-footer {\r\n\t@extend %nav-bg;\r\n\t.nav-footer-buttons {\r\n\r\n\t\t> li {\r\n\t\t\t> a {\r\n\t\t\t\tcolor: $nav-footer-link-color;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.nav-function-fixed {\r\n\r\n\t.nav-footer {\r\n\r\n\t\tbackground: $nav-background;\r\n\r\n\t\t&:before {\r\n\t\t\tbackground: rgba($nav-title-border-bottom-color, 0.2);\r\n\t\t\tbackground: -moz-linear-gradient(left, $nav-background 0%, lighten($nav-background, 15%) 50%, lighten($nav-background, 15%) 50%, $nav-background 100%);\r\n\t\t\tbackground: -webkit-linear-gradient(left, $nav-background 0%, lighten($nav-background, 15%) 50%, lighten($nav-background, 15%) 50%, $nav-background 100%);\r\n\t\t\tbackground: linear-gradient(to right, $nav-background 0%, lighten($nav-background, 15%) 50%, lighten($nav-background, 15%) 50%, $nav-background 100%);\r\n\t\t}\r\n\r\n\t}\r\n\r\n}\r\n\r\n@include media-breakpoint-up(lg) {\r\n\r\n\t.nav-function-minify {\r\n\r\n\t\t.nav-footer {\r\n\r\n\t\t\tbackground-color: darken($nav-background, 2%);\r\n\r\n\t\t\t[data-class=\"nav-function-minify\"] {\r\n\t\t\t\tcolor: $nav-icon-color;\r\n\t\t\t}\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground-color: lighten($nav-background, 3%);\r\n\r\n\t\t\t\t[data-class=\"nav-function-minify\"] {\r\n\t\t\t\t\tcolor: $nav-icon-hover-color;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n", ".page-content-wrapper {\r\n\tbackground-color: $page-bg;      \r\n}", ".subheader-icon {\r\n\tcolor: $nav-icon-hover-color;\r\n}\r\n.subheader-title {\r\n\tcolor: $fusion-500;\r\n\ttext-shadow: $white 0 1px;\r\n\tsmall {\r\n\t\tcolor: $fusion-100; //$fusion-100;\r\n\t}\r\n}", ".page-footer {\r\n\tbackground: $footer-bg;\r\n    color:$footer-text-color;\r\n}\r\n", ".accordion {\r\n\t.card {\r\n\t\t.card-header {\r\n\t\t\tbackground-color: $frame-border-color;\r\n\t\t\t.card-title {\r\n\t\t\t\tcolor: $primary-500;\r\n\r\n\t\t\t\t&.collapsed {\r\n\t\t\t\t\tcolor: $fusion-100;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t&.accordion-clean {\r\n\t\t.card-header {\r\n\t\t\tbackground: $white;\r\n\t\t}\r\n\t}\r\n\r\n\t&.accordion-hover {\r\n\t\t.card-header {\r\n\t\t\tbackground:$white;\r\n\t\t\t&:hover {\r\n\t\t\t\t.card-title.collapsed {\r\n\t\t\t\t\tcolor: $white;\r\n\t\t\t\t\tbackground-color: $primary-300;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.card-title:not(.collapsed) {\r\n\t\t\tcolor: $white;\r\n\t\t\tbackground-color: $primary-500;\r\n\t\t\t\r\n\t\t}\r\n\t}\r\n}\r\n", "/* \tDEV NOTE: The reason why we had to add this layer for alert colors is because BS4 \r\n\tdoes not allow you to add your own alert colors via variable control rather \r\n\tthrough a systemetic agent that changes the theme colors. \r\n\r\n\tREF: https://github.com/twbs/bootstrap/issues/24341#issuecomment-337457218\r\n*/\r\n\r\n.alert-primary {\r\n\tcolor: desaturate($primary-800, 45%);\r\n\tbackground-color: desaturate(lighten($primary-500, 39%), 17%);\r\n\tborder-color: desaturate(lighten($primary-500, 28%), 25%);\r\n}\r\n\r\n.alert-success {\r\n\tcolor:  desaturate($success-500, 35%);\r\n\tbackground-color: desaturate(lighten($success-500, 53%), 15%);\r\n\tborder-color: desaturate(lighten($success-500, 33%), 10%);\r\n}\r\n\r\n.alert-danger {\r\n\tcolor: $danger-800;\r\n\tbackground-color: lighten($danger-500, 34%);\r\n\tborder-color: lighten($danger-500, 20%);\r\n}\r\n\r\n\r\n.alert-warning {\r\n\tcolor: darken($warning-900, 5%);\r\n\tbackground-color: lighten($warning-500, 33%);\r\n\tborder-color: lighten($warning-500, 7%);\r\n}\r\n\r\n\r\n.alert-info {\r\n\tcolor: $info-800;\r\n\tbackground-color: lighten($info-500, 40%);\r\n\tborder-color: lighten($info-500, 20%);\r\n}\r\n\r\n\r\n.alert-secondary {\r\n\tcolor: $fusion-500;\r\n\tbackground-color: lighten($fusion-50, 42%);\r\n\tborder-color: lighten($fusion-500, 55%);\r\n}", ".badge.badge-icon {\r\n\tbackground-color: $nav-badge-bg-color;\r\n\tcolor: $nav-badge-color;\t\r\n\t@include box-shadow(0 0 0 1px $header-bg);\r\n}\t\r\n", "/* btn switch */\r\n.btn-switch {\r\n\tbackground: $fusion-300;\r\n\t@include text-contrast($fusion-300);\r\n\r\n\t&:hover{\r\n\t\t@include text-contrast($fusion-300);\r\n\t}\r\n\r\n\t&:after{\r\n\t\t@include text-contrast(#828282);\r\n\t}\r\n\r\n\t&.active {\r\n\t\tcolor:$white;\r\n\t\tbackground:$color-primary;\r\n\t\t&:before {\r\n\t\t\t@include text-contrast($color-primary);\r\n\t\t}\r\n\r\n\t\t&:after {\r\n\t\t\tbackground:$white;\r\n\t\t\tcolor:$color-primary;\r\n\t\t}\r\n\r\n\t}\r\n}\r\n\r\n/* button used to close filter and mobile search */\r\n.btn-search-close {\r\n\tcolor: $white;\r\n}\r\n\r\n/* buttons used in the header section of the page */\r\n.header-btn {\r\n\t@extend %header-btn;\r\n\r\n\t&[data-class='mobile-nav-on'] {\r\n\t\tborder-color: $danger-700;\r\n\t\t@include gradient-img($danger-600,$danger-800);\r\n\t\tcolor:$white;\r\n\t}\r\n\r\n}\r\n\r\n\r\n/* dropdown btn */\r\n/* used on info card pulldown filter */\r\n.pull-trigger-btn {\r\n\tbackground: rgba($black, .4);\r\n\tborder: 1px solid rgba($black, .4);\r\n\tcolor: $white !important;\r\n\tbox-shadow: 0px 0px 2px rgba($primary-500, 0.3);\r\n\r\n\t&:hover {\r\n\t\tbackground: $primary-500;\r\n\t\tborder-color: $primary-600;\r\n\t}\r\n}\r\n\r\n/* btn misc */\r\n.btn-default {\r\n\t@extend %btn-default;\r\n}\r\n\r\n.btn-outline-default {\r\n\tcolor: $body-color;\r\n\tborder-color: $input-border-color;\r\n\r\n\t&:hover,\r\n\t&:not(:disabled):not(.disabled):active,\r\n\t&:not(:disabled):not(.disabled).active,\r\n\t.show > &.dropdown-toggle {\r\n\t\tcolor: $body-color;\r\n\t\tbackground-color: lighten($input-border-color, 8%);\r\n\t\tborder-color: $input-border-color;\r\n\t}\r\n\r\n\t&.disabled,\r\n\t&:disabled {\r\n\t\tcolor: $body-color;\r\n\t}\r\n\r\n}\r\n\r\n\r\n/* btn shadows */\r\n@mixin button-shadow($value) {\r\n  box-shadow: 0 2px 6px 0 rgba($value, .5);\r\n}\r\n@each $color, $value in $theme-colors {\r\n  .btn-#{$color} {\r\n\t@include button-shadow($value);\r\n  }\r\n}\r\n\r\n.btn-icon-light {\r\n\r\n\tcolor: rgba($white, 0.7) !important;\r\n\tborder-color: transparent !important;\r\n\r\n\t&:not(.active):not(:active):not(:hover):not(:focus) {\r\n\t\tcolor: rgba($white, 0.7) !important;\r\n\t}\r\n\r\n\t&:hover {\r\n\t\tcolor: $white !important;\r\n\t\tbackground-color: rgba($white,0.2) !important;\r\n\t\t\r\n\t}\t\r\n}\r\n\r\n/* bootstrap buttons */\r\n.btn-link {\r\n  color: $primary-500;\r\n\r\n  @include hover() {\r\n    color: $primary-700;\r\n  }\r\n}", ".nav-pills {\r\n  .nav-link.active,\r\n  .show > .nav-link {\r\n    color: $nav-pills-link-active-color;\r\n    background-color: $primary-500;\r\n  }\r\n}", ".card-header {\r\n\tbackground-color: $frame-border-color;\r\n} ", ".carousel-control-prev:hover {\r\n\tbackground: linear-gradient(to right, rgba(0,0,0,0.25) 0%,rgba(0,0,0,0) 45%); \r\n}\r\n\r\n.carousel-control-next:hover {\r\n\tbackground: linear-gradient(to left, rgba(0,0,0,0.25) 0%,rgba(0,0,0,0) 45%); \r\n}", "/* dropdown menu multi-level */\r\n.dropdown-menu {\r\n\t.dropdown-menu {\r\n\t\tbackground:$white;\r\n\t}\r\n\t.dropdown-multilevel {\r\n\t\t&:hover {\r\n\t\t\t> .dropdown-item:not(.disabled) {\r\n\t\t\t\tbackground: $gray-100;\r\n\t\t\t\tcolor: $dropdown-link-hover-color;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n}\r\n\r\n.dropdown-item {\r\n  @include hover-focus() {\r\n    color: $primary-700;\r\n    @include gradient-bg(#f8f9fa);\r\n  }\r\n\r\n  &.active,\r\n  &:active {\r\n   \tcolor: $primary-800;\r\n    @include gradient-bg(lighten($primary-50, 10%));\r\n  }\r\n\r\n}", "// Gradients\n\n@mixin gradient-bg($color) {\n  @if $enable-gradients {\n    background: $color linear-gradient(180deg, mix($body-bg, $color, 15%), $color) repeat-x;\n  } @else {\n    background-color: $color;\n  }\n}\n\n// Horizontal gradient, from left to right\n//\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\n@mixin gradient-x($start-color: $gray-700, $end-color: $gray-800, $start-percent: 0%, $end-percent: 100%) {\n  background-image: linear-gradient(to right, $start-color $start-percent, $end-color $end-percent);\n  background-repeat: repeat-x;\n}\n\n// Vertical gradient, from top to bottom\n//\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\n@mixin gradient-y($start-color: $gray-700, $end-color: $gray-800, $start-percent: 0%, $end-percent: 100%) {\n  background-image: linear-gradient(to bottom, $start-color $start-percent, $end-color $end-percent);\n  background-repeat: repeat-x;\n}\n\n@mixin gradient-directional($start-color: $gray-700, $end-color: $gray-800, $deg: 45deg) {\n  background-image: linear-gradient($deg, $start-color, $end-color);\n  background-repeat: repeat-x;\n}\n@mixin gradient-x-three-colors($start-color: $blue, $mid-color: $purple, $color-stop: 50%, $end-color: $red) {\n  background-image: linear-gradient(to right, $start-color, $mid-color $color-stop, $end-color);\n  background-repeat: no-repeat;\n}\n@mixin gradient-y-three-colors($start-color: $blue, $mid-color: $purple, $color-stop: 50%, $end-color: $red) {\n  background-image: linear-gradient($start-color, $mid-color $color-stop, $end-color);\n  background-repeat: no-repeat;\n}\n@mixin gradient-radial($inner-color: $gray-700, $outer-color: $gray-800) {\n  background-image: radial-gradient(circle, $inner-color, $outer-color);\n  background-repeat: no-repeat;\n}\n@mixin gradient-striped($color: rgba($white, .15), $angle: 45deg) {\n  background-image: linear-gradient($angle, $color 25%, transparent 25%, transparent 50%, $color 50%, $color 75%, transparent 75%, transparent);\n}\n", ".chat-segment-get {\r\n\t.chat-message {\r\n\t\tbackground: $msgr-get-background;\t\t\r\n\t}\r\n}\r\n\r\n.chat-segment-sent {\r\n\t.chat-message {\r\n\t\tbackground: $msgr-sent-background;\r\n\t}\r\n}\r\n", "/* transparent modal */\r\n.modal-transparent {\r\n\t.modal-content {\r\n\t\tbox-shadow: 0 1px 15px 1px rgba($primary-900, 0.3);\r\n\t}\r\n\t.modal-content {\r\n\t\tbackground: rgba(desaturate(darken($primary-800, 25%), 20%), 0.85); \r\n\t}\r\n}\r\n", ".panel {\r\n\r\n\tbackground-color: $white;\r\n\r\n\t//experimental ...\r\n\tborder-bottom: 1px solid #e0e0e0;\r\n\r\n\t/* panel fullscreen */\r\n\t&.panel-fullscreen {\r\n\r\n\t\t/* make panel header bigger */\r\n\t\t.panel-hdr {\r\n\t\t\tbox-shadow: 0 0.125rem 0.125rem -0.0625rem  rgba(darken($primary-800, 10%), (10/100));\t\r\n\t\t}\r\n\r\n\t}\r\n\r\n\t/* panel locked */\r\n\t&.panel-locked:not(.panel-fullscreen) {\r\n\r\n\t\t.panel-hdr {\r\n\t\t\t&:active {\r\n\r\n\t\t\t\th2:before {\r\n\t\t\t\t\tcolor: $danger-500;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t}\r\n\r\n}\r\n\r\n/* panel tag can be used globally */\r\n.panel-tag {\r\n\tbackground: #eef7fd;\r\n}\r\n\r\n/* panel header */\r\n.panel-hdr {\r\n\tbackground: $panel-hdr-background;\r\n}\r\n\r\n/* panel tap highlight */\r\n.panel-sortable:not(.panel-locked) {\r\n\t.panel-hdr {\r\n\t\t&:active {\r\n\t\t\tborder-top-color: rgba($primary-300, 0.7);\r\n\t\t\tborder-left-color: rgba($primary-500, 0.7);\r\n\t\t\tborder-right-color: rgba($primary-500, 0.7);\r\n\r\n\t\t\t& + .panel-container {\r\n\r\n\t\t\t\tborder-color: transparent rgba($primary-500, 0.7) rgba($primary-600, 0.7);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/*.panel-sortable .panel-hdr:active,\r\n.panel-sortable .panel-hdr:active + .panel-container {\r\n\t@include transition-border(0.4s, ease-out);\r\n}*/\r\n\r\n.panel-sortable.panel-locked {\r\n\t.panel-hdr {\r\n\t\t&:active {\r\n\t\t\tborder-top-color: $danger-300;\r\n\t\t\tborder-left-color: $danger;\r\n\t\t\tborder-right-color: $danger;\r\n\r\n\t\t\t& + .panel-container {\r\n\t\t\t\tborder-color: transparent $danger $danger;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/* panel toolbar (sits inside panel header) */\r\n.panel-toolbar {\r\n\r\n\t.btn-panel {\r\n\r\n\t\t/* add default colors for action buttons */\r\n\t\t&[data-action=\"panel-collapse\"],\r\n\t\t&.js-panel-collapse {\r\n\t\t\tbackground: $success-500;\r\n\t\t}\r\n\r\n\t\t&[data-action=\"panel-fullscreen\"],\r\n\t\t&.js-panel-fullscreen {\r\n\t\t\tbackground: $warning-500;\r\n\t\t}\r\n\r\n\t\t&[data-action=\"panel-close\"],\r\n\t\t&.js-panel-close {\r\n\t\t\tbackground: $danger-500;\r\n\t\t}\r\n\r\n\t}\r\n\r\n}\r\n\r\n/* placeholder */\r\n.panel-placeholder {\r\n\tbackground-color: $panel-placeholder-color;\r\n\r\n\t&:before {\r\n\t\tbackground: $panel-placeholder-color;\r\n\t}\r\n}\r\n\r\n.mod-panel-clean {\r\n\r\n\t.panel-hdr {\r\n\t\tbackground: $white;\r\n\t\tbackground-image: linear-gradient(to bottom,#f7f7f7, $white);\r\n\t\t\r\n\t}\r\n\r\n}\t\r\n\r\n@media only screen and ( max-width: 420px ){\r\n\t/* making mobile spacing a little narrow */\r\n\t.panel {\r\n\t\t.panel-hdr {\r\n\t\t\tcolor: #060606;\r\n\t\t}\r\n\r\n\t}\r\n}", ".popover {\r\n\t.arrow {\r\n\t\tborder-color: $popover-arrow-outer-color;\r\n\t}\r\n}\r\n\r\n", "%ball{\r\n  background:$primary-500;\r\n  color:$white !important;\r\n \r\n    &:hover {\r\n      background: $primary-700;\r\n    }\r\n}\r\n\r\n.menu-item,\r\nlabel.menu-open-button {\r\n  @extend %ball;\r\n}\r\n\r\n.app-shortcut-icon {\r\n  background: #ecf0f1;\r\n  color: #ecf0f1;\r\n}\r\n\r\n.menu-open:checked+.menu-open-button{\r\n  background: $fusion-500;\r\n}\r\n", "/* nav tabs panel */\r\n.nav-tabs-clean {\r\n\r\n\t.nav-item {\r\n\r\n\t\t.nav-link {\r\n\r\n\t\t\t&.active {\r\n\t\t\t\tborder-bottom: 1px solid $primary-500;\r\n\t\t\t\tcolor: $primary-500;\r\n\t\t\t}\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\tcolor: $primary-500;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.nav-tabs .nav-link.active, \r\n.nav-tabs .nav-item.show .nav-link {\r\n\tcolor: $primary-500;\r\n}", "// Embedded icons from Open Iconic.\r\n// Released under MIT and copyright 2014 Waybury.\r\n// https://useiconic.com/open\r\n\r\n\r\n// Checkboxes and radios\r\n//\r\n// Base class takes care of all the key behavioral aspects.\r\n\r\n\r\n.custom-control-input {\r\n\r\n  &:checked ~ .custom-control-label::before {\r\n    color: $custom-control-indicator-checked-color;\r\n    border-color: $custom-control-indicator-checked-border-color;\r\n    @include gradient-bg($custom-control-indicator-checked-bg);\r\n  }\r\n\r\n  &:focus:not(:checked) ~ .custom-control-label::before {\r\n    border-color: $custom-control-indicator-focus-border-color;\r\n  }\r\n\r\n  &:not(:disabled):active ~ .custom-control-label::before {\r\n    color: $custom-control-indicator-active-color;\r\n    background-color: $custom-control-indicator-active-bg;\r\n    border-color: $custom-control-indicator-active-border-color;\r\n  }\r\n\r\n  // Use [disabled] and :disabled to work around https://github.com/twbs/bootstrap/issues/28247\r\n  &[disabled],\r\n  &:disabled {\r\n    ~ .custom-control-label {\r\n      color: $custom-control-label-disabled-color;\r\n\r\n      &::before {\r\n        background-color: $custom-control-indicator-disabled-bg;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// Custom control indicators\r\n//\r\n// Build the custom controls out of pseudo-elements.\r\n\r\n.custom-control-label {\r\n  color: $custom-control-label-color;\r\n\r\n  // Background-color and (when enabled) gradient\r\n  &::before {\r\n    background-color: $custom-control-indicator-bg;\r\n    border: $custom-control-indicator-border-color solid $custom-control-indicator-border-width;\r\n  }\r\n}\r\n\r\n\r\n// Checkboxes\r\n//\r\n// Tweak just a few things for checkboxes.\r\n\r\n.custom-checkbox {\r\n\r\n  .custom-control-input:indeterminate ~ .custom-control-label {\r\n    &::before {\r\n      border-color: $custom-checkbox-indicator-indeterminate-border-color;\r\n      @include gradient-bg($custom-checkbox-indicator-indeterminate-bg);\r\n    }\r\n    &::after {\r\n      background-image: escape-svg($custom-checkbox-indicator-icon-indeterminate);\r\n    }\r\n  }\r\n\r\n  .custom-control-input:disabled {\r\n    &:checked ~ .custom-control-label::before {\r\n      background-color: $custom-control-indicator-checked-disabled-bg;\r\n    }\r\n    &:indeterminate ~ .custom-control-label::before {\r\n      background-color: $custom-control-indicator-checked-disabled-bg;\r\n    }\r\n  }\r\n}\r\n\r\n// Radios\r\n//\r\n// Tweak just a few things for radios.\r\n\r\n.custom-radio {\r\n  .custom-control-input:disabled {\r\n    &:checked ~ .custom-control-label::before {\r\n      background-color: $custom-control-indicator-checked-disabled-bg;\r\n    }\r\n  }\r\n}\r\n\r\n\r\n// switches\r\n//\r\n// Tweak a few things for switches\r\n\r\n.custom-switch {\r\n\r\n  .custom-control-label {\r\n\r\n    &::after {\r\n      background-color: $custom-control-indicator-border-color;\r\n    }\r\n  }\r\n\r\n  .custom-control-input:checked ~ .custom-control-label {\r\n    &::after {\r\n      background-color: $custom-control-indicator-bg;\r\n    }\r\n  }\r\n\r\n  .custom-control-input:disabled {\r\n    &:checked ~ .custom-control-label::before {\r\n      background-color: $custom-control-indicator-checked-disabled-bg;\r\n    }\r\n  }\r\n}\r\n\r\n\r\n// Select\r\n//\r\n// Replaces the browser default select with a custom one, mostly pulled from\r\n// https://primer.github.io/.\r\n//\r\n\r\n.custom-select {\r\n  color: $custom-select-color;\r\n  background: $custom-select-bg $custom-select-background;\r\n  border: $custom-select-border-width solid $custom-select-border-color;\r\n\r\n\r\n  &:focus {\r\n    border-color: $custom-select-focus-border-color;\r\n\r\n    &::-ms-value {\r\n      // For visual consistency with other platforms/browsers,\r\n      // suppress the default white text on blue background highlight given to\r\n      // the selected option text when the (still closed) <select> receives focus\r\n      // in IE and (under certain conditions) Edge.\r\n      // See https://github.com/twbs/bootstrap/issues/19398.\r\n      color: $input-color;\r\n      background-color: $input-bg;\r\n    }\r\n  }\r\n\r\n  &:disabled {\r\n    color: $custom-select-disabled-color;\r\n    background-color: $custom-select-disabled-bg;\r\n  }\r\n}\r\n\r\n\r\n// File\r\n//\r\n// Custom file input.\r\n\r\n.custom-file-input {\r\n  &:focus ~ .custom-file-label {\r\n    border-color: $custom-file-focus-border-color;\r\n  }\r\n\r\n  // Use [disabled] and :disabled to work around https://github.com/twbs/bootstrap/issues/28247\r\n  &[disabled] ~ .custom-file-label,\r\n  &:disabled ~ .custom-file-label {\r\n    background-color: $custom-file-disabled-bg;\r\n  }\r\n\r\n  ~ .custom-file-label[data-browse]::after {\r\n    content: attr(data-browse);\r\n  }\r\n}\r\n\r\n.custom-file-label {\r\n  color: $custom-file-color;\r\n  background-color: $custom-file-bg;\r\n  border: $custom-file-border-width solid $custom-file-border-color;\r\n\r\n  &::after {\r\n    color: $custom-file-button-color;\r\n    @include gradient-bg($custom-file-button-bg);\r\n  }\r\n}\r\n\r\n// Range\r\n//\r\n// Style range inputs the same across browsers. Vendor-specific rules for pseudo\r\n// elements cannot be mixed. As such, there are no shared styles for focus or\r\n// active states on prefixed selectors.\r\n\r\n.custom-range {\r\n  background-color: transparent;\r\n\r\n  &::-webkit-slider-thumb {\r\n    @include gradient-bg($custom-range-thumb-bg);\r\n    border: $custom-range-thumb-border;\r\n\r\n    &:active {\r\n      @include gradient-bg($custom-range-thumb-active-bg);\r\n    }\r\n  }\r\n\r\n  &::-webkit-slider-runnable-track {\r\n    background-color: $custom-range-track-bg;\r\n  }\r\n\r\n  &::-moz-range-thumb {\r\n    @include gradient-bg($custom-range-thumb-bg);\r\n    border: $custom-range-thumb-border;\r\n\r\n    &:active {\r\n      @include gradient-bg($custom-range-thumb-active-bg);\r\n    }\r\n  }\r\n\r\n  &::-moz-range-track {\r\n    background-color: $custom-range-track-bg;\r\n  }\r\n\r\n  &::-ms-thumb {\r\n    @include gradient-bg($custom-range-thumb-bg);\r\n    border: $custom-range-thumb-border;\r\n\r\n    &:active {\r\n      @include gradient-bg($custom-range-thumb-active-bg);\r\n    }\r\n  }\r\n\r\n\r\n  &::-ms-fill-lower {\r\n    background-color: $custom-range-track-bg;\r\n  }\r\n\r\n  &::-ms-fill-upper {\r\n    background-color: $custom-range-track-bg;\r\n  }\r\n\r\n  &:disabled {\r\n    &::-webkit-slider-thumb {\r\n      background-color: $custom-range-thumb-disabled-bg;\r\n    }\r\n\r\n\r\n    &::-moz-range-thumb {\r\n      background-color: $custom-range-thumb-disabled-bg;\r\n    }\r\n\r\n\r\n    &::-ms-thumb {\r\n      background-color: $custom-range-thumb-disabled-bg;\r\n    }\r\n  }\r\n}", "// Bootstrap functions\n//\n// Utility mixins and functions for evaluating source code across our variables, maps, and mixins.\n\n// Ascending\n// Used to evaluate Sass maps like our grid breakpoints.\n@mixin _assert-ascending($map, $map-name) {\n  $prev-key: null;\n  $prev-num: null;\n  @each $key, $num in $map {\n    @if $prev-num == null or unit($num) == \"%\" or unit($prev-num) == \"%\" {\n      // Do nothing\n    } @else if not comparable($prev-num, $num) {\n      @warn \"Potentially invalid value for #{$map-name}: This map must be in ascending order, but key '#{$key}' has value #{$num} whose unit makes it incomparable to #{$prev-num}, the value of the previous key '#{$prev-key}' !\";\n    } @else if $prev-num >= $num {\n      @warn \"Invalid value for #{$map-name}: This map must be in ascending order, but key '#{$key}' has value #{$num} which isn't greater than #{$prev-num}, the value of the previous key '#{$prev-key}' !\";\n    }\n    $prev-key: $key;\n    $prev-num: $num;\n  }\n}\n\n// Starts at zero\n// Used to ensure the min-width of the lowest breakpoint starts at 0.\n@mixin _assert-starts-at-zero($map, $map-name: \"$grid-breakpoints\") {\n  @if length($map) > 0 {\n    $values: map-values($map);\n    $first-value: nth($values, 1);\n    @if $first-value != 0 {\n      @warn \"First breakpoint in #{$map-name} must start at 0, but starts at #{$first-value}.\";\n    }\n  }\n}\n\n// Replace `$search` with `$replace` in `$string`\n// Used on our SVG icon backgrounds for custom forms.\n//\n// <AUTHOR> Giraudel\n// @param {String} $string - Initial string\n// @param {String} $search - Substring to replace\n// @param {String} $replace ('') - New value\n// @return {String} - Updated string\n@function str-replace($string, $search, $replace: \"\") {\n  $index: str-index($string, $search);\n\n  @if $index {\n    @return str-slice($string, 1, $index - 1) + $replace + str-replace(str-slice($string, $index + str-length($search)), $search, $replace);\n  }\n\n  @return $string;\n}\n\n// See https://codepen.io/kevinweber/pen/dXWoRw\n@function escape-svg($string) {\n  @if str-index($string, \"data:image/svg+xml\") {\n    @each $char, $encoded in $escaped-characters {\n      // Do not escape the url brackets\n      @if str-index($string, \"url(\") == 1 {\n        $string: url(\"#{str-replace(str-slice($string, 6, -3), $char, $encoded)}\");\n      } @else {\n        $string: str-replace($string, $char, $encoded);\n      }\n    }\n  }\n\n  @return $string;\n}\n\n// Color contrast\n@function color-yiq($color, $dark: $yiq-text-dark, $light: $yiq-text-light) {\n  $r: red($color);\n  $g: green($color);\n  $b: blue($color);\n\n  $yiq: (($r * 299) + ($g * 587) + ($b * 114)) / 1000;\n\n  @if ($yiq >= $yiq-contrasted-threshold) {\n    @return $dark;\n  } @else {\n    @return $light;\n  }\n}\n\n// Retrieve color Sass maps\n@function color($key: \"blue\") {\n  @return map-get($colors, $key);\n}\n\n@function theme-color($key: \"primary\") {\n  @return map-get($theme-colors, $key);\n}\n\n@function gray($key: \"100\") {\n  @return map-get($grays, $key);\n}\n\n// Request a theme color level\n@function theme-color-level($color-name: \"primary\", $level: 0) {\n  $color: theme-color($color-name);\n  $color-base: if($level > 0, $black, $white);\n  $level: abs($level);\n\n  @return mix($color-base, $color, $level * $theme-color-interval);\n}\n\n// Return valid calc\n@function add($value1, $value2, $return-calc: true) {\n  @if $value1 == null {\n    @return $value2;\n  }\n\n  @if $value2 == null {\n    @return $value1;\n  }\n\n  @if type-of($value1) == number and type-of($value2) == number and comparable($value1, $value2) {\n    @return $value1 + $value2;\n  }\n\n  @return if($return-calc == true, calc(#{$value1} + #{$value2}), $value1 + unquote(\" + \") + $value2);\n}\n\n@function subtract($value1, $value2, $return-calc: true) {\n  @if $value1 == null and $value2 == null {\n    @return null;\n  }\n\n  @if $value1 == null {\n    @return -$value2;\n  }\n\n  @if $value2 == null {\n    @return $value1;\n  }\n\n  @if type-of($value1) == number and type-of($value2) == number and comparable($value1, $value2) {\n    @return $value1 - $value2;\n  }\n\n  @return if($return-calc == true, calc(#{$value1} - #{$value2}), $value1 + unquote(\" - \") + $value2);\n}\n", "\r\n\r\n.page-link {\r\n  color: $primary-500;\r\n  background-color: $pagination-bg;\r\n  border: $pagination-border-width solid $pagination-border-color;\r\n\r\n  &:hover {\r\n    color: $pagination-hover-color;\r\n    background-color: $pagination-hover-bg;\r\n    border-color: $pagination-hover-border-color;\r\n  }\r\n\r\n  /*&:focus {\r\n    outline: $pagination-focus-outline;\r\n  }*/\r\n}\r\n\r\n.page-item {\r\n  &.active .page-link {\r\n    color: $pagination-active-color;\r\n    background-color: $primary-500;\r\n  }\r\n\r\n  &.disabled .page-link {\r\n    color: $pagination-disabled-color;\r\n    background-color: $pagination-disabled-bg;\r\n  }\r\n}\r\n\r\n.pagination {\r\n\r\n\t.page-item:first-child:not(.active),\r\n\t.page-item:last-child:not(.active),\r\n\t.page-item.disabled {\r\n\t\t.page-link {\r\n\t\t\tbackground: lighten($primary-50, 10%);\r\n\t\t}\r\n\t}\r\n\r\n\t.page-link {\r\n\t\t&:hover {\r\n\t\t\tbackground-color: $primary-500 !important;\r\n\t\t\tcolor: $white;\r\n\t\t}\r\n\t}\r\n}", ".list-group-item {\r\n  border: $list-group-border-width solid rgba(var(--theme-rgb-primary), 0.15);\r\n  &.active {\r\n    background-color: $primary-500;\r\n    border-color: $primary-500;\r\n  }\r\n\r\n}", "/* backgrounds */\r\n.bg-white { background-color: $white; color: $base-text-color}\r\n.bg-faded {\tbackground-color: $frame-border-color }\r\n.bg-offwhite-fade {\t@include gradient-img($white, lighten($color-fusion, 66%)) }\r\n.bg-subtlelight { background-color: lighten($color-primary, 44%) }\r\n.bg-subtlelight-fade { @include gradient-img($white, lighten($color-primary, 44%))\t}\r\n.bg-highlight { background-color: lighten($warning-50, 9%)}\r\n\r\n.bg-gray-50  { background-color: $gray-50;  }\r\n.bg-gray-100 { background-color: $gray-100; }\r\n.bg-gray-200 { background-color: $gray-200; }\r\n.bg-gray-300 { background-color: $gray-300; }\r\n.bg-gray-400 { background-color: $gray-400; }\r\n.bg-gray-500 { background-color: $gray-500; }\r\n.bg-gray-600 { background-color: $gray-600; }\r\n.bg-gray-700 { background-color: $gray-700; }\r\n.bg-gray-800 { background-color: $gray-800; }\r\n.bg-gray-900 { background-color: $gray-900; }\r\n\r\n/* borders */\r\n.border-faded { border: 1px solid rgba($fusion-900, 0.07) }\r\n\r\n\r\n/* hover any bg */\r\n/* inherits the parent background on hover */\r\n.hover-bg {\r\n\tbackground: $white;\r\n}\r\n\r\n/* states */\r\n.state-selected {\r\n\tbackground: lighten($info-500, 41%) !important;\r\n}\r\n\r\n/* demo window */\r\n.demo-window {\r\n\tbox-shadow: 0 2px 10px rgba(0,0,0,0.12);\r\n\r\n\t&:before {\r\n\t\tbackground: #e5e5e5;\r\n\t}\r\n\r\n\t&:after,\r\n\t.demo-window-content:before,\r\n\t.demo-window-content:after {\r\n\t\tbackground: #ccc;\r\n\t}\r\n\r\n}", ".bg-trans-gradient {\r\n\tbackground: -webkit-linear-gradient(250deg, desaturate($info-500, 25%), desaturate($primary-500, 10%));\r\n\tbackground: linear-gradient(250deg, desaturate($info-500, 25%), desaturate($primary-500, 10%));\r\n}\r\n\r\n.bg-brand-gradient {\r\n\t@extend %nav-bg;\r\n}\r\n\r\n.notes {\r\n\tbackground: #f9f4b5;\r\n}\r\n\r\n/* disclaimer class */\r\n.disclaimer {\r\n\tcolor: #a2a2a2;\r\n}\r\n\r\n\r\n/* online status */\r\n.status {\r\n\tposition: relative;\r\n\r\n\t&:before {\r\n\t\tbackground: $fusion-500;\r\n\t\tborder: 2px solid #fff;\r\n\t}\r\n\r\n\t&.status-success:before {\r\n\t\tbackground: $success-500;\r\n\t}\r\n\r\n\t&.status-danger:before {\r\n\t\tbackground: $danger-500;\r\n\t}\r\n\r\n\t&.status-warning:before {\r\n\t\tbackground: $warning-500;\r\n\t}\r\n}\r\n\r\n\r\n/* display frame */\r\n.frame-heading {\r\n\tcolor: lighten($fusion-50, 7%);\r\n}\r\n.frame-wrap {\r\n\tbackground: white;\r\n}\r\n\r\n/* time stamp */\r\n.time-stamp {\r\n\tcolor: $fusion-200;\r\n}\r\n\r\n/* data-hasmore */\r\n[data-hasmore] {\r\n\tcolor: $white;\r\n\t&:before {\r\n\t\tbackground: rgba($black, 0.4);\r\n\t}\r\n}\r\n\r\n/* code */\r\ncode {\r\n\tbackground: lighten(desaturate($primary-800, 60%), 56%);\r\n}", "/* select background */\r\n::selection {\r\n  background: $color-fusion;\r\n  color: $white;\r\n}\r\n::-moz-selection {\r\n  background: $color-fusion;\r\n  color: $white;\r\n}", "\r\n@media only screen and ( max-width: $mobile-breakpoint-size ){\r\n\r\n\r\n\t.page-wrapper {\r\n\t\tbackground: $white;\r\n\r\n\t\t.page-header {\r\n\t\t\tborder-bottom: 1px solid rgba($black,0.09);\r\n\t\t}\r\n\r\n\r\n\r\n\t\t.page-content {\r\n\t\t\tcolor: #222;\r\n\r\n\t\t\t.subheader {\r\n\r\n\t\t\t\t.subheader-title {\r\n\t\t\t\t\t//color: #22282d;\r\n\r\n\t\t\t\t\tsmall {\r\n\t\t\t\t\t\t//color: #181c21;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.p-g {\r\n\t\t\t\tpadding: map-get($grid-gutter-widths, xs);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.page-footer {\r\n\t\t\tborder-top: 1px solid rgba($black,0.09);\r\n\t\t}\r\n\r\n\t}\r\n\r\n\t/* Off canvas */\r\n\t&.nav-mobile-slide-out {\r\n\r\n\t\t.page-wrapper {\r\n\r\n\t\t\t.page-content {\r\n\t\t\t\tbackground: $page-bg;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t/* mobile nav show & hide button */\r\n\t/* general */\r\n\t&.mobile-nav-on {\r\n\r\n\t\t.page-sidebar {\r\n\t\t\tborder-right:1px solid rgba(0,0,0,0.03);\r\n\t\t\t@include box-shadow( 0 3px 35px 3px rgba(0,0,0,0.52) );\r\n\t\t}\r\n\r\n\t\t.page-content-overlay {\r\n\t\t\tbackground: rgba($black,0.09);\r\n\t\t}\t\t\r\n\r\n\t}\r\n\r\n}\r\n\r\n@media only screen and ( max-width: map-get($grid-breakpoints, sm) ){\r\n\r\n\t/* here we turn on mobile font for smaller screens */\r\n\t/*body {\r\n\t\tfont-family: $mobile-page-font !important;\r\n\t}*/\r\n\r\n\t/* mobile nav search */\r\n\t.mobile-search-on:not(.mobile-nav-on) {\r\n\r\n\t\t.search {\r\n\r\n\t\t\t.app-forms {\r\n\r\n\t\t\t\t#search-field {\r\n\t\t\t\t\tbackground: $white;\r\n\r\n\r\n\t\t\t\t\t&:focus {\r\n\t\t\t\t\t\tborder-color: $primary-500;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\t\r\n\r\n\t}\r\n\r\n}\r\n", "/* text area */\r\n[contenteditable=\"true\"] {\r\n\r\n\t&:empty:not(:focus):before{\r\n\t\tcontent: attr(data-placeholder);\r\n\t\tcolor: $fusion-50;\r\n\t}\r\n\r\n\t&::selection {\r\n\t\tbackground: rgba(0, 132, 255, .2);\r\n\t\tcolor: $black;\r\n\t}\r\n\r\n\t&::-moz-selection {\r\n\t\tbackground: rgba(0, 132, 255, .2);\r\n\t\tcolor: $black;\r\n\t}\r\n}\r\n/* add background to focused inpur prepend and append */\r\n.form-control:focus ~ .input-group-prepend {\r\n\tbackground: $primary-500;\r\n}\r\n.has-length {\r\n\t.input-group-text {\r\n\t\tborder-color: $primary-500;\r\n\t\t& + .input-group-text {\r\n\t\t\tborder-left: 1px solid rgba($black, 0.1);\r\n\t\t}\r\n\t}\r\n\t.input-group-text:not([class^=\"bg-\"]):not([class*=\" bg-\"]) {\r\n\t\tbackground: $primary-500;\r\n\t\tcolor: $white !important;\r\n\t}\r\n\r\n}\r\n\r\n/* help block and validation feedback texts*/\r\n.help-block {\r\n\tcolor: $fusion-50;\r\n}\r\n\r\n.form-control:focus {\r\n\tcolor: $input-focus-color;\r\n\tbackground-color: $input-focus-bg;\r\n\tborder-color: $input-focus-border-color;\r\n}", ".settings-panel {\r\n\r\n\r\n\th5 {\r\n\t\tcolor: $fusion-500;\r\n\r\n\t}\r\n\r\n\t.list {\r\n\t\tcolor:darken($white, 60%);\r\n\r\n\t\t&:hover{\r\n\t\t\tcolor:darken($white, 80%);\r\n\t\t\tbackground:rgba( $white, .7 );\r\n\t\t}\r\n\r\n\t}\r\n\r\n\t.expanded {\r\n\r\n\t\t&:before {\r\n\t\t\tborder-bottom-color: $fusion-400;\r\n\t\t}\r\n \r\n\t}\r\n}\r\n\r\n\r\n@include set-settings(header-function-fixed);\r\n@include set-settings(nav-function-fixed);\r\n@include set-settings(nav-function-minify);\r\n@include set-settings(nav-function-hidden);\r\n@include set-settings(nav-function-top);\r\n@include set-settings(footer-function-fixed);\r\n@include set-settings(nav-mobile-push);\r\n@include set-settings(nav-mobile-no-overlay);\r\n@include set-settings(nav-mobile-slide-out);\r\n@include set-settings(mod-main-boxed);\r\n@include set-settings(mod-fixed-bg);\r\n@include set-settings(mod-clean-page-bg);\r\n@include set-settings(mod-pace-custom);\r\n@include set-settings(mod-bigger-font);\r\n@include set-settings(mod-high-contrast);\r\n@include set-settings(mod-color-blind);\r\n@include set-settings(mod-hide-nav-icons);\r\n@include set-settings(mod-hide-info-card);\r\n@include set-settings(mod-lean-subheader);\r\n@include set-settings(mod-disable-animation);\r\n@include set-settings(mod-nav-link);\r\n@include set-settings(mod-nav-dark);\r\n//@include set-settings(mod-app-rtl);\r\n@include set-settings(mod-panel-icon);", ".nav-mobile-slide-out {\r\n\t#nmp,\r\n\t#nmno {\r\n\t\t@extend %not-compatible;\r\n\t}\r\n}\r\n\r\n.nav-function-top,\r\n.nav-function-minify,\r\n.mod-hide-nav-icons {\r\n\t#mnl {\r\n\t\t@extend %not-compatible;\r\n\t}\r\n}\r\n\r\n@include media-breakpoint-up($mobile-breakpoint-size) {\r\n\t.nav-function-top {\r\n\t\t#nfh {\r\n\t\t\t@extend %not-compatible;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n@media only screen and (max-width: $mobile-breakpoint-size) {\r\n\r\n\t.mobile-view-activated {\r\n\t\t#nff,\r\n\t\t#nfm,\r\n\t\t#nfh,\r\n\t\t#nft,\r\n\t\t#mmb {\r\n\t\t      position:relative;\r\n\t\t    \r\n\t\t    .onoffswitch-title {\r\n\t\t      color: $settings-incompat-title !important; \r\n\t\t    }\r\n\t\t    .onoffswitch-title-desc {\r\n\t\t      color: $settings-incompat-desc !important;\r\n\t\t    }\r\n\t\t    &:after {\r\n\t\t      background: $settings-incompat-bg;\r\n\t\t      border: 1px solid $settings-incompat-border;\r\n\t\t      color:$fusion-900;\r\n\t\t    }\r\n\t\t}\r\n\t}\r\n}\r\n", "/* Hierarchical Navigation */\r\n\r\n.mod-nav-link:not(.nav-function-top):not(.nav-function-minify):not(.mod-hide-nav-icons) {\r\n\tul.nav-menu:not(.nav-menu-compact) {\r\n\t\t> li {\r\n\t\t\t> ul {\r\n\t\t\t\t&:before {\r\n\t\t\t\t\tborder-left: 1px solid darken($nav-icon-color, 15%);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t/* addressing all second, third children */\r\n\t\t\t\t> li {\r\n\t\t\t\t\ta {\r\n\t\t\t\t\t\t&:after {\r\n\t\t\t\t\t\t\tbackground-color: $nav-icon-color;\t\t\t\t\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}", "// primary\r\n@include paint($primary-50, bg-primary-50);\r\n@include paint($primary-100, bg-primary-100);\r\n@include paint($primary-200, bg-primary-200);\r\n@include paint($primary-300, bg-primary-300);\r\n@include paint($primary-400, bg-primary-400);\r\n@include paint($primary-500, bg-primary-500);\r\n@include paint($primary-600, bg-primary-600);\r\n@include paint($primary-700, bg-primary-700);\r\n@include paint($primary-800, bg-primary-800);\r\n@include paint($primary-900, bg-primary-900);\r\n\r\n@include brush($primary-50, color-primary-50);\r\n@include brush($primary-100, color-primary-100);\r\n@include brush($primary-200, color-primary-200);\r\n@include brush($primary-300, color-primary-300);\r\n@include brush($primary-400, color-primary-400);\r\n@include brush($primary-500, color-primary-500);\r\n@include brush($primary-600, color-primary-600);\r\n@include brush($primary-700, color-primary-700);\r\n@include brush($primary-800, color-primary-800);\r\n@include brush($primary-900, color-primary-900);\r\n\r\n// success\r\n@include paint($success-50, bg-success-50);\r\n@include paint($success-100, bg-success-100);\r\n@include paint($success-200, bg-success-200);\r\n@include paint($success-300, bg-success-300);\r\n@include paint($success-400, bg-success-400);\r\n@include paint($success-500, bg-success-500);\r\n@include paint($success-600, bg-success-600);\r\n@include paint($success-700, bg-success-700);\r\n@include paint($success-800, bg-success-800);\r\n@include paint($success-900, bg-success-900);\r\n\r\n@include brush($success-50, color-success-50);\r\n@include brush($success-100, color-success-100);\r\n@include brush($success-200, color-success-200);\r\n@include brush($success-300, color-success-300);\r\n@include brush($success-400, color-success-400);\r\n@include brush($success-500, color-success-500);\r\n@include brush($success-600, color-success-600);\r\n@include brush($success-700, color-success-700);\r\n@include brush($success-800, color-success-800);\r\n@include brush($success-900, color-success-900);\r\n\r\n// info\r\n@include paint($info-50, bg-info-50);\r\n@include paint($info-100, bg-info-100);\r\n@include paint($info-200, bg-info-200);\r\n@include paint($info-300, bg-info-300);\r\n@include paint($info-400, bg-info-400);\r\n@include paint($info-500, bg-info-500);\r\n@include paint($info-600, bg-info-600);\r\n@include paint($info-700, bg-info-700);\r\n@include paint($info-800, bg-info-800);\r\n@include paint($info-900, bg-info-900);\r\n\r\n@include brush($info-50, color-info-50);\r\n@include brush($info-100, color-info-100);\r\n@include brush($info-200, color-info-200);\r\n@include brush($info-300, color-info-300);\r\n@include brush($info-400, color-info-400);\r\n@include brush($info-500, color-info-500);\r\n@include brush($info-600, color-info-600);\r\n@include brush($info-700, color-info-700);\r\n@include brush($info-800, color-info-800);\r\n@include brush($info-900, color-info-900);\r\n\r\n// warning\r\n@include paint($warning-50, bg-warning-50);\r\n@include paint($warning-100, bg-warning-100);\r\n@include paint($warning-200, bg-warning-200);\r\n@include paint($warning-300, bg-warning-300);\r\n@include paint($warning-400, bg-warning-400);\r\n@include paint($warning-500, bg-warning-500);\r\n@include paint($warning-600, bg-warning-600);\r\n@include paint($warning-700, bg-warning-700);\r\n@include paint($warning-800, bg-warning-800);\r\n@include paint($warning-900, bg-warning-900);\r\n\r\n@include brush($warning-50, color-warning-50);\r\n@include brush($warning-100, color-warning-100);\r\n@include brush($warning-200, color-warning-200);\r\n@include brush($warning-300, color-warning-300);\r\n@include brush($warning-400, color-warning-400);\r\n@include brush($warning-500, color-warning-500);\r\n@include brush($warning-600, color-warning-600);\r\n@include brush($warning-700, color-warning-700);\r\n@include brush($warning-800, color-warning-800);\r\n@include brush($warning-900, color-warning-900);\r\n\r\n// danger\r\n@include paint($danger-50, bg-danger-50);\r\n@include paint($danger-100, bg-danger-100);\r\n@include paint($danger-200, bg-danger-200);\r\n@include paint($danger-300, bg-danger-300);\r\n@include paint($danger-400, bg-danger-400);\r\n@include paint($danger-500, bg-danger-500);\r\n@include paint($danger-600, bg-danger-600);\r\n@include paint($danger-700, bg-danger-700);\r\n@include paint($danger-800, bg-danger-800);\r\n@include paint($danger-900, bg-danger-900);\r\n\r\n@include brush($danger-50, color-danger-50);\r\n@include brush($danger-100, color-danger-100);\r\n@include brush($danger-200, color-danger-200);\r\n@include brush($danger-300, color-danger-300);\r\n@include brush($danger-400, color-danger-400);\r\n@include brush($danger-500, color-danger-500);\r\n@include brush($danger-600, color-danger-600);\r\n@include brush($danger-700, color-danger-700);\r\n@include brush($danger-800, color-danger-800);\r\n@include brush($danger-900, color-danger-900);\r\n\r\n// fusion\r\n@include paint($fusion-50, bg-fusion-50);\r\n@include paint($fusion-100, bg-fusion-100);\r\n@include paint($fusion-200, bg-fusion-200);\r\n@include paint($fusion-300, bg-fusion-300);\r\n@include paint($fusion-400, bg-fusion-400);\r\n@include paint($fusion-500, bg-fusion-500);\r\n@include paint($fusion-600, bg-fusion-600);\r\n@include paint($fusion-700, bg-fusion-700);\r\n@include paint($fusion-800, bg-fusion-800);\r\n@include paint($fusion-900, bg-fusion-900);\r\n\r\n@include brush($fusion-50, color-fusion-50);\r\n@include brush($fusion-100, color-fusion-100);\r\n@include brush($fusion-200, color-fusion-200);\r\n@include brush($fusion-300, color-fusion-300);\r\n@include brush($fusion-400, color-fusion-400);\r\n@include brush($fusion-500, color-fusion-500);\r\n@include brush($fusion-600, color-fusion-600);\r\n@include brush($fusion-700, color-fusion-700);\r\n@include brush($fusion-800, color-fusion-800);\r\n@include brush($fusion-900, color-fusion-900);\r\n\r\n//white\r\n@include brush($white, color-white);\r\n@include brush(lighten($black, 13.5%), color-black);\r\n\r\n\r\n@include paint-gradient($primary-900, bg-primary-gradient);\r\n@include paint-gradient($danger-900, bg-danger-gradient);\r\n@include paint-gradient($info-900, bg-info-gradient);\r\n@include paint-gradient($warning-900, bg-warning-gradient);\r\n@include paint-gradient($success-900, bg-success-gradient);\r\n@include paint-gradient($fusion-900, bg-fusion-gradient);", "// Button variants\n//\n// Easily pump out default styles, as well as :hover, :focus, :active,\n// and disabled options for all buttons\n\n@mixin button-variant($background, $border, $hover-background: darken($background, 7.5%), $hover-border: darken($border, 10%), $active-background: darken($background, 10%), $active-border: darken($border, 12.5%)) {\n  color: color-yiq($background);\n  @include gradient-bg($background);\n  border-color: $border;\n  @include box-shadow($btn-box-shadow);\n\n  @include hover() {\n    color: color-yiq($hover-background);\n    @include gradient-bg($hover-background);\n    border-color: $hover-border;\n  }\n\n  &:focus,\n  &.focus {\n    color: color-yiq($hover-background);\n    @include gradient-bg($hover-background);\n    border-color: $hover-border;\n    @if $enable-shadows {\n      @include box-shadow($btn-box-shadow, 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5));\n    } @else {\n      // Avoid using mixin so we can pass custom focus shadow properly\n      box-shadow: 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5);\n    }\n  }\n\n  // Disabled comes first so active can properly restyle\n  &.disabled,\n  &:disabled {\n    color: color-yiq($background);\n    background-color: $background;\n    border-color: $border;\n    // Remove CSS gradients if they're enabled\n    @if $enable-gradients {\n      background-image: none;\n    }\n  }\n\n  &:not(:disabled):not(.disabled):active,\n  &:not(:disabled):not(.disabled).active,\n  .show > &.dropdown-toggle {\n    color: color-yiq($active-background);\n    background-color: $active-background;\n    @if $enable-gradients {\n      background-image: none; // Remove the gradient for the pressed/active state\n    }\n    border-color: $active-border;\n\n    &:focus {\n      @if $enable-shadows and $btn-active-box-shadow != none {\n        @include box-shadow($btn-active-box-shadow, 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5));\n      } @else {\n        // Avoid using mixin so we can pass custom focus shadow properly\n        box-shadow: 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5);\n      }\n    }\n  }\n}\n\n@mixin button-outline-variant($color, $color-hover: color-yiq($color), $active-background: $color, $active-border: $color) {\n  color: $color;\n  border-color: $color;\n\n  @include hover() {\n    color: $color-hover;\n    background-color: $active-background;\n    border-color: $active-border;\n  }\n\n  &:focus,\n  &.focus {\n    box-shadow: 0 0 0 $btn-focus-width rgba($color, .5);\n  }\n\n  &.disabled,\n  &:disabled {\n    color: $color;\n    background-color: transparent;\n  }\n\n  &:not(:disabled):not(.disabled):active,\n  &:not(:disabled):not(.disabled).active,\n  .show > &.dropdown-toggle {\n    color: color-yiq($active-background);\n    background-color: $active-background;\n    border-color: $active-border;\n\n    &:focus {\n      @if $enable-shadows and $btn-active-box-shadow != none {\n        @include box-shadow($btn-active-box-shadow, 0 0 0 $btn-focus-width rgba($color, .5));\n      } @else {\n        // Avoid using mixin so we can pass custom focus shadow properly\n        box-shadow: 0 0 0 $btn-focus-width rgba($color, .5);\n      }\n    }\n  }\n}\n\n// Button sizes\n@mixin button-size($padding-y, $padding-x, $font-size, $line-height, $border-radius) {\n  padding: $padding-y $padding-x;\n  @include font-size($font-size);\n  line-height: $line-height;\n  // Manually declare to provide an override to the browser default\n  @include border-radius($border-radius, 0);\n}\n", "\r\n@mixin bg-variant($parent, $color, $ignore-warning: false) {\r\n\t#{$parent} {\r\n\t\tbackground-color: $color !important;\r\n\t}\r\n\ta#{$parent},\r\n\tbutton#{$parent} {\r\n\t\t@include hover-focus() {\r\n\t\t\tbackground-color: darken($color, 10%) !important;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n@mixin bg-gradient-variant($parent, $color) {\r\n\t#{$parent} {\r\n\t\tbackground: $color linear-gradient(180deg, mix($body-bg, $color, 15%), $color) repeat-x !important;\r\n\t}\r\n}\r\n\r\n\r\n@mixin text-emphasis-variant($parent, $color, $ignore-warning: false) {\r\n\t#{$parent} {\r\n\t\tcolor: $color !important;\r\n\t}\r\n\t@if $emphasized-link-hover-darken-percentage != 0 {\r\n\t\ta#{$parent} {\r\n\t\t\t@include hover-focus() {\r\n\t\t\t\tcolor: darken($color, $emphasized-link-hover-darken-percentage) !important;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n\r\n//bootstrap button colors\r\n@each $color, $value in $theme-colors {\r\n\t.btn-#{$color} {\r\n\t\t@include button-variant($value, $value);\r\n\t}\r\n}\r\n\r\n@each $color, $value in $theme-colors {\r\n\t.btn-outline-#{$color} {\r\n\t\t@include button-outline-variant($value);\r\n\t}\r\n}\r\n\r\n//border\r\n@each $color, $value in $theme-colors {\r\n\t.border-#{$color} {\r\n\t\tborder-color: $value !important;\r\n\t}\r\n}\r\n\r\n// Typography\r\n@each $color, $value in $theme-colors {\r\n\t@include text-emphasis-variant(\".text-#{$color}\", $value);\r\n}\r\n\r\n//bg varient\r\n@each $color, $value in $theme-colors {\r\n\t@include bg-variant(\".bg-#{$color}\", $value);\r\n}\r\n\r\n@if $enable-gradients {\r\n\t@each $color, $value in $theme-colors {\r\n\t\t@include bg-gradient-variant(\".bg-gradient-#{$color}\", $value);\r\n\t}\r\n}\r\n\r\n//root colors\r\n:root {\r\n\t@each $theme-colors, $value in $theme-colors {\r\n\t\t--theme-#{$theme-colors}: #{$value};\r\n\t}\r\n\r\n\t@each $theme-colors-extended, $value in $theme-colors-extended {\r\n\t\t--theme-#{$theme-colors-extended}: #{$value};\r\n\t}\r\n}", "//chartist\r\n$ct-class-series: ct-series;\r\n$ct-class-point: ct-point;\r\n$ct-class-line: ct-line;\r\n$ct-class-bar: ct-bar;\r\n$ct-class-slice-donut: ct-slice-donut;\r\n$ct-class-slice-pie: ct-slice-pie;\r\n$ct-class-slice-donut-solid: ct-slice-donut-solid;\r\n$ct-class-area: ct-area;\r\n\r\n\r\n$ct-series-names: (a, b, c, d, e, f, g, h, i, j, k, l, m, n, o);\r\n$ct-series-colors: (\r\n  $primary-500, //red\r\n  $danger-500, //light red\r\n  $warning-500, //yellow\r\n  $info-500, //dark orange\r\n  $fusion-500, //ash\r\n  $success-500, //green\r\n  $info-500, //blue\r\n  $primary-900, //purple\r\n  $danger-100, //lighter red\r\n  $warning-200, //faded orange\r\n  $danger-900, //faded yellow\r\n  $fusion-300, //light gray\r\n  $success-300, //light green\r\n  $info-300, //light blue\r\n  $primary-300 //light purple\r\n);\r\n\r\n@mixin ct-chart-series-color($color) {\r\n  .#{$ct-class-point}, .#{$ct-class-line}, .#{$ct-class-bar}, .#{$ct-class-slice-donut} {\r\n    stroke: $color;\r\n  }\r\n  .#{$ct-class-slice-pie}, .#{$ct-class-slice-donut-solid}, .#{$ct-class-area} {\r\n    fill: $color;\r\n  }\r\n}\r\n\r\n\r\n@for $i from 0 to length($ct-series-names) {\r\n  .#{$ct-class-series}-#{nth($ct-series-names, $i + 1)} {\r\n    $color: nth($ct-series-colors, $i + 1);\r\n    @include ct-chart-series-color($color);\r\n  }\r\n}\r\n\r\n", "//select2\r\n.select2-container--default .select2-selection--single,\r\n.select2-container--default .select2-selection--multiple {\r\n\tborder-color: $input-border-color;\r\n}\r\n.select2-dropdown {\r\n\tborder-color: $input-border-color;\r\n}\r\n\r\n.select2-search--dropdown {\r\n\r\n\t&:before {\r\n\t\tcolor: $primary-500;\r\n\t}\r\n}\r\n\r\n.select2-results__message {\r\n\tcolor: $primary-500 !important;\r\n}\r\n\r\n.select2-container--open .select2-dropdown--above {\r\n\tborder-color: $input-focus-border-color;\r\n}\r\n\r\n.select2-container--open .select2-dropdown--below {\r\n\tborder-color: $input-focus-border-color;\r\n}\r\n\r\n\r\n.select2-container--default .select2-search--dropdown .select2-search__field {\r\n  color: $input-color;\r\n  background-color: $input-bg;\r\n  border-color: $input-border-color;\r\n  box-shadow: inset 0 .25rem .125rem 0 rgba(33, 37, 41, .025);\r\n\r\n  &:focus {\r\n  \tborder-color: darken($input-border-color, 10%);\r\n  }\r\n}\r\n\r\n.select2-container--default .select2-results__group {\r\n    padding: 0.5rem 0;\r\n    color: #8e8e8e;\r\n}\r\n\r\n.select2-container--default .select2-results__option[aria-selected=true] {\r\n\tbackground: $dropdown-link-active-bg;\r\n\tcolor: $dropdown-link-active-color;\r\n}\r\n\r\n.select2-container--default .select2-results__option--highlighted[aria-selected] {\r\n\tbackground-color: $primary-500;\r\n\tcolor: $white;\r\n}\r\n\r\n.select2-container--default.select2-container--open.select2-container--below .select2-selection--single, \r\n.select2-container--default.select2-container--open.select2-container--below .select2-selection--multiple,\r\n.select2-container--default.select2-container--open.select2-container--above .select2-selection--single, \r\n.select2-container--default.select2-container--open.select2-container--above .select2-selection--multiple {\r\n\tborder-color: $input-focus-border-color;\r\n}\r\n\r\n.select2-container--default.select2-container--focus .select2-selection--single,\r\n.select2-container--default.select2-container--focus .select2-selection--multiple {\r\n\tborder-color: $input-focus-border-color;\r\n}\r\n\r\n.select2-container--default .select2-selection--multiple .select2-selection__choice {\r\n\tbackground: $dropdown-link-active-bg;\r\n\tborder-color: $primary-400;\r\n\tcolor: $dropdown-link-active-color;\r\n\r\n\t.select2-selection__choice__remove {\r\n\t\tcolor: lighten($primary-500, 10%);\r\n\r\n\t\t&:hover {\r\n\t\t\tcolor: $primary-500;\r\n\t\t}\r\n\r\n\t\t&:active {\r\n\t\t\tbox-shadow: 0 2px 5px rgba(0, 0, 0, 0.15) inset;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.select2-container--default .select2-selection--single .select2-selection__clear {\r\n\tcolor: $danger-500;\r\n\r\n\t&:hover {\r\n\t\tcolor: $danger-600;\r\n\t}\r\n}\r\n.select2-results__message {\r\n\tcolor: $danger-500;\r\n}", ".sorting_asc,\r\n.sorting_desc, \r\n.even .sorting_1 {\r\n\tbackground-color: rgba($primary-500, 0.03);\r\n}\r\n\r\n.odd .sorting_1 {\r\n\tbackground-color: rgba($primary-500, 0.04);\r\n}\r\n\r\n.table-dark {\r\n\t.sorting_asc,\r\n\t.sorting_desc, \r\n\t.even .sorting_1 {\r\n\t\tbackground-color: rgba($warning-500, 0.15);\r\n\t}\r\n\r\n\t.odd .sorting_1 {\r\n\t\tbackground-color: rgba($warning-500, 0.15);\r\n\t}\r\n}\r\n\r\ntable.dataTable.dtr-inline.collapsed > tbody > tr[role=\"row\"] > td:first-child:before, \r\ntable.dataTable.dtr-inline.collapsed > tbody > tr[role=\"row\"] > th:first-child:before,\r\ntable.dataTable.dtr-column > tbody > tr > td.control:before, \r\ntable.dataTable.dtr-column > tbody > tr > th.control:before {\r\n\tbackground-color: $primary-500;\r\n}\r\n\r\ntable.dataTable.dtr-inline.collapsed > tbody > tr.parent > td:first-child:before, \r\ntable.dataTable.dtr-inline.collapsed > tbody > tr.parent > th:first-child:before,\r\ntable.dataTable.dtr-column > tbody > tr.parent td.control:before, \r\ntable.dataTable.dtr-column > tbody > tr.parent th.control:before {\r\n\tbackground-color: $success-500;\r\n}\r\n\r\n\r\n.dataTables_empty {\r\n\tcolor: $danger-500;\r\n}\r\n\r\n.dataTables_wrapper tr.child td.child {\r\n\r\n\t.dtr-details {\r\n\r\n\t\t&:before {\r\n\t\t\tcolor: $success-400;\r\n\t\t}\r\n\r\n\t\t&:after {\r\n\t\t\tbackground: $success-200;\r\n\r\n\t\t}\r\n\t}\r\n\r\n}\r\n\r\ndiv.dt-autofill-background {\r\n\topacity: 0.2;\r\n\tbackground-color: $black;\r\n}\r\n\r\ndiv.dt-autofill-handle {\r\n\tbackground: $primary-500;\r\n}\r\n\r\ndiv.dt-autofill-select {\r\n  background-color: $primary-500;\r\n}\r\n\r\n/* FixedColumns */\r\n.DTFC_LeftHeadWrapper,\r\n.DTFC_LeftBodyWrapper,\r\n.DTFC_LeftFootWrapper {\r\n\t\r\n\t&:before {\r\n\t\tbackground: $danger-500;\r\n\t}\r\n\r\n}\r\n\r\n/* KeyTable */\r\ntable.dataTable tbody th.focus, \r\ntable.dataTable tbody td.focus {\r\n    box-shadow: inset 0 0 0px 1px $primary-500;\r\n    background: rgba($primary-500, 0.1);\r\n}\r\n\r\ntable.dataTable:not(.table-dark) tr.dtrg-group td {\r\n\tbackground: $white;\r\n}\r\n\r\ntr.dt-rowReorder-moving {\r\n  outline-color: $success-500;\r\n}\r\n\r\ntable.dt-rowReorder-float {\r\n  outline-color: $primary-500;\r\n}  \r\n\r\n\r\n/* Select */\r\ntable.dataTable  {\r\n\r\n\t&.table-bordered {\r\n\t\t.selected {\r\n\t\t\ttd {\r\n\t\t\t\tborder-color: rgba($black,0.1);\r\n\t\t\t}\r\n\t\t}\r\n\t\ttd.selected {\r\n\t\t\tborder-color: rgba($black,0.1);\r\n\t\t}\r\n\t}\r\n\r\n\ttbody {\r\n\t\t> tr.selected, \r\n\t\t> tr > .selected {\r\n\t\t\tbox-shadow: inset 0 0 0px 1px $primary-500;\r\n\t\t\tbackground: rgba($primary-500, 0.1);\r\n\t\t}\r\n\t}\r\n\r\n\t\t\r\n}\r\n\r\n", ".datepicker table tr td.old, \r\n.datepicker table tr td.new {\r\n\tcolor: lighten($fusion-50, 10%);\r\n}\r\n\r\n.datepicker table tr td.active:active, \r\n.datepicker table tr td.active.highlighted:active, \r\n.datepicker table tr td.active.active, \r\n.datepicker table tr td.active.highlighted.active,\r\n.datepicker table tr td.selected,\r\n.datepicker table tr td.selected.highlighted,\r\n.datepicker table tr td span.active.active,\r\n.datepicker table tr td span.focused {\r\n\tbackground-color: $primary-400;\r\n\tborder-color: $primary-500;\r\n\tcolor: $white;\r\n}\r\n\r\n\r\n.datepicker table tr td.active:active:hover, \r\n.datepicker table tr td.active.highlighted:active:hover, \r\n.datepicker table tr td.active.active:hover, \r\n.datepicker table tr td.active.highlighted.active:hover, \r\n.datepicker table tr td.active:active:focus, \r\n.datepicker table tr td.active.highlighted:active:focus, \r\n.datepicker table tr td.active.active:focus, \r\n.datepicker table tr td.active.highlighted.active:focus, \r\n.datepicker table tr td.active:active.focus, \r\n.datepicker table tr td.active.highlighted:active.focus, \r\n.datepicker table tr td.active.active.focus, \r\n.datepicker table tr td.active.highlighted.active.focus,\r\n.datepicker table tr td.selected:active:hover, \r\n.datepicker table tr td.selected.highlighted:active:hover, \r\n.datepicker table tr td.selected.active:hover, \r\n.datepicker table tr td.selected.highlighted.active:hover,\r\n.datepicker table tr td.selected:active:focus, \r\n.datepicker table tr td.selected.highlighted:active:focus, \r\n.datepicker table tr td.selected.active:focus, \r\n.datepicker table tr td.selected.highlighted.active:focus, \r\n.datepicker table tr td.selected:active.focus, \r\n.datepicker table tr td.selected.highlighted:active.focus, \r\n.datepicker table tr td.selected.active.focus, \r\n.datepicker table tr td.selected.highlighted.active.focus,\r\n.datepicker table tr td.selected:hover, \r\n.datepicker table tr td.selected.highlighted:hover {\r\n\tbackground-color: $primary-600;\r\n\tborder-color: $primary-700;\r\n\tcolor: $white;\r\n}\r\n\r\n.datepicker.datepicker-inline {\r\n    border: 1px solid #ebedf2;\r\n}\r\n\r\n.datepicker thead th.prev, .datepicker thead th.datepicker-switch, .datepicker thead th.next {\r\n    color: #a1a8c3;\r\n}", ".daterangepicker  table tr td.old, \r\n.daterangepicker  table tr td.new {\r\n\tcolor: lighten($fusion-50, 10%);\r\n}\r\n\r\n.daterangepicker  table tr td.active:active, \r\n.daterangepicker  table tr td.active.highlighted:active, \r\n.daterangepicker  table tr td.active.active, \r\n.daterangepicker  table tr td.active.highlighted.active,\r\n.daterangepicker  table tr td.selected,\r\n.daterangepicker  table tr td.selected.highlighted,\r\n.daterangepicker  table tr td span.active.active,\r\n.daterangepicker  table tr td span.focused {\r\n\tbackground-color: $primary-400;\r\n\tcolor: $white;\r\n}\r\n\r\n\r\n.daterangepicker  table tr td.active:active:hover, \r\n.daterangepicker  table tr td.active.highlighted:active:hover, \r\n.daterangepicker  table tr td.active.active:hover, \r\n.daterangepicker  table tr td.active.highlighted.active:hover, \r\n.daterangepicker  table tr td.active:active:focus, \r\n.daterangepicker  table tr td.active.highlighted:active:focus, \r\n.daterangepicker  table tr td.active.active:focus, \r\n.daterangepicker  table tr td.active.highlighted.active:focus, \r\n.daterangepicker  table tr td.active:active.focus, \r\n.daterangepicker  table tr td.active.highlighted:active.focus, \r\n.daterangepicker  table tr td.active.active.focus, \r\n.daterangepicker  table tr td.active.highlighted.active.focus,\r\n.daterangepicker  table tr td.selected:active:hover, \r\n.daterangepicker  table tr td.selected.highlighted:active:hover, \r\n.daterangepicker  table tr td.selected.active:hover, \r\n.daterangepicker  table tr td.selected.highlighted.active:hover,\r\n.daterangepicker  table tr td.selected:active:focus, \r\n.daterangepicker  table tr td.selected.highlighted:active:focus, \r\n.daterangepicker  table tr td.selected.active:focus, \r\n.daterangepicker  table tr td.selected.highlighted.active:focus, \r\n.daterangepicker  table tr td.selected:active.focus, \r\n.daterangepicker  table tr td.selected.highlighted:active.focus, \r\n.daterangepicker  table tr td.selected.active.focus, \r\n.daterangepicker  table tr td.selected.highlighted.active.focus,\r\n.daterangepicker  table tr td.selected:hover, \r\n.daterangepicker  table tr td.selected.highlighted:hover {\r\n\tbackground-color: $primary-600;\r\n\tcolor: $white;\r\n}\r\n\r\n\r\n.daterangepicker .calendar-table .next,\r\n.daterangepicker .calendar-table .prev  {\r\n\r\n    span {\r\n    \tborder-color: #a1a8c3;\r\n    }\r\n}\r\n\r\n.daterangepicker .in-range.available {\r\n\tbackground-color: $warning-100;\r\n}\r\n\r\n.daterangepicker .off.ends.in-range.available {\r\n\tbackground-color: $warning-50;\r\n}\r\n\r\n\r\n.daterangepicker td.available:hover, \r\n.daterangepicker th.available:hover {\r\n\tbackground-color: $warning-300;\r\n}\r\n\r\n\r\n.daterangepicker .calendar-table table thead tr th {\r\n\r\n\t&.month {\r\n\t\tcolor: #a1a8c3;\r\n\t}\r\n}\r\n\r\n.daterangepicker .ranges li {\r\n\r\n    &.active {\r\n    \tbackground-color: $primary-500;\r\n    }\r\n}\r\n", ".irs--flat .irs-bar,\r\n.irs--flat .irs-from, \r\n.irs--flat .irs-to, \r\n.irs--flat .irs-single,\r\n.irs--flat .irs-handle > i:first-child {\r\n\tbackground-color: $primary-500;\r\n}\r\n\r\n.irs--flat .irs-from:before, \r\n.irs--flat .irs-to:before, \r\n.irs--flat .irs-single:before {\r\n\tborder-top-color: $primary-500;\r\n}\r\n\r\n.irs--flat .irs-handle.state_hover > i:first-child, \r\n.irs--flat .irs-handle:hover > i:first-child {\r\n\tbackground-color: $primary-600;\r\n}\r\n\r\n\r\n.irs--big .irs-bar {\r\n\tbackground-color: $primary-300;\r\n    border-color: $primary-500;\r\n\tbackground: linear-gradient(to bottom, #ffffff 0%, $primary-300 30%, $primary-500 100%);\r\n}\r\n\r\n.irs--big .irs-from, \r\n.irs--big .irs-to, \r\n.irs--big .irs-single {\r\n\tbackground: $primary-500;\r\n}\r\n\r\n\r\n.irs--modern .irs-bar {\r\n\tbackground: $success-600;\r\n\tbackground: linear-gradient(to bottom, $success-400 0%, $success-600 100%);\r\n}\r\n\r\n.irs--modern .irs-from, \r\n.irs--modern .irs-to, \r\n.irs--modern .irs-single {\r\n\tbackground-color: $success-500\r\n}\r\n\r\n.irs--modern .irs-from:before, \r\n.irs--modern .irs-to:before,\r\n.irs--modern .irs-single:before {\r\n\tborder-top-color: $success-500;\r\n}\r\n\r\n.irs--sharp .irs-bar,\r\n.irs--sharp .irs-handle,\r\n.irs--sharp .irs-from, \r\n.irs--sharp .irs-to, \r\n.irs--sharp .irs-single {\r\n\tbackground-color: $danger-500;\r\n}\r\n\r\n.irs--sharp .irs-handle > i:first-child,\r\n.irs--sharp .irs-from:before, \r\n.irs--sharp .irs-to:before, \r\n.irs--sharp .irs-single:before {\r\n\tborder-top-color: $danger-500;\r\n}\r\n\r\n.irs--sharp .irs-min, \r\n.irs--sharp .irs-max {\r\n\tbackground-color: $danger-800;\r\n}\r\n\r\n.irs--round .irs-handle {\r\n\tborder-color: $info-500;\r\n}\r\n\r\n.irs--round .irs-bar,\r\n.irs--round .irs-from, \r\n.irs--round .irs-to, \r\n.irs--round .irs-single {\r\n\tbackground-color: $info-500;\r\n}\r\n\r\n.irs--round .irs-from:before, \r\n.irs--round .irs-to:before, \r\n.irs--round .irs-single:before {\r\n\tborder-top-color: $info-500;\r\n}", "body:not(.mod-pace-custom) {\r\n\t.pace {\r\n\t\t.pace-progress {\r\n\t\t\tbackground: $color-primary;\r\n\t\t}\r\n\t}\r\n\r\n}\r\n\r\n.mod-pace-custom {\r\n\r\n\t.pace {\r\n\t\tbackground: $white;\r\n\r\n\t\t.pace-progress {\r\n\t\t\tbackground-color:$color-primary;\r\n\t\t\tbackground-image: linear-gradient(135deg, $color-primary 0%, $color-primary 25%, darken($color-primary,10%) 25%, darken($color-primary,10%)\r\n\t\t\t\t50%, $color-primary 50%, $color-primary 75%, darken($color-primary,10%) 75%, darken($color-primary,10%) 100%);\r\n\r\n\t\t}\r\n\t}\r\n\r\n\t&.pace-running {\r\n\t\t.page-content {\r\n\t\t\t&:before {\r\n\t\t\t\tbackground-color: $page-bg;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}"]}