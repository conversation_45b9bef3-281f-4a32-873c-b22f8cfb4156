@extends('site.index')
@section('content')

<title>
{{trans('admin.Login')}}
</title>

    <!--==============================
    Breadcumb
    ============================== -->
    <div class="breadcumb-wrapper" data-bg-src="{{asset('Front/assets/img/bg/breadcrumb-bg.png')}}">
        <!-- bg animated image/ -->
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="breadcumb-content">
                        <h1 class="breadcumb-title">{{trans('admin.Login')}}</h1>
                        <ul class="breadcumb-menu">
                            <li><a href="{{url('/')}}">{{trans('admin.Home')}}</a></li>
                            <li class="active">{{trans('admin.Login')}}</li>
                        </ul>
                    </div>
                </div>
            </div>

        </div>
    </div>


<style>
    td{
        text-align: center;
    }
    </style>
    <!--==============================
    Login Area  
    ==============================-->
    <div class="feature-area-3 space-bottom overflow-hidden space">
        <div class="container">
            <div class="title-area">
                <span class="sub-title style2">{{trans('admin.Join')}}</span>
        
            </div>
        </div>
        <div class="container">
            <div class="row">
                <div class="col-xl-4">
                    <div class="feature-tab-button">
                        <div class="filter-menu-active">
                            <button data-filter=".cat1" class="active" type="button"> {{trans('admin.Login')}}
                            <i class="fas fa-right-to-bracket"></i>
                            </button>
                            <button data-filter=".cat2" type="button">{{trans('admin.Register')}} <i class="fas fa-user"></i></button>
                        </div>
                    </div>
                </div>
                <div class="col-xl-8">
                    <div class="feature-tab-content filter-active-cat1 mt-xl-0 mt-40">
                 
                            <div class="filter-item cat1">
                        
                            <div class="tab-content mt-n1">
                                           <div class="bmi-area-1 ">
            <div class="container">
                <div class="row justify-content-between">
              
                    <div class="col-lg-12 align-self-end">
                        <div class="bmi-calculator-form">
                            <h4 class="form-title">{{trans('admin.Login')}}</h4>
                                        <form  method="post" action="{{url('PostLoginSite')}}" class="account__form">
                     @csrf
                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="form-group">
                         <input  type='email' name='email' value="{{old('email')}}" required class="form-control style-border" placeholder="{{trans('admin.Email')}}">
                                        </div>
                                    </div>
                                 
                                    <div class="col-lg-12">
                                        <div class="form-group">
                                            <input type='password' name='password'  class="form-control style-border" required placeholder="{{trans('admin.Password')}}">
                                        </div>
                                    </div>
                                 
                             
             
                                </div>
                            
                             <a href="{{url('ForgetSite')}}" class="">{{trans('admin.Forget_Password')}}</a>
                                <div class="btn style3 mt-5">
                                    <button type="submit" class="btn style2">   {{trans('admin.Login')}}   </button>                          
                                </div>
                              
                            </form>
                        </div>    
                        
                        
                    </div>
                </div>
            </div>
        </div>
                            </div>
                        </div>
                        
                        
                             
                       <div class="filter-item cat2">
                            <div class="tab-content mt-n1">
                                     <div class="bmi-area-1 ">
            <div class="container">
                <div class="row justify-content-between">
              
                    <div class="col-lg-12 align-self-end">
                        <div class="bmi-calculator-form">
                            <h4 class="form-title">{{trans('admin.Register')}}</h4>
                      <form method="post" action="{{url('PostRegisterSite')}}" class="account__form">
                     @csrf
                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <input type="text" name="name" required class="form-control style-border" placeholder="{{trans('admin.Name')}}">
                                        </div>
                                    </div>
                                 
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <input type="email" name="email" required class="form-control style-border" placeholder="{{trans('admin.Email')}}">
                                        </div>
                                    </div>
                                      <div class="col-lg-6">
                                        <div class="form-group">
                                            <input type="tel" name="phone" required class="form-control style-border" placeholder="{{trans('admin.Phone')}}">
                                        </div>
                                    </div>
                                   <div class="col-lg-6">
                                        <div class="form-group">
                                            <input type="password" name="password" required class="form-control style-border" placeholder="{{trans('admin.Password')}}">
                                        </div>
                                    </div>
                             
             
                                </div>
                 

                                <div class="btn style3 mt-5">
                                       <button type="submit" class="btn style2">   {{trans('admin.Register')}}   </button>                                   
                                </div>
                              
                            </form>
                        </div>                  
                    </div>
                </div>
            </div>
        </div>

                            </div>
                        </div>
                        
                    
                    
                        </div>
                 
                    
                    
                    
                    
               
                    
                    
                    </div>
                </div>
            </div>
        </div>


@endsection