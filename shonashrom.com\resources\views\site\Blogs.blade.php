@extends('site.index')
@section('content')

<title>{{trans('admin.Blogs')}}</title>

    <!--==============================
    Breadcumb
    ============================== -->
    <div class="breadcumb-wrapper" data-bg-src="{{asset('Front/assets/img/bg/breadcrumb-bg.png')}}">
        <!-- bg animated image/ -->
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="breadcumb-content">
                        <h1 class="breadcumb-title">{{trans('admin.Blogs')}}</h1>
                        <ul class="breadcumb-menu">
                            <li><a href="{{url('/')}}">{{trans('admin.Home')}}</a></li>
                            <li class="active">{{trans('admin.Blogs')}}</li>
                        </ul>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!--==============================
    Blog Area  
    ==============================-->
     <section class="blog-area space bg-smoke3">
        <div class="container">
            <div class="title-area text-center">
                <span class="sub-title">
                    {{trans('admin.Blogs')}}
                </span>

            </div>
            <div class="row" data-slide-show="3" data-lg-slide-show="2" data-md-slide-show="2" data-sm-slide-show="1" data-xs-slide-show="1" data-dots="false" data-md-dots="true">
         
                  @foreach($Articles as $art)
                <div class="col-md-6 col-lg-4">
                    <div class="blog-card">
                        <div class="blog-img">
                            <img src="{{URL::to($art->Sub_Image)}}" alt="blog image">
                        </div>
                        <div class="blog-content" data-bg-src="{{asset('Front/assets/img/blog/blog_card1_bg.png')}}">
                            <div class="blog-meta">
                                


                                <a href="{{url('BlogsDet/'.$art->id)}}"><i class="fal fa-calendar"></i>{{$art->Date}}</a>
                                <a href="{{url('BlogsDet/'.$art->id)}}"><i class="far fa-user"></i>{{$art->Author}}</a>
                            </div>
                            <h3 class="blog-title box-title"><a href="{{url('BlogsDet/'.$art->id)}}"> {{app()->getLocale() == 'ar' ?$art->Arabic_Title :$art->English_Title}}</a></h3>
                     
                        </div>
                    </div>
                </div>
@endforeach
               
            </div>
        </div>
            <div class="pagination justify-content-center">
                <ul>
                        {{$Articles->Links()}}
                </ul>
            </div>
    </section>
 


@endsection
      