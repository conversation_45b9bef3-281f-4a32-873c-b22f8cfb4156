<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Auth;
class Users
{    
     public function handle($request, Closure  $next=null,$guard=null)
    {
         if(Auth::check()){  
             
               return $next($request);
               return back();
        
                    }else{

             return redirect('/');
                      }

      
    }
    
    
}
