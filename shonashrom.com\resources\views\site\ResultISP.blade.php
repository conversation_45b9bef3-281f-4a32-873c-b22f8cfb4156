@extends('site.index')
@section('content')

<title>ISP-ONE Result</title>

<style>
    .XX{
        background-color:white;
        border:1px solid black;
        padding:15px;
        border-radius:10px;
        box-shadow: 8px 14px 9px 2px #ea1b29;
            top: 500px;
                width: 28%;
    text-align: center;
    }
    @media (max-width: 575px) {
  .XX {
 top: 500px;
 width:unset;
     right: 26px;
  }
}

</style>
     <main>
         <!-- page__title-start -->
         <div class="page__title align-items-center" style="background-image: url({{asset('Front/assets/img/bg/Background2.png')}});">
            <div class="container">
               <div class="row">
                  <div class="col-xl-12">
                     <div class="page__title-content text-center">
                        <h3 class="breadcrumb-title breadcrumb-title-sd mb-15">ISP-ONE Result</h3>
                        <div class="page_title__bread-crumb">
                        <nav>
                           <nav class="breadcrumb-trail breadcrumbs">
                              <ul class="breadcrumb-menu">
                                 <li class="breadcrumb-trail">
                                    <a href="{{url('/')}}"><span>{{trans('admin.Home')}}</span></a>
                                 </li>
                                 <li class="trail-item">
                                    <span>ISP-ONE Result</span>
                                 </li>
                              </ul>
                           </nav> 
                        </nav>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
         <!-- page__title-end -->


         
                <!------------------------ Produst start ------------------->
  
         <div class="blog_area pt-120 pb-70">
            <div class="container custome-container">
               <div class="row">
                  <div class="col-lg-12">
                     <div class="main-blog">
                        <div class="single_mblog mb-50">
                           <div class="mblog_image mb-30">
                              <a><img src="{{URL::to($Result->Image)}}" alt=""></a>
                           </div>
                           <div class="mblog_info mblog_details">
                         
                              <h6 class="mblog__title mb-15"><a>
                                  
                        {{app()->getLocale() == 'ar' ?$Result->Arabic_Title :$Result->English_Title}}          
                                  </a></h6>
                              <p>{{app()->getLocale() == 'ar' ?$Result->Arabic_Desc :$Result->English_Desc}}</p>
                              
                           </div> 
                        </div>
                 
               </div>
            </div>
         </div>
            </div>
         </div>
         <!-- blog_area-end -->
        
       <!------------------------ Product End ------------------->
         <!--------------------------------- Schedule Start ------------------------------>
         <div class="schedule-area pb-110">
            <div class="container custome-container">
               <div class="section-wrap-two text-center">
               </div>
            </div>
            <div class="schedule-shape-bg fix">
               <img src="{{asset('Front/assets/img/features/schedule-shape.png')}}"  class="img-fluid" alt="">
            </div>
            <div class="schedule-shape-one fix">
               <img src="{{asset('Front/assets/img/bg/BgAnimi3.png')}}"  class="img-fluid" alt="">
            </div>
            <div class="schedule-shape-two fix">
               <img src="{{asset('Front/assets/img/bg/BgAnimi3.png')}}"  class="img-fluid" alt="">
            </div>
            <div id="futexo-classes-area" class="mt-65">
               <div class="container custome-container">
                   <div class="futexo-classes-content">
                       <div class="row justify-content-center">
                           <div class="col-lg-12">
                               <div class="futexo-classes-tab">
                                   <div class="tab-content current">
                                       <div class="futexo-tab-d futexo-tab-all">
                                          <span class="schedule-icon">
                                             <i class="flaticon-muscle"></i>
                                          </span>
                                          <ul>
                                             <li><span class="time">{{trans('admin.MoashrKotlt')}}</span></li>
                                             <li><span class="time">{{trans('admin.HaltWazn')}}</span></li>
                                              
                                            @if(!empty(auth()->user()->id))
                                              
                                             <li><span class="time">{{trans('admin.WaznMasaly')}}</span></li>
                                             <li><span class="time">{{trans('admin.MkdarWaznFkdanah')}}</span></li>
                                             <li><span class="time">{{trans('admin.So3ratFokdanah')}}</span></li>
                                             <li><span class="time">{{trans('admin.WaktMtwk3')}}</span></li>
                                             <li><span class="time">{{trans('admin.3dd3bwat')}}</span></li>
                                             <li><span class="time">{{trans('admin.So3ratYomen')}}</span></li>
                                             <li><span class="time">{{trans('admin.PortinatYomen')}}</span></li>
                                             <li style="    height: 128px;"><span class="time" style="line-height: normal;">{{trans('admin.PortinatBlgram')}}</span></li>
                                              @else
                                              
                                                   <li  class="Blur"><span class="time">{{trans('admin.MustLogin')}}</span></li>
                                             <li  class="Blur"><span class="time">{{trans('admin.MustLogin')}}</span></li>
                                             <li  class="Blur"><span class="time">{{trans('admin.MustLogin')}}</span></li>
                                             <li  class="Blur"><span class="time">{{trans('admin.MustLogin')}}</span></li>
                                             <li  class="Blur"><span class="time">{{trans('admin.MustLogin')}}</span></li>
                                             <li  class="Blur"><span class="time">{{trans('admin.MustLogin')}}</span></li>
                                             <li  class="Blur"><span class="time">{{trans('admin.MustLogin')}}</span></li>
                                             <li  class="Blur"><span class="time">{{trans('admin.MustLogin')}}</span></li>      
                                              
                                        @endif
                                          </ul>
                                       </div>
                                       <div class="futexo-tab-all">
                                          <h5>{{trans('admin.Result')}}</h5>
                                          <ul>
                                              <li><span>{{number_format((float)$Moshr_Kotlt_Gsm, 2, '.', '')}}</span> </li>
                                              <li><span>
                                            
                                                  @if($Moshr_Kotlt_Gsm < 16)
                                                      {{trans('admin.NhafaShdeda')}}    
                                                  @elseif($Moshr_Kotlt_Gsm  >=  16 and $Moshr_Kotlt_Gsm  <= 16.9 )
                                                                                                               
                                                              {{trans('admin.NahafaMoatadla')}}                                                 
                                                  @elseif($Moshr_Kotlt_Gsm  >=  17 and $Moshr_Kotlt_Gsm  <= 18.4 )
                                                                                                               
                                                              {{trans('admin.NahafaKhfefa')}}                                                 
                                                  @elseif($Moshr_Kotlt_Gsm  >=  18.5 and $Moshr_Kotlt_Gsm  <= 24.9 )
                                                                                                                 
                                                                {{trans('admin.WaznTabe3y')}}                                                 
                                                  @elseif($Moshr_Kotlt_Gsm  >=  25 and $Moshr_Kotlt_Gsm  <= 29.9 )
                                                                                                               
                                                              {{trans('admin.zaydatWazn')}}                                                 
                                                  @elseif($Moshr_Kotlt_Gsm  >=  30 and $Moshr_Kotlt_Gsm  <= 34.9 )
                                                                                                               
                                                              {{trans('admin.smnaLevel1')}}                                                 
                                                  @elseif($Moshr_Kotlt_Gsm  >=  35 and $Moshr_Kotlt_Gsm  <= 39.9 )
                                                                                                               
                                                              {{trans('admin.smnaLevel2')}}                                                 
                                                  @elseif($Moshr_Kotlt_Gsm  >=  40)
                                                  
                                                  {{trans('admin.smnaLevel3')}}                                                 
                                                  
                                                  
                                                  @endif
                                                  
                                            </span> </li>
                                              
                                                     @if(!empty(auth()->user()->id))
                                              <li><span>{{number_format((float)$WaznMsaly, 2, '.', '')}}</span>{{trans('admin.KG')}} </li>
                                              <li><span>{{number_format((float)$mkdar_wazn_mtlop_fkdanh, 2, '.', '')}}</span> {{trans('admin.KG')}}</li>
                                              <li><span>{{number_format((float)$saarat_hararya_mtlop_fkdnah, 2, '.', '')}}</span> {{trans('admin.Calories')}}</li>
                                              <li><span>{{number_format((float)$waktMatwkaaLlwsolWaznMasaly, 2, '.', '')}}</span>{{trans('admin.Month')}} </li>
                                              <li><span>{{number_format((float)$Add3bwatOstegy, 2, '.', '')}}</span> {{trans('admin.Package')}}</li>
                                              <li><span>{{number_format((float)$So3ratYomen, 2, '.', '')}}</span> {{trans('admin.Calories')}}</li>
                                              <li><span>{{number_format((float)$so3ratPoortinat, 2, '.', '')}}</span> {{trans('admin.Calories')}}</li>
                                              <li style="    height: 128px;"><span>{{number_format((float)$gramProtinat, 2, '.', '')}}</span> {{trans('admin.Gram')}}</li>
                                                  @else
                                        <li class="Blur">{{trans('admin.MustLogin')}} </li>
                                        <li class="Blur">{{trans('admin.MustLogin')}} </li>
                                        <li class="Blur">{{trans('admin.MustLogin')}} </li>
                                        <li class="Blur">{{trans('admin.MustLogin')}} </li>
                                        <li class="Blur">{{trans('admin.MustLogin')}} </li>
                                        <li class="Blur">{{trans('admin.MustLogin')}} </li>
                                        <li class="Blur">{{trans('admin.MustLogin')}} </li>
                                        <li class="Blur">{{trans('admin.MustLogin')}} </li>
                                        
                                    <div class="XX" style="position: absolute">{{trans('admin.MustLoginToSeeMore')}}</div>              
                                                  @endif
                                          </ul>
                                       </div>
                                      
                                   </div>
                               </div>
                           </div>
                       </div>
                   </div>
               </div>
       
            </div>
         </div>
         <!--------------------------------- Schedule--end ------------------------------>
  
       
 
    

      </main>

   
<style>
.futexo-tab-all {
    max-width: 1000px !important;
}    
    
    .Blur{
        
        filter: blur(5px);
    }
</style>


@endsection
      