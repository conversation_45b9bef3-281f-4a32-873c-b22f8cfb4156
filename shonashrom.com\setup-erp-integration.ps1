Write-Host "=== Shonashrom ERP Integration Setup ===" -ForegroundColor Green
Write-Host ""

Write-Host "1. Running database migrations..." -ForegroundColor Yellow
php artisan migrate --force
Write-Host ""

Write-Host "2. Running database seeders..." -ForegroundColor Yellow
php artisan db:seed --force
Write-Host ""

Write-Host "3. Clearing application cache..." -ForegroundColor Yellow
php artisan cache:clear
Write-Host ""

Write-Host "4. Clearing config cache..." -ForegroundColor Yellow
php artisan config:clear
Write-Host ""

Write-Host "5. Clearing view cache..." -ForegroundColor Yellow
php artisan view:clear
Write-Host ""

Write-Host "=== Integration Setup Complete ===" -ForegroundColor Green
Write-Host ""
Write-Host "SUCCESS: Your Shonashrom site now uses the SAME design system as your main ERP!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Visit http://yoursite.com/DesignSystem in your admin panel" -ForegroundColor White
Write-Host "2. Configure colors, fonts, and styling to match your brand" -ForegroundColor White
Write-Host "3. Test the site - it now has identical styling to your main ERP" -ForegroundColor White
Write-Host ""
Write-Host "Features enabled:" -ForegroundColor Cyan
Write-Host "- Same font system as main ERP (Cairo, Roboto, Open Sans, Poppins, Alexandria)" -ForegroundColor White
Write-Host "- Same color configuration system" -ForegroundColor White
Write-Host "- Same footer, header, and breadcrumb styling" -ForegroundColor White
Write-Host "- Consistent design across ERP and Shonashrom site" -ForegroundColor White
Write-Host "- Same admin interface for design management" -ForegroundColor White
Write-Host ""
Read-Host "Press Enter to continue"
