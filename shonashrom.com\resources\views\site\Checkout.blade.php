@extends('site.index')
@section('content')
<title>{{trans('admin.Checkout')}}</title>
<style>
    select, .single-select, .form-control, .form-select, textarea, input{
        color:white !important;
    }
</style>

    <!--==============================
    Breadcumb
    ============================== -->
    <div class="breadcumb-wrapper" data-bg-src="{{asset('Front/assets/img/bg/breadcrumb-bg.png')}}">
        <!-- bg animated image/ -->
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="breadcumb-content">
                        <h1 class="breadcumb-title">{{trans('admin.Checkout')}}</h1>
                        <ul class="breadcumb-menu">
                            <li><a href="{{url('/')}}">{{trans('admin.Home')}}</a></li>
                            <li class="active">{{trans('admin.Checkout')}}</li>
                        </ul>
                    </div>
                </div>
            </div>

        </div>
    </div>






<style>
    td{
        text-align: center;
    }
    </style>
    <!--==============================
    Cart Area  
    ==============================-->
   <div class="container">

  <div class="checkout-Area space" >
      
                 <form action="{{url('PlaceOrder')}}" method="post">
                    @csrf
                         
  <div class="row">
      <div class="col-lg-7 col-xl-7">
             <div class="bmi-area-1 space">
            <div class="container">
                <div class="row justify-content-between">
                    <div class="col-lg-12 align-self-end">
                          
                        <div class="bmi-calculator-form">
                            <h4 class="form-title">{{trans('admin.InvoiceInformation')}}  </h4>
                            
               
                           @if(empty(auth()->user()->id))    
                                       <div class="col-lg-12">
                            <h3><a href="{{url('LoginSite')}}"> <span id="">{{trans('admin.Login')}}</span></a></h3>                  
                                             </div>
                                
                           @else        
                                      <div class="col-lg-12">
                                <label for="Weight Unit">{{trans('admin.MyAddress')}}</label>

         <select class="form-control X"  id="Address" onchange="AddressData()" style="width: 100%">
                                            <option value=""> {{trans('admin.MyAddress')}}</option>
                                        @foreach($Addresses as $address)    
                                            <option value="{{$address->id}}">{{$address->Address_Name}}</option>
                                @endforeach
                                        </select>
                                </div>
                                
                            @endif        
                                
                                
                         
                                
                                <div class="row">
                                    <div class="col-lg-6 mt-5">
                                        <div class="form-group">
                                                         <label>{{trans('admin.Name')}} <span class="required">*</span></label>
                                                       @if(!empty(auth()->user()->id))  
                                            <input type="text" value="{{auth()->user()->name}}" name="Name" class="form-control" readonly>
                                            @else
                                              <input type="text" value="{{old('Name')}}" name="Name" class="form-control" required>   
                                            @endif
                                        </div>
                                    </div>
                                 
                                    <div class="col-lg-6 mt-5">
                                        <div class="form-group">
                                                        <label>{{trans('admin.Email')}} <span class="required">*</span></label>
                                                         @if(!empty(auth()->user()->id)) 
                                            <input type="email" value="{{auth()->user()->email}}" name="Email" class="form-control" readonly>
                                            @else
                                              <input type="email" value="{{old('Email')}}" name="Email" class="form-control" required>   
                                            @endif
                                        </div>
                                    </div>
                              <div class="col-lg-6">
                                        <div class="form-group">
                                                                 <label>{{trans('admin.Phone')}} <span class="required">*</span></label>
                                                @if(!empty(auth()->user()->id))  
                                            <input type="tel" value="{{auth()->user()->phone}}" name="Phone" class="form-control" readonly>
                                            @else
                                              <input type="tel" value="{{old('Phone')}}" name="Phone" class="form-control" required>   
                                            @endif
                                        </div>
                                    </div>       
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                                   <label>{{trans('admin.Other_Phone')}} <span class="required">*</span></label>
                                           <input type="tel" name="OtherPhone" value="{{old('OtherPhone')}}" class="form-control">
                                        </div>
                                    </div>
                                          <div class="col-lg-6">
                                        <div class="form-group">
                                           <label>{{trans('admin.Address_Name')}} <span class="required">*</span></label>
                                      <input type="text" name="Address_Name" id="Address_Name" class="form-control" required>
                                        </div>
                                    </div>
                              <div class="col-lg-6">
                                        <div class="form-group">
                                                    <label>{{trans('admin.Special_Mark')}} <span class="required">*</span></label>
                                       <input type="text" name="Special_MarkAdd" id="Special_MarkAdd" class="form-control" >
                                        </div>
                                    </div>
                                       <div class="col-lg-6">
                                        <div class="form-group">
                                      <label>{{trans('admin.Street')}} <span class="required">*</span></label>
                                             <input type="text" name="StreetAdd" id="StreetAdd" class="form-control" required>
                                        </div>
                                    </div>
                                          <div class="col-lg-6">
                                        <div class="form-group">
                                         <label> {{trans('admin.Buliding')}} <span class="required">*</span></label>
                                         <input type="text" name="BulidingAdd" id="BulidingAdd" class="form-control" required>
                                        </div>
                                    </div>
                              <div class="col-lg-6">
                                        <div class="form-group">
                                                 <label>{{trans('admin.Floor')}} <span class="required">*</span></label>
                                      <input type="text" name="FloorAdd" id="FloorAdd" class="form-control" required>
                                        </div>
                                    </div>
                                                <div class="col-lg-6">
                                        <div class="form-group">
                                            <label>{{trans('admin.Flat')}} <span class="required">*</span></label>
                                          <input type="text" name="FlatAdd" id="FlatAdd" class="form-control" required>
                                        </div>
                                    </div>
                                          <div class="col-lg-6">
                                        <div class="form-group">
                                                       <label>{{trans('admin.Location')}} <span class="required">*</span></label>
                                                  <input type="text" name="LocationAdd" id="LocationAdd" class="form-control" required>
                                        </div>
                                    </div>
                              <div class="col-lg-12">
                                        <div class="form-group">
                                                  <label>{{trans('admin.Address_Details')}} <span class="required">*</span></label>
                                               <input type="text" name="Address_DetailsAdd" id="Address_DetailsAdd" class="form-control" required>
                                        </div>
                                    </div>
                                               <div class="col-lg-6">
           <label>{{trans('admin.Governrate')}} <span class="required">*</span></label>
           <select class="form-control X"  name="Governrate" id="Governrate" required >
                                              
                                       <option value="">{{trans('admin.Governrate')}}</option>
                                           @foreach($Governrates as $gov)
                                          <option value="{{$gov->id}}" >  
                                     {{app()->getLocale() == 'ar' ?$gov->Arabic_Name :$gov->English_Name}}          
                                          </option>
                                          @endforeach  
                                                </select>
	 

                                </div>
                                               <div class="col-lg-6">
        <label>{{trans('admin.City')}} <span class="required">*</span></label>
         <select class="form-control X"    name="City" id="City" required >
                                       <option value="">{{trans('admin.City')}}</option>
                                     
                                                </select>
                                </div>
                                               <div class="col-lg-12">
    	    <label>{{trans('admin.Place')}} <span class="required">*</span></label>
          <select class="form-control X"   name="Place" id="Place" required  >
                                       <option value="">{{trans('admin.Place')}}</option>
                                     
                                                </select>
                                </div>
                                </div>
                             
                             
                  
                              
                            
                        </div>                  
                    </div>
                </div>
            </div>
        </div>

      
      
      </div>
           <div class="col-lg-5 col-xl-5">
               <div class="row  space">
                              <div class="col-md-12">
                                    <div class="cart-page-total">
                                       <h2>     {{trans('admin.Total')}}</h2>
                                        <hr>
                                       <ul class="mb-20">
                                          <li class="cartch">{{trans('admin.Sub_Total')}} <span>{{Cart::subtotal()}}</span></li>
                                             <li class="cartch">{{trans('admin.Shipping')}}<span id="Ship">0</span>  <input type="hidden" id="ShipeHide" name="Shipping" value="0">  </li>
                                          <li class="cartch">{{trans('admin.Grand_Total')}} <span id="Tot">{{str_replace(',', '', Cart::total())}}</span>
                                           
                                            <input type="hidden" name="Total_Price" id="Total_Price" value="{{str_replace(',', '', Cart::total())}}">                
           <input type="hidden" id="Total_PriceHide" value="{{str_replace(',', '', Cart::total())}}">   
                                           
                                           </li>
                                            <li class="cartch">{{trans('admin.Payment_Method')}} </li>
                              <div class="payment-method">
                                 
                                        <p class="methods">
                                            <input type="radio" checked="" id="cash-on-delivery" name="radio-group">
                                            <label for="cash-on-delivery">{{trans('admin.Cash_on_Delivery')}}</label>
                                              <input type="radio" id="" name="radio-group" checked="true">
                                              <input type="hidden" name="Payment_Method" value="Later">  
                                              <input type="hidden" name="Status" value="1">   
                                        </p>
       
                                    </div>
                                       </ul>
                                       <div class="checkout_btn f-right">
                                          <button type="submit" class="btn d-xl-block "> {{trans('admin.Place_Order')}}</button>
                                       </div>
                                    </div>
                              </div>
                           </div>
               
            










   
 

      </div>
      </div>
                     
      </form>
  </div>

</div>













<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

<!-- Filter Governrate and City !-->
<script>
   $(document).ready(function() {
   
       $('#Governrate').on('change', function(){
           var countryId = $(this).val();
           if(countryId) {
               $.ajax({
                   url: 'GovernrateFilterr/'+countryId,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('#City').empty();
   
                       $.each(data, function(key, value){
   
             $('#City').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                       
                      var CIITY = $('#City').val();  
                         $.ajax({
                   url: 'CityFilterr/'+CIITY,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('#Place').empty();
   
                       $.each(data, function(key, value){
   
             $('#Place').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                       
       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
                       
                                $.ajax({
                   url: 'CityShip/'+CIITY,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
                       
    $('#Ship').text(data.ship); 
    $('#ShipeHide').val(data.ship);                     
     var total = $('#Total_PriceHide').val();  
     var SHIP = $('#ShipeHide').val();  
                       
        var Res=parseFloat(total) + parseFloat(SHIP) ;               
           
$('#Total_Price').val(Res);
$('#Tot').text(Res);


                       
       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
                       
       
                       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
           } else {
   
               $('select[name="states"]').empty();
           }
   
       });
   
   });
    
   $(document).ready(function() {
   
   
           var countryId = $('#Governrate').val();
           if(countryId) {
               $.ajax({
                   url: 'GovernrateFilterr/'+countryId,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('#City').empty();
   
                       $.each(data, function(key, value){
   
             $('#City').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                       
                     var CIITY = $('#City').val();  
                         $.ajax({
                   url: 'CityFilterr/'+CIITY,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('#Place').empty();
   
                       $.each(data, function(key, value){
   
             $('#Place').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                       
       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
                              
                             $.ajax({
                   url: 'CityShip/'+CIITY,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
                       
    $('#Ship').text(data.ship); 
    $('#ShipeHide').val(data.ship);                     
     var total = $('#Total_PriceHide').val();  
     var SHIP = $('#ShipeHide').val();  
                       
        var Res=parseFloat(total) + parseFloat(SHIP) ;               
           
$('#Total_Price').val(Res);
$('#Tot').text(Res);


                       
       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
                       
       
                       
                       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
           } else {
   
               $('select[name="states"]').empty();
           }
   
   
   });  
    
    
 $('#City').on('change', function(){
      var CIITY = $('#City').val();  
                         $.ajax({
                   url: 'CityFilterr/'+CIITY,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('#Place').empty();
   
                       $.each(data, function(key, value){
   
             $('#Place').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                       
               $.ajax({
                   url: 'CityShip/'+CIITY,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
                       
    $('#Ship').text(data.ship); 
    $('#ShipeHide').val(data.ship);                     
     var total = $('#Total_PriceHide').val();  
     var SHIP = $('#ShipeHide').val();  
                       
        var Res=parseFloat(total) + parseFloat(SHIP) ;               
           
$('#Total_Price').val(Res);
$('#Tot').text(Res);


                       
       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
                       
       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
                       
     
 });
    
</script>

<!-- Address Data -->
<script>
    function AddressData()   {
        
    var Address =$('#Address').val();   
          $.ajax({
                             url: 'ChangeAddressSite',
                             type:"GET",
                             data:{
                                Address:Address, 
                             },
                             dataType:"json",
                             beforeSend: function(){
                                 $('#loader').css("visibility", "visible");
                             },
   
                             success:function(data) {
                           $('#Governrate').empty();    
                           $('#City').empty();    
                           $('#Place').empty();    
                                     
                       $('#Address_Name').val(data.Address_Name); 
                       $('#Special_MarkAdd').val(data.Special_MarkAdd); 
                       $('#StreetAdd').val(data.StreetAdd); 
                       $('#BulidingAdd').val(data.BulidingAdd); 
                       $('#FloorAdd').val(data.FloorAdd); 
                       $('#FlatAdd').val(data.FlatAdd); 
                       $('#LocationAdd').val(data.LocationAdd); 
                       $('#Address_DetailsAdd').val(data.Address_DetailsAdd); 
                                 
         $('#Governrate').append('<option value="'+ data.Gov +'">' + data.GovName + '</option>');
         $('#City').append('<option value="'+ data.Cit +'">' + data.CitName + '</option>');
         $('#Place').append('<option value="'+ data.Pla +'">' + data.PlaName + '</option>');

                                 
                  var CIITY = $('#City').val(); 
       $.ajax({
                   url: 'CityShip/'+CIITY,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
                       
    $('#Ship').text(data.ship); 
    $('#ShipeHide').val(data.ship);                     
     var total = $('#Total_PriceHide').val();  
     var SHIP = $('#ShipeHide').val();  
                       
        var Res=parseFloat(total) + parseFloat(SHIP) ;               
           
$('#Total_Price').val(Res);
$('#Tot').text(Res);


                       
       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
                                      
                                 
                                 
                                 
                             },
                             complete: function(){
                                 $('#loader').css("visibility", "hidden");
                             }
                         });
        
    }

</script>

<style>

    .X{
        display: block !important;
    }
    
    .nice-select{
display: none !important;
    }
    
</style>

@endsection