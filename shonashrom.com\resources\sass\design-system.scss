// Shonashrom Design System SCSS
// This file contains the base styles for the integrated design system

// Import Bootstrap if needed
// @import '~bootstrap/scss/bootstrap';

// Base variables (these will be overridden by CSS custom properties)
:root {
  // Default color palette
  --primary-color: #3498db;
  --secondary-color: #2c3e50;
  --success-color: #27ae60;
  --warning-color: #f39c12;
  --danger-color: #e74c3c;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  
  // Typography
  --font-family-primary: 'Roboto', sans-serif;
  --font-family-secondary: 'Open Sans', sans-serif;
  --font-size-base: 16px;
  --line-height-base: 1.6;
  
  // Spacing
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 3rem;
  
  // Border radius
  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 1rem;
  
  // Shadows
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  --shadow-md: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
  --shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
  
  // Transitions
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

// Base styles
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
  margin: 0;
  padding: 0;
}

// Utility classes
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-flex { display: flex; }

.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-around { justify-content: space-around; }
.align-items-center { align-items: center; }
.align-items-start { align-items: flex-start; }
.align-items-end { align-items: flex-end; }

// Spacing utilities
@for $i from 0 through 5 {
  .m-#{$i} { margin: #{$i * 0.25}rem; }
  .mt-#{$i} { margin-top: #{$i * 0.25}rem; }
  .mb-#{$i} { margin-bottom: #{$i * 0.25}rem; }
  .ml-#{$i} { margin-left: #{$i * 0.25}rem; }
  .mr-#{$i} { margin-right: #{$i * 0.25}rem; }
  .mx-#{$i} { margin-left: #{$i * 0.25}rem; margin-right: #{$i * 0.25}rem; }
  .my-#{$i} { margin-top: #{$i * 0.25}rem; margin-bottom: #{$i * 0.25}rem; }
  
  .p-#{$i} { padding: #{$i * 0.25}rem; }
  .pt-#{$i} { padding-top: #{$i * 0.25}rem; }
  .pb-#{$i} { padding-bottom: #{$i * 0.25}rem; }
  .pl-#{$i} { padding-left: #{$i * 0.25}rem; }
  .pr-#{$i} { padding-right: #{$i * 0.25}rem; }
  .px-#{$i} { padding-left: #{$i * 0.25}rem; padding-right: #{$i * 0.25}rem; }
  .py-#{$i} { padding-top: #{$i * 0.25}rem; padding-bottom: #{$i * 0.25}rem; }
}

// Component styles
.btn {
  display: inline-block;
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius-md);
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  font-weight: 500;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }
  
  &.btn-primary {
    background-color: var(--primary-button-bg-color, var(--primary-color));
    color: var(--primary-button-txt-color, white);
    
    &:hover {
      background-color: var(--primary-button-hover-bg-color, var(--primary-color));
      color: var(--primary-button-hover-txt-color, white);
    }
  }
  
  &.btn-secondary {
    background-color: var(--secondary-button-bg-color, var(--secondary-color));
    color: var(--secondary-button-txt-color, white);
    
    &:hover {
      background-color: var(--secondary-button-hover-bg-color, var(--secondary-color));
      color: var(--secondary-button-hover-txt-color, white);
    }
  }
}

.card {
  background-color: var(--card-bg-color, white);
  border: 1px solid var(--card-border-color, #e9ecef);
  border-radius: var(--card-border-radius, var(--border-radius-md));
  box-shadow: var(--card-shadow-color, var(--shadow-sm));
  transition: all var(--transition-normal);
  
  &:hover {
    background-color: var(--card-hover-bg-color, #f8f9fa);
    box-shadow: var(--card-hover-shadow-color, var(--shadow-md));
    transform: translateY(-5px);
  }
  
  .card-header {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--card-border-color, #e9ecef);
    background-color: var(--section-alt-bg-color, #f8f9fa);
  }
  
  .card-body {
    padding: var(--spacing-md);
  }
  
  .card-footer {
    padding: var(--spacing-md);
    border-top: 1px solid var(--card-border-color, #e9ecef);
    background-color: var(--section-alt-bg-color, #f8f9fa);
  }
}

.form-control {
  display: block;
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--form-input-border-color, #ced4da);
  border-radius: var(--border-radius-md);
  background-color: var(--form-input-bg-color, white);
  color: var(--form-input-txt-color, #495057);
  transition: border-color var(--transition-normal);
  
  &:focus {
    outline: none;
    border-color: var(--form-input-focus-border-color, var(--primary-color));
    box-shadow: 0 0 0 0.2rem rgba(var(--form-input-focus-border-color), 0.25);
  }
}

.form-group {
  margin-bottom: var(--spacing-md);
  
  label {
    display: block;
    margin-bottom: var(--spacing-xs);
    color: var(--form-label-color, #495057);
    font-weight: 500;
  }
}

// Responsive utilities
@media (max-width: 768px) {
  .d-md-none { display: none; }
  .d-md-block { display: block; }
  .text-md-center { text-align: center; }
  .text-md-left { text-align: left; }
  .text-md-right { text-align: right; }
}

@media (max-width: 576px) {
  .d-sm-none { display: none; }
  .d-sm-block { display: block; }
  .text-sm-center { text-align: center; }
  .text-sm-left { text-align: left; }
  .text-sm-right { text-align: right; }
}

// RTL support
[dir="rtl"] {
  .text-left { text-align: right; }
  .text-right { text-align: left; }
  .ml-auto { margin-left: 0; margin-right: auto; }
  .mr-auto { margin-right: 0; margin-left: auto; }
}

// Animation utilities
.fade-in {
  animation: fadeIn var(--transition-normal) ease-in-out;
}

.slide-up {
  animation: slideUp var(--transition-normal) ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

// Print styles
@media print {
  .no-print { display: none !important; }
  .print-only { display: block !important; }
}
