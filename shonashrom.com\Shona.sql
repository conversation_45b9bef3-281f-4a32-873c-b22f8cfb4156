-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.0
-- https://www.phpmyadmin.net/
--
-- Host: localhost:8889
-- Generation Time: May 29, 2024 at 07:30 PM
-- Server version: 5.7.39
-- PHP Version: 8.2.0

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `Shona`
--

-- --------------------------------------------------------

--
-- Table structure for table `abouts`
--

CREATE TABLE `abouts` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `Image` longtext COLLATE utf8mb4_unicode_ci,
  `Arabic_Desc` longtext COLLATE utf8mb4_unicode_ci,
  `Arabic_Title` longtext COLLATE utf8mb4_unicode_ci,
  `English_Title` longtext COLLATE utf8mb4_unicode_ci,
  `English_Desc` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `abouts`
--

INSERT INTO `abouts` (`id`, `Image`, `Arabic_Desc`, `Arabic_Title`, `English_Title`, `English_Desc`, `created_at`, `updated_at`) VALUES
(1, 'AboutImages/maPORuXWtvAVfyni50jq.jpeg', 'Duis nunc sodales conubia a laoreet aliquet on nostra eleifend lacinia prasent hendrerit quisque penatibus erat a pulvina integer semper ridiculus lectus con dimentum obor tise verodar capmtaso morin', 'Make yourself stronger than your best excuses', 'Make yourself stronger than your best excuses', 'Duis nunc sodales conubia a laoreet aliquet on nostra eleifend lacinia prasent hendrerit quisque penatibus erat a pulvina integer semper ridiculus lectus con dimentum obor tise verodar capmtaso morin', NULL, '2024-03-23 04:15:02');

-- --------------------------------------------------------

--
-- Table structure for table `addersses`
--

CREATE TABLE `addersses` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `Address_Name` longtext COLLATE utf8mb4_unicode_ci,
  `User` longtext COLLATE utf8mb4_unicode_ci,
  `Gov` longtext COLLATE utf8mb4_unicode_ci,
  `City` longtext COLLATE utf8mb4_unicode_ci,
  `Place` longtext COLLATE utf8mb4_unicode_ci,
  `Street` longtext COLLATE utf8mb4_unicode_ci,
  `Bulliding` longtext COLLATE utf8mb4_unicode_ci,
  `Floor` longtext COLLATE utf8mb4_unicode_ci,
  `Flat` longtext COLLATE utf8mb4_unicode_ci,
  `Special_Mark` longtext COLLATE utf8mb4_unicode_ci,
  `Location` longtext COLLATE utf8mb4_unicode_ci,
  `Address_Details` longtext COLLATE utf8mb4_unicode_ci,
  `Address_ID` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `addersses`
--

INSERT INTO `addersses` (`id`, `Address_Name`, `User`, `Gov`, `City`, `Place`, `Street`, `Bulliding`, `Floor`, `Flat`, `Special_Mark`, `Location`, `Address_Details`, `Address_ID`, `created_at`, `updated_at`) VALUES
(1, 'XX', '1', '2', '1', '4', '1', '1', '1ss', '1', '10', '1', '1', '16', '2023-11-20 19:48:37', '2023-11-20 20:25:50'),
(2, 'ee', '2', '2', '1', '4', 'ee', 'ee', 'ee', 'ee', 'ee', 'ee', 'ee', '18', '2023-11-23 08:39:14', '2023-11-23 08:39:14'),
(3, '1', '4', '2', '1', '4', '1', '1', '1', '1', '1', '1', '1', NULL, '2024-03-21 00:50:02', '2024-03-21 00:50:02');

-- --------------------------------------------------------

--
-- Table structure for table `admins`
--

CREATE TABLE `admins` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `image` longtext COLLATE utf8mb4_unicode_ci,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admins`
--

INSERT INTO `admins` (`id`, `name`, `email`, `email_verified_at`, `image`, `password`, `remember_token`, `created_at`, `updated_at`) VALUES
(1, 'Admin', '<EMAIL>', NULL, NULL, '$2y$10$MpzHdrp8RaR5U8IK0G2gg.EMIGFxoFvnQ0IivcJ1GNUgeWu4IR0AG', NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `articles`
--

CREATE TABLE `articles` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `Sub_Image` longtext COLLATE utf8mb4_unicode_ci,
  `Image` longtext COLLATE utf8mb4_unicode_ci,
  `Arabic_Title` longtext COLLATE utf8mb4_unicode_ci,
  `English_Title` longtext COLLATE utf8mb4_unicode_ci,
  `Arabic_Desc` longtext COLLATE utf8mb4_unicode_ci,
  `English_Desc` longtext COLLATE utf8mb4_unicode_ci,
  `Date` longtext COLLATE utf8mb4_unicode_ci,
  `Category` longtext COLLATE utf8mb4_unicode_ci,
  `Author` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `articles`
--

INSERT INTO `articles` (`id`, `Sub_Image`, `Image`, `Arabic_Title`, `English_Title`, `Arabic_Desc`, `English_Desc`, `Date`, `Category`, `Author`, `created_at`, `updated_at`) VALUES
(1, 'ArticlesImages/HvPSxvuu8493XIeAt9dR.jpg', 'ArticlesImages/sjriMIRxEehefpxeWFzE.jpg', 'Magna sea dolor ipsum amet lorem eos', 'Magna sea dolor ipsum amet lorem eos', '<p style=\"color: rgb(107, 106, 117); font-family: &quot;Open Sans&quot;, sans-serif; font-size: 16px; letter-spacing: normal;\">Sadipscing labore amet rebum est et justo gubergren. Et eirmod ipsum sit diam ut magna lorem. Nonumy vero labore lorem sanctus rebum et lorem magna kasd, stet amet magna accusam consetetur eirmod. Kasd accusam sit ipsum sadipscing et at at sanctus et. Ipsum sit gubergren dolores et, consetetur justo invidunt at et aliquyam ut et vero clita. Diam sea sea no sed dolores diam nonumy, gubergren sit stet no diam kasd vero.</p><p style=\"color: rgb(107, 106, 117); font-family: &quot;Open Sans&quot;, sans-serif; font-size: 16px; letter-spacing: normal;\">Voluptua est takimata stet invidunt sed rebum nonumy stet, clita aliquyam dolores vero stet consetetur elitr takimata rebum sanctus. Sit sed accusam stet sit nonumy kasd diam dolores, sanctus lorem kasd duo dolor dolor vero sit et. Labore ipsum duo sanctus amet eos et. Consetetur no sed et aliquyam ipsum justo et, clita lorem sit vero amet amet est dolor elitr, stet et no diam sit. Dolor erat justo dolore sit invidunt.</p><p style=\"color: rgb(107, 106, 117); font-family: &quot;Open Sans&quot;, sans-serif; font-size: 16px; letter-spacing: normal;\">Diam dolor est labore duo invidunt ipsum clita et, sed et lorem voluptua tempor invidunt at est sanctus sanctus. Clita dolores sit kasd diam takimata justo diam lorem sed. Magna amet sed rebum eos. Clita no magna no dolor erat diam tempor rebum consetetur, sanctus labore sed nonumy diam lorem amet eirmod. No at tempor sea diam kasd, takimata ea nonumy elitr sadipscing gubergren erat. Gubergren at lorem invidunt sadipscing rebum sit amet ut ut, voluptua diam dolores at sadipscing stet. Clita dolor amet dolor ipsum vero ea ea eos.</p>', '<div><p style=\"color: rgb(107, 106, 117); font-family: &quot;Open Sans&quot;, sans-serif; font-size: 16px; letter-spacing: normal;\">Sadipscing labore amet rebum est et justo gubergren. Et eirmod ipsum sit diam ut magna lorem. Nonumy vero labore lorem sanctus rebum et lorem magna kasd, stet amet magna accusam consetetur eirmod. Kasd accusam sit ipsum sadipscing et at at sanctus et. Ipsum sit gubergren dolores et, consetetur justo invidunt at et aliquyam ut et vero clita. Diam sea sea no sed dolores diam nonumy, gubergren sit stet no diam kasd vero.</p><p style=\"color: rgb(107, 106, 117); font-family: &quot;Open Sans&quot;, sans-serif; font-size: 16px; letter-spacing: normal;\">Voluptua est takimata stet invidunt sed rebum nonumy stet, clita aliquyam dolores vero stet consetetur elitr takimata rebum sanctus. Sit sed accusam stet sit nonumy kasd diam dolores, sanctus lorem kasd duo dolor dolor vero sit et. Labore ipsum duo sanctus amet eos et. Consetetur no sed et aliquyam ipsum justo et, clita lorem sit vero amet amet est dolor elitr, stet et no diam sit. Dolor erat justo dolore sit invidunt.</p><p style=\"color: rgb(107, 106, 117); font-family: &quot;Open Sans&quot;, sans-serif; font-size: 16px; letter-spacing: normal;\">Diam dolor est labore duo invidunt ipsum clita et, sed et lorem voluptua tempor invidunt at est sanctus sanctus. Clita dolores sit kasd diam takimata justo diam lorem sed. Magna amet sed rebum eos. Clita no magna no dolor erat diam tempor rebum consetetur, sanctus labore sed nonumy diam lorem amet eirmod. No at tempor sea diam kasd, takimata ea nonumy elitr sadipscing gubergren erat. Gubergren at lorem invidunt sadipscing rebum sit amet ut ut, voluptua diam dolores at sadipscing stet. Clita dolor amet dolor ipsum vero ea ea eos.</p></div>', '2023-05-11', 'WEBDESIGN', 'ADMIN', '2023-04-05 06:15:09', '2023-04-05 20:41:03'),
(2, 'ArticlesImages/rL8VIpBNNrrxeUXKWnUr.jpg', 'ArticlesImages/MFM5raRBsrCC7yvlN9Wb.jpg', 'Magna sea dolor ipsum amet lorem eos', 'Magna sea dolor ipsum amet lorem eos', '<a class=\"h4\" href=\"https://orientglorygroup.com/blog.html\" style=\"color: rgb(21, 36, 64); margin-top: 0px; font-family: Barlow, sans-serif; line-height: 1.2; font-size: 1.5rem; letter-spacing: normal; background-color: rgb(246, 246, 246);\">Magna sea dolor ipsum amet lorem eos</a>', '<a class=\"h4\" href=\"https://orientglorygroup.com/blog.html\" style=\"color: rgb(21, 36, 64); margin-top: 0px; font-family: Barlow, sans-serif; line-height: 1.2; font-size: 1.5rem; letter-spacing: normal; background-color: rgb(246, 246, 246);\">Magna sea dolor ipsum amet lorem eos</a>', '2023-04-26', 'WEBDESIGN', 'ADMIN', '2023-04-05 06:15:34', '2023-04-05 06:15:34'),
(3, 'ArticlesImages/nHohNrRI78q3oPPkWvqO.jpg', 'ArticlesImages/mcESxWKuLtOWjq3RsmGB.jpg', 'Magna sea dolor ipsum amet lorem eos', 'Magna sea dolor ipsum amet lorem eos', '<a class=\"h4\" href=\"https://orientglorygroup.com/blog.html\" style=\"color: rgb(21, 36, 64); margin-top: 0px; font-family: Barlow, sans-serif; line-height: 1.2; font-size: 1.5rem; letter-spacing: normal; background-color: rgb(246, 246, 246);\">Magna sea dolor ipsum amet lorem eos</a>', '<a class=\"h4\" href=\"https://orientglorygroup.com/blog.html\" style=\"color: rgb(21, 36, 64); margin-top: 0px; font-family: Barlow, sans-serif; line-height: 1.2; font-size: 1.5rem; letter-spacing: normal; background-color: rgb(246, 246, 246);\">Magna sea dolor ipsum amet lorem eos</a>', '2023-04-23', 'WEBDESIGN', 'ADMIN', '2023-04-05 06:15:57', '2023-04-05 06:15:57');

-- --------------------------------------------------------

--
-- Table structure for table `categories`
--

CREATE TABLE `categories` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `Arabic_Name` longtext COLLATE utf8mb4_unicode_ci,
  `English_Name` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `categories`
--

INSERT INTO `categories` (`id`, `Arabic_Name`, `English_Name`, `created_at`, `updated_at`) VALUES
(1, 'Websites', 'Websites', '2023-06-12 08:55:24', '2023-06-12 11:58:10'),
(2, 'Logos', 'Logos', '2023-06-12 11:58:21', '2023-06-12 11:58:21'),
(3, 'Mobile Apps', 'Mobile Apps', '2023-06-12 11:58:45', '2023-06-12 11:58:45');

-- --------------------------------------------------------

--
-- Table structure for table `cities`
--

CREATE TABLE `cities` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `Arabic_Name` longtext COLLATE utf8mb4_unicode_ci,
  `English_Name` longtext COLLATE utf8mb4_unicode_ci,
  `Ship_Price` longtext COLLATE utf8mb4_unicode_ci,
  `CIT_ID` longtext COLLATE utf8mb4_unicode_ci,
  `GOV_ID` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `cities`
--

INSERT INTO `cities` (`id`, `Arabic_Name`, `English_Name`, `Ship_Price`, `CIT_ID`, `GOV_ID`, `created_at`, `updated_at`) VALUES
(1, 'اكتوبر', 'October', '10', '1', '2', '2023-11-20 14:57:49', '2023-11-20 14:57:49'),
(2, 'Zamalek', 'Zamalek', '20', '2', '2', '2023-11-20 14:57:49', '2023-11-20 14:57:49'),
(3, 'Maadi', 'Maadi', '30', '4', '1', '2023-11-20 14:57:49', '2023-11-20 14:57:49'),
(4, 'Nasr City', 'Nasr City', '40', '5', '1', '2023-11-20 14:57:49', '2023-11-20 14:57:49'),
(5, 'شرم الشيخ', 'شرم الشيخ', '50', '6', '4', '2023-11-20 14:57:49', '2023-11-20 14:57:49'),
(6, 'الطور', 'الطور', '70', '7', '4', '2023-11-20 14:57:49', '2023-11-20 14:57:49'),
(7, 'دهب', 'دهب', '80', '8', '4', '2023-11-20 14:57:49', '2023-11-20 14:57:49'),
(8, 'aa', 'aa', '11', NULL, '5', '2024-03-21 00:38:39', '2024-03-21 00:38:39');

-- --------------------------------------------------------

--
-- Table structure for table `comments`
--

CREATE TABLE `comments` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `Comment` longtext COLLATE utf8mb4_unicode_ci,
  `Status` longtext COLLATE utf8mb4_unicode_ci,
  `User` longtext COLLATE utf8mb4_unicode_ci,
  `Product` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `comments`
--

INSERT INTO `comments` (`id`, `Comment`, `Status`, `User`, `Product`, `created_at`, `updated_at`) VALUES
(2, 'thanks', '1', '4', '2', '2024-03-21 00:45:21', '2024-03-21 00:46:33'),
(3, 'Hio', '1', '5', '2', '2024-03-23 06:34:29', '2024-03-23 06:35:18');

-- --------------------------------------------------------

--
-- Table structure for table `company_data`
--

CREATE TABLE `company_data` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `Name` longtext COLLATE utf8mb4_unicode_ci,
  `NameEn` longtext COLLATE utf8mb4_unicode_ci,
  `Logo` longtext COLLATE utf8mb4_unicode_ci,
  `Icon` longtext COLLATE utf8mb4_unicode_ci,
  `Logo_Store` longtext COLLATE utf8mb4_unicode_ci,
  `Icon_Store` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `company_data`
--

INSERT INTO `company_data` (`id`, `Name`, `NameEn`, `Logo`, `Icon`, `Logo_Store`, `Icon_Store`, `created_at`, `updated_at`) VALUES
(1, 'Shona Shorm', 'Shona Shorm', 'LogoImages/4loxBchsPAWjmnBgvq2u.png', 'IconImages/c76pLRxt8iKiILhFOck2.png', 'LogoImages/BCNdc3plDIjWKXID3P5U.png', 'LogoImages/1IApQsmfs6HdfZKmDh6b.png', NULL, '2024-03-23 03:26:46');

-- --------------------------------------------------------

--
-- Table structure for table `failed_jobs`
--

CREATE TABLE `failed_jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `features`
--

CREATE TABLE `features` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `Arabic_Title` longtext COLLATE utf8mb4_unicode_ci,
  `English_Title` longtext COLLATE utf8mb4_unicode_ci,
  `Icon` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `features`
--

INSERT INTO `features` (`id`, `Arabic_Title`, `English_Title`, `Icon`, `created_at`, `updated_at`) VALUES
(1, 'Qualified Instructor', 'Qualified Instructor', 'WebSliderImages/UXN3RD4hwCUCLZU8rbvk.png', NULL, '2024-03-23 04:17:40'),
(2, 'Dedicated Services', 'Dedicated Services', 'WebSliderImages/wHsGMnnIzwo0AJeU4yQj.png', NULL, '2024-03-23 04:17:30'),
(3, 'Award Programs', 'Award Programs', 'WebSliderImages/oNOA8huIai9WEekUUos3.png', NULL, '2024-03-23 04:17:24'),
(4, 'Organic Proteins', 'Organic Proteins', 'WebSliderImages/lo47ks7yZbxapkBCTajR.png', NULL, '2024-03-23 04:17:35');

-- --------------------------------------------------------

--
-- Table structure for table `footers`
--

CREATE TABLE `footers` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `Arabic_Desc` longtext COLLATE utf8mb4_unicode_ci,
  `English_Desc` longtext COLLATE utf8mb4_unicode_ci,
  `Arabic_Address` longtext COLLATE utf8mb4_unicode_ci,
  `English_Address` longtext COLLATE utf8mb4_unicode_ci,
  `Phone` longtext COLLATE utf8mb4_unicode_ci,
  `Phone2` longtext COLLATE utf8mb4_unicode_ci,
  `Email` longtext COLLATE utf8mb4_unicode_ci,
  `Video` longtext COLLATE utf8mb4_unicode_ci,
  `Map` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `footers`
--

INSERT INTO `footers` (`id`, `Arabic_Desc`, `English_Desc`, `Arabic_Address`, `English_Address`, `Phone`, `Phone2`, `Email`, `Video`, `Map`, `created_at`, `updated_at`) VALUES
(1, 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor indidunt ut labore et dolore magna', 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor indidunt ut labore et dolore magna', '25/7 Barden, London', '25/7 Barden, London', '+201507503008', '+201155470900', '<EMAIL>', 'https://www.youtube.com/embed/0m1q8QKxdWE', '<iframe src=\"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d13817.189479992634!2d30.976159350000003!3d30.028325499999998!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x145859ba2d49096b%3A0x3a7a6b87d37ed657!2sThe%20Gate%20Plaza%20Mall!5e0!3m2!1sen!2seg!4v1685795553326!5m2!1sen!2seg\" width=\"500\" height=\"200\" style=\"border:0;\" allowfullscreen=\"\" loading=\"lazy\" referrerpolicy=\"no-referrer-when-downgrade\"></iframe>', NULL, '2023-11-18 12:28:16');

-- --------------------------------------------------------

--
-- Table structure for table `f_a_q_s`
--

CREATE TABLE `f_a_q_s` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `Arabic_Question` longtext COLLATE utf8mb4_unicode_ci,
  `English_Question` longtext COLLATE utf8mb4_unicode_ci,
  `Arabic_Answer` longtext COLLATE utf8mb4_unicode_ci,
  `English_Answer` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `f_a_q_s`
--

INSERT INTO `f_a_q_s` (`id`, `Arabic_Question`, `English_Question`, `Arabic_Answer`, `English_Answer`, `created_at`, `updated_at`) VALUES
(1, 'Lorem ipsum dolor sit amet.', 'Lorem ipsum dolor sit amet.', 'Lorem ipsum dolor sit amet.', 'Lorem ipsum dolor sit amet.', '2023-11-18 07:07:52', '2023-11-18 16:07:35'),
(2, 'safasfd', 'asdasd', 'asdasd', 'asdasd', '2023-11-18 16:08:21', '2023-11-18 16:08:21');

-- --------------------------------------------------------

--
-- Table structure for table `galleries`
--

CREATE TABLE `galleries` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `Image` longtext COLLATE utf8mb4_unicode_ci,
  `Category` bigint(20) UNSIGNED DEFAULT NULL,
  `Arabic_Name` longtext COLLATE utf8mb4_unicode_ci,
  `English_Name` longtext COLLATE utf8mb4_unicode_ci,
  `Links` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `galleries`
--

INSERT INTO `galleries` (`id`, `Image`, `Category`, `Arabic_Name`, `English_Name`, `Links`, `created_at`, `updated_at`) VALUES
(1, 'ServicesImages/2xBRCApSZn8slDuKCDG3.png', 1, 'Couple Fitness Workout', 'Couple Fitness Workout', 'https://www.facebook.com/', '2023-06-12 11:59:00', '2024-03-23 04:17:58'),
(2, 'ServicesImages/GAw3k6nUsevHn0bdGvGR.png', 1, 'Couple Fitness Workout', 'Couple Fitness Workout', 'https://www.facebook.com/', '2023-06-12 11:59:12', '2024-03-23 04:18:05'),
(3, 'ServicesImages/BvSl00q3aBXGZic8RYpl.png', 2, 'Couple Fitness Workout', 'Couple Fitness Workout', 'https://www.facebook.com/', '2023-06-12 11:59:34', '2024-03-23 04:18:21');

-- --------------------------------------------------------

--
-- Table structure for table `governrates`
--

CREATE TABLE `governrates` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `Arabic_Name` longtext COLLATE utf8mb4_unicode_ci,
  `English_Name` longtext COLLATE utf8mb4_unicode_ci,
  `GOV_ID` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `governrates`
--

INSERT INTO `governrates` (`id`, `Arabic_Name`, `English_Name`, `GOV_ID`, `created_at`, `updated_at`) VALUES
(1, 'الجيزه', 'الجيزه', '2', '2023-11-20 14:57:37', '2023-11-20 14:57:37'),
(2, 'القاهره', 'القاهره', '1', '2023-11-20 14:57:37', '2023-11-20 14:57:37'),
(3, 'جنوب سيناء', 'جنوب سيناء', '4', '2023-11-20 14:57:37', '2023-11-20 14:57:37'),
(4, 'الرياض', 'Riyadh', '5', '2023-11-20 14:57:37', '2023-11-20 14:57:37'),
(5, 'ee', 'ee', NULL, '2024-03-21 00:38:29', '2024-03-21 00:38:29');

-- --------------------------------------------------------

--
-- Table structure for table `groups`
--

CREATE TABLE `groups` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `Group_ID` longtext COLLATE utf8mb4_unicode_ci,
  `Name` longtext COLLATE utf8mb4_unicode_ci,
  `NameEn` longtext COLLATE utf8mb4_unicode_ci,
  `Image` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `groups`
--

INSERT INTO `groups` (`id`, `Group_ID`, `Name`, `NameEn`, `Image`, `created_at`, `updated_at`) VALUES
(1, '34', 'الاحذيه', 'Shoes', 'http://localhost:8888/laravel/ERP/public/ItemsGroupsImages/KdkKDFpWOxEeeqoTWyHk.png', '2023-11-23 08:34:36', '2023-11-23 08:34:36'),
(2, '35', 'Cosmetics', 'Cosmetics', 'ArticlesImages/teEc2AdzQ2IWyyS2dCwF.png', '2023-11-23 08:34:36', '2024-03-20 23:58:36'),
(3, '38', 'Jewellry', 'Jewellry', 'http://localhost:8888/laravel/ERP/public/ItemsGroupsImages/EUIST2lfVdd4dMSfY2qV.png', '2023-11-23 08:34:36', '2023-11-23 08:34:36'),
(4, '33', 'الغذاء', 'Dinner', 'http://localhost:8888/laravel/ERP/public/ItemsGroupsImages/DgfYOiGEawGhtBp3wxD9.png', '2023-11-23 08:34:36', '2023-11-23 08:34:36');

-- --------------------------------------------------------

--
-- Table structure for table `how_work_icons`
--

CREATE TABLE `how_work_icons` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `Icon` longtext COLLATE utf8mb4_unicode_ci,
  `Arabic_Title` longtext COLLATE utf8mb4_unicode_ci,
  `English_Title` longtext COLLATE utf8mb4_unicode_ci,
  `Arabic_Desc` longtext COLLATE utf8mb4_unicode_ci,
  `English_Desc` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `how_work_icons`
--

INSERT INTO `how_work_icons` (`id`, `Icon`, `Arabic_Title`, `English_Title`, `Arabic_Desc`, `English_Desc`, `created_at`, `updated_at`) VALUES
(1, 'WebSliderImages/8ShUYvhvnaSWdIsAKsaz.png', 'Trained People', 'Trained People', 'Whey Vanilla Ice Cream, 5 Lbs.', 'Whey Vanilla Ice Cream, 5 Lbs.', NULL, '2024-03-23 04:23:42'),
(2, 'WebSliderImages/TgA4zOoTQQKHorLfeTYe.png', 'Trained People', 'Trained People', 'Buy with installments and pay EGP 76.35 for 60 months with select banks.', 'Buy with installments and pay EGP 76.35 for 60 months with select banks.', NULL, '2024-03-23 04:23:49'),
(3, 'WebSliderImages/T88d8dyozkfd2xlBJJxz.png', 'Trained People', 'Trained People', 'Vulputate nam massa pellentesque accumsan hyme lectus parturient sultan and interdum mattis.', 'Vulputate nam massa pellentesque accumsan hyme lectus parturient sultan and interdum mattis.', NULL, '2024-03-23 04:23:57'),
(4, 'WebSliderImages/jzPmAssqKw9xWHQPwyFo.png', 'Trained People', 'Trained People', 'Optimum Nutrition Gold Standard 100%', 'Optimum Nutrition Gold Standard 100%', NULL, '2024-03-23 04:24:09');

-- --------------------------------------------------------

--
-- Table structure for table `i_s_p_results`
--

CREATE TABLE `i_s_p_results` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `Arabic_Desc` longtext COLLATE utf8mb4_unicode_ci,
  `English_Desc` longtext COLLATE utf8mb4_unicode_ci,
  `Arabic_Title` longtext COLLATE utf8mb4_unicode_ci,
  `English_Title` longtext COLLATE utf8mb4_unicode_ci,
  `Image` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `i_s_p_results`
--

INSERT INTO `i_s_p_results` (`id`, `Arabic_Desc`, `English_Desc`, `Arabic_Title`, `English_Title`, `Image`, `created_at`, `updated_at`) VALUES
(1, 'Ridiculus vestibulum tempus lacinia hendrerit sociis mauris disersn dictum tempus ate justo laoreet ipsum hymenaeos proin imperdiet. Eule Habitant fusce vivamus sollicitudin. Suscipit faucibus tortor sodales donec velit urna ante magnis id socis urna volutpat risus neque auctor etiam et lacinia urna class sociosqu, libero laoreet senectus class risus penatibus habitant uteir ipsum tempor purus sem rutrum. Posuere vitae siteet dictumst sociosqu dictum porttitor sitevar neque an enim pellentesque venenatis mattis bibendum, adipiscing litora erater an diam leo rutrum. At purus sodales malesuada primis lectus tortor dolor odio nunc fermentum magna. Congue pulvinar turpis bibendum sollicitudi cras sagittis, consectetuer sapien enim dolor faucibus tempus. Malesuada justo an montes condime ntum faucibus cubilia commodo sapien adipiscing', 'Ridiculus vestibulum tempus lacinia hendrerit sociis mauris disersn dictum tempus ate justo laoreet ipsum hymenaeos proin imperdiet. Eule Habitant fusce vivamus sollicitudin. Suscipit faucibus tortor sodales donec velit urna ante magnis id socis urna volutpat risus neque auctor etiam et lacinia urna class sociosqu, libero laoreet senectus class risus penatibus habitant uteir ipsum tempor purus sem rutrum. Posuere vitae siteet dictumst sociosqu dictum porttitor sitevar neque an enim pellentesque venenatis mattis bibendum, adipiscing litora erater an diam leo rutrum. At purus sodales malesuada primis lectus tortor dolor odio nunc fermentum magna. Congue pulvinar turpis bibendum sollicitudi cras sagittis, consectetuer sapien enim dolor faucibus tempus. Malesuada justo an montes condime ntum faucibus cubilia commodo sapien adipiscing', 'Pulvinar neces velo nostra ligula quisque aptent', 'Pulvinar neces velo nostra ligula quisque aptent', 'AboutImages/8YV63b3WcW8E4c5wDBdv.jpg', NULL, '2023-11-20 13:12:43');

-- --------------------------------------------------------

--
-- Table structure for table `i_s_p_s`
--

CREATE TABLE `i_s_p_s` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `Name` longtext COLLATE utf8mb4_unicode_ci,
  `Height` longtext COLLATE utf8mb4_unicode_ci,
  `HeightUnit` longtext COLLATE utf8mb4_unicode_ci,
  `Weight` longtext COLLATE utf8mb4_unicode_ci,
  `WightUnit` longtext COLLATE utf8mb4_unicode_ci,
  `Age` longtext COLLATE utf8mb4_unicode_ci,
  `Gender` longtext COLLATE utf8mb4_unicode_ci,
  `Sports` longtext COLLATE utf8mb4_unicode_ci,
  `Moshr_Kotlt_Gsm` longtext COLLATE utf8mb4_unicode_ci,
  `ZaydtTool` longtext COLLATE utf8mb4_unicode_ci,
  `ZaydtToolToInch` longtext COLLATE utf8mb4_unicode_ci,
  `WaznMsaly` longtext COLLATE utf8mb4_unicode_ci,
  `mkdar_wazn_mtlop_fkdanh` longtext COLLATE utf8mb4_unicode_ci,
  `saarat_hararya_mtlop_fkdnah` longtext COLLATE utf8mb4_unicode_ci,
  `waktMatwkaaLlwsolWaznMasaly` longtext COLLATE utf8mb4_unicode_ci,
  `3dd3bwatOstegy` longtext COLLATE utf8mb4_unicode_ci,
  `So3ratYomen` longtext COLLATE utf8mb4_unicode_ci,
  `so3ratPoortinat` longtext COLLATE utf8mb4_unicode_ci,
  `gramProtinat` longtext COLLATE utf8mb4_unicode_ci,
  `User` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `i_s_p_s`
--

INSERT INTO `i_s_p_s` (`id`, `Name`, `Height`, `HeightUnit`, `Weight`, `WightUnit`, `Age`, `Gender`, `Sports`, `Moshr_Kotlt_Gsm`, `ZaydtTool`, `ZaydtToolToInch`, `WaznMsaly`, `mkdar_wazn_mtlop_fkdanh`, `saarat_hararya_mtlop_fkdnah`, `waktMatwkaaLlwsolWaznMasaly`, `3dd3bwatOstegy`, `So3ratYomen`, `so3ratPoortinat`, `gramProtinat`, `User`, `created_at`, `updated_at`) VALUES
(6, 'Goooo', '169', 'CM', '88', 'KG', '30', 'Male', '0', '30.811246104828', '0.17', '6.6929', '65.636989', '22.363011', '172195.1847', '11.1815055', '13.4178066', '2522.52', '630.63', '189.189', '1', '2023-11-20 16:09:42', '2023-11-20 16:09:42'),
(7, 'Loay', '170', 'CM', '100', 'KG', '33', 'Male', '0', '34.602076124567', '0.18', '7.0866', '66.192106', '33.807894', '260320.7838', '16.903947', '20.2847364', '2866.5', '716.625', '214.9875', '2', '2023-11-23 08:41:59', '2023-11-23 08:41:59'),
(9, 'Kikoooo', '169', 'CM', '79', 'KG', '31', 'Male', '0', '27.660095935016', '0.17', '6.6929', '65.636989', '13.363011', '102895.1847', '6.6815055', '8.0178066', '2264.535', '566.13375', '169.840125', '4', '2024-03-21 01:15:26', '2024-03-21 01:15:26');

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` int(10) UNSIGNED NOT NULL,
  `migration` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `migrations`
--

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(1, '2019_12_14_000001_create_personal_access_tokens_table', 1),
(2, '2023_10_09_070644_create_f_a_q_s_table', 1),
(3, '2023_10_09_073515_create_features_table', 2),
(4, '2023_11_18_092350_create_groups_table', 3),
(5, '2023_11_18_112505_create_products_table', 4),
(6, '2023_11_18_112516_create_products_imags_table', 4),
(7, '2023_11_19_224950_create_i_s_p_s_table', 5),
(8, '2023_11_20_130856_create_comments_table', 6),
(9, '2023_11_20_130916_create_rates_table', 6),
(10, '2023_11_20_150300_create_i_s_p_results_table', 7),
(11, '2023_11_20_162346_create_governrates_table', 8),
(12, '2023_11_20_162356_create_cities_table', 8),
(13, '2023_11_20_162403_create_places_table', 8),
(14, '2023_11_20_204140_create_addersses_table', 9),
(15, '2023_11_21_025657_create_orders_table', 10),
(16, '2023_11_21_030809_create_product_orders_table', 10);

-- --------------------------------------------------------

--
-- Table structure for table `orders`
--

CREATE TABLE `orders` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `Date` longtext COLLATE utf8mb4_unicode_ci,
  `Payment_Method` longtext COLLATE utf8mb4_unicode_ci,
  `Product_Numbers` longtext COLLATE utf8mb4_unicode_ci,
  `Total_Qty` longtext COLLATE utf8mb4_unicode_ci,
  `Total_Price` longtext COLLATE utf8mb4_unicode_ci,
  `Shipping` longtext COLLATE utf8mb4_unicode_ci,
  `Name` longtext COLLATE utf8mb4_unicode_ci,
  `Email` longtext COLLATE utf8mb4_unicode_ci,
  `Phone` longtext COLLATE utf8mb4_unicode_ci,
  `OtherPhone` longtext COLLATE utf8mb4_unicode_ci,
  `Address_Name` longtext COLLATE utf8mb4_unicode_ci,
  `Special_MarkAdd` longtext COLLATE utf8mb4_unicode_ci,
  `StreetAdd` longtext COLLATE utf8mb4_unicode_ci,
  `BulidingAdd` longtext COLLATE utf8mb4_unicode_ci,
  `FloorAdd` longtext COLLATE utf8mb4_unicode_ci,
  `FlatAdd` longtext COLLATE utf8mb4_unicode_ci,
  `Governrate` longtext COLLATE utf8mb4_unicode_ci,
  `City` longtext COLLATE utf8mb4_unicode_ci,
  `Place` longtext COLLATE utf8mb4_unicode_ci,
  `LocationAdd` longtext COLLATE utf8mb4_unicode_ci,
  `Address_DetailsAdd` longtext COLLATE utf8mb4_unicode_ci,
  `USER_ID` longtext COLLATE utf8mb4_unicode_ci,
  `User` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `orders`
--

INSERT INTO `orders` (`id`, `Date`, `Payment_Method`, `Product_Numbers`, `Total_Qty`, `Total_Price`, `Shipping`, `Name`, `Email`, `Phone`, `OtherPhone`, `Address_Name`, `Special_MarkAdd`, `StreetAdd`, `BulidingAdd`, `FloorAdd`, `FlatAdd`, `Governrate`, `City`, `Place`, `LocationAdd`, `Address_DetailsAdd`, `USER_ID`, `User`, `created_at`, `updated_at`) VALUES
(40, '2024-03-21', 'Later', '1', '4', '54', '10', 'Kikoooo', '<EMAIL>', '099999', NULL, '1', '1', '1', '1', '1', '1', '2', '1', '4', '1', '1', NULL, '4', NULL, NULL),
(41, '2024-03-23', 'Later', '1', '3', '43', '10', 'Kikoooo', '<EMAIL>', '099999', NULL, '1', '1', '1', '1', '1', '1', '2', '1', '4', '1', '1', NULL, '4', NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `password_resets`
--

CREATE TABLE `password_resets` (
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `personal_access_tokens`
--

CREATE TABLE `personal_access_tokens` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `tokenable_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tokenable_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `abilities` text COLLATE utf8mb4_unicode_ci,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `places`
--

CREATE TABLE `places` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `Arabic_Name` longtext COLLATE utf8mb4_unicode_ci,
  `English_Name` longtext COLLATE utf8mb4_unicode_ci,
  `Ship_Price` longtext COLLATE utf8mb4_unicode_ci,
  `CIT_ID` longtext COLLATE utf8mb4_unicode_ci,
  `PLACE_ID` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `places`
--

INSERT INTO `places` (`id`, `Arabic_Name`, `English_Name`, `Ship_Price`, `CIT_ID`, `PLACE_ID`, `created_at`, `updated_at`) VALUES
(1, 'شارع ٩', 'شارع ٩', '10', '4', '1', '2023-11-20 14:58:48', '2023-11-20 14:58:48'),
(2, 'الحي ٧', 'الحي ٧', '20', '5', '2', '2023-11-20 14:58:48', '2023-11-20 14:58:48'),
(3, 'شارع البرازيل', 'شارع البرازيل', '10', '2', '3', '2023-11-20 14:58:48', '2023-11-20 14:58:48'),
(4, 'الحصري', 'الحصري', '20', '1', '4', '2023-11-20 14:58:48', '2023-11-20 14:58:48'),
(5, 'rr', 'rr', '44', '8', NULL, '2024-03-21 00:38:50', '2024-03-21 00:38:50');

-- --------------------------------------------------------

--
-- Table structure for table `privacies`
--

CREATE TABLE `privacies` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `Arabic_Desc` longtext COLLATE utf8mb4_unicode_ci,
  `English_Desc` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `privacies`
--

INSERT INTO `privacies` (`id`, `Arabic_Desc`, `English_Desc`, `created_at`, `updated_at`) VALUES
(1, '<section style=\"display: flex; flex-wrap: wrap; max-width: 100%; position: relative; width: 2237px; color: rgb(62, 74, 89); font-family: &quot;Nunito Sans&quot;; font-size: 17px; letter-spacing: normal;\"><div class=\"w-100 pt-70 position-relative\" style=\"width: 2237px; padding-top: 4.375rem;\"><div class=\"container\" style=\"width: 1566px; padding: 0px; max-width: 1566px;\"><div class=\"content-wrap position-relative w-100\" style=\"width: 1566px;\"><h1 style=\"margin-bottom: 2.1875rem; font-weight: 700; line-height: 1.2; font-size: 3.125rem; color: var(--color1); font-family: var(--Open-Sans);\">Dividers</h1><p style=\"margin-bottom: 1.7rem; color: var(--color1); line-height: 1.875rem; font-size: 1.0625rem; opacity: 0.8;\">Nullam sodales ullamcorper odio sed efficitur. Orci varius natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Aliquam aliquam velit nisi, vitae porttitor est finibus in. Cras mollis tincidunt purus, ut pretium turpis eleifend eu. Vivamus consequat mi sagittis, bibendum nulla et, fermentum eros. Sed blandit odio a nibh porttitor aliquet. Morbi efficitur mollis pretium. Sed et tortor dui.</p><div class=\"devider bg-color9 mt-40 mb-40 position-relative w-100\" style=\"width: 1566px; background-color: var(--color17); margin-top: 2.5rem; margin-bottom: 2.5rem; opacity: 1; height: 1px;\"></div><p style=\"margin-bottom: 1.7rem; color: var(--color1); line-height: 1.875rem; font-size: 1.0625rem; opacity: 0.8;\">Nullam sodales ullamcorper odio sed efficitur. Orci varius natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Aliquam aliquam velit nisi, vitae porttitor est finibus in. Cras mollis tincidunt purus, ut pretium turpis eleifend eu. Vivamus consequat mi sagittis, bibendum nulla et, fermentum eros. Sed blandit odio a nibh porttitor aliquet. Morbi efficitur mollis pretium. Sed et tortor dui.</p><div class=\"devider bg-color25 mt-40 mb-40 position-relative text-center w-100\" style=\"width: 1566px; background-color: var(--color25); margin-top: 2.5rem; margin-bottom: 2.5rem; opacity: 1; height: 1px;\"><i class=\"btm-ln v2 bg-color9\" style=\"transition: var(--transition2); background-color: var(--color17); bottom: 0px; height: inherit; position: absolute; width: 6px; left: 783px; transform: translateX(-50%);\"></i></div><p style=\"margin-bottom: 1.7rem; color: var(--color1); line-height: 1.875rem; font-size: 1.0625rem; opacity: 0.8;\">Nullam sodales ullamcorper odio sed efficitur. Orci varius natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Aliquam aliquam velit nisi, vitae porttitor est finibus in. Cras mollis tincidunt purus, ut pretium turpis eleifend eu. Vivamus consequat mi sagittis, bibendum nulla et, fermentum eros. Sed blandit odio a nibh porttitor aliquet. Morbi efficitur mollis pretium. Sed et tortor dui.</p><div class=\"devider mt-40 mb-40 position-relative text-center w-100\" style=\"width: 1566px; margin-top: 2.5rem; margin-bottom: 2.5rem; opacity: 1; height: 1px;\"><i class=\"btm-ln bg-color16\" style=\"transition: var(--transition2); background-color: var(--color16); bottom: -10px; height: inherit; position: absolute; width: 3.4375rem; left: 783px; transform: translateX(-50%);\"></i></div><p style=\"margin-bottom: 1.7rem; color: var(--color1); line-height: 1.875rem; font-size: 1.0625rem; opacity: 0.8;\">Nullam sodales ullamcorper odio sed efficitur. Orci varius natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Aliquam aliquam velit nisi, vitae porttitor est finibus in. Cras mollis tincidunt purus, ut pretium turpis eleifend eu. Vivamus consequat mi sagittis, bibendum nulla et, fermentum eros. Sed blandit odio a nibh porttitor aliquet. Morbi efficitur mollis pretium. Sed et tortor dui.</p><div class=\"devider v2 mt-40 position-relative w-100\" style=\"width: 1566px; margin-top: 2.5rem; opacity: 1; height: 1px; background-image: -webkit-linear-gradient(90deg, transparent, transparent 50%, rgb(255, 255, 255) 50%, rgb(255, 255, 255) 100%), linear-gradient(90deg, rgb(229, 229, 229), rgb(229, 229, 229)); background-size: 20px 3px, 100% 3px;\"></div></div></div></div></section><footer style=\"display: flex; flex-wrap: wrap; max-width: 100%; position: relative; width: 2237px; color: rgb(62, 74, 89); font-family: &quot;Nunito Sans&quot;; font-size: 17px; letter-spacing: normal;\"><div class=\"w-100 pt-40 bg-color11 position-relative\" style=\"width: 2237px; background-color: var(--color11); padding-top: 2.5rem; background-image: url(&quot;../images/resources/slide-bg1.jpg&quot;); background-repeat: no-repeat; background-size: cover;\"><div class=\"container\" style=\"width: 1566px; padding: 0px; max-width: 1566px;\"><div class=\"logo text-center w-100\" style=\"width: 1566px;\"></div></div></div></footer>', '<section style=\"display: flex; flex-wrap: wrap; max-width: 100%; position: relative; width: 2237px; color: rgb(62, 74, 89); font-family: &quot;Nunito Sans&quot;; font-size: 17px; letter-spacing: normal;\"><div class=\"w-100 pt-70 position-relative\" style=\"width: 2237px; padding-top: 4.375rem;\"><div class=\"container\" style=\"width: 1566px; padding: 0px; max-width: 1566px;\"><div class=\"content-wrap position-relative w-100\" style=\"width: 1566px;\"><h1 style=\"margin-bottom: 2.1875rem; font-weight: 700; line-height: 1.2; font-size: 3.125rem; color: var(--color1); font-family: var(--Open-Sans);\">Dividers</h1><p style=\"margin-bottom: 1.7rem; color: var(--color1); line-height: 1.875rem; font-size: 1.0625rem; opacity: 0.8;\">Nullam sodales ullamcorper odio sed efficitur. Orci varius natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Aliquam aliquam velit nisi, vitae porttitor est finibus in. Cras mollis tincidunt purus, ut pretium turpis eleifend eu. Vivamus consequat mi sagittis, bibendum nulla et, fermentum eros. Sed blandit odio a nibh porttitor aliquet. Morbi efficitur mollis pretium. Sed et tortor dui.</p><div class=\"devider bg-color9 mt-40 mb-40 position-relative w-100\" style=\"width: 1566px; background-color: var(--color17); margin-top: 2.5rem; margin-bottom: 2.5rem; opacity: 1; height: 1px;\"></div><p style=\"margin-bottom: 1.7rem; color: var(--color1); line-height: 1.875rem; font-size: 1.0625rem; opacity: 0.8;\">Nullam sodales ullamcorper odio sed efficitur. Orci varius natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Aliquam aliquam velit nisi, vitae porttitor est finibus in. Cras mollis tincidunt purus, ut pretium turpis eleifend eu. Vivamus consequat mi sagittis, bibendum nulla et, fermentum eros. Sed blandit odio a nibh porttitor aliquet. Morbi efficitur mollis pretium. Sed et tortor dui.</p><div class=\"devider bg-color25 mt-40 mb-40 position-relative text-center w-100\" style=\"width: 1566px; background-color: var(--color25); margin-top: 2.5rem; margin-bottom: 2.5rem; opacity: 1; height: 1px;\"><i class=\"btm-ln v2 bg-color9\" style=\"transition: var(--transition2); background-color: var(--color17); bottom: 0px; height: inherit; position: absolute; width: 6px; left: 783px; transform: translateX(-50%);\"></i></div><p style=\"margin-bottom: 1.7rem; color: var(--color1); line-height: 1.875rem; font-size: 1.0625rem; opacity: 0.8;\">Nullam sodales ullamcorper odio sed efficitur. Orci varius natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Aliquam aliquam velit nisi, vitae porttitor est finibus in. Cras mollis tincidunt purus, ut pretium turpis eleifend eu. Vivamus consequat mi sagittis, bibendum nulla et, fermentum eros. Sed blandit odio a nibh porttitor aliquet. Morbi efficitur mollis pretium. Sed et tortor dui.</p><div class=\"devider mt-40 mb-40 position-relative text-center w-100\" style=\"width: 1566px; margin-top: 2.5rem; margin-bottom: 2.5rem; opacity: 1; height: 1px;\"><i class=\"btm-ln bg-color16\" style=\"transition: var(--transition2); background-color: var(--color16); bottom: -10px; height: inherit; position: absolute; width: 3.4375rem; left: 783px; transform: translateX(-50%);\"></i></div><p style=\"margin-bottom: 1.7rem; color: var(--color1); line-height: 1.875rem; font-size: 1.0625rem; opacity: 0.8;\">Nullam sodales ullamcorper odio sed efficitur. Orci varius natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Aliquam aliquam velit nisi, vitae porttitor est finibus in. Cras mollis tincidunt purus, ut pretium turpis eleifend eu. Vivamus consequat mi sagittis, bibendum nulla et, fermentum eros. Sed blandit odio a nibh porttitor aliquet. Morbi efficitur mollis pretium. Sed et tortor dui.</p><div class=\"devider v2 mt-40 position-relative w-100\" style=\"width: 1566px; margin-top: 2.5rem; opacity: 1; height: 1px; background-image: -webkit-linear-gradient(90deg, transparent, transparent 50%, rgb(255, 255, 255) 50%, rgb(255, 255, 255) 100%), linear-gradient(90deg, rgb(229, 229, 229), rgb(229, 229, 229)); background-size: 20px 3px, 100% 3px;\"></div></div></div></div></section><footer style=\"display: flex; flex-wrap: wrap; max-width: 100%; position: relative; width: 2237px; color: rgb(62, 74, 89); font-family: &quot;Nunito Sans&quot;; font-size: 17px; letter-spacing: normal;\"><div class=\"w-100 pt-40 bg-color11 position-relative\" style=\"width: 2237px; background-color: var(--color11); padding-top: 2.5rem; background-image: url(&quot;../images/resources/slide-bg1.jpg&quot;); background-repeat: no-repeat; background-size: cover;\"><div class=\"container\" style=\"width: 1566px; padding: 0px; max-width: 1566px;\"><div class=\"logo text-center w-100\" style=\"width: 1566px;\"></div></div></div></footer>', NULL, '2023-10-03 14:45:40');

-- --------------------------------------------------------

--
-- Table structure for table `products`
--

CREATE TABLE `products` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `Pro_ID` longtext COLLATE utf8mb4_unicode_ci,
  `Group_ID` longtext COLLATE utf8mb4_unicode_ci,
  `P_Ar_Name` longtext COLLATE utf8mb4_unicode_ci,
  `P_En_Name` longtext COLLATE utf8mb4_unicode_ci,
  `Ar_Desc` longtext COLLATE utf8mb4_unicode_ci,
  `En_Desc` longtext COLLATE utf8mb4_unicode_ci,
  `Ar_Spec` longtext COLLATE utf8mb4_unicode_ci,
  `En_Spec` longtext COLLATE utf8mb4_unicode_ci,
  `Ar_Sup_Desc` longtext COLLATE utf8mb4_unicode_ci,
  `En_Sup_Desc` longtext COLLATE utf8mb4_unicode_ci,
  `Image` longtext COLLATE utf8mb4_unicode_ci,
  `Price` longtext COLLATE utf8mb4_unicode_ci,
  `Offer_Price` longtext COLLATE utf8mb4_unicode_ci,
  `Symbol` longtext COLLATE utf8mb4_unicode_ci,
  `Type` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `products`
--

INSERT INTO `products` (`id`, `Pro_ID`, `Group_ID`, `P_Ar_Name`, `P_En_Name`, `Ar_Desc`, `En_Desc`, `Ar_Spec`, `En_Spec`, `Ar_Sup_Desc`, `En_Sup_Desc`, `Image`, `Price`, `Offer_Price`, `Symbol`, `Type`, `created_at`, `updated_at`) VALUES
(2, NULL, '2', 'x', 'x', 'asdasd', 'asdasd', 'asdasd', 'asdasd', 'sadasd', 'asdas', 'ServicesImages/MjAPDDcXvfGfeY2uH0jI.jpg', '11', NULL, 'Egp', '0', '2024-03-20 22:16:44', '2024-03-23 04:18:50');

-- --------------------------------------------------------

--
-- Table structure for table `products_imags`
--

CREATE TABLE `products_imags` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `Pro_ID` longtext COLLATE utf8mb4_unicode_ci,
  `Image` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `products_imags`
--

INSERT INTO `products_imags` (`id`, `Pro_ID`, `Image`, `created_at`, `updated_at`) VALUES
(1, '414', 'http://localhost:8888/laravel/ERP/public/storage/ProductImagesCatlouge/z62Fn2JR0eCS5rFdbV2KTf3jTX2ddkdukk9HYwRG.jpg', '2023-11-23 08:34:15', '2023-11-23 08:34:15'),
(2, '414', 'http://localhost:8888/laravel/ERP/public/storage/ProductImagesCatlouge/BMSmJXYQMWGQimhPgUK2MVCGa5Zl3Um8hGWhSC6m.jpg', '2023-11-23 08:34:15', '2023-11-23 08:34:15'),
(5, NULL, 'ProductImagesCatlouge/zKmW2CDQIhbaj9wjjDjk7lTvREhTZmTL1Bmt67XI.png', '2024-03-20 22:23:01', '2024-03-20 22:23:01'),
(6, NULL, 'ProductImagesCatlouge/qmE3DbROPAZg7WnvEMqHb5iBlGhHI63G8KJZj75F.png', '2024-03-20 22:23:01', '2024-03-20 22:23:01'),
(7, NULL, 'ProductImagesCatlouge/iawo9XcDmVsF5Wo3NI8R2Ov92XHdzaYWBIJQppxm.png', '2024-03-20 22:26:48', '2024-03-20 22:26:48'),
(8, NULL, 'ProductImagesCatlouge/GmQl6C0CIA8tIJdWcqZrZPwTQIhKvCUCW3xcm9El.png', '2024-03-20 22:26:48', '2024-03-20 22:26:48'),
(9, '2', 'ProductImagesCatlouge/hIPxBLxu4Kzi53WRQrQQMmBpdwXWGcezyvxKHRcL.png', '2024-03-20 22:27:56', '2024-03-20 22:27:56'),
(10, '2', 'ProductImagesCatlouge/wn5x9v77P1J5uvlsPS0jQTamym5QexjhlHyagAiI.png', '2024-03-20 22:27:56', '2024-03-20 22:27:56'),
(11, '2', 'ProductImagesCatlouge/Wg4OVzEbMnXFXQZFOx0DVMsRFWOo4u3ej0bopDPy.jpg', '2024-03-23 04:18:50', '2024-03-23 04:18:50');

-- --------------------------------------------------------

--
-- Table structure for table `product_orders`
--

CREATE TABLE `product_orders` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `P_Ar_Name` longtext COLLATE utf8mb4_unicode_ci,
  `P_En_Name` longtext COLLATE utf8mb4_unicode_ci,
  `Qty` longtext COLLATE utf8mb4_unicode_ci,
  `Price` longtext COLLATE utf8mb4_unicode_ci,
  `Total` longtext COLLATE utf8mb4_unicode_ci,
  `Product` longtext COLLATE utf8mb4_unicode_ci,
  `Order` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `product_orders`
--

INSERT INTO `product_orders` (`id`, `P_Ar_Name`, `P_En_Name`, `Qty`, `Price`, `Total`, `Product`, `Order`, `created_at`, `updated_at`) VALUES
(39, 'x', 'x', '4', '11', '44', '2', '40', '2024-03-21 00:52:57', '2024-03-21 00:52:57'),
(40, 'x', 'x', '3', '11', '33', '2', '41', '2024-03-23 07:46:46', '2024-03-23 07:46:46');

-- --------------------------------------------------------

--
-- Table structure for table `rates`
--

CREATE TABLE `rates` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `Rate` longtext COLLATE utf8mb4_unicode_ci,
  `User` longtext COLLATE utf8mb4_unicode_ci,
  `Product` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `rates`
--

INSERT INTO `rates` (`id`, `Rate`, `User`, `Product`, `created_at`, `updated_at`) VALUES
(1, '4', '1', '414', '2023-11-20 12:33:28', '2023-11-20 12:33:39'),
(2, '4', '4', '2', '2024-03-21 00:43:02', '2024-03-21 00:43:02'),
(3, '4', '5', '2', '2024-03-23 06:34:14', '2024-03-23 06:34:14');

-- --------------------------------------------------------

--
-- Table structure for table `services`
--

CREATE TABLE `services` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `Arabic_Title` longtext COLLATE utf8mb4_unicode_ci,
  `English_Title` longtext COLLATE utf8mb4_unicode_ci,
  `Arabic_Desc` longtext COLLATE utf8mb4_unicode_ci,
  `English_Desc` longtext COLLATE utf8mb4_unicode_ci,
  `Icon` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `services`
--

INSERT INTO `services` (`id`, `Arabic_Title`, `English_Title`, `Arabic_Desc`, `English_Desc`, `Icon`, `created_at`, `updated_at`) VALUES
(9, 'Organic Proteins', 'Organic Proteins', 'Varius facilisis Dictum faucibus feugiat facil', 'Varius facilisis Dictum faucibus feugiat facil', 'WebSliderImages/Gq3znPQlxbfNY02EDM8g.svg', '2023-09-04 10:59:00', '2024-03-23 04:16:25'),
(10, 'Organic Proteins', 'Organic Proteins', 'Varius facilisis Dictum faucibus feugiat facil', 'Varius facilisis Dictum faucibus feugiat facil', 'WebSliderImages/BZt9dwJevJshRypKn1G4.svg', '2023-09-10 19:18:20', '2024-03-23 04:16:33'),
(11, 'Organic Proteins', 'Organic Proteins', 'Varius facilisis Dictum faucibus feugiat facil', 'Varius facilisis Dictum faucibus feugiat facil', 'WebSliderImages/9fJWL3BaQfzo2wa2zxbt.svg', '2023-09-10 19:21:02', '2024-03-23 04:16:40'),
(12, 'Organic Proteins', 'Organic Proteins', 'Varius facilisis Dictum faucibus feugiat facil', 'Varius facilisis Dictum faucibus feugiat facil', 'WebSliderImages/1iidUlKUxgWsEmNHLBAY.svg', '2023-09-10 19:26:32', '2024-03-23 04:16:48');

-- --------------------------------------------------------

--
-- Table structure for table `social_media`
--

CREATE TABLE `social_media` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `Facebook` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `Twitter` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `Instagram` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `Youtube` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `Snapchat` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `Whatsapp` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `Google_Plus` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `LinkedIn` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `Pinterest` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `Telegram` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `iOS` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `Android` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `social_media`
--

INSERT INTO `social_media` (`id`, `Facebook`, `Twitter`, `Instagram`, `Youtube`, `Snapchat`, `Whatsapp`, `Google_Plus`, `LinkedIn`, `Pinterest`, `Telegram`, `iOS`, `Android`, `created_at`, `updated_at`) VALUES
(1, 'https://www.facebook.com/klarapps', 'https://twitter.com/klarapps', 'https://www.instagram.com/klarapps', 'https://www.youtube.com/@klarapps6428', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-11-18 12:50:00');

-- --------------------------------------------------------

--
-- Table structure for table `terms`
--

CREATE TABLE `terms` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `Arabic_Desc` longtext COLLATE utf8mb4_unicode_ci,
  `English_Desc` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `terms`
--

INSERT INTO `terms` (`id`, `Arabic_Desc`, `English_Desc`, `created_at`, `updated_at`) VALUES
(1, '<section style=\"display: flex; flex-wrap: wrap; max-width: 100%; position: relative; width: 2237px; color: rgb(62, 74, 89); font-family: &quot;Nunito Sans&quot;; font-size: 17px; letter-spacing: normal;\"><div class=\"w-100 pt-70 position-relative\" style=\"width: 2237px; padding-top: 4.375rem;\"><div class=\"container\" style=\"width: 1566px; padding: 0px; max-width: 1566px;\"><div class=\"content-wrap position-relative w-100\" style=\"width: 1566px;\"><h1 style=\"margin-bottom: 2.1875rem; font-weight: 700; line-height: 1.2; font-size: 3.125rem; color: var(--color1); font-family: var(--Open-Sans);\">Dividers</h1><p style=\"margin-bottom: 1.7rem; color: var(--color1); line-height: 1.875rem; font-size: 1.0625rem; opacity: 0.8;\">Nullam sodales ullamcorper odio sed efficitur. Orci varius natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Aliquam aliquam velit nisi, vitae porttitor est finibus in. Cras mollis tincidunt purus, ut pretium turpis eleifend eu. Vivamus consequat mi sagittis, bibendum nulla et, fermentum eros. Sed blandit odio a nibh porttitor aliquet. Morbi efficitur mollis pretium. Sed et tortor dui.</p><div class=\"devider bg-color9 mt-40 mb-40 position-relative w-100\" style=\"width: 1566px; background-color: var(--color17); margin-top: 2.5rem; margin-bottom: 2.5rem; opacity: 1; height: 1px;\"></div><p style=\"margin-bottom: 1.7rem; color: var(--color1); line-height: 1.875rem; font-size: 1.0625rem; opacity: 0.8;\">Nullam sodales ullamcorper odio sed efficitur. Orci varius natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Aliquam aliquam velit nisi, vitae porttitor est finibus in. Cras mollis tincidunt purus, ut pretium turpis eleifend eu. Vivamus consequat mi sagittis, bibendum nulla et, fermentum eros. Sed blandit odio a nibh porttitor aliquet. Morbi efficitur mollis pretium. Sed et tortor dui.</p><div class=\"devider bg-color25 mt-40 mb-40 position-relative text-center w-100\" style=\"width: 1566px; background-color: var(--color25); margin-top: 2.5rem; margin-bottom: 2.5rem; opacity: 1; height: 1px;\"><i class=\"btm-ln v2 bg-color9\" style=\"transition: var(--transition2); background-color: var(--color17); bottom: 0px; height: inherit; position: absolute; width: 6px; left: 783px; transform: translateX(-50%);\"></i></div><p style=\"margin-bottom: 1.7rem; color: var(--color1); line-height: 1.875rem; font-size: 1.0625rem; opacity: 0.8;\">Nullam sodales ullamcorper odio sed efficitur. Orci varius natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Aliquam aliquam velit nisi, vitae porttitor est finibus in. Cras mollis tincidunt purus, ut pretium turpis eleifend eu. Vivamus consequat mi sagittis, bibendum nulla et, fermentum eros. Sed blandit odio a nibh porttitor aliquet. Morbi efficitur mollis pretium. Sed et tortor dui.</p><div class=\"devider mt-40 mb-40 position-relative text-center w-100\" style=\"width: 1566px; margin-top: 2.5rem; margin-bottom: 2.5rem; opacity: 1; height: 1px;\"><i class=\"btm-ln bg-color16\" style=\"transition: var(--transition2); background-color: var(--color16); bottom: -10px; height: inherit; position: absolute; width: 3.4375rem; left: 783px; transform: translateX(-50%);\"></i></div><p style=\"margin-bottom: 1.7rem; color: var(--color1); line-height: 1.875rem; font-size: 1.0625rem; opacity: 0.8;\">Nullam sodales ullamcorper odio sed efficitur. Orci varius natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Aliquam aliquam velit nisi, vitae porttitor est finibus in. Cras mollis tincidunt purus, ut pretium turpis eleifend eu. Vivamus consequat mi sagittis, bibendum nulla et, fermentum eros. Sed blandit odio a nibh porttitor aliquet. Morbi efficitur mollis pretium. Sed et tortor dui.</p><div class=\"devider v2 mt-40 position-relative w-100\" style=\"width: 1566px; margin-top: 2.5rem; opacity: 1; height: 1px; background-image: -webkit-linear-gradient(90deg, transparent, transparent 50%, rgb(255, 255, 255) 50%, rgb(255, 255, 255) 100%), linear-gradient(90deg, rgb(229, 229, 229), rgb(229, 229, 229)); background-size: 20px 3px, 100% 3px;\"></div></div></div></div></section><footer style=\"display: flex; flex-wrap: wrap; max-width: 100%; position: relative; width: 2237px; color: rgb(62, 74, 89); font-family: &quot;Nunito Sans&quot;; font-size: 17px; letter-spacing: normal;\"><div class=\"w-100 pt-40 bg-color11 position-relative\" style=\"width: 2237px; background-color: var(--color11); padding-top: 2.5rem; background-image: url(&quot;../images/resources/slide-bg1.jpg&quot;); background-repeat: no-repeat; background-size: cover;\"><div class=\"container\" style=\"width: 1566px; padding: 0px; max-width: 1566px;\"><div class=\"logo text-center w-100\" style=\"width: 1566px;\"></div></div></div></footer>', '<div><section style=\"display: flex; flex-wrap: wrap; max-width: 100%; position: relative; width: 2237px; color: rgb(62, 74, 89); font-family: &quot;Nunito Sans&quot;; font-size: 17px; letter-spacing: normal;\"><div class=\"w-100 pt-70 position-relative\" style=\"width: 2237px; padding-top: 4.375rem;\"><div class=\"container\" style=\"width: 1566px; padding: 0px; max-width: 1566px;\"><div class=\"content-wrap position-relative w-100\" style=\"width: 1566px;\"><h1 style=\"margin-bottom: 2.1875rem; font-weight: 700; line-height: 1.2; font-size: 3.125rem; color: var(--color1); font-family: var(--Open-Sans);\">Dividers</h1><p style=\"margin-bottom: 1.7rem; color: var(--color1); line-height: 1.875rem; font-size: 1.0625rem; opacity: 0.8;\">Nullam sodales ullamcorper odio sed efficitur. Orci varius natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Aliquam aliquam velit nisi, vitae porttitor est finibus in. Cras mollis tincidunt purus, ut pretium turpis eleifend eu. Vivamus consequat mi sagittis, bibendum nulla et, fermentum eros. Sed blandit odio a nibh porttitor aliquet. Morbi efficitur mollis pretium. Sed et tortor dui.</p><div class=\"devider bg-color9 mt-40 mb-40 position-relative w-100\" style=\"width: 1566px; background-color: var(--color17); margin-top: 2.5rem; margin-bottom: 2.5rem; opacity: 1; height: 1px;\"></div><p style=\"margin-bottom: 1.7rem; color: var(--color1); line-height: 1.875rem; font-size: 1.0625rem; opacity: 0.8;\">Nullam sodales ullamcorper odio sed efficitur. Orci varius natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Aliquam aliquam velit nisi, vitae porttitor est finibus in. Cras mollis tincidunt purus, ut pretium turpis eleifend eu. Vivamus consequat mi sagittis, bibendum nulla et, fermentum eros. Sed blandit odio a nibh porttitor aliquet. Morbi efficitur mollis pretium. Sed et tortor dui.</p><div class=\"devider bg-color25 mt-40 mb-40 position-relative text-center w-100\" style=\"width: 1566px; background-color: var(--color25); margin-top: 2.5rem; margin-bottom: 2.5rem; opacity: 1; height: 1px;\"><i class=\"btm-ln v2 bg-color9\" style=\"transition: var(--transition2); background-color: var(--color17); bottom: 0px; height: inherit; position: absolute; width: 6px; left: 783px; transform: translateX(-50%);\"></i></div><p style=\"margin-bottom: 1.7rem; color: var(--color1); line-height: 1.875rem; font-size: 1.0625rem; opacity: 0.8;\">Nullam sodales ullamcorper odio sed efficitur. Orci varius natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Aliquam aliquam velit nisi, vitae porttitor est finibus in. Cras mollis tincidunt purus, ut pretium turpis eleifend eu. Vivamus consequat mi sagittis, bibendum nulla et, fermentum eros. Sed blandit odio a nibh porttitor aliquet. Morbi efficitur mollis pretium. Sed et tortor dui.</p><div class=\"devider mt-40 mb-40 position-relative text-center w-100\" style=\"width: 1566px; margin-top: 2.5rem; margin-bottom: 2.5rem; opacity: 1; height: 1px;\"><i class=\"btm-ln bg-color16\" style=\"transition: var(--transition2); background-color: var(--color16); bottom: -10px; height: inherit; position: absolute; width: 3.4375rem; left: 783px; transform: translateX(-50%);\"></i></div><p style=\"margin-bottom: 1.7rem; color: var(--color1); line-height: 1.875rem; font-size: 1.0625rem; opacity: 0.8;\">Nullam sodales ullamcorper odio sed efficitur. Orci varius natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Aliquam aliquam velit nisi, vitae porttitor est finibus in. Cras mollis tincidunt purus, ut pretium turpis eleifend eu. Vivamus consequat mi sagittis, bibendum nulla et, fermentum eros. Sed blandit odio a nibh porttitor aliquet. Morbi efficitur mollis pretium. Sed et tortor dui.</p><div class=\"devider v2 mt-40 position-relative w-100\" style=\"width: 1566px; margin-top: 2.5rem; opacity: 1; height: 1px; background-image: -webkit-linear-gradient(90deg, transparent, transparent 50%, rgb(255, 255, 255) 50%, rgb(255, 255, 255) 100%), linear-gradient(90deg, rgb(229, 229, 229), rgb(229, 229, 229)); background-size: 20px 3px, 100% 3px;\"></div></div></div></div></section><footer style=\"display: flex; flex-wrap: wrap; max-width: 100%; position: relative; width: 2237px; color: rgb(62, 74, 89); font-family: &quot;Nunito Sans&quot;; font-size: 17px; letter-spacing: normal;\"><div class=\"w-100 pt-40 bg-color11 position-relative\" style=\"width: 2237px; background-color: var(--color11); padding-top: 2.5rem; background-image: url(&quot;../images/resources/slide-bg1.jpg&quot;); background-repeat: no-repeat; background-size: cover;\"><div class=\"container\" style=\"width: 1566px; padding: 0px; max-width: 1566px;\"><div class=\"logo text-center w-100\" style=\"width: 1566px;\"></div></div></div></footer></div>', NULL, '2023-10-04 23:47:29');

-- --------------------------------------------------------

--
-- Table structure for table `testiminoals`
--

CREATE TABLE `testiminoals` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `Arabic_Name` longtext COLLATE utf8mb4_unicode_ci,
  `English_Name` longtext COLLATE utf8mb4_unicode_ci,
  `Arabic_Desc` longtext COLLATE utf8mb4_unicode_ci,
  `English_Desc` longtext COLLATE utf8mb4_unicode_ci,
  `Arabic_Job` longtext COLLATE utf8mb4_unicode_ci,
  `English_Job` longtext COLLATE utf8mb4_unicode_ci,
  `Image` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `testiminoals`
--

INSERT INTO `testiminoals` (`id`, `Arabic_Name`, `English_Name`, `Arabic_Desc`, `English_Desc`, `Arabic_Job`, `English_Job`, `Image`, `created_at`, `updated_at`) VALUES
(1, 'Normal distribution', 'Normal distribution', 'It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using \'Content here, content here\', making it look', 'It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using \'Content here, content here\', making it look', 'Profession', 'Profession', 'TestiImages/1nD7x6tvyAy4OxMmBhUl.png', '2023-04-05 05:44:13', '2024-03-23 04:17:04'),
(2, 'Client Name', 'Client Name', 'Dolores sed duo clita tempor justo dolor et stet lorem kasd labore dolore lorem ipsum. At lorem lorem magna ut et, nonumy et labore et tempor diam tempor erat dolor rebum sit ipsum.', 'Dolores sed duo clita tempor justo dolor et stet lorem kasd labore dolore lorem ipsum. At lorem lorem magna ut et, nonumy et labore et tempor diam tempor erat dolor rebum sit ipsum.', 'Profession', 'Profession', 'TestiImages/58MnLWIq2gA0sxoxfsyl.png', '2023-04-05 05:48:13', '2024-03-23 04:16:57');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) CHARACTER SET utf8 NOT NULL,
  `email` varchar(255) CHARACTER SET utf8 NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) CHARACTER SET utf8 NOT NULL,
  `remember_token` varchar(100) CHARACTER SET utf8 DEFAULT NULL,
  `phone` longtext COLLATE utf8mb4_unicode_ci,
  `USER_ID` longtext COLLATE utf8mb4_unicode_ci,
  `code` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `name`, `email`, `email_verified_at`, `password`, `remember_token`, `phone`, `USER_ID`, `code`, `created_at`, `updated_at`) VALUES
(1, 'Ahmed', '<EMAIL>', NULL, '$2y$10$ka4vB7hdvFD46PAl1P3Qlujg/3lbLivVlD39DNjKQ/2FtEXp.rvZy', NULL, '7788', '600', NULL, '2023-11-20 10:54:45', '2023-11-20 17:32:21'),
(2, 'Loay', '<EMAIL>', NULL, '$2y$10$2bvZfl6VSPMva1DeOHiYWeD2qJ0.OzJMvytn7lfKmJsli2DVx/tw6', NULL, '88009900', '601', NULL, '2023-11-23 08:37:59', '2023-11-23 08:37:59'),
(3, 'admin', '<EMAIL>', NULL, '$2y$10$.o6KAezUc6TVuv5clTd6BeeS9OQ0NGBD.zan7/s/py/q84LY1MoTy', NULL, '9', '2', NULL, '2024-02-16 12:01:51', '2024-02-16 12:01:51'),
(4, 'Kikoooo', '<EMAIL>', NULL, '$2y$10$qVZZwZMlidOUXSUEPbCPHe8uk7eahGtgmgD9bykGTzPmhZUjgQhPG', NULL, '099999', NULL, NULL, '2024-03-21 00:40:12', '2024-03-21 00:48:04'),
(5, 'Akram', '<EMAIL>', NULL, '$2y$10$r0ASO5C7Bjvi/pLzPkIu1.D10xUX9zf290ADr8/6OK.5Qawc31Q32', NULL, '23234', NULL, NULL, '2024-03-23 06:32:25', '2024-03-23 06:42:10');

-- --------------------------------------------------------

--
-- Table structure for table `websliders`
--

CREATE TABLE `websliders` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `Arabic_Title` longtext COLLATE utf8mb4_unicode_ci,
  `English_Title` longtext COLLATE utf8mb4_unicode_ci,
  `Arabic_Desc` longtext COLLATE utf8mb4_unicode_ci,
  `English_Desc` longtext COLLATE utf8mb4_unicode_ci,
  `Image` longtext COLLATE utf8mb4_unicode_ci,
  `Type` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `websliders`
--

INSERT INTO `websliders` (`id`, `Arabic_Title`, `English_Title`, `Arabic_Desc`, `English_Desc`, `Image`, `Type`, `created_at`, `updated_at`) VALUES
(4, 'be storong', 'be storong', 'Hardest part is walking out in the front door', 'Hardest part is walking out in the front door', 'WebSliderImages/n4m2DflOB9QKB0tCbcjg.jpg', '0', '2023-09-02 13:59:30', '2023-11-18 12:46:22'),
(9, 'be storong', 'be storong', 'Hardest part is walking out in the front door', 'Hardest part is walking out in the front door', 'WebSliderImages/QhSx4qzMjwR5s6GgZjeP.jpg', '0', '2023-09-02 14:14:02', '2023-11-18 12:46:48'),
(11, 'be storong', 'be storong', 'Hardest part is walking out in the front door', 'Hardest part is walking out in the front door', 'WebSliderImages/j1wjinYrFjQQzgm3u1hK.jpg', '0', '2023-09-02 14:16:13', '2023-11-18 12:47:05');

-- --------------------------------------------------------

--
-- Table structure for table `why_chooses`
--

CREATE TABLE `why_chooses` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `Arabic_Title` longtext COLLATE utf8mb4_unicode_ci,
  `English_Title` longtext COLLATE utf8mb4_unicode_ci,
  `Arabic_Desc` longtext COLLATE utf8mb4_unicode_ci,
  `English_Desc` longtext COLLATE utf8mb4_unicode_ci,
  `Image` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `why_chooses`
--

INSERT INTO `why_chooses` (`id`, `Arabic_Title`, `English_Title`, `Arabic_Desc`, `English_Desc`, `Image`, `created_at`, `updated_at`) VALUES
(1, 'Make yourself stronger than your best excuses', 'Make yourself stronger than your best excuses', 'Make yourself stronger than your best excuses', 'Make yourself stronger than your best excuses', 'AboutImages/wA9tka0fUaq9ubs7MJL7.jpeg', NULL, '2024-03-23 04:15:31');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `abouts`
--
ALTER TABLE `abouts`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `addersses`
--
ALTER TABLE `addersses`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `admins`
--
ALTER TABLE `admins`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `admins_email_unique` (`email`);

--
-- Indexes for table `articles`
--
ALTER TABLE `articles`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `categories`
--
ALTER TABLE `categories`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `cities`
--
ALTER TABLE `cities`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `comments`
--
ALTER TABLE `comments`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `company_data`
--
ALTER TABLE `company_data`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

--
-- Indexes for table `features`
--
ALTER TABLE `features`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `footers`
--
ALTER TABLE `footers`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `f_a_q_s`
--
ALTER TABLE `f_a_q_s`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `galleries`
--
ALTER TABLE `galleries`
  ADD PRIMARY KEY (`id`),
  ADD KEY `galleries_category_foreign` (`Category`);

--
-- Indexes for table `governrates`
--
ALTER TABLE `governrates`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `groups`
--
ALTER TABLE `groups`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `how_work_icons`
--
ALTER TABLE `how_work_icons`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `i_s_p_results`
--
ALTER TABLE `i_s_p_results`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `i_s_p_s`
--
ALTER TABLE `i_s_p_s`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `orders`
--
ALTER TABLE `orders`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `password_resets`
--
ALTER TABLE `password_resets`
  ADD KEY `password_resets_email_index` (`email`);

--
-- Indexes for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  ADD KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`);

--
-- Indexes for table `places`
--
ALTER TABLE `places`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `privacies`
--
ALTER TABLE `privacies`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `products`
--
ALTER TABLE `products`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `products_imags`
--
ALTER TABLE `products_imags`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `product_orders`
--
ALTER TABLE `product_orders`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `rates`
--
ALTER TABLE `rates`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `services`
--
ALTER TABLE `services`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `social_media`
--
ALTER TABLE `social_media`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `terms`
--
ALTER TABLE `terms`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `testiminoals`
--
ALTER TABLE `testiminoals`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_email_unique` (`email`);

--
-- Indexes for table `websliders`
--
ALTER TABLE `websliders`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `why_chooses`
--
ALTER TABLE `why_chooses`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `abouts`
--
ALTER TABLE `abouts`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `addersses`
--
ALTER TABLE `addersses`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `admins`
--
ALTER TABLE `admins`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `articles`
--
ALTER TABLE `articles`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `categories`
--
ALTER TABLE `categories`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `cities`
--
ALTER TABLE `cities`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `comments`
--
ALTER TABLE `comments`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `company_data`
--
ALTER TABLE `company_data`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `features`
--
ALTER TABLE `features`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `footers`
--
ALTER TABLE `footers`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `f_a_q_s`
--
ALTER TABLE `f_a_q_s`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `galleries`
--
ALTER TABLE `galleries`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `governrates`
--
ALTER TABLE `governrates`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `groups`
--
ALTER TABLE `groups`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `how_work_icons`
--
ALTER TABLE `how_work_icons`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `i_s_p_results`
--
ALTER TABLE `i_s_p_results`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `i_s_p_s`
--
ALTER TABLE `i_s_p_s`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT for table `orders`
--
ALTER TABLE `orders`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=42;

--
-- AUTO_INCREMENT for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `places`
--
ALTER TABLE `places`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `privacies`
--
ALTER TABLE `privacies`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `products`
--
ALTER TABLE `products`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `products_imags`
--
ALTER TABLE `products_imags`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `product_orders`
--
ALTER TABLE `product_orders`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=41;

--
-- AUTO_INCREMENT for table `rates`
--
ALTER TABLE `rates`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `services`
--
ALTER TABLE `services`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `social_media`
--
ALTER TABLE `social_media`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `terms`
--
ALTER TABLE `terms`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `testiminoals`
--
ALTER TABLE `testiminoals`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `websliders`
--
ALTER TABLE `websliders`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `why_chooses`
--
ALTER TABLE `why_chooses`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `galleries`
--
ALTER TABLE `galleries`
  ADD CONSTRAINT `galleries_category_foreign` FOREIGN KEY (`Category`) REFERENCES `categories` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
