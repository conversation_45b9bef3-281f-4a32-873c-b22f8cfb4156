@extends('admin.index')
@section('content')
    @php
use App\Models\CompanyData;
$Def=CompanyData::orderBy('id','desc')->first();
@endphp
 <title>
                @if(!empty($Def->Name))
                      {{$Def->Name}}
                      @else
                       {{trans('admin.Klar')}}
                      @endif
</title>

       <!-- BEGIN Page Content -->
               <!-- the #js-page-content id is needed for some plugins to initialize -->
               <main id="js-page-content" role="main" class="page-content">
         
                  <ol class="breadcrumb page-breadcrumb">
                     <li class="breadcrumb-item"><a href="{{url('KlarAdmin')}}">       @if(!empty($Def->Name))
                      {{$Def->Name}}
                      @else
                       {{trans('admin.Klar')}}
                      @endif</a></li>
                     <li class="breadcrumb-item active">   {{trans('admin.Home')}}</li>
                     <li class="position-absolute pos-top pos-right d-none d-sm-block"><span class="js-get-date"></span></li>
                  </ol>
                  <div class=" row subheader">
                      <div class="col-lg-9">
                     <h1 class="subheader-title">
                        <i class='subheader-icon fal fa-chart-area'></i> Home <span class='fw-300'>Dashboard</span>
                     </h1>
                     </div>
                     
 </div>
                 
                   
                   <div class="row"></div>
                   

                   
               </main>
               <!-- this overlay is activated only when mobile menu is triggered -->
         

   
@endsection 
