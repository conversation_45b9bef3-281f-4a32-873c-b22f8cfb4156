<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProductsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->longText('Pro_ID')->nullable();
            $table->longText('Group_ID')->nullable();
            $table->longText('P_Ar_Name')->nullable();
            $table->longText('P_En_Name')->nullable();
            $table->longText('Ar_Desc')->nullable();
            $table->longText('En_Desc')->nullable();    
            $table->longText('Ar_Spec')->nullable();
            $table->longText('En_Spec')->nullable();    
            $table->longText('Ar_Sup_Desc')->nullable();
            $table->longText('En_Sup_Desc')->nullable();
            $table->longText('Image')->nullable();
            $table->longText('Price')->nullable();
            $table->longText('Offer_Price')->nullable();
            $table->longText('Symbol')->nullable();
            $table->longText('Type')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('products');
    }
}
