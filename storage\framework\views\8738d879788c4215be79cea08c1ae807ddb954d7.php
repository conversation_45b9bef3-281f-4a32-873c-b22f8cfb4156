<?php $__env->startSection('content'); ?>

<div class="page-wrapper">
    <div class="page-content">
        <!--breadcrumb-->
        <div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
            <div class="breadcrumb-title pe-3"><?php echo e(trans('admin.Website')); ?></div>
            <div class="ps-3">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0 p-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(url('Admin')); ?>"><i class="bx bx-home-alt"></i></a></li>
                        <li class="breadcrumb-item active" aria-current="page"><?php echo e(trans('admin.Team')); ?></li>
                    </ol>
                </nav>
            </div>
        </div>
        <!--end breadcrumb-->

        <div class="row">
            <div class="col-xl-9 mx-auto">
                <div class="card border-top border-0 border-4 border-primary">
                    <div class="card-body p-5">
                        <div class="card-title d-flex align-items-center">
                            <div><i class="bx bxs-group me-1 font-22 text-primary"></i></div>
                            <h5 class="mb-0 text-primary"><?php echo e(trans('admin.Team')); ?></h5>
                        </div>
                        <hr>

                        <!-- Add New Team Member Form -->
                        <form method="POST" action="<?php echo e(url('AddTeamMember')); ?>" enctype="multipart/form-data">
                            <?php echo csrf_field(); ?>
                            <div class="row mb-3">
                                <div class="col-sm-6">
                                    <label class="form-label"><?php echo e(trans('admin.Arabic_Name')); ?></label>
                                    <input type="text" class="form-control" name="Arabic_Name" required>
                                </div>
                                <div class="col-sm-6">
                                    <label class="form-label"><?php echo e(trans('admin.English_Name')); ?></label>
                                    <input type="text" class="form-control" name="English_Name" required>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-6">
                                    <label class="form-label"><?php echo e(trans('admin.Arabic_Position')); ?></label>
                                    <input type="text" class="form-control" name="Arabic_Position">
                                </div>
                                <div class="col-sm-6">
                                    <label class="form-label"><?php echo e(trans('admin.English_Position')); ?></label>
                                    <input type="text" class="form-control" name="English_Position">
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-6">
                                    <label class="form-label"><?php echo e(trans('admin.Arabic_Bio')); ?></label>
                                    <textarea class="form-control" name="Arabic_Bio" rows="3"></textarea>
                                </div>
                                <div class="col-sm-6">
                                    <label class="form-label"><?php echo e(trans('admin.English_Bio')); ?></label>
                                    <textarea class="form-control" name="English_Bio" rows="3"></textarea>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-6">
                                    <label class="form-label"><?php echo e(trans('admin.Image')); ?></label>
                                    <input type="file" class="form-control" name="Image" accept="image/*">
                                </div>
                                <div class="col-sm-6">
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <label class="form-label"><?php echo e(trans('admin.Status')); ?></label>
                                            <select class="form-control" name="Status" required>
                                                <option value="1"><?php echo e(trans('admin.Active')); ?></option>
                                                <option value="0"><?php echo e(trans('admin.Inactive')); ?></option>
                                            </select>
                                        </div>
                                        <div class="col-sm-6">
                                            <label class="form-label"><?php echo e(trans('admin.Sort_Order')); ?></label>
                                            <input type="number" class="form-control" name="Sort_Order" value="0">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-3">
                                    <label class="form-label">Facebook</label>
                                    <input type="url" class="form-control" name="Facebook" placeholder="https://facebook.com/username">
                                </div>
                                <div class="col-sm-3">
                                    <label class="form-label">Twitter</label>
                                    <input type="url" class="form-control" name="Twitter" placeholder="https://twitter.com/username">
                                </div>
                                <div class="col-sm-3">
                                    <label class="form-label">LinkedIn</label>
                                    <input type="url" class="form-control" name="LinkedIn" placeholder="https://linkedin.com/in/username">
                                </div>
                                <div class="col-sm-3">
                                    <label class="form-label">Instagram</label>
                                    <input type="url" class="form-control" name="Instagram" placeholder="https://instagram.com/username">
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-sm-12">
                                    <button type="submit" class="btn btn-primary px-5"><?php echo e(trans('admin.Add')); ?></button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Team Members List -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered">
                                <thead>
                                    <tr>
                                        <th><?php echo e(trans('admin.Image')); ?></th>
                                        <th><?php echo e(trans('admin.Name')); ?></th>
                                        <th><?php echo e(trans('admin.Position')); ?></th>
                                        <th><?php echo e(trans('admin.Social_Media')); ?></th>
                                        <th><?php echo e(trans('admin.Status')); ?></th>
                                        <th><?php echo e(trans('admin.Sort_Order')); ?></th>
                                        <th><?php echo e(trans('admin.Actions')); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <?php if($item->Image): ?>
                                                <img src="<?php echo e(URL::to($item->Image)); ?>" width="60" height="60" class="rounded-circle">
                                            <?php else: ?>
                                                <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center" style="width:60px;height:60px;">
                                                    <i class="bx bx-user text-white"></i>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <strong>
                                                <?php if(app()->getLocale() == 'ar'): ?>
                                                    <?php echo e($item->Arabic_Name); ?>

                                                <?php else: ?>
                                                    <?php echo e($item->English_Name); ?>

                                                <?php endif; ?>
                                            </strong>
                                        </td>
                                        <td>
                                            <?php if(app()->getLocale() == 'ar'): ?>
                                                <?php echo e($item->Arabic_Position); ?>

                                            <?php else: ?>
                                                <?php echo e($item->English_Position); ?>

                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($item->Facebook): ?>
                                                <a href="<?php echo e($item->Facebook); ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                                    <i class="bx bxl-facebook"></i>
                                                </a>
                                            <?php endif; ?>
                                            <?php if($item->Twitter): ?>
                                                <a href="<?php echo e($item->Twitter); ?>" target="_blank" class="btn btn-sm btn-outline-info">
                                                    <i class="bx bxl-twitter"></i>
                                                </a>
                                            <?php endif; ?>
                                            <?php if($item->LinkedIn): ?>
                                                <a href="<?php echo e($item->LinkedIn); ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                                    <i class="bx bxl-linkedin"></i>
                                                </a>
                                            <?php endif; ?>
                                            <?php if($item->Instagram): ?>
                                                <a href="<?php echo e($item->Instagram); ?>" target="_blank" class="btn btn-sm btn-outline-danger">
                                                    <i class="bx bxl-instagram"></i>
                                                </a>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($item->Status == 1): ?>
                                                <span class="badge bg-success"><?php echo e(trans('admin.Active')); ?></span>
                                            <?php else: ?>
                                                <span class="badge bg-danger"><?php echo e(trans('admin.Inactive')); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e($item->Sort_Order); ?></td>
                                        <td>
                                            <a href="<?php echo e(url('EditTeamMember/'.$item->id)); ?>" class="btn btn-primary btn-sm"><?php echo e(trans('admin.Edit')); ?></a>
                                            <a href="<?php echo e(url('DeleteTeamMember/'.$item->id)); ?>" class="btn btn-danger btn-sm" onclick="return confirm('<?php echo e(trans('admin.Are_You_Sure')); ?>')"><?php echo e(trans('admin.Delete')); ?></a>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\ost_erp\resources\views/admin/Website/Team.blade.php ENDPATH**/ ?>