.panel.panel-locked:not(.panel-fullscreen) .panel-hdr h2:before{
    display:none;
}

.custom-control-label{
    /* position: absolute; */
   
    /* bottom: 27px; */
}

.select2-dropdown{
    z-index: 2070!important;
}

td button, td a{
    padding: 5px!important;
}

/* .hide-table{
    display: none;
} */

.strick{
    color: red;
    padding:3px;
}

.fc-head-container{
    overflow:auto!important;
}

.bg-color{
    background:#f6dffd;
    width:100%;
    height:22px;
    border-radius:50px;
    display:inline-block;
    text-align:center;
}

.margin-btn{
    margin:0 5px;
}
@media only screen and (max-width: 420px){
    .select2-dropdown--below{
    position: absolute;
    z-index: 9999;
}

    .th-width th{
        padding: 10px 38px;
    }
    .panel-hdr{
        display:block;
    }
    .panel-toolbar{
        display: inline-flex;
        margin-top:-9px;
        margin-bottom:10px;
    }
    .panel-toolbar button:last-child{
        margin-left:3px;
        margin-right:3px;
    }
    .btn-switch, [class*="btn-outline-"], .panel-toolbar .btn-panel,
    .settings-panel .list, .settings-panel .list .onoffswitch,
    .settings-panel .list:hover .onoffswitch, .color-disp-demo tr td, .icon-demo li{
        margin-top:7px;
    }
    .mobile-width{
        width:160%!important;
    }
    
    .mobile-width-more{
          width:400%!important;
    }
    #mobile-overflow {
        overflow:auto;
    }
    .breadcrumb > li.breadcrumb-item{
        overflow:unset;
    }
    .keyboard{
        display:none;
    }
    .second-section{
        overflow-x:auto!important;
    }
    .pos-icon{
      margin-top:0px;
    }
}

 #table-overflow{
        overflow:auto;
    }
    
table.dataTable.fixedHeader-locked {
    display: none!important;
}
table.dataTable.fixedHeader-floating{
    opacity:0!important;
}

.text-muted{
    color:black!important;
    font-weight: 600;
    font-size:16px;
}

@media print{
   
    
    .panel .panel-container .panel-content{
        padding:0 10px;
    }
    .panel {
        border:none;
    }
    .col-6-print{
         max-width: 50%;
    }
    .col-4-print{
          max-width: 33.33333%;
      }
      
      .col-3-print{
          max-width: 25%;
      }
      .col-2-print{
          max-width: 16.66667%;
      }
    .no-print{
        display:none;
    }
    *{
        font-size:18px;
        color:black;
        font-weight:bold;
       
    }
    .table-bordered, .table-bordered th, .table-bordered td {
    border: 1px solid black!important;
}
.pagination ,.dataTables_info ,.dataTables_filter , tfoot
,.sorting_asc:before, .sorting_asc:after {
            display:none;
        }
        
        .sorting_asc{
            padding-right:unset;
        }
        .mod-skin-dark:not(.mod-skin-light) .text-muted{
            color:black!important;
        }
}

.customize-input{
    font-size: 20px;
    padding-top: 10px;
    cursor: pointer;
    }
    
    #blah{
        height: 80px;
        width: auto;
        border: 1px solid lightgray;
        position: relative;
    }
    
    .close{
      
        font-size: 25px;
    }
    
    .note-editable{
    height: 55px!important;
    }
    
    .start-pos{
        background-color: #fbf8fa;
    }
    
    .first-card, .second-card{
        display: flex;
        flex-flow: column;
        height: 100vh;
    }
    
    .second-card{
        /* width: 95%; */
    }
    
    .second-card, .first-card{
        height: 100vh;
        padding: 5px 10px;
    }
    
    .second-card .form-group, .first-card .form-group{
        margin-bottom: 7px;
    }
    
    .top-btn .btn{
    margin-bottom: 10px;
    padding: 5px 1.125rem;
    }
    
    .input-items{
        position: relative;
    }
    .input-items i{
        position: absolute;
        top: 5px;
        left: 5px;
        font-size: 27px;
        color: #b56a9f;
    }
    
    .first-section{
        flex: 0 1 auto;
    
    }
    
    .second-section{
        flex: 1 1 auto;
        overflow-y:auto;
        overflow-x:hidden;
    }
    
    .second-section table{
        margin-right: 1px;
    }
    
    .last-section{
        flex: 0 1 40px;
    }
    
    .last-section .table{
        margin-bottom: 7px;
    }
    
    .first-card .table td,.first-card .table th{
        padding: 5px!important;
    }
    
    .first-card .select2-container--default .select2-selection--single .select2-selection__rendered{
        line-height: 27px;
    }
    
    .first-card .select2-container .select2-selection--single ,.first-card input{
        height: 27px;
    }
    
    .second-card .groups{
        height: 135px;
        overflow-y: auto;
    }
    
    .second-card .groups button{
        margin-bottom: 5px;
    }
    
    .second-card h4{
        border-bottom: 1px solid lightgray;
    }
    
    
    .one-variable{
        background: #cf9abf3b;
    }
    .two-variable{
        background: #db24a563;
    }
    
    #two-variable .btn ,.food-size .btn,.food-added .btn{
    padding: 3px 11px;
    }
    
    #two-variable .size ,.food-size .size ,.food-added .size{
        border-left: 1px solid;
        padding-left: 5px;
    }
    
    .food-size{
        background: #ff93933b;
        padding: 10px;
        border: 1px solid #cccc;
        border-radius:3px;
    }
    .food-added{
        background: #8a555533;
        padding: 10px;
        border: 1px solid #cccc;
        border-radius:3px;
        margin-top:12px;
    }
    .serial{
        background: #ff9c9c85;
    }
    
    .item-details ,.group-details ,.brand-details{
        text-align: center;
        margin-top: 20px;
        border: 1px solid #80808080 ;
        transition: .5s;
        padding: 5px;
    }
    
    .item-details:hover{
        transform: translateY(-8px);
        box-shadow: 5px 10px 12px #888888;
        cursor: pointer;
        transition: .5s;
    }
    
    .group-details:hover ,.brand-details:hover{
        transform: scale(1.1);
        cursor: pointer;
        transition: .5s; 
    }
    
    .item-details img{
        height: 60px;
        max-width: 100%;
      
    }
    
    .group-details img ,.brand-details img{
        height: 106px;
        max-width: 100%; 
    }
    
    .item-details p,.group-details p ,.brand-details p{
        margin-bottom:0;
        margin-top: 3px;
    }
    
    .item-details span{
        color: #ad5994;
    }
    
    .first-part{
        /* height: 207px; */
    }
    
    .second-part{
        flex-flow: column;
        overflow-x:hidden;
        overflow-y: auto;
    }
    
    .second-part h4 {
        border-bottom: 1px solid lightgray;
        position: fixed;
        z-index: 2;
        background: white;
       
        width: 45%;
       
    }
    
    .second-part .items{
        margin-top: 0px!important;
    }
    
    #custom-btn-table{
        height: 64px;
    }
    
    .fixed-items{
        position: fixed;
        z-index: 3;
        top: 150px;
        left: 0;
        display: none;
    }
    
    .fixed-items .btn{
        display: block;
        border-radius: 0px 30px 30px 0px;
    }
    
    .customer-slider img{
        height: 300px;
        max-width: 100%;
    }
    
    #color td{
        width: 80px;
    }
    
    #variables{
        width: 200%;
    }
    
    #variables th, #variables td{
        padding: 5px;
    width: 70px!important;
    }
    
    .padding-tables{
        
        overflow: auto;
        padding-left: 20px;
        border: 1px solid #d3d3d361;
    
    }
    
    #groups .modal-body, #brands .modal-body{
        overflow: auto;
        height: 430px;
    }
    
    .input-width{
        width:70px;
    }
    
    .qty input{
        width:40px;
    }
    
    .qty .btn{
        height:28px;
    }
    
    .table-hall td{
        width: 60%;
    }
    
    .table-order th, .table-order td{
        padding: 3px;
        font-size: 12px;
        text-align: center;
    }
    
    .select-table{
        border: 1px solid #cccc;
        border-radius: 7px;
        padding: 5px;
        margin-top: 5px;
    
    }
    
    .select-table input{
        display: inline-block;
        width: 70%;
        height: 26px;
    }
    
    .select-table .btn{
        height: 24px;
        line-height:13px; 
    }
    
    .wating-orders .orders-btn .btn{
        margin:  0 auto;
    }
    
    .pos-icon{
        margin-top: 23px;height: 27px;line-height: 13px;width:100%;
    }
    
    .pos-icon img{
        height:25px;
        margin-top:-7px;
        margin-right:-11px;
    }
    
.table-color1{

    background: #c9c9c9a6;
}
.table-color2{

    background: #c37bb7b5;
}

.long-barcode{
    max-width:150px;
}

.img-table{
    max-height:70px;
}

.shortcut{
    text-align:center;
}