@echo off
echo === Shonashrom ERP Integration Setup ===
echo.

echo 1. Running database migrations...
php artisan migrate --force
echo.

echo 2. Running database seeders...
php artisan db:seed --force
echo.

echo 3. Clearing application cache...
php artisan cache:clear
echo.

echo 4. Clearing config cache...
php artisan config:clear
echo.

echo 5. Clearing view cache...
php artisan view:clear
echo.

echo === Integration Setup Complete ===
echo.
echo SUCCESS: Your Shonashrom site now uses the SAME design system as your main ERP!
echo.
echo Next steps:
echo 1. Visit http://yoursite.com/DesignSystem in your admin panel
echo 2. Configure colors, fonts, and styling to match your brand
echo 3. Test the site - it now has identical styling to your main ERP
echo.
echo Features enabled:
echo - Same font system as main ERP (Cairo, Roboto, Open Sans, Poppins, Alexandria)
echo - Same color configuration system
echo - Same footer, header, and breadcrumb styling
echo - Consistent design across ERP and Shonashrom site
echo - Same admin interface for design management
echo.
pause
