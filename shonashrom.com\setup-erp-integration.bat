@echo off
echo === Shonashrom ERP Integration Setup ===
echo.

echo 1. Running database migrations...
php artisan migrate --force
echo.

echo 2. Running database seeders...
php artisan db:seed --class=MainEComDesignSeeder --force
echo.

echo 3. Clearing application cache...
php artisan cache:clear
echo.

echo 4. Clearing config cache...
php artisan config:clear
echo.

echo 5. Clearing view cache...
php artisan view:clear
echo.

echo 6. Optimizing application...
php artisan optimize
echo.

echo === Integration Setup Complete ===
echo.
echo Next steps:
echo 1. Visit /DesignSystem in your admin panel to configure the design
echo 2. Test the site to ensure all styling is working correctly
echo 3. The site now uses the same design system as your main ERP
echo.
echo Features enabled:
echo - Same font system as main ERP (Cairo, Roboto, Open Sans, Poppins, Alexandria)
echo - Same color configuration system
echo - Same footer, header, and breadcrumb styling
echo - Consistent design across ERP and Shonashrom site
echo.
pause
