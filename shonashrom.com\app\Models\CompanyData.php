<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CompanyData extends Model
{
    use HasFactory;

    protected $table = 'company_data';

    protected $fillable = [
        'Name',
        'NameEn',
        'Phone1',
        'Phone2',
        'Phone3',
        'Phone4',
        'Address',
        'AddressEn',
        'Logo',
        'Icon',
        'Print_Text',
        'Print_Text_Footer',
        'Print_Text_Footer_Sales',
        'Print_Text_Footer_Quote',
        'Print_Text_Footer_Manufacturing',
        'Seal',
        'Name_Sales_Bill',
        'Name_Sales_Order_Bill',
        'Name_Quote_Bill',
        'Logo_Store',
        'Icon_Store',
        'View',
        'Print_Text_Footer_Secretariat',
        'Commercial_Record',
        'Tax_File_Number',
        'Tax_Registration_Number',
        'Tax_activity_code',
        'work_nature',
        'Governrate',
        'City',
        'Place',
        'Nationality',
        'Buliding_Num',
        'Street',
        'Postal_Code',
        'tax_magistrate',
        'Client_ID',
        'Serial_Client_ID',
        'Version_Type',
        'Computer_SN',
        'Invoice_Type',
        'Floor',
        'Room',
        'Landmark',
        'Add_Info',
        'POS_Version',
        'Path',
        'DB_Backup',
        'Print_Text_En',
        'Print_Text_Footer_En',
        'Print_Text_Footer_Manufacturing_En',
        'Print_Text_Footer_Sales_En',
        'Print_Text_Footer_Quote_En',
        'Print_Text_Footer_Secretariat_En',
        'Name_Sales_Bill_En',
        'Name_Sales_Order_Bill_En',
        'Name_Quote_Bill_En',
        'Location',
        'Email',
        'PDF',
        'HomeMainScreen',
        'Bill_View',
        'Font_Type',
        'Welcome_Arabic_Word_App',
        'Welcome_English_Word_App',
    ];

    public function Governrate()
    {
        return $this->belongsTo(Governrate::class, 'Governrate');
    }

    public function City()
    {
        return $this->belongsTo(City::class, 'City');
    }

    public function Place()
    {
        return $this->belongsTo(Places::class, 'Place');
    }
}
