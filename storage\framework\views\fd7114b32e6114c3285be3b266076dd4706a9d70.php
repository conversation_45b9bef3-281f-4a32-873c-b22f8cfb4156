   
<?php $__env->startSection('content'); ?>
<title><?php echo e(trans('admin.Terms')); ?></title>
 <?php
use App\Models\MainEComDesign;
use App\Models\SupPagesEComDesign;
$main=MainEComDesign::orderBy('id','desc')->first();
$sub=SupPagesEComDesign::orderBy('id','desc')->first();
?>

 <style>
.page-title-area {
    background-color: <?php echo e($main->Breadcumb_BG_Color); ?> !important;
}
     
    .terms-of-service-area {
      background-color: <?php echo e($main->Sub_Page_BG_Color); ?> !important;
} 
</style>


        <!-- Start Page Banner -->
        <div class="page-title-area">
            <div class="container">
                <div class="page-title-content">
                    <h2  style="color: <?php echo e($main->Breadcumb_Txt_Color); ?> !important"><?php echo e(trans('admin.Terms')); ?></h2>

                    <ul>
                        <li><a  style="color: <?php echo e($main->Breadcumb_Txt_Color); ?> !important" href="<?php echo e(url('/')); ?>"><?php echo e(trans('admin.Home')); ?></a></li>
                        <li  style="color: <?php echo e($main->Breadcumb_Txt_Color); ?> !important"><?php echo e(trans('admin.Terms')); ?></li>
                    </ul>
                </div>
            </div>
        </div>
        <!-- End Page Banner -->

        <!-- Start Terms of Service Area -->
        <section class="terms-of-service-area ptb-50">
            <div class="container">
      
				<div class="terms-of-service-content">

                    <?php echo app()->getLocale() == 'ar' ?$Terms->Arabic_Desc :$Terms->English_Desc; ?>

				</div>
            </div>
        </section>
        <!-- End Terms of Service Area -->

<?php $__env->stopSection(); ?> 
<?php echo $__env->make('site.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\ost_erp\resources\views/site/Terms.blade.php ENDPATH**/ ?>