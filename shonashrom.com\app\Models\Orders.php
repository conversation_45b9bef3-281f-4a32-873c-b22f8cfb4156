<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Orders extends Model
{
    use HasFactory;
     protected $table = 'orders';
     protected $fillable = [
   
     
                'Date',
                'Payment_Method',   
                'Product_Numbers',
                'Total_Qty',
                'Total_Price',
                'Shipping',
                'Name',   
                'Email',
                'Phone',
                'OtherPhone', 
                'Address_Name',
                'Special_MarkAdd',   
                'StreetAdd',
                'BulidingAdd',
                'FloorAdd',
                'FlatAdd',
                'Governrate',   
                'City',
                'Place',
                'LocationAdd',
                'Address_DetailsAdd',
                'USER_ID',
                'User',
              

    ];
}
