@php
use App\Models\CompanyData;
use App\Models\Footer;
use App\Models\MainEComDesign;

$Def = CompanyData::orderBy('id','desc')->first();
$Footer = Footer::orderBy('id','desc')->first();
$main = MainEComDesign::orderBy('id','desc')->first();
@endphp


     <body>
    <!--********************************
   		Code Start From Here
	******************************** -->




    <!--==============================
     Preloader
    ==============================-->
    <div class="preloader ">
        <div class="preloader-inner">
            <span class="loader"></span>
        </div>
    </div>
    <div class="popup-search-box">
        <button class="searchClose"><i class="fal fa-times"></i></button>
        <form action="#">
            <input type="text" placeholder="Search Here..">
            <button type="submit"><i class="fal fa-search"></i></button>
        </form>
    </div>
    <div class="sidemenu-wrapper">
        <div class="sidemenu-content">
            <button class="closeButton sideMenuCls"><i class="far fa-times"></i></button>
            <div class="widget footer-widget">
                <div class="widget-about">
                    <div class="footer-logo">

                          <a href="{{url('/')}}"><img src="{{URL::to($Def->Logo_Store)}}" alt=""></a>
                    </div>

                    <div class="social-btn style2">
                   @if(!empty($Social->Facebook))
                                  <a href="{{$Social->Facebook}}" title="Facebook" target="_blank"><i class="fab fa-facebook-f"></i></a>
                                  @endif

                                        @if(!empty($Social->Twitter))
                                  <a href="{{$Social->Twitter}}" title="Facebook" target="_blank"><i class="fab fa-twitter"></i></a>
                                  @endif

                                        @if(!empty($Social->Youtube))
                                  <a href="{{$Social->Youtube}}" title="Facebook" target="_blank"><i class="fab fa-youtube"></i></a>
                                  @endif


                                        @if(!empty($Social->LinkedIn))
                                  <a href="{{$Social->LinkedIn}}" title="Facebook" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                                  @endif



                                        @if(!empty($Social->Instagram))
                                  <a href="{{$Social->Instagram}}" title="Facebook" target="_blank"><i class="fab fa-instagram"></i></a>
                                  @endif


                                        @if(!empty($Social->Snapchat))
                                  <a href="{{$Social->Snapchat}}" title="Facebook" target="_blank"><i class="fab fa-snapchat"></i></a>
                                  @endif


                                        @if(!empty($Social->Whatsapp))
                                  <a href="https://wa.me/{{$Social->Whatsapp}}" title="Facebook" target="_blank"><i class="fab fa-whatsapp"></i></a>
                                  @endif


                                        @if(!empty($Social->Google_Plus))
                                  <a href="{{$Social->Google_Plus}}" title="Facebook" target="_blank"><i class="fab fa-google-plus-g"></i></a>
                                  @endif


                                        @if(!empty($Social->Pinterest))
                                  <a href="{{$Social->Pinterest}}" title="Facebook" target="_blank"><i class="fab fa-pinterest"></i></a>
                                  @endif


                                        @if(!empty($Social->Telegram))
                                  <a href="https://t.me/{{$Social->Telegram}}" title="Facebook" target="_blank"><i class="fab fa-telegram"></i></a>
                                  @endif
                    </div>
                </div>
            </div>
            <div class="widget widget_nav_menu footer-widget">
                <h3 class="widget_title">{{trans('admin.Pages')}}</h3>
                <ul class="menu">

                    <li><a href="{{url('/')}}">{{trans('admin.Home')}}</a></li>
                              <li><a href="#About">{{trans('admin.About')}}</a></li>
                              <li><a href="{{url('ShopSite')}}">{{trans('admin.Shop')}}</a></li>
                              <li><a href="{{url('PolicySite')}}">{{trans('admin.Policy')}} </a></li>
                              <li><a href="{{url('TermsSite')}}">{{trans('admin.Terms')}} </a></li>
                              <li><a href="{{url('FAQSite')}}">{{trans('admin.FAQ')}}</a></li>
                </ul>
            </div>
        </div>
    </div>
    <!--==============================
    Mobile Menu
    ============================== -->
    <div class="mobile-menu-wrapper">
        <div class="mobile-menu-area text-center">
            <button class="menu-toggle"><i class="fal fa-times"></i></button>
            <div class="mobile-logo">
               <a href="{{url('/')}}"><img src="{{URL::to($Def->Logo_Store)}}" alt=""></a>
            </div>
            <div class="mobile-menu">
                <ul>
                    <li>
                      <a href="{{url('/')}}">{{trans('admin.Home')}} </a>

                    </li>
                       <li>
                                @if(url()->current() == URL::to('/'))
                    <a href="#About">{{trans('admin.About')}} </a>
                            @else
<a href="{{url('/')}}">{{trans('admin.About')}} </a>
 @endif

                    </li>
                    <li>
                  <a href="{{url('ShopSite')}}">{{trans('admin.Shop')}}</a>

                    </li>
                    <li>
               <a href="{{url('BlogSite')}}">{{trans('admin.Blog')}}</a>

                    </li>


                       <li>
                    <a href="{{url('FAQSite')}}">{{trans('admin.FAQ')}} </a>
                    </li>

                    <li>
                    <a href="{{url('ContactSite')}}">{{trans('admin.Contact')}} </a>

                    </li>


                    <li>
                       <div class="col-auto  d-lg-block iconsNav">
                            <div class="navbar-right-desc">

                                     @if(!empty(auth()->user()->id))

                                 <a href="{{url('MyProfileSite')}}"> <i class="fas fa-id-card" ></i></a>
                                   <a href="{{url('LogoutSite')}}">     <i class="fa-solid fa-right-from-bracket"></i></a>

                                  @else

                                       <a href="{{url('LoginSite')}}"> <i class="fas fa-user" ></i></a>
                                    @endif

                                    <a href="{{url('CartSite')}}"><i class="fas fa-shopping-basket"></i>
                              <span>{{Cart::content()->count()}}</span>
                           </a>
                            </div>
                        </div>




                    </li>
                </ul>
            </div>
        </div>
    </div>




    <!--==============================
	Header Area
    ==============================-->
    <header class="nav-header header-layout1">
        <div class="header-top d-lg-block d-none">
            <div class="container-fluid">
                <div class="row justify-content-center justify-content-lg-between align-items-center gy-2">
                    <div class="col-auto d-none d-lg-block">
                        <div class="header-links">
                            <ul>
                                <li><i class="far fa-envelope"></i><a href="mailto:{{$Footer->Email}}">{{$Footer->Email}}</a></li>
                                <li><i class="far fa-phone"></i> {{$Footer->Phone}}</li>


                            </ul>
                        </div>
                    </div>
                    <div class="col-auto">
                        <div class="header-links">
                            <ul>
                                <li>
                                    <div class="social-links">


                                           @if(!empty($Social->Facebook))
                        <a href="{{$Social->Facebook}}" title="Facebook" target="_blank"><i class="fab fa-facebook-f"></i></a>
                                  @endif

                                        @if(!empty($Social->Twitter))
                      <a href="{{$Social->Twitter}}" title="Facebook" target="_blank"><i class="fab fa-twitter"></i></a>
                                  @endif

                                        @if(!empty($Social->Youtube))
                  <a href="{{$Social->Youtube}}" title="Facebook" target="_blank"><i class="fab fa-youtube"></i></a>
                                  @endif


                                        @if(!empty($Social->LinkedIn))
                        <a href="{{$Social->LinkedIn}}" title="Facebook" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                                  @endif



                                        @if(!empty($Social->Instagram))
                    <a href="{{$Social->Instagram}}" title="Facebook" target="_blank"><i class="fab fa-instagram"></i></a>
                                  @endif


                                        @if(!empty($Social->Snapchat))
                     <a href="{{$Social->Snapchat}}" title="Facebook" target="_blank"><i class="fab fa-snapchat"></i></a>
                                  @endif


                                        @if(!empty($Social->Whatsapp))
                   <a href="https://wa.me/{{$Social->Whatsapp}}" title="Facebook" target="_blank"><i class="fab fa-whatsapp"></i></a>
                                  @endif


                                        @if(!empty($Social->Google_Plus))
                  <a href="{{$Social->Google_Plus}}" title="Facebook" target="_blank"><i class="fab fa-google-plus-g"></i></a>
                                  @endif


                                        @if(!empty($Social->Pinterest))
                   <a href="{{$Social->Pinterest}}" title="Facebook" target="_blank"><i class="fab fa-pinterest"></i></a>
                                  @endif


                                        @if(!empty($Social->Telegram))
                      <a href="https://t.me/{{$Social->Telegram}}" title="Facebook" target="_blank"><i class="fab fa-telegram"></i></a>
                                  @endif



                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="sticky-wrapper">
            <!-- Main Menu Area -->
            <div class="menu-area">
                <div class="container-fluid">
                    <div class="row align-items-center justify-content-lg-start justify-content-between">
                        <div class="col-auto">
                            <div class="header-logo">
                                <a href="{{url('/')}}"><img src="{{URL::to($Def->Logo_Store)}}" alt="logo" style="width:100px; height:auto;"></a>

                            </div>
                        </div>
                        <div class="col-auto">
                            <nav class="main-menu d-none d-lg-inline-block">
                                <ul>
                                    <li>
                                      <a href="{{url('/')}}">{{trans('admin.Home')}} </a>

                                    </li>

                                    <li >
                                     @if(url()->current() == URL::to('/'))
                                    <a href="#About">{{trans('admin.About')}} </a>
                                       @else
                                    <a href="{{url('/')}}">{{trans('admin.About')}} </a>
                                     @endif


                                    </li>

                                    <li >
                                   <a href="{{url('ShopSite')}}">{{trans('admin.Shop')}}</a>

                                    </li>
                                    <li>
                               <a href="{{url('BlogSite')}}">{{trans('admin.Blog')}}</a>

                                    </li>

                                         <li>
                                    <a href="{{url('FAQSite')}}">{{trans('admin.FAQ')}} </a>
                                    </li>

                                      <li>
                                      <a href="{{url('ContactSite')}}">{{trans('admin.Contact')}} </a>

                                    </li>





                                  @if(url()->current() != URL::to('/CalculateISP'))
                                             <li class="menu-item-has-children">
                        <a href=""><i class="fas fa-language"></i></a>
                      <ul class="sub-menu">
                        <li><a href="{{url('lang/ar')}}">العربية</a></li>
                                       <li><a href="{{url('lang/en')}}">English</a></li>

                          </ul>
                    </li>
                           @endif



                                </ul>
                            </nav>
                            <div class="navbar-right d-inline-flex d-lg-none">
                                <button type="button" class="menu-toggle icon-btn"><i class="far fa-bars"></i></button>
                            </div>
                        </div>
                        <div class="col-auto ms-auto d-lg-block d-none">
                            <div class="navbar-right-desc">

                               @if(!empty(auth()->user()->id))
                                 <a href="{{url('MyProfileSite')}}"> <i class="fas fa-id-card" ></i></a>

                                   <a href="{{url('LogoutSite')}}">     <i class="fa-solid fa-right-from-bracket"></i></a>

                                @else
                                  <a href="{{url('LoginSite')}}"> <i class="fas fa-user" ></i></a>
                                @endif

                              <a href="{{url('CartSite')}}"><i class="fas fa-shopping-basket"></i>
                              <span>{{Cart::content()->count()}}</span>
                           </a>
                            </div>



                        </div>


                      <!-- <div class="col-auto d-none d-lg-block">
                            <div class="header-button">


                                <a href="{{url('ISP')}}" class="btn d-xl-block d-none">
                                  ISP-ONE
                                </a>

                            </div>
                        </div>  -->



                    </div>
                </div>
            </div>
        </div>
    </header>






