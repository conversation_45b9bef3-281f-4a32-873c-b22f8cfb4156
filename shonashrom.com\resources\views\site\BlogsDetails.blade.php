@extends('site.index')
@section('content')

<title>{{trans('admin.Blogs')}}</title>

    <!--==============================
    Breadcumb
    ============================== -->
    <div class="breadcumb-wrapper" data-bg-src="{{asset('Front/assets/img/bg/breadcrumb-bg.png')}}">
        <!-- bg animated image/ -->
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="breadcumb-content">
                        <h1 class="breadcumb-title">{{trans('admin.Blogs')}}</h1>
                        <ul class="breadcumb-menu">
                            <li><a href="{{url('/')}}">{{trans('admin.Home')}}</a></li>
                            <li class="active">{{trans('admin.Blogs')}}</li>
                        </ul>
                    </div>
                </div>
            </div>

        </div>
    </div>


 <!--==============================
    Blog Area  
    ==============================-->
    <section class="blog-area space-top space-extra-bottom">
        <div class="container">
            <div class="row">
                <div class="col-xxl-12 col-lg-12">
                    <div class="blog-single">
                        <div class="blog-thumb">
                            <img src="{{URL::to($art->Image)}}" alt="img">
                        </div>
                        <div class="blog-content">
                            <div class="blog-meta">
                                <a href="#"><i class="far fa-clock"></i>{{$art->Date}}</a>
                                <a href="#"><i class="far fa-user"></i>{{$art->Author}}</a>  
                            </div>
                            <h2 class="blog-title">{{app()->getLocale() == 'ar' ?$art->Arabic_Title :$art->English_Title}}</h2>
                     
                              {!!app()->getLocale() == 'ar' ?$art->Arabic_Desc :$art->English_Desc!!}     
                            
                         
                        </div>
                
                    </div>
             
                </div>

            </div>
        </div>
    </section>   


@endsection
      