    @php
use App\Models\CompanyData;
$Def=CompanyData::orderBy('id','desc')->first();
@endphp
<!DOCTYPE html>

<html lang="en">
    <head>
        <meta charset="utf-8">
        <title>
           {{trans('admin.Login')}}
        </title>
        <meta name="description" content="Login">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, user-scalable=no, minimal-ui">
        <!-- Call App Mode on ios devices -->
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <!-- Remove Tap Highlight on Windows Phone IE -->
        <meta name="msapplication-tap-highlight" content="no">
  
 
              <!-- Place favicon.ico in the root directory -->
      <link rel="apple-touch-icon" sizes="180x180" href="{{URL::to($Def->Icon)}}">
      <link rel="icon" type="image/png" sizes="32x32" href="{{URL::to($Def->Icon)}}">
      <link rel="mask-icon" href="{{URL::to($Def->Icon)}}" color="#5bbad5">
       
            <?php 
if(!function_exists('direction')){

        function direction(){

            if(session()->has('lang')){
                if(session('lang') == 'ar'){

                 return 'rtl';
                }else{
                    return 'ltr' ;
                }
            }else{
                return 'rtl' ;
            }
        }


        }
    
?>  

  @if(direction()=='ltr') 
         <link id="vendorsbundle" rel="stylesheet" media="screen, print" href="{{asset('Admin/css/vendors.bundle.css')}}">
        <link id="appbundle" rel="stylesheet" media="screen, print" href="{{asset('Admin/css/app.bundle.css')}}">
        <link id="mytheme" rel="stylesheet" media="screen, print" href="#">
        <link id="myskin" rel="stylesheet" media="screen, print" href="{{asset('Admin/css/skins/skin-master.css')}}">
        <link rel="stylesheet" media="screen, print" href="{{asset('Admin/css/page-login-alt.css')}}">
        
       <link rel="stylesheet" media="screen, print" href="{{asset('Admin/css/style.css')}}">
   
  @else
  
       
                  <!-- base css -->
   <link id="vendorsbundle" rel="stylesheet" media="screen, print" href="{{asset('Admin/css/vendors.bundle.css')}}">
        <link id="appbundle" rel="stylesheet" media="screen, print" href="{{asset('Admin/css/app.bundle.css')}}">
        <link id="mytheme" rel="stylesheet" media="screen, print" href="#">
        <link id="myskin" rel="stylesheet" media="screen, print" href="{{asset('Admin/css/skins/skin-master.css')}}">
        <link rel="stylesheet" media="screen, print" href="{{asset('Admin/css/page-login-alt.css')}}">
        
       <link rel="stylesheet" media="screen, print" href="{{asset('Admin/css/style.css')}}">
               <link rel="stylesheet" media="screen, print" href="{{asset('Admin/css/style-ar.css')}}">
  
       
       
   
       
       
    @endif   
       
       
   
        
    </head>
 
    <body>
        <!-- DOC: script to save and load page settings -->
        <script>
            /**
             *	This script should be placed right after the body tag for fast execution 
             *	Note: the script is written in pure javascript and does not depend on thirdparty library
             **/
            'use strict';

            var classHolder = document.getElementsByTagName("BODY")[0],
                /** 
                 * Load from localstorage
                 **/
                themeSettings = (localStorage.getItem('themeSettings')) ? JSON.parse(localStorage.getItem('themeSettings')) :
                {},
                themeURL = themeSettings.themeURL || '',
                themeOptions = themeSettings.themeOptions || '';
            /** 
             * Load theme options
             **/
            if (themeSettings.themeOptions)
            {
                classHolder.className = themeSettings.themeOptions;
                console.log("%c✔ Theme settings loaded", "color: #148f32");
            }
            else
            {
                console.log("%c✔ Heads up! Theme settings is empty or does not exist, loading default settings...", "color: #ed1c24");
            }
            if (themeSettings.themeURL && !document.getElementById('mytheme'))
            {
                var cssfile = document.createElement('link');
                cssfile.id = 'mytheme';
                cssfile.rel = 'stylesheet';
                cssfile.href = themeURL;
                document.getElementsByTagName('head')[0].appendChild(cssfile);

            }
            else if (themeSettings.themeURL && document.getElementById('mytheme'))
            {
                document.getElementById('mytheme').href = themeSettings.themeURL;
            }
            /** 
             * Save to localstorage 
             **/
            var saveSettings = function()
            {
                themeSettings.themeOptions = String(classHolder.className).split(/[^\w-]+/).filter(function(item)
                {
                    return /^(nav|header|footer|mod|display)-/i.test(item);
                }).join(' ');
                if (document.getElementById('mytheme'))
                {
                    themeSettings.themeURL = document.getElementById('mytheme').getAttribute("href");
                };
                localStorage.setItem('themeSettings', JSON.stringify(themeSettings));
            }
            /** 
             * Reset settings
             **/
            var resetSettings = function()
            {
                localStorage.setItem("themeSettings", "");
            }

        </script>
        <div class="blankpage-form-field">
              
            <div class="page-logo m-0 w-100 align-items-center justify-content-center rounded border-bottom-left-radius-0 border-bottom-right-radius-0 px-4">
                <a href="javascript:void(0)" class="page-logo-link press-scale-down d-flex align-items-center">
                       <!--Logo-->
                    @if(!empty($Def->Logo))
                        <img src="{{URL::to($Def->Logo)}}" alt="SmartAdmin WebApp" aria-roledescription="logo">
                    @else  
                  <img src="{{asset('Admin/img/logo.png')}}" alt="SmartAdmin WebApp" aria-roledescription="logo">
                      @endif
                  <span class="page-logo-text mr-1">
                            @if(!empty($Def->Name))
                      {{$Def->Name}}
                      @else
                       {{trans('admin.Klar')}}
                      @endif
                      </span>
                    
                    <i class="fal fa-angle-down d-inline-block ml-1 fs-lg color-primary-300"></i>
                </a>
            </div>
            
            <div class="card p-4 border-top-left-radius-0 border-top-right-radius-0">
                <span id="ex"> @include('admin.layouts.messages')</span>  
                <form action="{{url('Login')}}" method="post" enctype="multipart/form-data">
                    {!! csrf_field() !!}
                    <div class="form-group">
         <input type="email" name="email" id="username" class="form-control" placeholder="{{trans('admin.Email')}}" value="{{old('email')}}" required>
                    </div>
                    <div class="form-group">
                        <input type="password" name="password" id="password" class="form-control" placeholder="{{trans('admin.Password')}}" value="{{old('password')}}" required>
                    </div>
                    <div class="form-group text-left">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" name="rememberme" id="rememberme">
                            <label class="custom-control-label" for="rememberme"> {{trans('admin.RmemberMe')}}</label>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-default"> {{trans('admin.Login')}}</button>
                </form>
            </div>
            
            <div class="blankpage-footer text-center">
                <a href="{{url('forgotpassword')}}"><strong>{{trans('admin.ForgotPassword')}}</strong></a> 
            </div>
        </div>
        
    
        <video poster="{{asset('Admin/img/backgrounds/clouds.png')}}" id="bgvid" playsinline autoplay muted loop>
            <source src="{{asset('Admin/media/video/cc.webm')}}" type="video/webm">
            <source src="{{asset('Admin/media/video/cc.mp4')}}" type="video/mp4">
        </video>
        <!-- BEGIN Color profile -->
        <!-- this area is hidden and will not be seen on screens or screen readers -->
        <!-- we use this only for CSS color refernce for JS stuff -->
        <p id="js-color-profile" class="d-none">
            <span class="color-primary-50"></span>
            <span class="color-primary-100"></span>
            <span class="color-primary-200"></span>
            <span class="color-primary-300"></span>
            <span class="color-primary-400"></span>
            <span class="color-primary-500"></span>
            <span class="color-primary-600"></span>
            <span class="color-primary-700"></span>
            <span class="color-primary-800"></span>
            <span class="color-primary-900"></span>
            <span class="color-info-50"></span>
            <span class="color-info-100"></span>
            <span class="color-info-200"></span>
            <span class="color-info-300"></span>
            <span class="color-info-400"></span>
            <span class="color-info-500"></span>
            <span class="color-info-600"></span>
            <span class="color-info-700"></span>
            <span class="color-info-800"></span>
            <span class="color-info-900"></span>
            <span class="color-danger-50"></span>
            <span class="color-danger-100"></span>
            <span class="color-danger-200"></span>
            <span class="color-danger-300"></span>
            <span class="color-danger-400"></span>
            <span class="color-danger-500"></span>
            <span class="color-danger-600"></span>
            <span class="color-danger-700"></span>
            <span class="color-danger-800"></span>
            <span class="color-danger-900"></span>
            <span class="color-warning-50"></span>
            <span class="color-warning-100"></span>
            <span class="color-warning-200"></span>
            <span class="color-warning-300"></span>
            <span class="color-warning-400"></span>
            <span class="color-warning-500"></span>
            <span class="color-warning-600"></span>
            <span class="color-warning-700"></span>
            <span class="color-warning-800"></span>
            <span class="color-warning-900"></span>
            <span class="color-success-50"></span>
            <span class="color-success-100"></span>
            <span class="color-success-200"></span>
            <span class="color-success-300"></span>
            <span class="color-success-400"></span>
            <span class="color-success-500"></span>
            <span class="color-success-600"></span>
            <span class="color-success-700"></span>
            <span class="color-success-800"></span>
            <span class="color-success-900"></span>
            <span class="color-fusion-50"></span>
            <span class="color-fusion-100"></span>
            <span class="color-fusion-200"></span>
            <span class="color-fusion-300"></span>
            <span class="color-fusion-400"></span>
            <span class="color-fusion-500"></span>
            <span class="color-fusion-600"></span>
            <span class="color-fusion-700"></span>
            <span class="color-fusion-800"></span>
            <span class="color-fusion-900"></span>
        </p>
     
        <script src="{{asset('Admin/js/vendors.bundle.js')}}"></script>
        <script src="{{asset('Admin/js/app.bundle.js')}}"></script>
           <script>
$(document).ready(function(){
  setTimeout(function(){ $("#ex").hide(); }, 6000);
});
</script>
        <!-- Page related scripts -->
    </body>
    <!-- END Body -->
</html>
