@extends('admin.index')
@section('content')

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">{{trans('admin.EComDesign')}}</h4>
            </div>
            <div class="card-body">
                @if(!empty($Main))
                <form action="{{url('UpdateDesignSystem')}}" method="post" class="form-row" enctype="multipart/form-data">
                    {!! csrf_field() !!}
                    
                    <div class="col-md-12">
                        <div class="data-def">
                            <div class="form-row">
                                
                                <!-- Font Configuration -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{trans('admin.Font_Type')}}</label>
                                        <select name="Font_Type" class="form-control">
                                            <option value="1" {{$Main->Font_Type == 1 ? 'selected' : ''}}>Cairo</option>
                                            <option value="2" {{$Main->Font_Type == 2 ? 'selected' : ''}}>Roboto</option>
                                            <option value="3" {{$Main->Font_Type == 3 ? 'selected' : ''}}>Open Sans</option>
                                            <option value="4" {{$Main->Font_Type == 4 ? 'selected' : ''}}>Poppins</option>
                                            <option value="5" {{$Main->Font_Type == 5 ? 'selected' : ''}}>Alexandria</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- Body Background Type -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{trans('admin.Body_BG_Type')}}</label>
                                        <select name="Body_BG_Type" class="form-control" id="Body_BG_Type">
                                            <option value="1" {{$Main->Body_BG_Type == 1 ? 'selected' : ''}}>{{trans('admin.Color')}}</option>
                                            <option value="2" {{$Main->Body_BG_Type == 2 ? 'selected' : ''}}>{{trans('admin.Image')}}</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- Body Background Color -->
                                <div class="col-md-6" id="BodyColor">
                                    <div class="form-group">
                                        <label>{{trans('admin.Body_BG_Color')}}</label>
                                        <input type="color" name="Body_BG_Color" class="form-control" value="{{$Main->Body_BG_Color}}">
                                    </div>
                                </div>

                                <!-- Body Background Image -->
                                <div class="col-md-6" id="BodyImage" style="display: none;">
                                    <div class="form-group">
                                        <label>{{trans('admin.Body_BG_Image')}}</label>
                                        <input type="file" name="Body_BG_Image" class="form-control">
                                        @if($Main->Body_BG_Image)
                                            <img src="{{URL::to($Main->Body_BG_Image)}}" style="width: 100px; height: 60px; margin-top: 10px;">
                                        @endif
                                    </div>
                                </div>

                                <!-- Breadcrumb Background Color -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{trans('admin.Breadcumb_BG_Color')}}</label>
                                        <input type="color" name="Breadcumb_BG_Color" class="form-control" value="{{$Main->Breadcumb_BG_Color}}">
                                    </div>
                                </div>

                                <!-- Breadcrumb Text Color -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{trans('admin.Breadcumb_Txt_Color')}}</label>
                                        <input type="color" name="Breadcumb_Txt_Color" class="form-control" value="{{$Main->Breadcumb_Txt_Color}}">
                                    </div>
                                </div>

                                <!-- Sub Page Background Color -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{trans('admin.Sub_Page_BG_Color')}}</label>
                                        <input type="color" name="Sub_Page_BG_Color" class="form-control" value="{{$Main->Sub_Page_BG_Color}}">
                                    </div>
                                </div>

                                <!-- Footer Title Color -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{trans('admin.Footer_Title_Color')}}</label>
                                        <input type="color" name="Footer_Title_Color" class="form-control" value="{{$Main->Footer_Title_Color}}">
                                    </div>
                                </div>

                                <!-- Footer Text Color -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{trans('admin.Footer_Txt_Color')}}</label>
                                        <input type="color" name="Footer_Txt_Color" class="form-control" value="{{$Main->Footer_Txt_Color}}">
                                    </div>
                                </div>

                                <!-- Footer Text Hover Color -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{trans('admin.Footer_Txt_Hover_Color')}}</label>
                                        <input type="color" name="Footer_Txt_Hover_Color" class="form-control" value="{{$Main->Footer_Txt_Hover_Color}}">
                                    </div>
                                </div>

                                <!-- Footer Social Background Color -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{trans('admin.Footer_Social_BG_Color')}}</label>
                                        <input type="color" name="Footer_Social_BG_Color" class="form-control" value="{{$Main->Footer_Social_BG_Color}}">
                                    </div>
                                </div>

                                <!-- Footer Social Color -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{trans('admin.Footer_Social_Color')}}</label>
                                        <input type="color" name="Footer_Social_Color" class="form-control" value="{{$Main->Footer_Social_Color}}">
                                    </div>
                                </div>

                                <!-- Footer Social Hover Background Color -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{trans('admin.Footer_Social_Hover_BG_Color')}}</label>
                                        <input type="color" name="Footer_Social_Hover_BG_Color" class="form-control" value="{{$Main->Footer_Social_Hover_BG_Color}}">
                                    </div>
                                </div>

                                <!-- Footer Social Hover Text Color -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{trans('admin.Footer_Social_Hover_Txt_Color')}}</label>
                                        <input type="color" name="Footer_Social_Hover_Txt_Color" class="form-control" value="{{$Main->Footer_Social_Hover_Txt_Color}}">
                                    </div>
                                </div>

                                <!-- Preloader Background Color -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{trans('admin.Preloader_BG_Color')}}</label>
                                        <input type="color" name="Preloader_BG_Color" class="form-control" value="{{$Main->Preloader_BG_Color}}">
                                    </div>
                                </div>

                                <!-- Preloader Small Circle Color -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{trans('admin.Preloader_Small_Circle_Color')}}</label>
                                        <input type="color" name="Preloader_Small_Circle_Color" class="form-control" value="{{$Main->Preloader_Small_Circle_Color}}">
                                    </div>
                                </div>

                                <!-- Preloader Large Circle Color -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{trans('admin.Preloader_Large_Circle_Color')}}</label>
                                        <input type="color" name="Preloader_Large_Circle_Color" class="form-control" value="{{$Main->Preloader_Large_Circle_Color}}">
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <button type="submit" class="btn btn-primary">{{trans('admin.Update')}}</button>
                    </div>
                </form>
                @else
                <div class="alert alert-warning">
                    {{trans('admin.No_Design_Configuration_Found')}}
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    var x = $('#Body_BG_Type').val();
    
    if (parseFloat(x) == 1) {
        document.getElementById('BodyColor').style.display = "block";
        document.getElementById('BodyImage').style.display = "none";
    } else {
        document.getElementById('BodyColor').style.display = "none";
        document.getElementById('BodyImage').style.display = "block";
    }
    
    $('#Body_BG_Type').change(function() {
        var x = $(this).val();
        
        if (parseFloat(x) == 1) {
            document.getElementById('BodyColor').style.display = "block";
            document.getElementById('BodyImage').style.display = "none";
        } else {
            document.getElementById('BodyColor').style.display = "none";
            document.getElementById('BodyImage').style.display = "block";
        }
    });
});
</script>

@endsection
