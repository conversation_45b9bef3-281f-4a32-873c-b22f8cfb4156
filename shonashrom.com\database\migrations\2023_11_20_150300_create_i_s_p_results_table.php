<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateISPResultsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('i_s_p_results', function (Blueprint $table) {
            $table->id();
            $table->longText('Arabic_Desc')->nullable();
            $table->longText('English_Desc')->nullable();
            $table->longText('Arabic_Title')->nullable();
            $table->longText('English_Title')->nullable();
            $table->longText('Image')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('i_s_p_results');
    }
}
