<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Admin;
use App\Models\CompanyData;
use App\Models\SocialMedia;
use App\Models\Webslider;
use App\Models\About;
use App\Models\Services;
use App\Models\HowWeWork;
use App\Models\Gallery;
use App\Models\Categories;
use App\Models\Articles;
use App\Models\Testiminoals;
use App\Models\Products;
use App\Models\HowWorkIcons;
use App\Models\Terms;
use App\Models\Privacy;
use App\Models\Footer;
use App\Models\User;
use App\Models\Features;
use App\Models\FAQ;
use App\Models\Groups;
use App\Models\ProductsImags;
use App\Models\ISP;
use App\Models\Comments;
use App\Models\Rate;
use App\Models\ISPResult;
use App\Models\Addersses;
use App\Models\Governrate;
use App\Models\City;
use App\Models\Place;
use App\Models\Orders;
use App\Models\ProductOrder;
use App\Models\ShonaDesign;
use App\Helpers\DesignHelper;
use DB ;
use Str ;
use App\Mail\UserResetPassword;
use App\Mail\AdminResetPassword;
use Carbon\Carbon;
use Mail;
use Auth;
use URL;
use Cart;
use Hash;


class SiteController extends Controller
{
    //Home
         public function Home(){

        // Get design configuration
        $design = DesignHelper::getCurrentDesign();
        $company = DesignHelper::getCompanyData();
        $fonts = DesignHelper::getFontConfiguration($design);

        // Get content data
        $Def = CompanyData::orderBy('id','desc')->first();
        $Footer = Footer::orderBy('id','desc')->first();
        $Social = SocialMedia::orderBy('id','desc')->first();
        $HowWeWork = HowWeWork::orderBy('id','desc')->first();
        $HowWeWork1 = HowWorkIcons::find(1);
        $HowWeWork2 = HowWorkIcons::find(2);
        $HowWeWork3 = HowWorkIcons::find(3);
        $HowWeWork4 = HowWorkIcons::find(4);
        $About = About::orderBy('id','desc')->first();
        $Services = Services::all();
        $Articles = Articles::take(3)->get();
        $Testiminoals = Testiminoals::all();
        $Webslider = Webslider::all();
        $Features = Features::all();
        $Gallery = Gallery::all();
        $Products = Products::all();

        return view('site.Home',[
            'About' => $About,
            'Services' => $Services,
            'Def' => $Def,
            'HowWeWork' => $HowWeWork,
            'Articles' => $Articles,
            'Testiminoals' => $Testiminoals,
            'Webslider' => $Webslider,
            'HowWeWork1' => $HowWeWork1,
            'HowWeWork2' => $HowWeWork2,
            'HowWeWork3' => $HowWeWork3,
            'HowWeWork4' => $HowWeWork4,
            'Footer' => $Footer,
            'Features' => $Features,
            'Gallery' => $Gallery,
            'Products' => $Products,
            'Social' => $Social,
            // Design system data
            'design' => $design,
            'company' => $company,
            'fonts' => $fonts,
        ]);

    }


    //BlogSite
         public function BlogSite(){



        $Articles=Articles::paginate(9);

        return view('site.Blogs',[

        'Articles'=>$Articles,


        ]);

    }

            public function BlogsDet($id){



        $art=Articles::find($id);

        return view('site.BlogsDetails',[

        'art'=>$art,


        ]);

    }



    //TermsSite
             public function TermsSite(){



        $item=Terms::orderBy('id','desc')->first();

        return view('site.Terms',[

        'item'=>$item,


        ]);

    }


//PolicySite
               public function PolicySite(){



        $item=Privacy::orderBy('id','desc')->first();

        return view('site.Privacy',[

        'item'=>$item,


        ]);

    }

//ContactSite
     public function ContactSite(){

          $Footer=Footer::orderBy('id','desc')->first();

        return view('site.Contact',[

        'Footer'=>$Footer,


        ]);

    }


    //FAQSite
       public function FAQSite(){



        $Faqs=FAQ::all();

        return view('site.FAQ',[

        'Faqs'=>$Faqs,


        ]);

    }


    //Shop
         public function ShopSite(){



        $Products=Products::paginate(12);
        $Groups=Groups::all();

        return view('site.Shop',[

        'Products'=>$Products,
        'Groups'=>$Groups,


        ]);

    }
          public function FilterProGroup($id){



        $Products=Products::where('Group_ID',$id)->paginate(12);
        $Groups=Groups::all();

        return view('site.Shop',[

        'Products'=>$Products,
        'Groups'=>$Groups,


        ]);

    }

     public function FilterSearchName(){


$search=request('search');
        $Products=Products::where('P_Ar_Name','like','%' . $search . '%')
            ->orWhere('P_En_Name','like','%' . $search . '%')
            ->paginate(12);
        $Groups=Groups::all();

        return view('site.Shop',[

        'Products'=>$Products,
        'Groups'=>$Groups,


        ]);

    }




//PrescriptionPro
     public function PrescriptionPro($id){



        $Pro=Products::where('id',$id)->first();


        return view('site.Prescription',[

        'Pro'=>$Pro,



        ]);

    }
//ProDetails
      public function ProDetails($id){



        $Pro=Products::where('id',$id)->first();
        $pro=Products::where('id',$id)->first();

  $Subs=ProductsImags::where('Pro_ID',$Pro->id)->get();
  $Comments=Comments::where('Status',1)->where('Product',$Pro->id)->get();

      $Products=Products::where('Group_ID',$Pro->Group_ID)->take(3)->get();
        return view('site.ProDetails',[

        'Pro'=>$Pro,
        'pro'=>$pro,
        'Subs'=>$Subs,
        'Products'=>$Products,
        'Comments'=>$Comments,



        ]);

    }





    //ISP
      public function CalculateISP(){

             $data= $this->validate(request(),[

             'Name'=>'required',
             'Height'=>'required',
             'HeightUnit'=>'required',
             'Weight'=>'required',
             'WightUnit'=>'required',
             'Age'=>'required',
             'Gender'=>'required',
             'Sports'=>'required',


                ],[


         ]);




          if(request('HeightUnit') == 'CM'){

              $HeightInMeter=request('Height') /  100 ;

          }elseif(request('HeightUnit') == 'M'){

               $HeightInMeter=request('Height') /  1 ;

          }elseif(request('HeightUnit') == 'Inch'){

               $HeightInMeter=request('Height') /  39.37 ;

          }elseif(request('HeightUnit') == 'Feet'){

               $HeightInMeter=request('Height') /  3.28 ;

          }



            if(request('WightUnit') == 'KG'){

              $WeightInKG=request('Weight') /  1 ;

          }elseif(request('WightUnit') == 'Pound'){

               $WeightInKG=request('Weight') /  2.2046 ;

          }



          $Moshr_Kotlt_Gsm=$WeightInKG /  ($HeightInMeter * $HeightInMeter) ;



          if($HeightInMeter > 1.52){


              $ZaydtTool=$HeightInMeter - 1.52;
              $ZaydtToolToInch=$ZaydtTool * 39.37 ;

              if(request('Gender') == 'Male'){


                    $WaznMsaly=56.2 + (1.41 * $ZaydtToolToInch);

              }elseif(request('Gender') == 'Female'){

                  $WaznMsaly=53.1 + (1.36 * $ZaydtToolToInch);
              }


          }else{

                   $WaznMsaly=22 * ($HeightInMeter * $HeightInMeter);


          }


          $mkdar_wazn_mtlop_fkdanh=$WeightInKG - $WaznMsaly ;
          $saarat_hararya_mtlop_fkdnah=$mkdar_wazn_mtlop_fkdanh * 7700 ;
          $waktMatwkaaLlwsolWaznMasaly=$mkdar_wazn_mtlop_fkdanh / 2 ;
          $Add3bwatOstegy=($waktMatwkaaLlwsolWaznMasaly * 30) / 25 ;



          if(request('Sports') == 0){

                 $So3ratYomen=$WeightInKG * 13 * 2.205 ;

              }elseif(request('Sports') == 4){
                        $So3ratYomen=$WeightInKG * 15 * 2.205 ;

              }elseif(request('Sports') == 5){
                     $So3ratYomen=$WeightInKG * 18 * 2.205 ;

              }


          $so3ratPoortinat= $So3ratYomen * 0.25 ;

          $gramProtinat=$So3ratYomen * 0.075;



if(!empty(auth()->user()->id)){

    ISP::where('User',auth()->user()->id)->delete();

     $data['Name']=request('Name');
     $data['Height']=request('Height');
     $data['HeightUnit']=request('HeightUnit');
     $data['Weight']=request('Weight');
     $data['WightUnit']=request('WightUnit');
     $data['Age']=request('Age');
     $data['Gender']=request('Gender');
     $data['Sports']=request('Sports');
     $data['User']=auth()->user()->id;
     $data['Moshr_Kotlt_Gsm']=$Moshr_Kotlt_Gsm;
     $data['ZaydtTool']=$ZaydtTool;
     $data['ZaydtToolToInch']=$ZaydtToolToInch;
     $data['WaznMsaly']=$WaznMsaly;
     $data['mkdar_wazn_mtlop_fkdanh']=$mkdar_wazn_mtlop_fkdanh;
     $data['saarat_hararya_mtlop_fkdnah']=$saarat_hararya_mtlop_fkdnah;
     $data['waktMatwkaaLlwsolWaznMasaly']=$waktMatwkaaLlwsolWaznMasaly;
     $data['3dd3bwatOstegy']=$Add3bwatOstegy;
     $data['So3ratYomen']=$So3ratYomen;
     $data['so3ratPoortinat']=$so3ratPoortinat;
     $data['gramProtinat']=$gramProtinat;

                  ISP::create($data);



}

          $Result=ISPResult::orderBy('id','desc')->first();

           return view('site.ResultISP',[

               'Moshr_Kotlt_Gsm'=>$Moshr_Kotlt_Gsm,
               'WaznMsaly'=>$WaznMsaly,
               'mkdar_wazn_mtlop_fkdanh'=>$mkdar_wazn_mtlop_fkdanh,
               'saarat_hararya_mtlop_fkdnah'=>$saarat_hararya_mtlop_fkdnah,
               'waktMatwkaaLlwsolWaznMasaly'=>$waktMatwkaaLlwsolWaznMasaly,
               'Add3bwatOstegy'=>$Add3bwatOstegy,
               'So3ratYomen'=>$So3ratYomen,
               'so3ratPoortinat'=>$so3ratPoortinat,
               'gramProtinat'=>$gramProtinat,
               'Result'=>$Result,

           ]);


    }

        public function ISP(){

        return view('site.ISP');

    }



        //LoginSite
             public function LoginSite(){

        return view('site.Login');

    }

        public function PostLoginSite(){

        $data= $this->validate(request(),[
             'email'=>'required|email',
             'password'=>'required|min:6|max:50',
               ],[
         ]);

       if(auth()->attempt(['email'=>request('email'),'password'=>request('password')])){
          return redirect('/');

       }else{
          session()->flash('error',trans('admin.incorrect_information_login'));
       	 return back();
       }



    }

         public function LogoutSite(){
    auth()->logout();
	return redirect('/');

 }

                public function ForgetSite(){

        return view('site.Forget');

    }


            public function PostForgetSite(){

            $admin = User::where('email',request('email'))->first();
               if(!empty($admin)){


                   $code=rand(10,10000);

                   User::where('id',$admin->id)->update(['code'=>$code]);

                  $admin = User::where('email',request('email'))->first();
                 Mail::to($admin->email)->send(new UserResetPassword(['data'=>$admin]));
                   session()->flash('success',trans('admin.Reset_Password'));

                    return view('site.VerifyCode',['admin'=>$admin]);


               }else{

                session()->flash('success',trans('admin.WrongEmail'));
  return back();

               }



        }


  public function PostCodeSite(){


         $admin = User::where('id',request('user'))->first();
               if($admin->code == request('code')){

                   User::where('id',$admin->id)->update(['code'=>null]);

                    return view('site.ResetPassword',['admin'=>$admin]);

               }else{

                session()->flash('error',trans('admin.WrongCode'));
                return view('site.VerifyCode',['admin'=>$admin]);

               }



        }

     public function PostResetPassword(){


         $data['password']=bcrypt(request('Password'));

         User::where('id',request('user'))->update($data);

            if(auth()->attempt(['email'=>request('email'),'password'=>request('Password')])){

          return redirect('/');

       }else{

       return redirect('/');
            }


    }


    //Register
         public function PostRegisterSite(){

        $data= $this->validate(request(),[
             'email'=>'required|email|unique:users',
             'password'=>'required|min:6|max:50',
               ],[

            'email.email' =>trans('admin.emailEmail'),
            'email.required' =>trans('admin.emailRequired'),
            'email.unique' =>trans('admin.emailUnique'),
               'password.required' =>trans('admin.passwordRequired'),
            'password.min' => trans('admin.passwordmin_6'),
         ]);



           $data['name']=request('name');
         $data['email']=request('email');
         $data['phone']=request('phone');
         $data['password']=bcrypt(request('password'));

         User::create($data);


            if(auth()->attempt(['email'=>request('email'),'password'=>request('password')])){
          return redirect('/');

       }else{
          session()->flash('error',trans('admin.incorrect_information_login'));
       	 return back();
       }




    }



    //Comments
      public function AddComment(){

        $data= $this->validate(request(),[
             'Comment'=>'required',
               ],[


         ]);


         $data['Product']=request('Product');
         $data['User']=auth()->user()->id;
         $data['Comment']=request('Comment');
         $data['Date']=date('Y-m-d');
         Comments::create($data);

             session()->flash('success',trans('admin.Thanks'));
             return back();

    }

      public function DeleteComment($id){

            $del=Comments::find($id);
            $del->delete();
            session()->flash('error',trans('admin.Deleted'));
            return back();

}

     public function EditComment(){

        $data= $this->validate(request(),[
             'Comment'=>'required',
               ],[


         ]);


         Comments::where('id',request('ID'))->update(['Comment'=>request('Comment')]);

             session()->flash('success',trans('admin.Updated'));
             return back();

    }


    //Rate
      public function AddRate(){

        $data= $this->validate(request(),[
             'Rate'=>'required',
               ],[


         ]);


         $data['Product']=request('Product');
         $data['User']=auth()->user()->id;
         $data['Rate']=request('Rate');
         Rate::create($data);


             session()->flash('success',trans('admin.Thanks'));
             return back();

    }

      public function EditRate(){

        $data= $this->validate(request(),[
             'Rate'=>'required',
               ],[


         ]);


         $data['Rate']=request('Rate');
         Rate::where('id',request('ID'))->update($data);


             session()->flash('success',trans('admin.Thanks'));
             return back();

    }

    //MyProfileSite
               public function MyProfileSite(){



        $item=User::find(auth()->user()->id);
        $Addersses=Addersses::where('User',auth()->user()->id)->get();
        $Orders=Orders::where('User',auth()->user()->id)->get();
        $Governrate=Governrate::get();

        return view('site.MyProfile',[

        'item'=>$item,
        'Addersses'=>$Addersses,
        'Governrate'=>$Governrate,
        'Orders'=>$Orders,


        ]);

    }



    //UpdateAccount
         public function UpdateAccount(){

        $data= $this->validate(request(),[
             'name'=>'required',
              'email'=>'required|email|unique:users,email,'.auth()->user()->id,
             'phone'=>'required|unique:users,Phone,'.auth()->user()->id,

               ],[
            'name.required' => trans('admin.nameRequired'),
            'country.required' => trans('admin.countryRequired'),
            'email.required' => trans('admin.emailRequired'),
            'email.email' =>trans('admin.emailEmail'),
            'email.unique' =>trans('admin.emailUnique'),

         ]);








         $data['name']=request('name');
       $data['phone']=request('phone');
    $data['email']=request('email');


         User::where('id',auth()->user()->id)->update($data);




             session()->flash('success',trans('admin.Updated'));
             return back();

    }

  //UpdatePassword
     public function UpdatePassword(){

        $data= $this->validate(request(),[

             'password'=>'required|min:6|same:Confirm_Password',

               ],[

            'password.required' =>trans('admin.passwordRequired'),
            'password.min' => trans('admin.passwordmin_6'),
            'password.same' => trans('admin.passwordsame'),

         ]);

         $user= User::find(auth()->user()->id);



             if (Hash::check(request('CurrentPassword'), $user->password)) {

                       $data['password']=bcrypt(request('password'));

                User::where('id',auth()->user()->id)->update($data);






                       session()->flash('success',trans('admin.Updated'));
             return back();

             }else{

                         session()->flash('error',trans('admin.CurrentPassWrong'));
             return back();
             }






    }



    //UpdateAddress
        public function UpdateAddress(){


            $user=User::find(auth()->user()->id);



            $Addr['Address_Name']=request('Address_Name');
            $Addr['User']=auth()->user()->id;
            $Addr['Gov']=request('Governrate');
            $Addr['City']=request('City');
            $Addr['Place']=request('Place');
            $Addr['Street']=request('StreetAdd');
            $Addr['Bulliding']=request('BulidingAdd');
            $Addr['Floor']=request('FloorAdd');
            $Addr['Flat']=request('FlatAdd');
            $Addr['Special_Mark']=request('Special_MarkAdd');
            $Addr['Location']=request('LocationAdd');
            $Addr['Address_Details']=request('Address_DetailsAdd');

           Addersses::create($Addr);

             session()->flash('success',trans('admin.Updated'));
             return back();

    }


            public function EditAddress(){


            $user=User::find(auth()->user()->id);



            $Addr['Address_Name']=request('Address_Name');
            $Addr['User']=auth()->user()->id;
            $Addr['Gov']=request('Governrate');
            $Addr['City']=request('City');
            $Addr['Place']=request('Place');
            $Addr['Street']=request('StreetAdd');
            $Addr['Bulliding']=request('BulidingAdd');
            $Addr['Floor']=request('FloorAdd');
            $Addr['Flat']=request('FlatAdd');
            $Addr['Special_Mark']=request('Special_MarkAdd');
            $Addr['Location']=request('LocationAdd');
            $Addr['Address_Details']=request('Address_DetailsAdd');
            $Addr['Address_ID']=request('ADDRESS_ID');
           Addersses::where('id',request('ID'))->update($Addr);

             session()->flash('success',trans('admin.Updated'));
             return back();

    }


     public function DeleteMyAddress($id,$Address){


            $user=User::find(auth()->user()->id);



              $del=Addersses::find($id);
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();


    }



//
            public function GovernrateFilterr($id) {

               if(app()->getLocale() == 'ar' ){
       $states = DB::table("cities")->where("GOV_ID",$id)->pluck("Arabic_Name","id");
               }else{
          $states = DB::table("cities")->where("GOV_ID",$id)->pluck("English_Name","id");
               }

       return response()->json($states);

    }

      public function CityFilterr($id) {

               if(app()->getLocale() == 'ar' ){
       $states = DB::table("places")->where("CIT_ID",$id)->pluck("Arabic_Name","id");
               }else{
           $states = DB::table("places")->where("CIT_ID",$id)->pluck("English_Name","id");
               }

       return response()->json($states);

    }

    public function CityShip($id) {


        $states=[];

       $X = DB::table("cities")->where("id",$id)->first();


           $states +=['ship'=>$X->Ship_Price];

       return response()->json($states);

    }




        //Cart
     public function AddToCart(){

         if(  request('OfferPrice') == 0){

                 $Price=request('Price');
         }else{
                  $Price=request('OfferPrice');

         }


         $Qty=request('Qty');

         $pro =Products::where('id',request('Product'))->first();

            $Name=$pro->P_Ar_Name;
            $OtherName=$pro->P_En_Name;

       Cart::add(['id' =>request('Product'), 'name' =>$Name, 'qty' => $Qty, 'price' =>$Price, 'weight' =>0 ,'tax'=>0, 'options' => ['image' => $pro->Image ,'other_name'=>$OtherName] ]);


       return redirect('CartSite');
    }

     public function CartSite(){

        $Carts=Cart::content();

         return view('site.Cart',[
             'Carts'=>$Carts,

         ]);
    }

    public function DeleteCart($id){
          Cart::update($id,0);
           return back();
     }

    public function UpdateCart(){

         $qty=request('qty');
         $rowId=request('RowID');
       for($i=0; $i < count($rowId) ; $i++){

           Cart::update($rowId[$i],$qty[$i]);

       }

         return back();

     }


    //Checkout
          public function Checkout(){


              if(Cart::content()->count() == 0){

                  session()->flash('error',trans('admin.NoProductsInCart'));
        return back();

              }


          if(!empty(auth()->user()->id)){
    $Addresses=Addersses::where('User',auth()->user()->id)->get();
          }else{
             $Addresses='';
          }


          $Governrates=Governrate::get();
         return view('site.Checkout',[
             'Addresses'=>$Addresses,
             'Governrates'=>$Governrates,
         ]);
    }

      public function ChangeAddressSite(Request $request) {


           $id = $request->get('Address');

          $states=[];



          if(app()->getLocale() == 'ar' ){
               $adress=Addersses::find($id);

     $adressGov=Governrate::where('id',$adress->Gov)->first();
     $adressCit=City::where('id',$adress->City)->first();
     $adressPla=Place::where('id',$adress->Place)->first();



  $states += ['Address_Name' => $adress->Address_Name,'Special_MarkAdd'=>$adress->Special_Mark,'StreetAdd'=>$adress->Street,'BulidingAdd'=>$adress->Bulliding,'FloorAdd'=>$adress->Floor,'FlatAdd'=>$adress->Flat,'LocationAdd'=>$adress->Location,'Address_DetailsAdd'=>$adress->Address_Details,'Gov'=>$adress->Gov,'GovName'=>$adressGov->Arabic_Name,'Cit'=>$adress->City,'CitName'=>$adressCit->Arabic_Name , 'Pla'=>$adress->Place,'PlaName'=>$adressPla->Arabic_Name,'Address_ID'=>$adress->Address_ID];

          }else{

                   $adress=Addersses::find($id);

     $adressGov=Governrate::where('id',$adress->Gov)->first();
     $adressCit=City::where('id',$adress->City)->first();
     $adressPla=Place::where('id',$adress->Place)->first();



  $states += ['Address_Name' => $adress->Address_Name,'Special_MarkAdd'=>$adress->Special_Mark,'StreetAdd'=>$adress->Street,'BulidingAdd'=>$adress->Bulliding,'FloorAdd'=>$adress->Floor,'FlatAdd'=>$adress->Flat,'LocationAdd'=>$adress->Location,'Address_DetailsAdd'=>$adress->Address_Details,'Gov'=>$adress->Gov,'GovName'=>$adressGov->English_Name,'Cit'=>$adress->City,'CitName'=>$adressCit->English_Name , 'Pla'=>$adress->Place,'PlaName'=>$adressPla->English_Name,'Address_ID'=>$adress->Address_ID];

          }










       return response()->json($states);

    }


        //Orders
      public function PlaceOrder(){
 $Carts=Cart::content();
          if(!empty(auth()->user()->id)){
              $Client=auth()->user()->USER_ID;
              $ClientTwo=auth()->user()->id;
          }else{

              $Client='';
              $ClientTwo='';
          }

          $TotQty=0;

          foreach($Carts as $cart){
              $TotQty += $cart->qty ;
          }


        $ID = DB::table('orders')->insertGetId(
        array(
            'Date' => date('Y-m-d'),
            'Payment_Method' => request('Payment_Method'),
            'Product_Numbers' => Cart::content()->count(),
            'Total_Qty' => $TotQty,
            'Total_Price' => request('Total_Price'),
            'Shipping' => request('Shipping'),
            'Name' => request('Name'),
            'Email' => request('Email'),
            'Phone' => request('Phone'),
            'OtherPhone' => request('OtherPhone'),
            'Address_Name' => request('Address_Name'),
            'Special_MarkAdd' => request('Special_MarkAdd'),
            'StreetAdd' => request('StreetAdd'),
            'BulidingAdd' => request('BulidingAdd'),
            'FloorAdd' => request('FloorAdd'),
            'FlatAdd' => request('FlatAdd'),
            'Governrate' => request('Governrate'),
            'City' => request('City'),
            'Place' => request('Place'),
            'LocationAdd' => request('LocationAdd'),
            'Address_DetailsAdd' => request('Address_DetailsAdd'),
            'USER_ID' => $Client,
            'User' => $ClientTwo,


        )
    );



     foreach($Carts as $cart){


         $pro=Products::where('id',$cart->id)->first();

                $uu['P_Ar_Name']=$pro->P_Ar_Name;
                $uu['P_En_Name']=$pro->P_En_Name;
                $uu['Qty']=$cart->qty;
                $uu['Price']=$cart->price;
                $uu['Total']=$cart->total;
                $uu['Product']=$cart->id;
                $uu['Order']=$ID;

               ProductOrder::create($uu);



            }







   Cart::destroy();
             session()->flash('success',trans('admin.Order_Successfully'));
          return redirect('/');


    }







}
