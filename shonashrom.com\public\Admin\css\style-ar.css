body , html{
    direction: rtl!important;
    text-align: right;
    font-family: '<PERSON><PERSON><PERSON>', sans-serif!important;
}



.panel-content, .page-content{
    direction: rtl;
}

.select2-container--default .select2-selection--single .select2-selection__arrow{
    left: 0.5rem;
    right:unset;
}

.pos-right{
    left:0;
    right:unset;
}

table.dataTable thead .sorting:before,table.dataTable thead .sorting:after{
    left: 1em!important;
    right:unset;
}

.modal-header .close{
    margin:-1.25rem;
}

.panel-hdr > :last-child{
    padding-left: 1rem;
    padding-right: 1rem;
}

.buttons i{
    padding-left: 5px;
}

.modal-header,.modal-body{
    direction: rtl;
}


.pagination li:first-child{
    transform: rotate(180deg);
    margin-left: 5px;
}

.pagination li:last-child{
    transform: rotate(180deg);
}

.settings-panel .list{
    text-align: left;
}

.breadcrumb-item + .breadcrumb-item::before{
    padding-left: 0.5rem;
    padding-right: unset;
}

.breadcrumb-item + .breadcrumb-item {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
}

.nav-tabs{
    padding-inline-start: unset;
}

.tree{
  direction: ltr;
    text-align: left;
    }
.page-sidebar{
    direction:rtl!important;
}

.nav-menu li a > [class*='fa-'], .nav-menu li a > .ni{
    margin-left: 0.25rem;
    margin-right:unset;
    text-align: right;
}

.info-card .info-card-text{
    margin-right: 1rem;
    margin-left: unset;
}

.panel-toolbar{
    margin-left:7px;
}
.custom-control-label::before{
    right: -1.625rem;
    left:unset;
}
.custom-control{
    padding-right: 1.625rem;
    padding-left:unset;
}

.dropdown-icon-menu > ul > li .btn, .header-btn{
    margin-left: 0.9375rem;
    margin-right: unset;
}

.dropdown-icon-menu > .btn{
    margin-left:unset;
    margin-right:15px;
}

.header-icon:not(.btn)[data-toggle="dropdown"] + .dropdown-menu{
    right: auto !important;
    left: 2rem!important;
}

.modal-dialog-right{
    left:0;
    right:unset;
}

.dropdown-menu .dropdown-multilevel.dropdown-multilevel-left > .dropdown-menu {
    left: 100%;
    right: auto;
}

.float-right {
    float: left !important;
}

.dropdown-menu{
    text-align:right;
}

header .dropdown-menu .dropdown-multilevel > .dropdown-item:first-child:after{
    display:none;
}

.mr-2, .mx-2 {
    margin-left: 0.5rem !important;
    margin-right:unset !important;
}

.nav-function-hidden:not(.nav-function-top) .page-sidebar:hover{
    left:unset;
    right:0;
}

.nav-function-hidden:not(.nav-function-top) .page-sidebar{
    right: -16.25rem;
}

.shortcut-menu{
    left: 1.5rem;
}

.menu-item, label.menu-open-button{
    left:0;
}

html body .blankpage-form-field{
    direction:rtl!important;
}

@media only screen and (max-width: 992px){
.page-wrapper .page-sidebar {
    /*transform: translate3d(0, 0, -16.875rem );*/
    /*transform: translate3d(0px, 0, 0) !important;*/
    transform: translate3d(272px, 0, 0);
    will-change: right,left;
}
}

.page-logo-text{
    text-align: right;
}

.text-left {
    text-align: right !important;
}

@media only screen and (max-width: 420px){
.panel-toolbar {
    margin-left: 0;
}    
.panel .panel-hdr .panel-toolbar .btn-toolbar-master {
     margin-right: 0px; 
}
.panel-hdr > :last-child{
    padding-left:0;
}

.panel .panel-hdr{
    padding-right:5px;
}

.custom-control-label::after{
   right: -1.625rem;
   left:unset;
}
.breadcrumb-item + .breadcrumb-item::before{
    padding-right:7px;
}
}

.nav-function-top .page-sidebar .primary-nav .nav-menu > li > a > .ni, .nav-function-top .page-sidebar .primary-nav .nav-menu > li > a > [class*='fa-']{
    margin-left: .5rem !important;
    margin-right: unset!important;
}

.top-btn .btn span{
    padding-left: 10px;
}

.modal-footer{
    direction: ltr;
}

.select-table .custom-control{
    padding-left: 0;
}

.nav-function-fixed:not(.nav-function-top):not(.nav-function-hidden):not(.nav-function-minify) .page-content-wrapper{
    padding-right: 16.875rem;
    padding-left: unset;
}

.top-left-nav{
    margin-right:auto;
}