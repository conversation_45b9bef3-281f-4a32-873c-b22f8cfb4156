<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\MainEComDesign;

class MainEComDesignSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Check if design already exists
        if (MainEComDesign::count() == 0) {
            MainEComDesign::create([
                'Font_Type' => 1,
                
                // Pagination
                'Pagination_BG_Color' => '#ffffff',
                'Pagination_Txt_Color' => '#495057',
                'Pagination_Active_BG_Color' => '#3498db',
                'Pagination_Active_Txt_Color' => '#ffffff',
                
                // Body Background
                'Body_BG_Type' => 1,
                'Body_BG_Color' => '#ffffff',
                
                // Sub Pages & Breadcrumb
                'Sub_Page_BG_Color' => '#2c3e50',
                'Breadcumb_BG_Color' => '#f8f9fa',
                'Breadcumb_Txt_Color' => '#6c757d',
                
                // Modal
                'Modal_BG_Color' => '#ffffff',
                'Modal_Txt_Color' => '#495057',
                'Modal_Button_BG_Color' => '#3498db',
                'Modal_Button_Txt_Color' => '#ffffff',
                
                // Table
                'Table_Header_BG_Color' => '#f8f9fa',
                'Table_Header_Txt_Color' => '#495057',
                'Table_Body_BG_Color' => '#ffffff',
                'Table_Body_Txt_Color' => '#495057',
                'Table_Button_BG_Color' => '#3498db',
                'Table_Button_Txt_Color' => '#ffffff',
                
                // Copyright
                'CopyRights_Txt_Color' => '#6c757d',
                'CopyRights_Klar_Txt_Color' => '#3498db',
                'CopyRights_Klar_Hover_Txt_Color' => '#2980b9',
                
                // Preloader
                'Preloader_BG_Color' => '#ffffff',
                'Preloader_Small_Circle_Color' => '#3498db',
                'Preloader_Large_Circle_Color' => '#2980b9',
                
                // Footer
                'Footer_Title_Color' => '#ffffff',
                'Footer_Txt_Color' => '#bdc3c7',
                'Footer_Txt_Hover_Color' => '#ffffff',
                'Footer_Social_Color' => '#ffffff',
                'Footer_Social_BG_Color' => '#34495e',
                'Footer_Social_Hover_BG_Color' => '#3498db',
                'Footer_Social_Hover_Txt_Color' => '#ffffff',
                
                // Header
                'Header_Top_BG_Color' => '#2c3e50',
                'Header_Top_Txt_Color' => '#ffffff',
                'Header_Top_Txt_Hover_Color' => '#3498db',
                'Header_Middle_BG_Color' => '#ffffff',
                'Header_Middle_Icon_Color' => '#2c3e50',
                'Header_Middle_Icon_Hover_Color' => '#3498db',
                'Header_SearchBar_BG_Color' => '#f8f9fa',
                'Header_SearchBar_Txt_Color' => '#495057',
                'Header_SearchBar_Icon_BG_Color' => '#3498db',
                'Header_SearchBar_Icon_Txt_Color' => '#ffffff',
                'Header_SearchBar_Icon_Hover_BG_Color' => '#2980b9',
                'Header_SearchBar_Icon_Hover_Txt_Color' => '#ffffff',
                
                // Navbar
                'Navbar_BG_Color' => '#2c3e50',
                'Navbar_Txt_Color' => '#ffffff',
                'Navbar_Txt_Hover_Color' => '#3498db',
                'Navbar_Txt_Active_Color' => '#3498db',
                'Navbar_Dropdown_BG_Color' => '#34495e',
                'Navbar_Dropdown_Txt_Color' => '#ffffff',
                'Navbar_Dropdown_Txt_Hover_Color' => '#3498db',
                'Navbar_Dropdown_Border_Color' => '#2c3e50',
                
                // Slider
                'Slider_BG_Type' => 1,
                'Slider_BG_Color' => '#2c3e50',
                'Slider_Title_Color' => '#ffffff',
                'Slider_Txt_Color' => '#bdc3c7',
                'Slider_Button_BG_Color' => '#3498db',
                'Slider_Button_Txt_Color' => '#ffffff',
                'Slider_Button_Hover_BG_Color' => '#2980b9',
                'Slider_Button_Hover_Txt_Color' => '#ffffff',
                
                // Sections
                'Section_Title_Color' => '#2c3e50',
                'Section_Txt_Color' => '#555555',
                'Section_BG_Color' => '#ffffff',
                'Section_Alt_BG_Color' => '#f8f9fa',
                
                // Cards
                'Card_BG_Color' => '#ffffff',
                'Card_Border_Color' => '#e9ecef',
                'Card_Title_Color' => '#2c3e50',
                'Card_Txt_Color' => '#555555',
                'Card_Price_Color' => '#e74c3c',
                'Card_Button_BG_Color' => '#3498db',
                'Card_Button_Txt_Color' => '#ffffff',
                'Card_Button_Hover_BG_Color' => '#2980b9',
                'Card_Button_Hover_Txt_Color' => '#ffffff',
                
                // Forms
                'Form_BG_Color' => '#ffffff',
                'Form_Border_Color' => '#e9ecef',
                'Form_Input_BG_Color' => '#ffffff',
                'Form_Input_Border_Color' => '#ced4da',
                'Form_Input_Txt_Color' => '#495057',
                'Form_Label_Color' => '#495057',
                'Form_Button_BG_Color' => '#3498db',
                'Form_Button_Txt_Color' => '#ffffff',
                'Form_Button_Hover_BG_Color' => '#2980b9',
                'Form_Button_Hover_Txt_Color' => '#ffffff',
            ]);
        }
    }
}
