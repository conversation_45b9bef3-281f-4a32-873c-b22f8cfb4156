   
<?php $__env->startSection('content'); ?>
<?php
use App\Models\MainEComDesign;
use App\Models\SupPagesEComDesign;
$main=MainEComDesign::orderBy('id','desc')->first();
$sub=SupPagesEComDesign::orderBy('id','desc')->first();
?>

 <style>
.page-title-area {
    background-color: <?php echo e($main->Breadcumb_BG_Color); ?> !important;
}
     
    .blog-area {
      background-color: <?php echo e($main->Sub_Page_BG_Color); ?> !important;
} 
</style>
        <title><?php echo e(trans('admin.Blogs')); ?></title>

   

        <!-- Start Page Banner -->
        <div class="page-title-area">
            <div class="container">
                <div class="page-title-content">
                    <h2 style="color: <?php echo e($main->Breadcumb_Txt_Color); ?> !important"><?php echo e(trans('admin.Blogs')); ?></h2>

                    <ul>
                        <li><a style="color: <?php echo e($main->Breadcumb_Txt_Color); ?> !important" href="<?php echo e(url('/')); ?>"><?php echo e(trans('admin.Home')); ?></a></li>
                        <li style="color: <?php echo e($main->Breadcumb_Txt_Color); ?> !important"><?php echo e(trans('admin.Blogs')); ?></li>
                    </ul>
                </div>
            </div>
        </div>
        <!-- End Page Banner -->

        <!-- Start Blog Area -->
        <section class="blog-area bg-color pt-50 pb-50">
            <div class="container">
                <div class="row">
                    
                    
                           <?php $__currentLoopData = $Articles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $art): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-lg-4 col-md-6">
                        <div class="single-blog">
                            <div class="blog-image">
                                <a href="<?php echo e(url('BlogDetails/'.$art->id)); ?>"><img src="<?php echo e(URL::to($art->Sub_Image)); ?>" alt="image"></a>
                            </div>

                            <div class="blog-content">
                                <h3>
                 <a style="color: <?php echo e($sub->Blogs_Title_Color); ?>" href="<?php echo e(url('BlogDetails/'.$art->id)); ?>">       <?php echo e(app()->getLocale() == 'ar' ?$art->Arabic_Title :$art->English_Title); ?></a>
                                </h3>
                                <div class="post-meta">
                 <a style="color: <?php echo e($sub->Blogs_Txt_Color); ?>" href="<?php echo e(url('BlogDetails/'.$art->id)); ?>"><?php echo e($art->Date); ?></a>
                                </div>
                                <p style="display: none">  <?php echo app()->getLocale() == 'ar' ?$art->Arabic_Desc :$art->English_Desc; ?></p>
                            </div>
                        </div>
                    </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                   

                    <div class="col-lg-12 col-md-12">
                <?php echo e($Articles->Links()); ?>

                    </div>
                </div>
            </div>
        </section>
        <!-- End Blog Area -->

            <!-- Blogs -->
            <style>
                .single-blog .blog-content h3 a:hover {
                    
                    color: <?php echo e($sub->Blogs_Hover_Txt_Color); ?> !important;   
                }
                
                   .single-blog .blog-content .post-meta a:hover {
                    
                    color: <?php echo e($sub->Blogs_Hover_Txt_Color); ?> !important;   
                }
            </style>
            
    <!-- Pagination -->
<style> 
.page-item disabled,
.page-link,
.page-item active,
.page-item
    {
    		    background: <?php echo e($main->Pagination_BG_Color); ?> !important;
   color: <?php echo e($main->Pagination_Txt_Color); ?> !important;    
    }

    .page-item.active .page-link{
          		    background: <?php echo e($main->Pagination_Active_BG_Color); ?> !important;
   color: <?php echo e($main->Pagination_Active_Txt_Color); ?> !important;    
border-color:<?php echo e($main->Pagination_Active_Txt_Color); ?> !important;
        
    }

</style>


<?php $__env->stopSection(); ?> 
<?php echo $__env->make('site.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\ost_erp\resources\views/site/Articles.blade.php ENDPATH**/ ?>