<?php

use Illuminate\Support\Facades\Route;



Route::get('GovernrateFilterr/{id}', 'App\Http\Controllers\SiteController@GovernrateFilterr');
Route::get('CityFilterr/{id}', 'App\Http\Controllers\SiteController@CityFilterr');
Route::get('/', 'App\Http\Controllers\SiteController@Home');
Route::get('ISP', 'App\Http\Controllers\SiteController@ISP');
Route::post('CalculateISP', 'App\Http\Controllers\SiteController@CalculateISP');
Route::get('TermsSite', 'App\Http\Controllers\SiteController@TermsSite');
Route::get('PolicySite', 'App\Http\Controllers\SiteController@PolicySite');
Route::get('BlogSite', 'App\Http\Controllers\SiteController@BlogSite');
Route::get('BlogsDet/{id}', 'App\Http\Controllers\SiteController@BlogsDet');
Route::get('ContactSite', 'App\Http\Controllers\SiteController@ContactSite');
Route::get('FAQSite', 'App\Http\Controllers\SiteController@FAQSite');
//Shop
Route::get('ShopSite', 'App\Http\Controllers\SiteController@ShopSite');
Route::get('FilterSearchName', 'App\Http\Controllers\SiteController@FilterSearchName');
Route::get('FilterProGroup/{id}', 'App\Http\Controllers\SiteController@FilterProGroup');
Route::get('PrescriptionPro/{id}', 'App\Http\Controllers\SiteController@PrescriptionPro');
Route::get('ProDetails/{id}', 'App\Http\Controllers\SiteController@ProDetails');

Route::group(['middleware' =>'NotUserAuth'], function () {
//LoginSite
Route::get('LoginSite', 'App\Http\Controllers\SiteController@LoginSite');
Route::post('PostRegisterSite', 'App\Http\Controllers\SiteController@PostRegisterSite');
Route::get('ForgetSite', 'App\Http\Controllers\SiteController@ForgetSite');
Route::post('PostLoginSite', 'App\Http\Controllers\SiteController@PostLoginSite');
Route::post('PostForgetSite', 'App\Http\Controllers\SiteController@PostForgetSite');
Route::post('PostCodeSite', 'App\Http\Controllers\SiteController@PostCodeSite');
Route::post('PostResetPassword', 'App\Http\Controllers\SiteController@PostResetPassword');
});
Route::group(['middleware' =>'UserAuth'], function () {

    //Logout
Route::get('LogoutSite', 'App\Http\Controllers\SiteController@LogoutSite');
    
    //MyProfileSite
Route::get('MyProfileSite', 'App\Http\Controllers\SiteController@MyProfileSite');
Route::post('UpdateAccount', 'App\Http\Controllers\SiteController@UpdateAccount');
Route::post('UpdatePassword', 'App\Http\Controllers\SiteController@UpdatePassword');
Route::post('UpdateAddress', 'App\Http\Controllers\SiteController@UpdateAddress');
Route::get('DeleteMyAddress/{id}/{Address}', 'App\Http\Controllers\SiteController@DeleteMyAddress');
Route::post('EditAddress', 'App\Http\Controllers\SiteController@EditAddress');
    
    //Rate
    Route::post('EditRate', 'App\Http\Controllers\SiteController@EditRate');
    Route::post('AddRate', 'App\Http\Controllers\SiteController@AddRate');
    
    //Comment
    Route::post('AddComment', 'App\Http\Controllers\SiteController@AddComment');
    Route::get('DeleteComment/{id}', 'App\Http\Controllers\SiteController@DeleteComment');
    Route::post('EditComment', 'App\Http\Controllers\SiteController@EditComment');
    

});


//Cart
Route::post('AddToCart', 'App\Http\Controllers\SiteController@AddToCart');
Route::get('CartSite', 'App\Http\Controllers\SiteController@CartSite');
Route::get('DeleteCart/{id}', 'App\Http\Controllers\SiteController@DeleteCart');
Route::post('UpdateCart', 'App\Http\Controllers\SiteController@UpdateCart');

    //Checkout
Route::get('Checkout', 'App\Http\Controllers\SiteController@Checkout');
Route::get('ChangeAddressSite', 'App\Http\Controllers\SiteController@ChangeAddressSite');
Route::get('CityShip/{id}', 'App\Http\Controllers\SiteController@CityShip');

//Order
Route::post('PlaceOrder', 'App\Http\Controllers\SiteController@PlaceOrder');

    
    



    Route::group(['middleware' =>'Users'], function () {

//Ticket
Route::post('AddClientsTicket', 'App\Http\Controllers\AdminController@AddClientsTicket');  
Route::post('UpdateProfile', 'App\Http\Controllers\AdminController@UpdateProfile');  
        
Route::get('ProfileKlar', 'App\Http\Controllers\SiteController@ProfileKlar');   
Route::get('LogoutKlar', 'App\Http\Controllers\SiteController@LogoutKlar');   
Route::get('VideoKlarLearning', 'App\Http\Controllers\SiteController@VideoKlarLearning');   
    });
//Requests
Route::post('AddRequestMessages', 'App\Http\Controllers\AdminController@AddRequestMessages');
Route::post('AddJoin', 'App\Http\Controllers\AdminController@AddJoin');

//AddSubscribesOrders
Route::post('AddSubscribesOrders', 'App\Http\Controllers\AdminController@AddSubscribesOrders');


// === Admin Panel ===

//Language
Route::get('lang/{x}', 'App\Http\Controllers\LangController@Lang');
//Login and Register
Route::get('AdminLogin', 'App\Http\Controllers\AdminController@LoginPage');
Route::post('Login', 'App\Http\Controllers\AdminController@Login');
Route::get('Logout', 'App\Http\Controllers\AdminController@Logout');
Route::get('forgotpassword','App\Http\Controllers\AdminController@forgotpasswordPage');
Route::post('forgotpassword','App\Http\Controllers\AdminController@forgotpassword');
Route::get('reset/password/{token}','App\Http\Controllers\AdminController@reset_password');
Route::post('reset/password/{token}','App\Http\Controllers\AdminController@reset_password_final');

Config::set('auth.defines','admin');
Route::group(['middleware' =>'Admin:admin'], function () {


             Route::get('KlarAdmin', function () {
    return view('admin.home');
});

    
      //Default_Data
            Route::get('Company_Data', 'App\Http\Controllers\AdminController@Company_Data');
            Route::post('AddDefaultCompany', 'App\Http\Controllers\AdminController@AddDefaultCompany');
            Route::get('AddDefaultCompanyFirst', 'App\Http\Controllers\AdminController@AddDefaultCompanyFirst');
    
        //SocialMedia
            Route::get('SocialMedia', 'App\Http\Controllers\AdminController@SocialMediaPage');
            Route::post('SocialMediaUpdate/{id}', 'App\Http\Controllers\AdminController@SocialMediaUpdate');

            //Profile
Route::get('Profile','App\Http\Controllers\AdminController@Profile');
Route::post('UpdateAdminProfile/{id?}','App\Http\Controllers\AdminController@UpdateAdminProfile');

          //Admins
Route::get('Admins', 'App\Http\Controllers\AdminController@AdminsPage');
Route::post('AddAdmin', 'App\Http\Controllers\AdminController@AddAdmin');
Route::post('EditAdmin/{id}', 'App\Http\Controllers\AdminController@EditAdmin');
Route::get('DeleteAdmin/{id}', 'App\Http\Controllers\AdminController@DeleteAdmin');
    
    
//Webslider    
    Route::get('Webslider', 'App\Http\Controllers\AdminController@Webslider');
Route::post('AddWebslider', 'App\Http\Controllers\AdminController@AddWebslider');
Route::post('EditWebslider/{id}', 'App\Http\Controllers\AdminController@EditWebslider');
Route::get('DeleteWebslider/{id}', 'App\Http\Controllers\AdminController@DeleteWebslider');
    
    
    //Services
        Route::get('Services', 'App\Http\Controllers\AdminController@Services');
Route::post('AddServices', 'App\Http\Controllers\AdminController@AddServices');
Route::post('EditServices/{id}', 'App\Http\Controllers\AdminController@EditServices');
Route::get('DeleteServices/{id}', 'App\Http\Controllers\AdminController@DeleteServices');

    

    //Testiminoals
Route::get('Testiminoals', 'App\Http\Controllers\AdminController@Testiminoals');
Route::post('AddTestiminoals', 'App\Http\Controllers\AdminController@AddTestiminoals');
Route::post('EditTestiminoals/{id}', 'App\Http\Controllers\AdminController@EditTestiminoals');
Route::get('DeleteTestiminoals/{id}', 'App\Http\Controllers\AdminController@DeleteTestiminoals');
  
    
                      //Articles
Route::get('Articles', 'App\Http\Controllers\AdminController@ArticlesPage');
Route::post('AddArticles', 'App\Http\Controllers\AdminController@AddArticles');
Route::post('EditArticles/{id}', 'App\Http\Controllers\AdminController@EditArticles');
Route::get('DeleteArticles/{id}', 'App\Http\Controllers\AdminController@DeleteArticles');
    

    //About
             Route::get('About', 'App\Http\Controllers\AdminController@About');
            Route::post('AddAbout', 'App\Http\Controllers\AdminController@AddAbout');
        //ISP
             Route::get('ISPAdmin', 'App\Http\Controllers\AdminController@ISPAdmin');
            Route::post('AddISP', 'App\Http\Controllers\AdminController@AddISP');
    
    
    //CommentsAdmin
      Route::get('CommentsAdmin', 'App\Http\Controllers\AdminController@CommentsAdmin');
      Route::get('ApproveCommentsAdmin/{id}', 'App\Http\Controllers\AdminController@ApproveCommentsAdmin');
      Route::get('UnApproveCommentsAdmin/{id}', 'App\Http\Controllers\AdminController@UnApproveCommentsAdmin');
      Route::get('DeleteComment/{id}', 'App\Http\Controllers\AdminController@DeleteComment');
    
    
    //Groups
     Route::get('Groups', 'App\Http\Controllers\AdminController@Groups');
        Route::post('AddGroups', 'App\Http\Controllers\AdminController@AddGroups');
Route::post('EditGroups/{id}', 'App\Http\Controllers\AdminController@EditGroups');
Route::get('DeleteGroups/{id}', 'App\Http\Controllers\AdminController@DeleteGroups');
     Route::get('SyncGroups', 'App\Http\Controllers\AdminController@SyncGroups');  
    
    //Govenrate
         Route::get('Govenrate', 'App\Http\Controllers\AdminController@Govenrate');
            Route::post('AddGovenrate', 'App\Http\Controllers\AdminController@AddGovenrate');
Route::post('EditGovenrate/{id}', 'App\Http\Controllers\AdminController@EditGovenrate');
Route::get('DeleteGovenrate/{id}', 'App\Http\Controllers\AdminController@DeleteGovenrate');
     Route::get('SyncGovenrate', 'App\Http\Controllers\AdminController@SyncGovenrate');  
    
        //City
         Route::get('City/{id}', 'App\Http\Controllers\AdminController@City');
                Route::post('AddCity', 'App\Http\Controllers\AdminController@AddCity');
Route::post('EditGCity/{id}', 'App\Http\Controllers\AdminController@EditGCity');
Route::get('DeleteGCity/{id}', 'App\Http\Controllers\AdminController@DeleteGCity');
     Route::get('SyncCity', 'App\Http\Controllers\AdminController@SyncCity');  
    
            //Place
         Route::get('Place/{id}', 'App\Http\Controllers\AdminController@Place');
                    Route::post('AddPlace', 'App\Http\Controllers\AdminController@AddPlace');
Route::post('EditPlace/{id}', 'App\Http\Controllers\AdminController@EditPlace');
Route::get('DeletePlace/{id}', 'App\Http\Controllers\AdminController@DeletePlace');
     Route::get('SyncPlace', 'App\Http\Controllers\AdminController@SyncPlace');  
    
    
    
    
    //Products
     Route::get('Products', 'App\Http\Controllers\AdminController@Products');
    Route::post('AddProducts', 'App\Http\Controllers\AdminController@AddProducts');
Route::post('EditProducts/{id}', 'App\Http\Controllers\AdminController@EditProducts');
Route::get('DeleteProducts/{id}', 'App\Http\Controllers\AdminController@DeleteProducts');
Route::get('DelSupImg/{id}', 'App\Http\Controllers\AdminController@DelSupImg');
     Route::get('SyncProducts', 'App\Http\Controllers\AdminController@SyncProducts');
    

    //Terms
        Route::get('Terms', 'App\Http\Controllers\AdminController@Terms');
            Route::post('AddTerms', 'App\Http\Controllers\AdminController@AddTerms');
//Privacy
        Route::get('Privacy', 'App\Http\Controllers\AdminController@Privacy');
            Route::post('AddPrivacy', 'App\Http\Controllers\AdminController@AddPrivacy');


    //WhyChoose
             Route::get('HowWeWork', 'App\Http\Controllers\AdminController@HowWeWork');
            Route::post('AddHowWeWork', 'App\Http\Controllers\AdminController@AddHowWeWork');
            Route::post('EditHowWorkIcons/{id}', 'App\Http\Controllers\AdminController@EditHowWorkIcons');
    

    //Categories
        Route::get('Categories', 'App\Http\Controllers\AdminController@Categories');
Route::post('AddCategories', 'App\Http\Controllers\AdminController@AddCategories');
Route::post('EditCategories/{id}', 'App\Http\Controllers\AdminController@EditCategories');
Route::get('DeleteCategories/{id}', 'App\Http\Controllers\AdminController@DeleteCategories');
    

    //FAQ
        Route::get('FAQ', 'App\Http\Controllers\AdminController@FAQ');
Route::post('AddFAQ', 'App\Http\Controllers\AdminController@AddFAQ');
Route::post('EditFAQ/{id}', 'App\Http\Controllers\AdminController@EditFAQ');
Route::get('DeleteFAQ/{id}', 'App\Http\Controllers\AdminController@DeleteFAQ');    
    
    
        //Features
    Route::get('Features', 'App\Http\Controllers\AdminController@Features');
       Route::post('EditFeatures/{id}', 'App\Http\Controllers\AdminController@EditFeatures');
    
    
    //Gallery
            Route::get('Gallery', 'App\Http\Controllers\AdminController@Gallery');
Route::post('AddGallery', 'App\Http\Controllers\AdminController@AddGallery');
Route::post('EditGallery/{id}', 'App\Http\Controllers\AdminController@EditGallery');
Route::get('DeleteGallery/{id}', 'App\Http\Controllers\AdminController@DeleteGallery');
    



    
    //Footer
                 Route::get('Footer', 'App\Http\Controllers\AdminController@Footer');
            Route::post('FooterUpdate', 'App\Http\Controllers\AdminController@FooterUpdate');

//OrdersAdmin
        Route::get('OrdersAdmin', 'App\Http\Controllers\AdminController@OrdersAdmin');
    

    //Client
Route::get('Client', 'App\Http\Controllers\AdminController@Client');
Route::post('AddClient', 'App\Http\Controllers\AdminController@AddClient');
Route::post('EditClient/{id}', 'App\Http\Controllers\AdminController@EditClient');
Route::get('DeleteClient/{id}', 'App\Http\Controllers\AdminController@DeleteClient');    
    

    
    
    
    });