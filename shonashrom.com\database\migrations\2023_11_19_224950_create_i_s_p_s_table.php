<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateISPSTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('i_s_p_s', function (Blueprint $table) {
            $table->id();
            $table->longText('Name')->nullable();
            $table->longText('Height')->nullable();
            $table->longText('HeightUnit')->nullable();
            $table->longText('Weight')->nullable();
            $table->longText('WightUnit')->nullable();
            $table->longText('Age')->nullable();
            $table->longText('Gender')->nullable();
            $table->longText('Sports')->nullable();
            $table->longText('Moshr_Kotlt_Gsm')->nullable();
            $table->longText('ZaydtTool')->nullable();
            $table->longText('ZaydtToolToInch')->nullable();
            $table->longText('WaznMsaly')->nullable();
            $table->longText('mkdar_wazn_mtlop_fkdanh')->nullable();
            $table->longText('saarat_hararya_mtlop_fkdnah')->nullable();
            $table->longText('waktMatwkaaLlwsolWaznMasaly')->nullable();
            $table->longText('3dd3bwatOstegy')->nullable();
            $table->longText('So3ratYomen')->nullable();
            $table->longText('so3ratPoortinat')->nullable();
            $table->longText('gramProtinat')->nullable();
            $table->longText('User')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('i_s_p_s');
    }
}
