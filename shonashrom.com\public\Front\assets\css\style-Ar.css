@charset "UTF-8";
/*=================================
    CSS Index Here
==================================*/
/*

01. Theme Base
    1.1. Mixin
    1.2. Function
    1.3. Variable
    1.4. Typography
    1.5. Extend
    1.7. Wordpress Default
02. Reset
    2.1. Container
    2.2. Grid
    2.3. Input
    2.4. Slick Slider
    2.5. Mobile Menu
03. Utilities
    3.1. Preloader
    3.2. Buttons
    3.3. Titles
    3.4. Common
    3.6. Font
    3.7. Background
    3.8. Text Color
    3.9. Overlay
    3.10. Animation

04. Template Style
    4.1. Widget
    4.2. Header
    4.3. Footer
    4.4. Breadcumb
    4.5. Pagination
    4.6. Blog
    4.7. Comments
    4.8. Hero Area
    4.9. Error    
    4.00. Popup Search
    4.00. Popup Side Menu
    4.00. Wocommerce
    4.00. Subscribe
    4.00. Cart
    4.00. Checkout
    4.00. Wishlist
    4.00. Contact
    4.00. About
    4.00. Team
    4.00. Testimonial
    4.00. Counter
    4.00. Client
    4.00. Simple Sections
    4.00. Video
    4.00. Category
    4.00. Faq
    4.00. feature
    4.00. CTA
    4.00. Service
    4.00. Pricing
    4.00. Why choose Us
    4.00. Project
    4.00. Event
    4.00. Gallery
    4.00. Schedule

05. Spacing

*/
/*=================================
    CSS Index End
==================================*/
/*=================================
   01. Theme Base
==================================*/
/*------------------- 1.1. Mixin -------------------*/
/*------------------- 1.2. Function -------------------*/
/*------------------- 1.3. Variable-------------------*/
:root {
  --theme-color: #5c4b2d;
  --title-color: #1C1C1C;
  --body-color: #7B7E86;
  --smoke-color: #F3F3F3;
  --smoke-color2: #F6F6F7;
  --smoke-color3: #F5F5F6;
  --smoke-color4: #F4F4F4;
  --smoke-color5: #F0F2F4;
  --black-color: #202020;
  --gray-color: #303030;
  --white-color: #ffffff;
  --light-color: #9fa2ad;
  --yellow-color: #FFB539;
  --success-color: #28a745;
  --error-color: #dc3545;
  --border-color: #E3E3E3;
  --title-font: "Kanit", sans-serif;
  --body-font: "Kumbh Sans", sans-serif;
  --icon-font: "Font Awesome 6 Pro";
  --main-container: 1220px;
  --container-gutters: 24px;
  --section-space: 120px;
  --section-space-mobile: 80px;
  --section-title-space: 60px;
  --ripple-ani-duration: 5s;
}

/*------------------- 1.5. Typography -------------------*/
html,
body {
  scroll-behavior: smooth !important;
}

body {
  font-family: var(--body-font);
  font-size: 16px;
  font-weight: 400;
  color: var(--body-color);
  line-height: 26px;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  /***scroll-bar***/
}
body::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}
body::-webkit-scrollbar-track {
  background: rgba(251, 212, 81, 0.1);
}
body::-webkit-scrollbar-thumb {
  background: var(--theme-color);
  border-radius: 0px;
}
body.home-3 {
  background: var(--smoke-color5);
}
body.home-3 .btn {
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.05em;
  padding: 20.5px 35px 20.5px;
}

iframe {
  border: none;
  width: 100%;
}

.slick-slide:focus,
button:focus,
a:focus,
a:active,
input,
input:hover,
input:focus,
input:active,
textarea,
textarea:hover,
textarea:focus,
textarea:active {
  outline: none;
}

input:focus {
  outline: none;
  box-shadow: none;
}

img:not([draggable]),
embed,
object,
video {
  max-width: 100%;
  height: auto;
}

ul {
  list-style-type: disc;
}

ol {
  list-style-type: decimal;
}

table {
  margin: 0 0 1.5em;
  width: 100%;
 
  border-spacing: 0;
  border: 1px solid var(--border-color);
}

th {
  font-weight: 700;
  color: var(--title-color);
}

td,
th {
  border: 1px solid var(--border-color);
  padding: 9px 12px;
}

a {
  color: var(--theme-color);
  text-decoration: none;
  outline: 0;
  transition: all ease 0.4s;
}
a:hover {
  color: var(--title-color);
}
a:active, a:focus, a:hover, a:visited {
  text-decoration: none;
  outline: 0;
}

button {
  transition: all ease 0.4s;
}

img {
  border: none;
  max-width: 100%;
}

ins {
  text-decoration: none;
}
.space{
    direction: rtl;
}
pre {
  font-family: var(--body-font);
  background: #f5f5f5;
  color: #666;
  font-size: 14px;
  margin: 20px 0;
  overflow: auto;
  padding: 20px;
  white-space: pre-wrap;
  word-wrap: break-word;
}

span.ajax-loader:empty,
p:empty {
  display: none;
}

p {
  font-family: var(--body-font);
  margin: 0 0 18px 0;
  color: var(--body-color);
  line-height: 1.75;
}

h1 a,
h2 a,
h3 a,
h4 a,
h5 a,
h6 a,
p a,
span a {
  font-size: inherit;
  font-family: inherit;
  font-weight: inherit;
  line-height: inherit;
}

.h1,
h1,
.h2,
h2,
.h3,
h3,
.h4,
h4,
.h5,
h5,
.h6,
h6 {
  font-family: var(--title-font);
  color: var(--title-color);
  text-transform: none;
  font-weight: 700;
  line-height: 1.4;
  margin: 0 0 15px 0;
}

.h1,
h1 {
  font-size: 80px;
  line-height: 1.167;
}

.h2,
h2 {
  font-size: 48px;
  line-height: 1.227;
}

.h3,
h3 {
  font-size: 36px;
  line-height: 1.278;
}

.h4,
h4 {
  font-size: 24px;
  line-height: 1.333;
}

.h5,
h5 {
  font-size: 20px;
  line-height: 1.417;
}

.h6,
h6 {
  font-size: 18px;
  line-height: 1.5;
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .h1,
  h1 {
    font-size: 48px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .h1,
  h1 {
    font-size: 44px;
    line-height: 1.3;
  }
  .h2,
  h2 {
    font-size: 40px;
    line-height: 1.25;
  }
  .h3,
  h3 {
    font-size: 30px;
  }
  .h4,
  h4 {
    font-size: 24px;
  }
  .h5,
  h5 {
    font-size: 20px;
  }
  .h6,
  h6 {
    font-size: 16px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .h1,
  h1 {
    font-size: 40px;
  }
  .h2,
  h2 {
    font-size: 34px;
    line-height: 1.3;
  }
  .h3,
  h3 {
    font-size: 26px;
  }
  .h4,
  h4 {
    font-size: 22px;
  }
  .h5,
  h5 {
    font-size: 18px;
  }
  .h6,
  h6 {
    font-size: 16px;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .h1,
  h1 {
    font-size: 34px;
    line-height: 1.35;
  }
  .h2,
  h2 {
    font-size: 30px;
  }
}
/* Extra small devices */
@media (max-width: 375px) {
  .h1,
  h1 {
    font-size: 32px;
  }
}
/*------------------- 1.6. Extend -------------------*/
/*------------------- 1.7. Wordpress Default -------------------*/
.admin-bar .default-header .sticky-wrapper.sticky {
  top: 32px;
}

.wp-block-search__label {
  position: relative;
  font-size: 24px;
  font-weight: 500 !important;
  font-family: var(--title-font);
  line-height: 1em;
  margin: -0.12em 0 25px 0;
}

p.has-drop-cap {
  margin-bottom: 20px;
}

.page--item p:last-child .alignright {
  clear: right;
}

.blog-title,
.pagi-title,
.breadcumb-title {
  word-break: break-word;
}

.blocks-gallery-caption,
.wp-block-embed figcaption,
.wp-block-image figcaption {
  color: var(--body-color);
}

.bypostauthor,
.gallery-caption {
  display: block;
}

.page-links,
.clearfix {
  clear: both;
}

.page--item {
  margin-bottom: 30px;
}
.page--item p {
  line-height: 1.8;
}
.page--item .th-comment-form {
  padding: 0;
}
.page--item .th-comments-wrap {
  margin-left: 0;
  margin-right: 0;
  margin-top: 30px;
}

.content-none-search {
  margin-top: 30px;
}

.wp-block-button.aligncenter {
  text-align: center;
}

.alignleft {
  display: inline;
  float: left;
  margin-bottom: 10px;
  margin-right: 1.5em;
}

.alignright {
  display: inline;
  float: right;
  margin-bottom: 10px;
  margin-left: 1.5em;
  margin-right: 1em;
}

.aligncenter {
  clear: both;
  display: block;
  margin-left: auto;
  margin-right: auto;
  max-width: 100%;
}

.gallery {
  margin-bottom: 1.5em;
  width: 100%;
}

.gallery-item {
  display: inline-block;
  text-align: center;
  vertical-align: top;
  width: 100%;
  padding: 0 5px;
}

.wp-block-columns {
  margin-bottom: 1em;
}

figure.gallery-item {
  margin-bottom: 10px;
  display: inline-block;
}

figure.wp-block-gallery {
  margin-bottom: 14px;
}

.gallery-columns-2 .gallery-item {
  max-width: 50%;
}

.gallery-columns-3 .gallery-item {
  max-width: 33.33%;
}

.gallery-columns-4 .gallery-item {
  max-width: 25%;
}

.gallery-columns-5 .gallery-item {
  max-width: 20%;
}

.gallery-columns-6 .gallery-item {
  max-width: 16.66%;
}

.gallery-columns-7 .gallery-item {
  max-width: 14.28%;
}

.gallery-columns-8 .gallery-item {
  max-width: 12.5%;
}

.gallery-columns-9 .gallery-item {
  max-width: 11.11%;
}

.gallery-caption {
  display: block;
  font-size: 12px;
  color: var(--body-color);
  line-height: 1.5;
  padding: 0.5em 0;
}

.wp-block-cover p:not(.has-text-color),
.wp-block-cover-image-text,
.wp-block-cover-text {
  color: var(--white-color);
}

.wp-block-cover {
  margin-bottom: 15px;
}

.wp-caption-text {
  text-align: center;
}

.wp-caption {
  margin-bottom: 1.5em;
  max-width: 100%;
}
.wp-caption .wp-caption-text {
  margin: 0.5em 0;
  font-size: 14px;
}

.wp-block-media-text,
.wp-block-media-text.alignwide,
figure.wp-block-gallery {
  margin-bottom: 30px;
}

.wp-block-media-text.alignwide {
  background-color: var(--smoke-color);
}

.editor-styles-wrapper .has-large-font-size,
.has-large-font-size {
  line-height: 1.4;
}

.wp-block-latest-comments a {
  color: inherit;
}

.wp-block-button {
  margin-bottom: 10px;
}
.wp-block-button:last-child {
  margin-bottom: 0;
}
.wp-block-button .wp-block-button__link {
  color: #fff;
}
.wp-block-button .wp-block-button__link:hover {
  color: #fff;
  background-color: var(--theme-color);
}
.wp-block-button.is-style-outline .wp-block-button__link {
  background-color: transparent;
  border-color: var(--title-color);
  color: var(--title-color);
}
.wp-block-button.is-style-outline .wp-block-button__link:hover {
  color: #fff;
  background-color: var(--theme-color);
  border-color: var(--theme-color);
}
.wp-block-button.is-style-squared .wp-block-button__link {
  border-radius: 0;
}

ol.wp-block-latest-comments li {
  margin: 15px 0;
}

ul.wp-block-latest-posts {
  padding: 0;
  margin: 0;
  margin-bottom: 15px;
}
ul.wp-block-latest-posts a {
  color: inherit;
}
ul.wp-block-latest-posts a:hover {
  color: var(--theme-color);
}
ul.wp-block-latest-posts li {
  margin: 15px 0;
}

.wp-block-search {
  display: flex;
  flex-wrap: wrap;
}
.wp-block-search .wp-block-search__inside-wrapper {
  border: 1px solid #EAEBEE;
  border-radius: 10px 0 0 10px;
  overflow: hidden;
}
.wp-block-search .wp-block-search__input {
  width: 100%;
  max-width: 100%;
  padding-left: 20px;
  border: 0;
}
.wp-block-search .wp-block-search__button {
  margin: 0;
  min-width: 110px;
  border: none;
  color: #fff;
  border-radius: 10px;
  background-color: var(--theme-color);
}
.wp-block-search .wp-block-search__button.has-icon {
  min-width: 55px;
}
.wp-block-search .wp-block-search__button:hover {
  background-color: var(--title-color);
}

.wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper {
  padding: 0;
  border: none;
}
.wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper .wp-block-search__input {
  padding: 0 8px 0 25px;
}

ul.wp-block-rss a {
  color: inherit;
}

.wp-block-group.has-background {
  padding: 15px 15px 1px;
  margin-bottom: 30px;
}

.wp-block-table td,
.wp-block-table th {
  border-color: rgba(0, 0, 0, 0.1);
}

.wp-block-table.is-style-stripes {
  border: 1px solid rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.wp-block-table.is-style-stripes {
  border: 0;
  margin-bottom: 30px;
  border-bottom: 0;
}
.wp-block-table.is-style-stripes th, .wp-block-table.is-style-stripes td {
  border-color: var(--border-color);
}

.logged-in .will-sticky .sticky-active.active,
.logged-in .preloader .btn {
  top: 32px;
}
@media (max-width: 782px) {
  .logged-in .will-sticky .sticky-active.active,
  .logged-in .preloader .btn {
    top: 46px;
  }
}
@media (max-width: 600px) {
  .logged-in .will-sticky .sticky-active.active,
  .logged-in .preloader .btn {
    top: 0;
  }
}

.post-password-form {
  margin-bottom: 30px;
  margin-top: 20px;
}
.post-password-form p {
  display: flex;
  position: relative;
  gap: 15px;
  /* Extra small devices */
}
@media (max-width: 575px) {
  .post-password-form p {
    flex-wrap: wrap;
  }
}
.post-password-form label {
  display: flex;
  align-items: center;
  flex: auto;
  margin-bottom: 0;
  line-height: 1;
  margin-top: 0;
  gap: 15px;
  /* Extra small devices */
}
@media (max-width: 575px) {
  .post-password-form label {
    flex-wrap: wrap;
  }
}
.post-password-form input {
  width: 100%;
  border: none;
  height: 55px;
  padding-left: 25px;
  color: var(--body-color);
  border: 1px solid var(--border-color);
}
.post-password-form input[type=submit] {
  padding-left: 0;
  padding-right: 0;
  margin: 0;
  width: 140px;
  border: none;
  color: #fff;
  background-color: var(--theme-color);
  text-align: center;
}
.post-password-form input[type=submit]:hover {
  background-color: var(--title-color);
}

.page-links {
  clear: both;
  margin: 0 0 1.5em;
  padding-top: 1em;
}
.page-links > .page-links-title {
  margin-right: 10px;
}
.page-links > span:not(.page-links-title):not(.screen-reader-text),
.page-links > a {
  display: inline-block;
  padding: 5px 13px;
  background-color: var(--white-color);
  color: var(--title-color);
  border: 1px solid rgba(0, 0, 0, 0.08);
  margin-right: 10px;
}
.page-links > span:not(.page-links-title):not(.screen-reader-text):hover,
.page-links > a:hover {
  opacity: 0.8;
  color: var(--white-color);
  background-color: var(--theme-color);
  border-color: transparent;
}
.page-links > span:not(.page-links-title):not(.screen-reader-text).current,
.page-links > a.current {
  background-color: var(--theme-color);
  color: var(--white-color);
  border-color: transparent;
}
.page-links span.screen-reader-text {
  display: none;
}

.blog-single .wp-block-archives-dropdown {
  margin-bottom: 30px;
}
.blog-single.format-quote, .blog-single.format-link, .blog-single.tag-sticky-2, .blog-single.sticky {
  position: relative;
}
.blog-single.format-quote .blog-content, .blog-single.format-link .blog-content, .blog-single.tag-sticky-2 .blog-content, .blog-single.sticky .blog-content {
  background-color: var(--smoke-color);
  border: none;
  padding: 40px;
  border-radius: 15px;
}
.blog-single.format-quote .blog-content:before, .blog-single.format-link .blog-content:before, .blog-single.tag-sticky-2 .blog-content:before, .blog-single.sticky .blog-content:before {
  display: none;
}
.blog-single.format-quote:before, .blog-single.format-link:before, .blog-single.tag-sticky-2:before, .blog-single.sticky:before {
  content: "\f0c1";
  position: absolute;
  font-family: "Font Awesome 6 Pro";
  font-size: 5rem;
  opacity: 0.3;
  right: 15px;
  line-height: 1;
  top: 15px;
  color: var(--theme-color);
  z-index: 1;
}
.blog-single.tag-sticky-2::before, .blog-single.sticky::before {
  content: "\f08d";
  position: absolute;
  font-family: var(--icon-font);
  font-size: 16px;
  font-weight: 500;
  opacity: 1;
  right: 0;
  top: 0;
  color: var(--white-color);
  background-color: var(--theme-color);
  z-index: 1;
  height: 44px;
  width: 44px;
  line-height: 44px;
  text-align: center;
  border-radius: 0 15px 0 4px;
}
.blog-single.format-quote blockquote, .blog-single.format-quote .wp-block-quote {
  background: var(--white-color);
  margin-bottom: 0;
}
.blog-single.format-quote:before {
  content: "\f10e";
  top: 0;
}
.blog-single .blog-content .wp-block-categories-dropdown.wp-block-categories,
.blog-single .blog-content .wp-block-archives-dropdown {
  display: block;
  margin-bottom: 30px;
}
.blog-single.format-chat .entry-content > p:nth-child(2n) {
  background: var(--smoke-color);
  padding: 5px 20px;
}

.blog-details .blog-single:before {
  display: none;
}
.blog-details .blog-single .blog-content {
  background-color: transparent;
  overflow: hidden;
}
.blog-details .blog-single .blog-content p:last-child {
  margin-bottom: 0;
}
.blog-details .blog-single.format-chat .blog-meta {
  margin-bottom: 20px;
}
.blog-details .blog-single.format-chat .blog-content > p:nth-child(2n) {
  background: var(--smoke-color);
  padding: 5px 20px;
}
.blog-details .blog-single.tag-sticky-2, .blog-details .blog-single.sticky, .blog-details .blog-single.format-quote, .blog-details .blog-single.format-link {
  box-shadow: none;
  background-color: transparent;
}
.blog-details .blog-single.tag-sticky-2:before, .blog-details .blog-single.sticky:before, .blog-details .blog-single.format-quote:before, .blog-details .blog-single.format-link:before {
  display: none;
}

.blog-single .wp-block-tag-cloud {
  margin-bottom: 20px;
}
.blog-single .wp-block-tag-cloud a {
  background-color: var(--smoke-color);
  color: var(--title-color);
  box-shadow: none;
}

.th-search {
  background-color: #f3f3f3;
  margin-bottom: 30px;
  border: 1px solid #f3f3f3;
}
.th-search .search-grid-content {
  padding: 30px;
  /* Small devices */
}
@media (max-width: 767px) {
  .th-search .search-grid-content {
    padding: 20px;
  }
}
.th-search .search-grid-title {
  font-size: 20px;
  margin-bottom: 5px;
  margin-top: 0;
}
.th-search .search-grid-title a {
  color: inherit;
}
.th-search .search-grid-title a:hover {
  color: var(--theme-color);
}
.th-search .search-grid-meta > * {
  display: inline-block;
  margin-right: 15px;
  font-size: 14px;
}
.th-search .search-grid-meta > *:last-child {
  margin-right: 0;
}
.th-search .search-grid-meta a,
.th-search .search-grid-meta span {
  color: var(--body-color);
}

/* Large devices */
@media (max-width: 1199px) {
  .blog-single.format-quote:before, .blog-single.format-link:before, .blog-single.tag-sticky-2:before, .blog-single.sticky:before {
    font-size: 14px;
    padding: 8px 16px;
  }
  .blog-single.format-quote:before {
    top: 15px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .blog-single.format-quote:before, .blog-single.format-link:before, .blog-single.tag-sticky-2:before, .blog-single.sticky:before {
    font-size: 14px;
    padding: 8px 16px;
  }
}
@media (max-width: 768px) {
  .wp-block-latest-comments {
    padding-left: 10px;
  }
  .page--content.clearfix + .th-comment-form {
    margin-top: 24px;
  }
}
.site {
  overflow-x: hidden;
}

/*=================================
    02. Reset
==================================*/
/*------------------- 2.1. Container -------------------*/
/* Medium Large devices */
@media (max-width: 1399px) {
  :root {
    --main-container: 1250px;
  }
}
.container2 {
  max-width: 1330px;
  margin: auto;
}

@media (min-width: 1400px) {
  .container, .container-lg, .container-md, .container-sm, .container-xl, .container-xxl {
    max-width: calc(var(--main-container) + var(--container-gutters));
  }
  .container2 {
    max-width: 1330px;
  }
}
@media only screen and (min-width: 1300px) {
  .container2.px-0,
  .container-xxl.px-0,
  .container-xl.px-0,
  .container-lg.px-0,
  .container-md.px-0,
  .container-sm.px-0,
  .container.px-0 {
    max-width: var(--main-container);
  }
  .container2 {
    max-width: 1330px;
  }
}
/* Medium Large devices */
@media (max-width: 1399px) {
  .container2 {
    max-width: 1140px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .container2 {
    max-width: 960px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .container2 {
    max-width: 720px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .container2 {
    max-width: 540px;
  }
}
@media only screen and (max-width: 1600px) {
  .container-fluid.px-0 {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
  .container-fluid.px-0 .row {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .container2 {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
}
/*------------------- 2.2. Grid -------------------*/
.slick-track > [class*=col] {
  flex-shrink: 0;
  width: 100%;
  max-width: 100%;
  padding-right: calc(var(--bs-gutter-x) / 2);
  padding-left: calc(var(--bs-gutter-x) / 2);
  margin-top: var(--bs-gutter-y);
}

.gy-30 {
  --bs-gutter-y: 30px;
}

.gy-40 {
  --bs-gutter-y: 40px;
}

.gy-50 {
  --bs-gutter-y: 50px;
}

.gy-80 {
  --bs-gutter-y: 80px;
}

.gx-10 {
  --bs-gutter-x: 10px;
}

@media (min-width: 1299px) {
  .gx-60 {
    --bs-gutter-x: 60px;
  }
}
@media (min-width: 1399px) {
  .gx-30 {
    --bs-gutter-x: 30px;
  }
  .gx-25 {
    --bs-gutter-x: 25px;
  }
  .gx-40 {
    --bs-gutter-x: 40px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .gy-80 {
    --bs-gutter-y: 40px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .gy-50 {
    --bs-gutter-y: 40px;
  }
}
/*------------------- 2.3. Input -------------------*/
select,
.single-select,
.form-control,
.form-select,
textarea,
input {
  height: 60px;
  padding: 0 30px;
  font-size: 16px;

  font-family: var(--body-font);
  transition: 0.4s ease-in-out;
  padding-right: 45px;
  border-radius: 5px;
    background: transparent;
    border: 1px solid #404040;
    border-radius: 0;
    color: var(--white-color);
}
.cartch{
    list-style: none;
    font-size: 15px;
    color: #6f7172;
    padding: 10px 30px;
    border-bottom: 1px solid #e3e6e7;
    font-weight: 400;
    font-family: "Roboto", sans-serif;
    display: flex;
    justify-content: space-between;
}
.methods{
    display: flex;
    margin-bottom: 20px;
    margin-top:20px;
        justify-content: space-around;
}
@media (max-width: 767px) {
  .coupon {
display: flex;
    }}
    @media (min-width: 767px) {
  .coupon {
display: flex;
        }}
.coupon2{
    margin-left:10px;
    margin-right:10px;
    
}
select:focus,
.single-select:focus,
.form-control:focus,
.form-select:focus,
textarea:focus,
input:focus {
  outline: 0;
  box-shadow: none;

  background-color: transparent;
}
select::-moz-placeholder,
.single-select::-moz-placeholder,
.form-control::-moz-placeholder,
.form-select::-moz-placeholder,
textarea::-moz-placeholder,
input::-moz-placeholder {
  color: var(--body-color);
}
select::-webkit-input-placeholder,
.single-select::-webkit-input-placeholder,
.form-control::-webkit-input-placeholder,
.form-select::-webkit-input-placeholder,
textarea::-webkit-input-placeholder,
input::-webkit-input-placeholder {
  color: var(--body-color);
}
select:-ms-input-placeholder,
.single-select:-ms-input-placeholder,
.form-control:-ms-input-placeholder,
.form-select:-ms-input-placeholder,
textarea:-ms-input-placeholder,
input:-ms-input-placeholder {
  color: var(--body-color);
}
select::placeholder,
.single-select::placeholder,
.form-control::placeholder,
.form-select::placeholder,
textarea::placeholder,
input::placeholder {
  color: var(--body-color);
}
select.style2,
.single-select.style2,
.form-control.style2,
.form-select.style2,
textarea.style2,
input.style2 {
  height: auto;
  border: none;
  border: 2px solid var(--theme-color);
  padding: 14.5px 30px;
  background-color: transparent;
  line-height: initial;
}
select.style2 option,
.single-select.style2 option,
.form-control.style2 option,
.form-select.style2 option,
textarea.style2 option,
input.style2 option {
  background-color: var(--title-color);
  color: var(--body-color);
  padding: 2px 15px;
}
select.style2::-moz-placeholder,
.single-select.style2::-moz-placeholder,
.form-control.style2::-moz-placeholder,
.form-select.style2::-moz-placeholder,
textarea.style2::-moz-placeholder,
input.style2::-moz-placeholder {
  color: var(--body-color);
}
select.style2::-webkit-input-placeholder,
.single-select.style2::-webkit-input-placeholder,
.form-control.style2::-webkit-input-placeholder,
.form-select.style2::-webkit-input-placeholder,
textarea.style2::-webkit-input-placeholder,
input.style2::-webkit-input-placeholder {
  color: var(--body-color);
}
select.style2:-ms-input-placeholder,
.single-select.style2:-ms-input-placeholder,
.form-control.style2:-ms-input-placeholder,
.form-select.style2:-ms-input-placeholder,
textarea.style2:-ms-input-placeholder,
input.style2:-ms-input-placeholder {
  color: var(--body-color);
}
select.style2::placeholder,
.single-select.style2::placeholder,
.form-control.style2::placeholder,
.form-select.style2::placeholder,
textarea.style2::placeholder,
input.style2::placeholder {
  color: var(--body-color);
}
select.style-white,
.single-select.style-white,
.form-control.style-white,
.form-select.style-white,
textarea.style-white,
input.style-white {
  background: var(--white-color);
}
select.style-border,
.single-select.style-border,
.form-control.style-border,
.form-select.style-border,
textarea.style-border,
input.style-border {
  background: transparent;
  border: 1px solid #404040;
  border-radius: 0;
  color: var(--white-color);
}
select.style-border2,
.single-select.style-border2,
.form-control.style-border2,
.form-select.style-border2,
textarea.style-border2,
input.style-border2 {
  background: transparent;
  border: 1px solid #E8E8E8;
  border-radius: 0;
  color: var(--title-color);
}
select.style3,
.single-select.style3,
.form-control.style3,
.form-select.style3,
textarea.style3,
input.style3 {
  background: var(--smoke-color3);
}

.form-text {
  font-size: 16px;
}
.form-text a {
  color: var(--title-color);
  font-weight: 500;
}

.single-select,
.form-select,
select {
  display: block;
  width: 100%;
  line-height: 60px;
  cursor: pointer;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3E%3C/svg%3E");
  background-position: right 26px center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
.single-select:after,
.form-select:after,
select:after {
  right: 30px;
  height: 8px;
  width: 8px;
}
.single-select .list,
.form-select .list,
select .list {
  width: 100%;
}

textarea.form-control,
textarea {
  min-height: 150px;
  padding-top: 16px;
  padding-bottom: 17px;
}
textarea.form-control.style2,
textarea.style2 {
  min-height: 100px;
}

.form-group {
  margin-bottom: 20px;
  position: relative;
}
.form-group > i {
  display: inline-block;
  position: absolute;
  right: 25px;
  top: 21px;
  font-size: 16px;
  color: var(--body-color);
}
.form-group > i.fa-envelope {
  padding-top: 1px;
}
.form-group > i.fa-comment {
  margin-top: -2px;
}
.form-group > i.fa-chevron-down {
  width: 17px;
  background-color: var(--smoke-color);
}
.form-group.has-label > i {
  top: 50px;
}
.form-group.has-icon .form-control {
  padding-left: 70px;
}
.form-group.has-icon .input-icon {
  display: inline-block;
  position: absolute;
  left: 40px;
  top: 18px;
  font-size: 16px;
  color: var(--body-color);
}
.form-group.has-icon .input-icon:hover {
  color: var(--theme-color);
}
.form-group.radius-group input {
  border-radius: 100px;
}
.form-group.style-2 .form-control {
  padding: 0 30px 0 55px;
}
.form-group.style-2 textarea.form-control {
  padding: 16px 30px 30px 55px;
}
.form-group.style-3 .form-control {
  padding: 0 60px 0 30px;
}

[class*=col-].form-group > i {
  right: calc(var(--bs-gutter-x) / 2 + 25px);
}
[class*=col-].form-group .form-icon-left {
  left: calc(var(--bs-gutter-x) / 2 + 30px);
  right: auto;
}

option:checked, option:focus, option:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}

input[type=checkbox] {
  visibility: hidden;
  opacity: 0;
  display: inline-block;
  vertical-align: middle;
  width: 0;
  height: 0;
  display: none;
  border-radius: 10px;
}
input[type=checkbox]:checked ~ label:before {
  content: "\f00c";
  color: var(--white-color);
  background-color: var(--theme-color);
  border-color: var(--theme-color);
}
input[type=checkbox] ~ label {
  position: relative;
  padding-left: 30px;
  cursor: pointer;
  display: block;
}
input[type=checkbox] ~ label:before {
  content: "";
  font-family: var(--icon-font);
  font-weight: 700;
  position: absolute;
  left: 0px;
  top: 3.5px;
  background-color: var(--white-color);
  border: 1px solid var(--theme-color);
  height: 18px;
  width: 18px;
  line-height: 18px;
  text-align: center;
  font-size: 12px;
}
input[type=checkbox].style2 ~ label {
  color: #8B929C;
  padding-left: 23px;
  margin-bottom: -0.5em;
}
input[type=checkbox].style2 ~ label:before {
  background-color: white;
  border: 1px solid rgb(247, 204, 215);
  height: 14px;
  width: 14px;
  line-height: 14px;
  border-radius: 3px;
  top: 6px;
}
input[type=checkbox].style2:checked ~ label:before {
  color: var(--theme-color);
}

input[type=radio] {
  visibility: hidden;
  opacity: 0;
  display: inline-block;
  vertical-align: middle;
  width: 0;
  height: 0;
  display: none;
}
input[type=radio] ~ label {
  position: relative;
  padding-left: 30px;
  cursor: pointer;
  line-height: 1;
  display: inline-block;
  font-weight: 600;
  margin-bottom: 0;
}
input[type=radio] ~ label::before {
  content: "\f111";
  position: absolute;
  font-family: var(--icon-font);
  left: 0;
  top: -2px;
  width: 20px;
  height: 20px;
  padding-left: 0;
  font-size: 0.6em;
  line-height: 19px;
  text-align: center;
  border: 1px solid var(--theme-color);
  border-radius: 100%;
  font-weight: 700;
  background: var(--white-color);
  color: transparent;
  transition: all 0.2s ease;
}
input[type=radio]:checked ~ label::before {
  border-color: var(--theme-color);
  background-color: var(--theme-color);
  color: var(--white-color);
}

label {
  margin-bottom: 0.5em;
  margin-top: -0.3em;
  display: block;
 /*color: var(--title-color);*/
  font-family: var(--body-font);
  font-size: 16px;
}

textarea.is-invalid,
select.is-invalid,
input.is-invalid,
.was-validated input:invalid {
  border: 1px solid var(--error-color) !important;
  background-position: right calc(0.375em + 0.8875rem) center;
  background-image: none;
}
textarea.is-invalid:focus,
select.is-invalid:focus,
input.is-invalid:focus,
.was-validated input:invalid:focus {
  outline: 0;
  box-shadow: none;
}

textarea.is-invalid {
  background-position: top calc(0.375em + 0.5875rem) right calc(0.375em + 0.8875rem);
}

.row.no-gutters > .form-group {
  margin-bottom: 0;
}

.form-messages {
  display: none;
}
.form-messages.mb-0 * {
  margin-bottom: 0;
}
.form-messages.success {
  color: var(--success-color);
  display: block;
}
.form-messages.error {
  color: var(--error-color);
  display: block;
}
.form-messages pre {
  padding: 0;
  background-color: transparent;
  color: inherit;
}

/*------------------- 2.4. Slick Slider -------------------*/
.slick-track > [class*=col] {
  flex-shrink: 0;
  width: 100%;
  max-width: 100%;
  padding-right: calc(var(--bs-gutter-x) / 2);
  padding-left: calc(var(--bs-gutter-x) / 2);
  margin-top: var(--bs-gutter-y);
}

.slick-track {
  min-width: 100%;
}

.slick-list {
  padding-left: 0;
  padding-right: 0;
  overflow: hidden;
}

.slick-slide img {
  display: inline-block;
}

.slick-dots {
  list-style-type: none;
  padding: 0;
  margin: 40px 0 0px 0;
  line-height: 0;
  text-align: center;
  height: max-content;
}
.slick-dots li {
  display: inline-block;
  margin-right: 20px;
}
.slick-dots li:last-child {
  margin-right: 0;
}
.slick-dots button {
  font-size: 0;
  padding: 0;
  width: 8px;
  height: 8px;
  line-height: 0;
  border-radius: 9999px;
  border: none;
  background-color: var(--theme-color);
  transition: all ease 0.4s;
  position: relative;
}
.slick-dots button:hover {
  border-color: var(--theme-color);
}
.slick-dots button:before {
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 1px solid var(--theme-color);
  border-radius: 50%;
  transition: all ease 0.4s;
  opacity: 0;
  visibility: hidden;
}
.slick-dots .slick-active button {
  background-color: var(--theme-color);
}
.slick-dots .slick-active button::before {
  opacity: 1;
  visibility: visible;
}

.slick-arrow {
  --pos-x: 362px;
  display: inline-block;
  padding: 0;
  background-color: var(--white-color);
  color: var(--title-color);
  position: absolute;
  top: 50%;
  border: none;
  border-radius: 50%;
  right: var(--pos-x, 120px);
  width: var(--icon-size, 53px);
  height: var(--icon-size, 53px);
  line-height: var(--icon-size, 55px);
  font-size: var(--icon-font-size, 18px);
  margin-top: calc(var(--icon-size, 53px) / -1);
  z-index: 2;
  opacity: 0.2;
}
.slick-arrow.default {
  position: relative;
  --pos-x: 0;
  margin-top: 0;
}
.slick-arrow.slick-next {
  margin-top: 20px;
}
.slick-arrow:hover {
  opacity: 1;
}

.arrow-margin .slick-arrow {
  top: calc(50% - 30px);
}

.arrow-wrap .slick-arrow {
  opacity: 0;
  visibility: hidden;
}
.arrow-wrap:hover .slick-arrow {
  opacity: 1;
  visibility: visible;
}

/* Hight Resoulation devices */
@media (min-width: 1922px) {
  .slick-arrow {
    --pos-x: 362px;
  }
}
@media (max-width: 1600px) {
  .slick-arrow {
    --pos-x: 212px;
  }
}
/* Extra large devices */
@media (max-width: 1500px) {
  .slick-arrow {
    --pos-x: 132px;
  }
}
/* Medium Large devices */
@media (max-width: 1399px) {
  .slick-arrow {
    --pos-x: 120px;
  }
}
@media (max-width: 1200px) {
  .slick-arrow {
    --pos-x: 42px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .slick-arrow {
    display: none;
  }
  .slick-dots {
    margin: 40px 0 0 0;
  }
  .icon-box .slick-arrow {
    margin-right: 0;
  }
}
.slick-3d-active {
  margin-left: -12%;
  margin-right: -12%;
}
.slick-3d-active .slick-list {
  padding-left: 30% !important;
  padding-right: 30% !important;
}
.slick-3d-active .slick-track {
  max-width: 100% !important;
  transform: translate3d(0, 0, 0) !important;
  perspective: 100px;
}
.slick-3d-active .slick-slide {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  opacity: 0;
  width: 100% !important;
  transform: translate3d(0, 0, 0);
  transition: transform 1s, opacity 1s;
}
.slick-3d-active .slick-3d-next,
.slick-3d-active .slick-3d-prev,
.slick-3d-active .slick-3d-next2,
.slick-3d-active .slick-3d-prev2 {
  display: block;
}
.slick-3d-active .slick-current {
  opacity: 1;
  position: relative;
  display: block;
  z-index: 2;
}
.slick-3d-active .slick-3d-next {
  opacity: 1;
  transform: translate3d(50%, 0, -21px);
  z-index: 1;
  perspective: 1000px;
}
.slick-3d-active .slick-3d-next2 {
  opacity: 1;
  transform: translate3d(40%, 0, -23px);
  z-index: 0;
  perspective: 1000px;
}
.slick-3d-active .slick-3d-prev {
  opacity: 1;
  transform: translate3d(-50%, 0, -21px);
}
.slick-3d-active .slick-3d-prev .testi-card {
  box-shadow: none;
}
.slick-3d-active .slick-3d-prev2 {
  opacity: 1;
  transform: translate3d(-40%, 0, -23px);
}

/*------------------- 2.5. Mobile Menu -------------------*/
.mobile-menu-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999999;
  width: 0;
  width: 100%;
  height: 100%;
  transition: all ease 0.8s;
  opacity: 0;
  visibility: hidden;
}
.mobile-menu-wrapper .mobile-logo {
  padding-bottom: 30px;
  padding-top: 40px;
  display: block;
  text-align: center;
  background-color: var(--smoke-color2);
}
.mobile-menu-wrapper .mobile-logo svg {
  max-width: 185px;
}
.mobile-menu-wrapper .menu-toggle {
  border: none;
  font-size: 22px;
  position: absolute;
  right: -16.5px;
  top: 25px;
  padding: 0;
  line-height: 1;
  width: 33px;
  height: 33px;
  line-height: 36px;
  font-size: 18px;
  z-index: 1;
  color: var(--white-color);
  background-color: var(--theme-color);
  border-radius: 50%;
}
.mobile-menu-wrapper .menu-toggle:hover {
  background-color: var(--title-color);
  color: var(--white-color);
}
.mobile-menu-wrapper .mobile-menu-area {
  width: 100%;
  max-width: 310px;
  background-color: #fff;
  border-right: 3px solid var(--theme-color);
  height: 100%;
  position: relative;
  left: -110%;
  opacity: 0;
  visibility: hidden;
  transition: all ease 1s;
  z-index: 1;
}
.mobile-menu-wrapper.body-visible {
  opacity: 1;
  visibility: visible;
}
.mobile-menu-wrapper.body-visible .mobile-menu-area {
  left: 0;
  opacity: 1;
  visibility: visible;
}

.mobile-menu {
  overflow-y: scroll;
  max-height: calc(100vh - 200px);
  padding-bottom: 40px;
  margin-top: 33px;
  text-align: left;
}
.mobile-menu ul {
  margin: 0;
  padding: 0 0;
}
.mobile-menu ul li {
  border-bottom: 1px solid #fdedf1;
  list-style-type: none;
}
.mobile-menu ul li li:first-child {
  border-top: 1px solid #fdedf1;
}
.mobile-menu ul li a {
  display: block;
  position: relative;
  padding: 12px 0;
  line-height: 1.4;
  font-size: 16px;
  text-transform: capitalize;
  color: var(--title-color);
  padding-left: 18px;
}
.mobile-menu ul li a:before {
  content: "\f105";
  font-family: var(--icon-font);
  position: absolute;
  left: 0;
  top: 12px;
  margin-right: 10px;
  display: inline-block;
}
.mobile-menu ul li.active-class > a {
  color: var(--theme-color);
}
.mobile-menu ul li.active-class > a:before {
  transform: rotate(90deg);
}
.mobile-menu ul li ul li {
  padding-left: 20px;
}
.mobile-menu ul li ul li:last-child {
  border-bottom: none;
}
.mobile-menu ul .submenu-item-has-children > a .mean-expand-class {
  position: absolute;
  right: 0;
  top: 50%;
  font-weight: 400;
  font-size: 12px;
  width: 25px;
  height: 25px;
  line-height: 25px;
  margin-top: -12.5px;
  display: inline-block;
  text-align: center;
  background-color: var(--smoke-color);
  color: var(--title-color);
  box-shadow: 0 0 20px -8px rgba(173, 136, 88, 0.5);
  border-radius: 50%;
}
.mobile-menu ul .submenu-item-has-children > a .mean-expand-class:before {
  content: "\f067";
  font-family: var(--icon-font);
}
.mobile-menu ul .submenu-item-has-children > a:after {
  content: "\f067";
  font-family: var(--icon-font);
  width: 22px;
  height: 22px;
  line-height: 22px;
  display: inline-block;
  text-align: center;
  font-size: 12px;
  border-radius: 50px;
  background-color: var(--smoke-color);
  float: right;
  margin-top: 1px;
}
.mobile-menu ul .submenu-item-has-children.active-class > a .mean-expand-class:before {
  content: "\f068";
}
.mobile-menu ul .submenu-item-has-children.active-class > a:after {
  content: "\f068";
}
.mobile-menu > ul {
  padding: 0 40px;
}
.mobile-menu > ul > li:last-child {
  border-bottom: none;
}

@media (max-width: 400px) {
  .mobile-menu-wrapper .mobile-menu-area {
    width: 100%;
    max-width: 270px;
  }
  .mobile-menu > ul {
    padding: 0 20px;
  }
}
/*=================================
    03. Utilities
==================================*/
/*------------------- 3.1. Preloader -------------------*/
.preloader {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 999;
  background-color: var(--title-color);
}
.preloader .btn {
  padding: 15px 20px;
  border-radius: 0;
  font-size: 14px;
  text-transform: capitalize;
}
.preloader .btn:after {
  border-radius: 0;
}

.preloader-inner {
  text-align: center;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  line-height: 1;
}
.preloader-inner img {
  display: block;
  margin: 0 auto 10px auto;
}

.loader {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: inline-block;
  border-top: 4px solid #FFF;
  border-right: 4px solid transparent;
  box-sizing: border-box;
  animation: rotation 1s linear infinite;
}

.loader::after {
  content: "";
  box-sizing: border-box;
  position: absolute;
  left: 0;
  top: 0;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border-left: 4px solid var(--theme-color);
  border-bottom: 4px solid transparent;
  animation: rotation 0.5s linear infinite reverse;
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/*------------------- 3.2. Buttons -------------------*/
.btn {
  position: relative;
  z-index: 2;
  vertical-align: middle;
  display: inline-block;
  border: none;
  text-align: center;
  background-color: transparent;
  color: var(--white-color);
  font-family: var(--title-font);
  font-size: 16px;
  font-weight: 500;
  line-height: 1;
  padding: 18.5px 35px 20.5px;
}
.btn::before {
  position: absolute;
  content: "";
  left: 3px;
  top: 0;
  width: 97%;
  height: 100%;
  background: var(--theme-color);
  border: none;
  z-index: -1;
  transition: 0.4s;
  border-radius: 10px;
  transform: skewX(-10deg);
}
.btn:focus, .btn:hover, .btn:active {
  color: var(--white-color);
  box-shadow: none;
}
.btn:focus:before, .btn:hover:before, .btn:active:before {
  background: var(--black-color);
  width: 97%;
  transform: skewX(10deg);
}
.btn.style2:hover {
  color: var(--title-color);
}
.btn.style2:hover:before {
  background-color: var(--white-color);
}
.btn.style3 {
  border-radius: 0;
  background: var(--theme-color);
}
.btn.style3:before {
  border-radius: 0;
  transform: none;
  left: 0;
  width: 0;
}
.btn.style3:hover {
  color: var(--title-color);
}
.btn.style3:hover:before {
  background-color: var(--white-color);
  width: 100%;
}
.btn.style4:before {
  background: var(--title-color);
}
.btn.style4:hover {
  color: var(--white-color);
}
.btn.style4:hover:before {
  background-color: var(--theme-color);
}
.btn.style5 {
  border-radius: 0;
  padding: 21px 34px 23px;
  background: var(--title-color);
}
.btn.style5:before {
  border-radius: 0;
  transform: none;
  left: 0;
  width: 0;
}
.btn.style5:hover:before {
  background-color: var(--theme-color);
  width: 100%;
}
.btn.style6 {
  border-radius: 0;
  background: var(--theme-color);
  font-weight: 600;
  font-size: 14px;
  letter-spacing: 0.05em;
}
.btn.style6:before {
  border-radius: 0;
  transform: none;
  left: 0;
  width: 0;
}
.btn.style6:hover:before {
  width: 100%;
}
.btn.btn-border {
  color: var(--title-color);
  padding: 19.5px 21px 19.5px 25px;
}
.btn.btn-border:before {
  background: transparent;
  border: 1px solid var(--black-color);
}
.btn.btn-border:active, .btn.btn-border:focus, .btn.btn-border:hover {
  color: var(--white-color);
}
.btn.btn-border:active:before, .btn.btn-border:focus:before, .btn.btn-border:hover:before {
  background-color: var(--black-color);
}
.btn.btn-border2 {
  color: var(--theme-color);
  padding: 19.5px 40px 19.5px 40px;
}
.btn.btn-border2:before {
  background: transparent;
  border: 1px solid var(--theme-color);
}
.btn.btn-border2:active, .btn.btn-border2:focus, .btn.btn-border2:hover {
  color: var(--white-color);
}
.btn.btn-border2:active:before, .btn.btn-border2:focus:before, .btn.btn-border2:hover:before {
  background-color: var(--theme-color);
}
.btn.btn-border3 {
  color: var(--white-color);
  padding: 19.5px 21px 19.5px 25px;
}
.btn.btn-border3:before {
  background: transparent;
  border: 1px solid var(--white-color);
}
.btn.btn-border3:active, .btn.btn-border3:focus, .btn.btn-border3:hover {
  color: var(--theme-color);
}
.btn.btn-border3:active:before, .btn.btn-border3:focus:before, .btn.btn-border3:hover:before {
  background-color: var(--white-color);
}
.btn.btn-border4 {
  color: var(--title-color);
  border: 1px solid var(--title-color);
  border-radius: 0;
  font-family: var(--title-font);
}
.btn.btn-border4:before {
  display: none;
}
.btn.btn-border4:active, .btn.btn-border4:focus, .btn.btn-border4:hover {
  color: var(--white-color);
  background: var(--title-color);
}
.btn.style-r0:before {
  border-radius: 0;
}
.btn.btn-fw {
  width: 100%;
}
.btn.btn-fw:before, .btn.btn-fw:after {
  display: none;
}
.btn.btn-fw:hover {
  background-color: var(--title-color);
}

.icon-btn {
  display: inline-block;
  width: var(--btn-size, 46px);
  height: var(--btn-size, 46px);
  line-height: var(--btn-size, 46px);
  font-size: var(--btn-font-size, 16px);
  background-color: var(--theme-color);
  color: var(--white-color);
  text-align: center;
  border-radius: 6px;
  border: none;
  transition: 0.4s ease-in-out;
}
.icon-btn:hover {
  background-color: var(--title-color);
  color: var(--white-color);
}
.icon-btn.btn-border {
  background: transparent;
  border: 2px solid var(--theme-color);
  color: var(--theme-color);
}
.icon-btn.btn-border:hover {
  background: var(--theme-color);
  color: var(--white-color);
}

.play-btn {
  display: inline-block;
  position: relative;
  z-index: 1;
}
.play-btn > i {
  display: inline-block;
  width: var(--icon-size, 100px);
  height: var(--icon-size, 100px);
  line-height: var(--icon-size, 100px);
  text-align: center;
  background-color: var(--theme-color);
  color: var(--white-color);
  font-size: var(--icon-font-size, 30px);
  border-radius: 50%;
  z-index: 1;
  transition: all ease 0.4s;
}
.play-btn > i.fa-play {
  padding-right: 0.18em;
}
.play-btn:after, .play-btn:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background-color: transparent;
  border: 1px solid rgba(255, 255, 255, 0.1);
  z-index: -1;
  border-radius: 50%;
  transition: all ease 0.4s;
}
.play-btn:after {
  animation-delay: 2s;
}
.play-btn:hover i {
  background-color: var(--theme-color);
}
.play-btn.style2:before, .play-btn.style2:after {
  background-color: transparent;
  border: 1px solid var(--white-color);
}
.play-btn.style3 > i {
  background-color: var(--theme-color);
  color: var(--white-color);
  font-size: 20px;
}
.play-btn.style3:before, .play-btn.style3:after {
  background-color: var(--white-color);
}
.play-btn.style3:hover > i {
  background-color: var(--white-color);
  color: var(--theme-color);
}

.link-btn {
  font-size: 14px;
  font-weight: 600;
  display: inline-block;
  line-height: 0.8;
  position: relative;
  padding-bottom: 2px;
  margin-bottom: -2px;
  text-transform: uppercase;
  color: var(--theme-color);
}
.link-btn i {
  margin-left: 5px;
  font-size: 0.9rem;
}
.link-btn:before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 0;
  height: 2px;
  background-color: var(--theme-color);
  transition: all ease 0.4s;
}
.link-btn:hover {
  color: var(--theme-color);
}
.link-btn:hover::before {
  width: 100%;
}
.link-btn.style2 {
  font-weight: 500;
  font-family: var(--title-font);
  color: var(--title-color);
}
.link-btn.style2 i {
  height: 45px;
  width: 45px;
  line-height: 44px;
  border-radius: 50%;
  background: var(--white-color);
  border: 1px solid rgba(237, 237, 237, 0.89);
  box-shadow: 0px 16px 51px rgba(0, 0, 0, 0.07);
  text-align: center;
  color: var(--title-color);
  margin-right: 7px;
  margin-left: 0;
  transition: 0.4s;
}
.link-btn.style2:before {
  background-color: var(--theme-color);
  bottom: 12px;
  left: 55px;
}
.link-btn.style2:hover {
  color: var(--theme-color);
}
.link-btn.style2:hover i {
  background: var(--theme-color);
  color: var(--white-color);
}
.link-btn.style2:hover:before {
  width: calc(100% - 55px);
}

.line-btn {
  font-size: 14px;
  font-weight: 600;
  display: inline-block;
  line-height: 0.8;
  position: relative;
  padding-bottom: 4px;
  margin-bottom: -1px;
  text-transform: uppercase;
  color: var(--theme-color);
}
.line-btn i {
  margin-left: 5px;
  font-size: 0.9rem;
}
.line-btn:before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 1px;
  background-color: var(--theme-color);
  transition: all ease 0.4s;
}
.line-btn:hover {
  color: var(--title-color);
}
.line-btn:hover::before {
  background-color: var(--title-color);
  width: 45px;
}

.scroll-top {
  position: fixed;
  right: 30px;
  bottom: 30px;
  height: 50px;
  width: 50px;
  cursor: pointer;
  display: block;
  border-radius: 50px;
  z-index: 10000;
  opacity: 1;
  visibility: hidden;
  transform: translateY(45px);
  transition: all 300ms linear;
}
.scroll-top:after {
  content: "\f062";
  font-family: var(--icon-font);
  position: absolute;
  text-align: center;
  line-height: 50px;
  font-size: 20px;
  color: var(--theme-color);
  left: 0;
  top: 0;
  height: 50px;
  width: 50px;
  cursor: pointer;
  display: block;
  z-index: 1;
  border: 2px solid var(--theme-color);
  box-shadow: none;
  border-radius: 50%;
}
.scroll-top svg {
  color: var(--theme-color);
  border-radius: 50%;
  background: var(--white-color);
}
.scroll-top svg path {
  fill: none;
}
.scroll-top .progress-circle path {
  stroke: var(--theme-color);
  stroke-width: 20px;
  box-sizing: border-box;
  transition: all 400ms linear;
}
.scroll-top.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

/* Small devices */
@media (max-width: 767px) {
  .play-btn {
    --icon-size: 70px;
    --icon-font-size: 24px;
  }
  .btn {
    font-size: 14px;
    font-weight: 600;
    line-height: 1;
    padding: 17px 25px;
  }
  .scroll-btn img {
    width: 70px;
  }
  .scroll-btn.show {
    bottom: -50px;
  }
}
/*------------------- 3.3. Titles -------------------*/
.sec-title {
  margin-bottom: calc(var(--section-title-space) - 43px);
  margin-top: -0.23em;
  font-weight: 500;
  line-height: 58px;
}

.sub-title {
  display: inline-block;
  line-height: 1.2;
  color: var(--theme-color);
  text-transform: uppercase;
  font-size: 14px;
  position: relative;
  padding: 8px 20px 6px;
  font-weight: 500;
  font-family: var(--title-font);
  margin-bottom: 26px;
  letter-spacing: 0.05em;
  background: var(--white-color);
  box-shadow: 0px 8px 19px rgba(0, 0, 0, 0.07);
  border-radius: 50px;
}
.sub-title.style2 {
  background: transparent;
  box-shadow: none;
  padding: 0;
  margin-top: -0.2em;
  display: block;
}

.box-title {
  font-size: 24px;
  line-height: 1.417;
  font-weight: 600;
  margin-top: -0.3em;
}
.box-title a {
  color: inherit;
}
.box-title a:hover {
  color: var(--theme-color);
}

.sec-text {
  font-size: 16px;
  margin-top: 30px;
  line-height: 1.667;
  margin-bottom: 0;
    text-align: justify;
    direction: rtl;
}

.title-area {
  margin-bottom: calc(var(--section-title-space) - 17px);
  position: relative;
  z-index: 2;
      margin-top: calc(var(--section-title-space) - 17px);
}
.title-area.mb-0 .sec-title {
  margin-bottom: 17px;
}
.title-area .btn {
  margin-top: 36px;
}
.title-area .checklist {
  margin-top: 30px;
}

.white-title {
  color: #fff;
  font-weight: 700;
  position: relative;
  padding-bottom: 9px;
  margin-bottom: 19px;
}
.white-title:after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  background: #fff;
  height: 2px;
  width: 50px;
}

.page-title {
  font-size: 40px;
  font-weight: 500;
  margin-top: -0.3em;
}

.page-subtitle {
  font-size: 30px;
  font-weight: 500;
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .sec-title {
    font-size: 45px;
    line-height: 1.2;
  }
  .sec-text {
    margin-top: 0px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .title-area,
  .sec-title {
    --section-title-space: 60px;
  }
  .title-area.mb-45,
  .sec-title.mb-45 {
    margin-bottom: 36px;
  }
  .title-area.mb-50,
  .sec-title.mb-50 {
    margin-bottom: 40px;
  }
  .sec-btn,
  .title-line {
    --section-title-space: 55px;
  }
  .sec-text {
    font-size: 16px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .title-area,
  .sec-title {
    --section-title-space: 50px;
  }
  .title-area.mb-45,
  .sec-title.mb-45 {
    margin-bottom: 35px;
  }
  .sec-btn,
  .title-line {
    --section-title-space: 50px;
  }
  .sub-title {
    font-size: 14px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .sec-title {
    font-size: 34px;
  }
}
@media (max-width: 390px) {
  .sec-title {
    font-size: 30px;
  }
}
/*------------------- 3.4. Common -------------------*/
.shape-mockup-wrap {
  z-index: 2;
  position: relative;
}

.shape-mockup {
  position: absolute;
  z-index: -1;
}
.shape-mockup.z-index-3 {
  z-index: 3;
}

.z-index-step1 {
  position: relative;
  z-index: 4 !important;
}

.z-index-common {
  position: relative;
  z-index: 3;
}

.z-index-3 {
  z-index: 3;
}

.z-index-n1 {
  z-index: -1;
}

.media-body {
  flex: 1;
}

.badge {
  position: absolute;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  display: inline-block;
  text-align: center;
  background-color: var(--theme-color);
  color: var(--white-color);
  padding: 0.2em 0.45em;
  font-size: 0.6em;
  border-radius: 50%;
  top: 7px;
  left: 12px;
  font-weight: 400;
  transition: 0.3s ease-in-out;
}

.social-btn {
  display: flex;
  gap: 10px;
}
.social-btn a {
  height: 35px;
  width: 35px;
  line-height: 35px;
  border-radius: 7px;
  display: inline-block;
  background-color: var(--smoke-color4);
  color: var(--title-color);
  text-align: center;
  font-size: 12px;
}
.social-btn a:hover {
  background: var(--theme-color);
  color: var(--white-color);
}
.social-btn.style2 a {
  --icon-size: 40px;
  font-size: 16px;
  color: var(--white-color);
  background: var(--theme-color);
  border-radius: 6px;
}
.social-btn.style2 a:hover {
  border-color: var(--theme-color);
  color: var(--white-color);
  background-color: var(--theme-color);
}
.social-btn.style3 a {
  background: var(--smoke-color3);
  color: var(--title-color);
}
.social-btn.style3 a:hover {
  background: var(--theme-color);
  color: var(--white-color);
}
.social-btn.color-theme a {
  color: var(--body-color);
  border-color: var(--theme-color);
}

.global-carousel.slider-shadow .slick-list {
  padding: 30px 0px 40px 0px !important;
  margin: -30px 0px -40px 0px;
}

.btn-group {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 20px;
}
.btn-group .btn {
  border-radius: 25px !important;
  flex: none;
}
.btn-group.style2 {
  gap: 20px 70px;
  /* Small devices */
}
@media (max-width: 767px) {
  .btn-group.style2 {
    gap: 20px 30px;
  }
}

/*******Magnific Image*******/
.mfp-zoom-in .mfp-content {
  opacity: 0;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  -webkit-transform: scale(0.7);
  -ms-transform: scale(0.7);
  transform: scale(0.7);
}

.mfp-zoom-in.mfp-bg {
  opacity: 0;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
}

.mfp-zoom-in.mfp-ready .mfp-content {
  opacity: 1;
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
}

.mfp-zoom-in.mfp-ready.mfp-bg {
  opacity: 0.7;
}

.mfp-zoom-in.mfp-removing .mfp-content {
  -webkit-transform: scale(0.7);
  -ms-transform: scale(0.7);
  transform: scale(0.7);
  opacity: 0;
}

.mfp-zoom-in.mfp-removing.mfp-bg {
  opacity: 0;
}

/*------------------- 3.6. Font -------------------*/
.font-icon {
  font-family: var(--icon-font);
}

.font-title {
  font-family: var(--title-font);
}

.font-body {
  font-family: var(--body-font);
}

.fw-extralight {
  font-weight: 100;
}

.fw-light {
  font-weight: 300;
}

.fw-normal {
  font-weight: 400;
}

.fw-medium {
  font-weight: 500 !important;
}

.fw-semibold {
  font-weight: 600 !important;
}

.fw-bold {
  font-weight: 700;
}

.fw-extrabold {
  font-weight: 800;
}

.fs-md {
  font-size: 18px;
}

.fs-xs {
  font-size: 14px;
}

.text-underline {
  text-decoration: underline;
}

/*------------------- 3.7. Background -------------------*/
.bg-theme {
  background-color: var(--theme-color) !important;
}

.bg-theme2 {
  background-color: var(--theme-color) !important;
}

.bg-smoke {
  background-color: var(--smoke-color) !important;
}

.bg-smoke2 {
  background-color: var(--smoke-color2) !important;
}

.bg-smoke3 {
  background-color: var(--smoke-color3) !important;
}

.bg-smoke4 {
  background-color: var(--smoke-color4) !important;
}

.bg-smoke5 {
  background-color: var(--smoke-color5) !important;
}

.bg-white {
  background-color: var(--white-color) !important;
}

.bg-black {
  background-color: var(--black-color) !important;
}

.bg-title {
  background-color: var(--title-color) !important;
}

.background-image,
[data-bg-src] {
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
}

.bg-fluid {
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center center;
}

.bg-auto {
  background-size: auto auto;
}

.bg-top-center {
  background-size: auto;
  background-position: top center;
}

.bg-repeat {
  background-size: auto;
  background-repeat: repeat;
}

/*------------------- 3.8. Text Color -------------------*/
.text-theme {
  color: var(--theme-color) !important;
}

.text-theme2 {
  color: var(--theme-color) !important;
}

.text-title {
  color: var(--title-color) !important;
}

.text-body {
  color: var(--body-color) !important;
}

.text-white {
  color: var(--white-color) !important;
}

.text-light {
  color: var(--light-color) !important;
}

.text-yellow {
  color: var(--yellow-color) !important;
}

.text-success {
  color: var(--success-color) !important;
}

.text-error {
  color: var(--error-color) !important;
}

.text-inherit {
  color: inherit;
}
.text-inherit:hover {
  color: var(--theme-color);
}

a.text-theme:hover,
.text-reset:hover {
  text-decoration: underline;
}

/*------------------- 3.9. Overlay -------------------*/
.overlay {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
}

.position-center {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

[data-overlay] {
  position: relative;
  z-index: 2;
}
[data-overlay] [class^=col-],
[data-overlay] [class*=col-] {
  z-index: 1;
}

[data-overlay]:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

[data-overlay=theme]:before {
  background-color: var(--theme-color);
}

[data-overlay=title]:before {
  background-color: var(--title-color);
}

[data-overlay=white]:before {
  background-color: var(--white-color);
}

[data-overlay=black]:before {
  background-color: var(--black-color);
}

[data-overlay=overlay1]:before {
  background-color: #131B23;
}

[data-opacity="1"]:before {
  opacity: 0.1;
}

[data-opacity="2"]:before {
  opacity: 0.2;
}

[data-opacity="3"]:before {
  opacity: 0.3;
}

[data-opacity="4"]:before {
  opacity: 0.4;
}

[data-opacity="5"]:before {
  opacity: 0.5;
}

[data-opacity="6"]:before {
  opacity: 0.6;
}

[data-opacity="7"]:before {
  opacity: 0.7;
}

[data-opacity="8"]:before {
  opacity: 0.8;
}

[data-opacity="9"]:before {
  opacity: 0.9;
}

[data-opacity="10"]:before {
  opacity: 1;
}

/*------------------- 3.10. Animation -------------------*/
.ripple-animation, .play-btn:after, .play-btn:before {
  animation-duration: var(--ripple-ani-duration);
  animation-timing-function: ease-in-out;
  animation-iteration-count: infinite;
  animation-name: ripple;
}

@keyframes ripple {
  0% {
    transform: scale(1);
    opacity: 0;
  }
  30% {
    opacity: 1;
  }
  100% {
    transform: scale(2.1);
    opacity: 0;
  }
}
.movingX {
  animation: movingX 8s linear infinite;
}

@keyframes movingX {
  0% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(50px);
  }
  100% {
    transform: translateX(0);
  }
}
.moving {
  animation: moving 8s linear infinite;
}

@keyframes moving {
  0% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(-50px);
  }
  100% {
    transform: translateX(0);
  }
}
.jump {
  animation: jumpAni 7s linear infinite;
}

.jump1 {
  animation: jumpAni 6s linear infinite;
}

.jump2 {
  animation: jumpAni 5s linear infinite;
}

.jump3 {
  animation: jumpAni 4s linear infinite;
}

@keyframes jumpAni {
  0% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-30px);
  }
  100% {
    transform: translateY(0);
  }
}
.jump-reverse {
  animation: jumpReverseAni 7s linear infinite;
}

@keyframes jumpReverseAni {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(30px);
  }
  100% {
    transform: translateY(0);
  }
}
.spin {
  animation: spin 15s linear infinite;
}

.spin-slow {
  animation: spin 50s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes animate-positive {
  0% {
    width: 0;
  }
}
.scalein.slider-animated {
  --animation-name: scalein;
}

.slidetopleft.slider-animated {
  --animation-name: slidetopleft;
}

.slidebottomright.slider-animated {
  --animation-name: slidebottomright;
}

.slideinleft.slider-animated {
  --animation-name: slideinleft;
}

.slideinright.slider-animated {
  --animation-name: slideinright;
}

.slideinup.slider-animated {
  --animation-name: slideinup;
}

.slideindown.slider-animated {
  --animation-name: slideindown;
}

.rollinleft.slider-animated {
  --animation-name: rollinleft;
}

.rollinright.slider-animated {
  --animation-name: rollinright;
}

.scalein,
.slidetopleft,
.slidebottomright,
.slideinleft,
.slideinright,
.slideindown,
.slideinup,
.rollinleft,
.rollinright {
  opacity: 0;
  animation-fill-mode: both;
  animation-iteration-count: 1;
  animation-duration: 1s;
  animation-delay: 0.3s;
  animation-name: var(--animation-name);
}

.slider-animated {
  opacity: 1;
}

@keyframes slideinup {
  0% {
    opacity: 0;
    transform: translateY(70px);
  }
  100% {
    transform: translateY(0);
  }
}
@keyframes slideinright {
  0% {
    opacity: 0;
    transform: translateX(70px);
  }
  100% {
    transform: translateX(0);
  }
}
@keyframes slideindown {
  0% {
    opacity: 0;
    transform: translateY(-70px);
  }
  100% {
    transform: translateY(0);
  }
}
@keyframes slideinleft {
  0% {
    opacity: 0;
    transform: translateX(-70px);
  }
  100% {
    transform: translateX(0);
  }
}
@keyframes slidebottomright {
  0% {
    opacity: 0;
    transform: translateX(100px) translateY(100px);
  }
  100% {
    transform: translateX(0) translateY(0);
  }
}
@keyframes slidetopleft {
  0% {
    opacity: 0;
    transform: translateX(-100px) translateY(-100px);
  }
  100% {
    transform: translateX(0) translateY(0);
  }
}
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
  0% {
    border-right-color: transparent;
    border-bottom-color: transparent;
    border-top-color: transparent;
    border-left-color: transparent;
  }
  75% {
    border-top-color: #fff;
    border-left-color: #fff;
    border-right-color: #fff;
    border-bottom-color: transparent;
  }
  100% {
    border-right-color: transparent;
    border-bottom-color: transparent;
    border-top-color: transparent;
    border-left-color: transparent;
  }
}
/*=================================
    04. Template Style
==================================*/
/*------------------- 4.1. Widget  -------------------*/
.widget_nav_menu ul,
.widget_meta ul,
.widget_pages ul,
.widget_archive ul,
.wp-block-page-list ul,
.widget_categories ul {
  list-style: none;
  padding: 0;
  margin: 0 0 0 0;
}
.widget_nav_menu .menu,
.widget_nav_menu > ul,
.widget_meta .menu,
.widget_meta > ul,
.widget_pages .menu,
.widget_pages > ul,
.widget_archive .menu,
.widget_archive > ul,
.wp-block-page-list .menu,
.wp-block-page-list > ul,
.widget_categories .menu,
.widget_categories > ul {
  margin: -3px 0 0px 0;
}
.widget_nav_menu a,
.widget_meta a,
.widget_pages a,
.widget_archive a,
.wp-block-page-list a,
.widget_categories a {
  display: block;
  border: none;
  border-bottom: 1px solid #E6E6E6;
  margin: 0 0px 19px 0px;
  padding: 0px 0px 20px;
  font-size: 16px;
  font-weight: 400;
  font-family: var(--title-font);
  line-height: 1.313;
  color: var(--body-color);
  position: relative;
  display: flex;
  align-items: center;
}
.widget_nav_menu a i,
.widget_meta a i,
.widget_pages a i,
.widget_archive a i,
.wp-block-page-list a i,
.widget_categories a i {
  margin-left: 12px;
  transition: 0.4s all;
}
.widget_nav_menu a.active, .widget_nav_menu a:hover,
.widget_meta a.active,
.widget_meta a:hover,
.widget_pages a.active,
.widget_pages a:hover,
.widget_archive a.active,
.widget_archive a:hover,
.wp-block-page-list a.active,
.wp-block-page-list a:hover,
.widget_categories a.active,
.widget_categories a:hover {
  color: var(--theme-color);
}
.widget_nav_menu a.active i, .widget_nav_menu a:hover i,
.widget_meta a.active i,
.widget_meta a:hover i,
.widget_pages a.active i,
.widget_pages a:hover i,
.widget_archive a.active i,
.widget_archive a:hover i,
.wp-block-page-list a.active i,
.wp-block-page-list a:hover i,
.widget_categories a.active i,
.widget_categories a:hover i {
  color: var(--theme-color);
  opacity: 1;
}
.widget_nav_menu a.active ~ span, .widget_nav_menu a:hover ~ span,
.widget_meta a.active ~ span,
.widget_meta a:hover ~ span,
.widget_pages a.active ~ span,
.widget_pages a:hover ~ span,
.widget_archive a.active ~ span,
.widget_archive a:hover ~ span,
.wp-block-page-list a.active ~ span,
.wp-block-page-list a:hover ~ span,
.widget_categories a.active ~ span,
.widget_categories a:hover ~ span {
  color: var(--theme-color);
}
.widget_nav_menu li,
.widget_meta li,
.widget_pages li,
.widget_archive li,
.wp-block-page-list li,
.widget_categories li {
  display: block;
  position: relative;
}
.widget_nav_menu li > span,
.widget_meta li > span,
.widget_pages li > span,
.widget_archive li > span,
.wp-block-page-list li > span,
.widget_categories li > span {
  font-size: 16px;
  position: absolute;
  right: 0;
  top: 0px;
  background: transparent;
  border-radius: 0;
  height: auto;
  width: auto;
  line-height: initial;
  text-align: center;
  color: var(--body-color);
  transition: all ease 0.4s;
}
.widget_nav_menu li:last-child a,
.widget_meta li:last-child a,
.widget_pages li:last-child a,
.widget_archive li:last-child a,
.wp-block-page-list li:last-child a,
.widget_categories li:last-child a {
  margin-bottom: -5px;
  padding-bottom: 0;
  border-bottom: 0;
}
.widget_nav_menu .wp-block-navigation__submenu-container,
.widget_nav_menu .sub-menu,
.widget_nav_menu .children,
.widget_meta .wp-block-navigation__submenu-container,
.widget_meta .sub-menu,
.widget_meta .children,
.widget_pages .wp-block-navigation__submenu-container,
.widget_pages .sub-menu,
.widget_pages .children,
.widget_archive .wp-block-navigation__submenu-container,
.widget_archive .sub-menu,
.widget_archive .children,
.wp-block-page-list .wp-block-navigation__submenu-container,
.wp-block-page-list .sub-menu,
.wp-block-page-list .children,
.widget_categories .wp-block-navigation__submenu-container,
.widget_categories .sub-menu,
.widget_categories .children {
  margin-left: 10px;
}
.widget_nav_menu .wp-block-navigation__submenu-container li a,
.widget_nav_menu .sub-menu li a,
.widget_nav_menu .children li a,
.widget_meta .wp-block-navigation__submenu-container li a,
.widget_meta .sub-menu li a,
.widget_meta .children li a,
.widget_pages .wp-block-navigation__submenu-container li a,
.widget_pages .sub-menu li a,
.widget_pages .children li a,
.widget_archive .wp-block-navigation__submenu-container li a,
.widget_archive .sub-menu li a,
.widget_archive .children li a,
.wp-block-page-list .wp-block-navigation__submenu-container li a,
.wp-block-page-list .sub-menu li a,
.wp-block-page-list .children li a,
.widget_categories .wp-block-navigation__submenu-container li a,
.widget_categories .sub-menu li a,
.widget_categories .children li a {
  border-bottom: 1px solid #E6E6E6;
  margin: 0 0px 19px 0px;
  padding: 0px 0px 20px;
}

.widget_nav_menu a,
.widget_meta a,
.widget_pages a {
  padding-right: 20px;
}

.widget_nav_menu .sub-menu {
  margin-left: 10px;
}

.wp-block-page-list {
  padding: 0;
  margin: -3px 0 0px 0;
}

.product_list_widget .star-rating {
  width: auto;
  font-size: 12px;
}
.product_list_widget li a {
  color: var(--title-color);
}
.product_list_widget li a:hover {
  color: var(--theme-color);
}
.product_list_widget li:last-child {
  padding-bottom: 0 !important;
}

.wp-block-archives {
  list-style: none;
  margin: 0;
  padding: 0;
  margin-bottom: 20px;
}
.wp-block-archives a:not(:hover) {
  color: inherit;
}

.blog-single ul.wp-block-archives li {
  margin: 5px 0;
}

.widget {
  margin-bottom: 30px;
  position: relative;
  padding: var(--widget-padding-x, 40px);
  background: var(--smoke-color2);
  border-radius: 20px;
}
.widget[data-overlay]:before {
  z-index: -1;
}

.widget_title {
  position: relative;
  font-size: 24px;
  font-weight: 500;
  font-family: var(--title-font);
  line-height: 1em;
  margin: -0.12em 0 25px 0;
}

.widget .search-form {
  position: relative;
  display: flex;
}
.widget .search-form input {
  background: var(--white-color);
  border: 1px solid #EAEBEE;
  flex: 1;
  color: var(--body-color);
  font-size: 16px;
  font-weight: 400;
  border-radius: 10px;
  padding: 0 80px 0 30px;
}
.widget .search-form input::placeholder {
  color: var(--body-color);
}
.widget .search-form button {
  position: absolute;
  right: 0px;
  top: 0px;
  border: none;
  font-size: 16px;
  background-color: var(--theme-color);
  color: var(--white-color);
  display: inline-block;
  height: 60px;
  width: 60px;
  line-height: 60px;
  border-radius: 10px;
}
.widget .search-form button:hover {
  background: var(--title-color);
  color: var(--white-color);
}

.wp-block-tag-cloud,
.tagcloud {
  margin-right: -3px;
  margin-bottom: -10px;
}
.wp-block-tag-cloud a,
.tagcloud a {
  display: inline-block;
  border: 1px solid #EDEDED;
  font-size: 12px !important;
  font-weight: 500;
  font-family: var(--title-font);
  text-transform: uppercase;
  height: 35px;
  line-height: 34px;
  padding: 0px 15px;
  margin-right: 5px;
  margin-bottom: 10px;
  color: var(--body-color);
  background-color: var(--white-color);
  box-shadow: 0px 16px 51px rgba(0, 0, 0, 0.07);
  border-radius: 5px;
}
.wp-block-tag-cloud a:hover,
.tagcloud a:hover {
  background-color: var(--theme-color);
  color: var(--white-color) !important;
  border-color: var(--theme-color);
}

.widget_gallery .insta-feed {
  margin-bottom: -10px;
  margin-left: -4px;
  margin-right: -4px;
}
.widget_gallery a {
  width: 95px;
  height: 95px;
  position: relative;
  border-radius: 10px;
  display: inline-block;
  flex: none;
  margin: 0 4px 10px;
}
.widget_gallery a img {
  border-radius: 10px;
}
.widget_gallery a:after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: var(--theme-color);
  border-radius: 10px;
  transition: 0.4s;
  opacity: 0;
}
.widget_gallery a i {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
  opacity: 0;
  transition: 0.4s;
  color: var(--white-color);
  font-size: 20px;
}
.widget_gallery a:hover i {
  opacity: 1;
}
.widget_gallery a:hover:after {
  opacity: 0.7;
}

.base {
  border-radius: 6px;
  position: absolute;
  left: 1302px;
  top: 2098px;
  width: 80px;
  height: 78px;
  z-index: 209;
}

.recent-post {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  background: transparent;
  border-radius: 6px;
  transition: 0.4s;
}
.recent-post:last-child {
  margin-bottom: 0px;
  border: 0;
}
.recent-post .media-img {
  margin-right: 20px;
  width: 80px;
  overflow: hidden;
  border-radius: 10px;
}
.recent-post .media-img img {
  width: 100%;
  border-radius: 10px;
  transition: 0.4s ease-in-out;
}
.recent-post .post-title {
  font-weight: 500;
  font-size: 18px;
  margin: 0 0 5px;
  font-family: var(--title-font);
  text-transform: capitalize;
}
.recent-post .recent-post-meta {
  margin-bottom: -2px;
}
.recent-post .recent-post-meta a {
  font-size: 14px;
  font-weight: 400;
  text-transform: uppercase;
  font-family: var(--title-font);
  color: var(--body-color);
  margin-bottom: 0px;
  display: block;
}
.recent-post .recent-post-meta a i {
  margin-right: 6px;
  color: var(--theme-color);
  transition: 0.4s;
}
.recent-post .recent-post-meta a:hover {
  color: var(--theme-color);
}
.recent-post .recent-post-meta a:hover i {
  color: var(--theme-color);
}
.recent-post:hover .media-img img {
  transform: scale(1.1);
}

.sidebar-area select,
.sidebar-area input {
  background-color: var(--white-color);
  border: 1px solid var(--border-color);
}

.widget_shopping_cart .widget_title {
  margin-bottom: 30px;
  border-bottom: none;
}
.widget_shopping_cart ul {
  margin: 0;
  padding: 0;
}
.widget_shopping_cart ul li {
  list-style-type: none;
}
.widget_shopping_cart .mini_cart_item {
  position: relative;
  padding: 30px 30px 30px 90px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  margin-bottom: 0;
  text-align: left;
}
.widget_shopping_cart .mini_cart_item:first-child {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}
.widget_shopping_cart .cart_list a:not(.remove) {
  display: block;
  color: var(--body-color);
  font-size: 16px;
  font-weight: 500;
  font-family: var(--title-font);
  font-weight: 600;
  color: var(--title-color);
}
.widget_shopping_cart .cart_list a:not(.remove):hover {
  color: var(--theme-color);
}
.widget_shopping_cart .cart_list a.remove {
  position: absolute;
  top: 50%;
  left: 95%;
  transform: translateY(-50%);
  color: var(--body-color);
}
.widget_shopping_cart .cart_list a.remove:hover {
  color: var(--theme-color);
}
.widget_shopping_cart .cart_list img {
  width: 75px;
  height: 75px;
  position: absolute;
  left: 0;
  top: 18px;
}
.widget_shopping_cart .quantity {
  display: inline-flex;
  white-space: nowrap;
  vertical-align: top;
  margin-right: 20px;
  font-size: 14px;
  font-weight: 500;
}
.widget_shopping_cart .total {
  margin-top: 20px;
  font-size: 18px;
  color: var(--title-color);
  font-family: var(--body-font);
}
.widget_shopping_cart .total strong {
  font-family: var(--title-font);
}
.widget_shopping_cart .amount {
  padding-left: 5px;
}

.widget_schedule ul {
  padding: 0;
  margin: 0;
  margin-bottom: -10px;
}
.widget_schedule ul li {
  list-style: none;
  background: #FFFFFF;
  border: 1px solid rgba(237, 237, 237, 0.89);
  box-shadow: 0px 16px 51px rgba(0, 0, 0, 0.07);
  border-radius: 15px;
  margin-bottom: 15px;
  height: 55px;
  line-height: 55px;
  padding: 0 20px;
}
.widget_schedule ul li i {
  margin-right: 6px;
}
.widget_schedule ul li.unavailable {
  color: var(--theme-color);
}

.wp-block-calendar tbody td,
.wp-block-calendar th {
  padding: 10px;
}

.wp-block-calendar,
.calendar_wrap {
  position: relative;
  background-color: #fff;
  padding-bottom: 0;
  border: none;
}
.wp-block-calendar span[class*=wp-calendar-nav],
.calendar_wrap span[class*=wp-calendar-nav] {
  position: absolute;
  top: 9px;
  left: 20px;
  font-size: 14px;
  color: var(--white-color);
  font-weight: 400;
  z-index: 1;
  line-height: 1.7;
}
.wp-block-calendar span[class*=wp-calendar-nav] a,
.calendar_wrap span[class*=wp-calendar-nav] a {
  color: inherit;
}
.wp-block-calendar span.wp-calendar-nav-next,
.calendar_wrap span.wp-calendar-nav-next {
  left: auto;
  right: 20px;
}
.wp-block-calendar caption,
.calendar_wrap caption {
  caption-side: top;
  text-align: center;
  color: var(--white-color);
  background-color: var(--theme-color);
}
.wp-block-calendar th,
.calendar_wrap th {
  font-size: 14px;
  padding: 5px 5px;
  border: none;
  text-align: center;
  border-right: 1px solid #eee;
  color: var(--title-color);
  font-weight: 500;
}
.wp-block-calendar th:first-child,
.calendar_wrap th:first-child {
  border-left: 1px solid #eee;
}
.wp-block-calendar th:last-child,
.calendar_wrap th:last-child {
  border-right: 1px solid #eee;
}
.wp-block-calendar table th,
.calendar_wrap table th {
  font-weight: 500;
}
.wp-block-calendar td,
.calendar_wrap td {
  font-size: 14px;
  padding: 5px 5px;
  color: #01133c;
  border: 1px solid #eee;
  text-align: center;
  background-color: transparent;
  transition: all ease 0.4s;
}
.wp-block-calendar #today,
.calendar_wrap #today {
  color: var(--theme-color);
  background-color: var(--white-color);
  border-color: #ededed;
}
.wp-block-calendar thead,
.calendar_wrap thead {
  background-color: #fff;
}
.wp-block-calendar .wp-calendar-table,
.calendar_wrap .wp-calendar-table {
  margin-bottom: 0;
}
.wp-block-calendar .wp-calendar-nav .pad,
.calendar_wrap .wp-calendar-nav .pad {
  display: none;
}
.wp-block-calendar a,
.calendar_wrap a {
  color: inherit;
  text-decoration: none;
}
.wp-block-calendar a:hover,
.calendar_wrap a:hover {
  color: var(--title-color);
}

/***wp-calender***/
.wp-block-calendar {
  margin-bottom: 30px;
  border: none;
  padding-bottom: 0;
}
.wp-block-calendar table caption {
  color: var(--white-color);
}

/********widget_recent_comments********/
.widget_recent_comments ul,
.wp-block-latest-comments ul {
  list-style-type: none;
  padding-left: 0;
}

ul.widget_recent_comments,
ol.widget_recent_comments,
.wp-block-latest-comments {
  margin-top: -0.11em;
  padding-left: 0;
}

.widget_recent_comments ol,
.widget_recent_comments ul,
.wp-block-latest-comments ol,
.wp-block-latest-comments ul {
  margin-bottom: 0;
}
.widget_recent_comments li,
.wp-block-latest-comments li {
  margin-bottom: 0;
  color: var(--body-color);
  padding-left: 30px;
  position: relative;
}
.widget_recent_comments li:before,
.wp-block-latest-comments li:before {
  content: "\f086";
  position: absolute;
  left: 0;
  top: -1px;
  color: var(--theme-color);
  font-family: var(--icon-font);
}
.widget_recent_comments.has-avatars li,
.wp-block-latest-comments.has-avatars li {
  padding-left: 0;
  padding-bottom: 0 !important;
}
.widget_recent_comments.has-avatars li:before,
.wp-block-latest-comments.has-avatars li:before {
  display: none;
}
.widget_recent_comments .avatar,
.wp-block-latest-comments .avatar {
  margin-top: 0.4em;
}
.widget_recent_comments li:not(:last-child),
.wp-block-latest-comments li:not(:last-child) {
  padding-bottom: 12px;
}
.widget_recent_comments article,
.wp-block-latest-comments article {
  line-height: 1.5;
}
.widget_recent_comments a,
.wp-block-latest-comments a {
  color: inherit;
}
.widget_recent_comments a:hover,
.wp-block-latest-comments a:hover {
  color: var(--theme-color);
}

/******widget_recent_entries******/
.widget_recent_entries ul {
  margin: -0.3em 0 0 0;
  padding: 0;
  list-style: none;
}
.widget_recent_entries ul li > a {
  color: var(--body-color);
  font-weight: 500;
  display: inline-block;
}
.widget_recent_entries ul li > a:hover {
  color: var(--theme-color);
}
.widget_recent_entries ul li span.post-date {
  font-size: 14px;
}
.widget_recent_entries ul li:not(:last-child) {
  border-bottom: 1px solid #E6E6E6;
  padding-bottom: 12px;
  margin-bottom: 12px;
}

/*******widget_rss*******/
ul.widget_rss, ul.wp-block-rss,
ol.widget_rss,
ol.wp-block-rss {
  padding-left: 0;
}

.widget_rss,
.wp-block-rss {
  list-style-type: none;
}
.widget_rss ul,
.wp-block-rss ul {
  margin: -0.2em 0 -0.5em 0;
  padding: 0;
  list-style: none;
}
.widget_rss ul .rsswidget,
.wp-block-rss ul .rsswidget {
  color: var(--title-color);
  font-family: var(--theme-font);
  font-size: 18px;
  display: block;
  margin-bottom: 10px;
}
.widget_rss ul .rssSummary,
.wp-block-rss ul .rssSummary {
  font-size: 14px;
  margin-bottom: 7px;
  line-height: 1.5;
}
.widget_rss ul a,
.wp-block-rss ul a {
  display: block;
  font-weight: 600;
  color: inherit;
}
.widget_rss ul a:hover,
.wp-block-rss ul a:hover {
  color: var(--theme-color);
}
.widget_rss ul .rss-date,
.wp-block-rss ul .rss-date {
  font-size: 14px;
  display: inline-block;
  margin-bottom: 5px;
  font-weight: 400;
  color: var(--title-color);
}
.widget_rss ul .rss-date:before,
.wp-block-rss ul .rss-date:before {
  content: "\f073";
  font-family: var(--icon-font);
  margin-right: 10px;
  font-weight: 300;
  color: var(--theme-color);
}
.widget_rss ul cite,
.wp-block-rss ul cite {
  font-weight: 500;
  color: var(--title-color);
  font-family: var(--body-font);
  font-size: 14px;
}
.widget_rss ul cite:before,
.wp-block-rss ul cite:before {
  content: "";
  position: relative;
  top: -1px;
  left: 0;
  width: 20px;
  height: 2px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 8px;
  background-color: var(--theme-color);
}
.widget_rss li:not(:last-child),
.wp-block-rss li:not(:last-child) {
  margin-bottom: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 16px;
}
.widget_rss a:hover,
.wp-block-rss a:hover {
  color: var(--theme-color);
}

.wp-block-latest-comments__comment:first-child {
  margin-top: 0;
}
.wp-block-latest-comments__comment:last-child {
  margin-bottom: 0;
}
.wp-block-latest-comments__comment:last-child .wp-block-latest-comments__comment-excerpt p {
  margin-bottom: -0.3em;
}

ul.wp-block-latest-posts li:first-child {
  margin-top: 0;
}
ul.wp-block-latest-posts li:last-child {
  margin-bottom: -0.3em;
}

/* Large devices */
@media (max-width: 1199px) {
  .widget {
    --widget-padding-y: 30px;
    --widget-padding-x: 30px;
  }
  .widget_title {
    font-size: 22px;
    margin: -0.12em 0 24px 0;
  }
  .author-widget-wrap .name {
    font-size: 22px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .sidebar-area {
    padding-top: 40px;
  }
  .widget {
    --widget-padding-y: 40px;
    --widget-padding-x: 40px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .widget_info {
    padding: 0;
  }
  .author-widget-wrap {
    padding: 0;
  }
  .widget_info .widget_title {
    padding: 24px 30px 19px 28px;
  }
  .info-list {
    padding: 0 30px 30px;
  }
  .widget {
    padding: 30px;
  }
  .widget_banner {
    padding: 0;
  }
  .widget_estimate .estimate-wrap span {
    margin-bottom: 16px;
  }
  .widget_estimate .content p {
    margin-bottom: 25px;
  }
  .widget_estimate .content h5 {
    margin-bottom: 23px;
  }
  .widget_estimate .content .checklist {
    margin-bottom: 30px;
  }
}
/* Extra small devices */
@media (max-width: 375px) {
  .widget_schedule ul li {
    font-size: 14px;
  }
}
@media (max-width: 330px) {
  .recent-post .post-title {
    font-size: 16px;
    line-height: 24px;
  }
  .recent-post .recent-post-meta a {
    font-size: 12px;
  }
  .recent-post .media-img {
    width: 67px;
  }
  .recent-post .media-img {
    margin-right: 15px;
  }
  .widget_schedule ul li {
    font-size: 12px;
    padding: 0 15px;
  }
}
.footer-widget {
  margin-bottom: 50px !important;
}
.footer-widget,
.footer-widget .widget {
  padding: 0;
  border: none;
  padding-bottom: 0;
  background-color: transparent;
  box-shadow: none;
}
.footer-widget .wp-block-search__label,
.footer-widget .widget_title {
  max-width: 270px;
  color: var(--white-color);
  border-bottom: none;
  margin: -0.04em 0 50px 0;
  font-size: 22px;
  font-weight: 500;
  position: relative;
}
.footer-widget .wp-block-search__label:after,
.footer-widget .widget_title:after {
  content: "";
  position: absolute;
  right: 0;
  bottom: -15px;
  height: 3px;
  width: 41px;
  background: #D9D9D9;
}
.footer-widget.widget_meta ul, .footer-widget.widget_pages ul, .footer-widget.widget_archive ul, .footer-widget.widget_recent_entries ul, .footer-widget.widget_categories ul, .footer-widget.widget_nav_menu ul {
  margin-top: -5px;
}
.footer-widget.widget_meta .menu,
.footer-widget.widget_meta > ul, .footer-widget.widget_pages .menu,
.footer-widget.widget_pages > ul, .footer-widget.widget_archive .menu,
.footer-widget.widget_archive > ul, .footer-widget.widget_recent_entries .menu,
.footer-widget.widget_recent_entries > ul, .footer-widget.widget_categories .menu,
.footer-widget.widget_categories > ul, .footer-widget.widget_nav_menu .menu,
.footer-widget.widget_nav_menu > ul {
  margin-bottom: -5px;
}
.footer-widget.widget_meta a, .footer-widget.widget_pages a, .footer-widget.widget_archive a, .footer-widget.widget_recent_entries a, .footer-widget.widget_categories a, .footer-widget.widget_nav_menu a {
  font-size: 16px;
  font-weight: 400;
  padding: 0 0 0 23px;
  margin-bottom: 16px;
  font-family: var(--body-font);
  color: var(--body-color);
  display: block;
  max-width: 100%;
  width: max-content;
  background-color: transparent;
  border: none;
  position: relative;
}
.footer-widget.widget_meta a:before, .footer-widget.widget_pages a:before, .footer-widget.widget_archive a:before, .footer-widget.widget_recent_entries a:before, .footer-widget.widget_categories a:before, .footer-widget.widget_nav_menu a:before {
  content: "\f101";
  font-weight: 500;
  right: 0;
  top: 2px;
  transform: translateY(0);
  font-size: 0.9em;
  background-color: transparent;
  border: none;
  color: inherit;
  transition: 0.2s;
  font-family: var(--icon-font);
  position: absolute;
  opacity: 1;
}
.footer-widget.widget_meta a:hover, .footer-widget.widget_pages a:hover, .footer-widget.widget_archive a:hover, .footer-widget.widget_recent_entries a:hover, .footer-widget.widget_categories a:hover, .footer-widget.widget_nav_menu a:hover {
  background-color: transparent;
  color: var(--theme-color);
}
.footer-widget.widget_meta a:hover:before, .footer-widget.widget_pages a:hover:before, .footer-widget.widget_archive a:hover:before, .footer-widget.widget_recent_entries a:hover:before, .footer-widget.widget_categories a:hover:before, .footer-widget.widget_nav_menu a:hover:before {
  color: var(--theme-color);
}
.footer-widget.widget_meta li, .footer-widget.widget_pages li, .footer-widget.widget_archive li, .footer-widget.widget_recent_entries li, .footer-widget.widget_categories li, .footer-widget.widget_nav_menu li {
  border: 0 !important;
  padding-bottom: 0 !important;
}
.footer-widget.widget_meta li:last-child a, .footer-widget.widget_pages li:last-child a, .footer-widget.widget_archive li:last-child a, .footer-widget.widget_recent_entries li:last-child a, .footer-widget.widget_categories li:last-child a, .footer-widget.widget_nav_menu li:last-child a {
  margin-bottom: 0;
}
.footer-widget.widget_meta .wp-block-navigation__submenu-container,
.footer-widget.widget_meta .sub-menu,
.footer-widget.widget_meta .children, .footer-widget.widget_pages .wp-block-navigation__submenu-container,
.footer-widget.widget_pages .sub-menu,
.footer-widget.widget_pages .children, .footer-widget.widget_archive .wp-block-navigation__submenu-container,
.footer-widget.widget_archive .sub-menu,
.footer-widget.widget_archive .children, .footer-widget.widget_recent_entries .wp-block-navigation__submenu-container,
.footer-widget.widget_recent_entries .sub-menu,
.footer-widget.widget_recent_entries .children, .footer-widget.widget_categories .wp-block-navigation__submenu-container,
.footer-widget.widget_categories .sub-menu,
.footer-widget.widget_categories .children, .footer-widget.widget_nav_menu .wp-block-navigation__submenu-container,
.footer-widget.widget_nav_menu .sub-menu,
.footer-widget.widget_nav_menu .children {
  margin-left: 10px;
}
.footer-widget.widget_meta .wp-block-navigation__submenu-container li a,
.footer-widget.widget_meta .sub-menu li a,
.footer-widget.widget_meta .children li a, .footer-widget.widget_pages .wp-block-navigation__submenu-container li a,
.footer-widget.widget_pages .sub-menu li a,
.footer-widget.widget_pages .children li a, .footer-widget.widget_archive .wp-block-navigation__submenu-container li a,
.footer-widget.widget_archive .sub-menu li a,
.footer-widget.widget_archive .children li a, .footer-widget.widget_recent_entries .wp-block-navigation__submenu-container li a,
.footer-widget.widget_recent_entries .sub-menu li a,
.footer-widget.widget_recent_entries .children li a, .footer-widget.widget_categories .wp-block-navigation__submenu-container li a,
.footer-widget.widget_categories .sub-menu li a,
.footer-widget.widget_categories .children li a, .footer-widget.widget_nav_menu .wp-block-navigation__submenu-container li a,
.footer-widget.widget_nav_menu .sub-menu li a,
.footer-widget.widget_nav_menu .children li a {
  border-bottom: 0;
  margin: 0 0px 19px 0px;
  padding: 0 0 0 23px;
}
.footer-widget .recent-post {
  max-width: 300px;
  margin-top: -0.3em;
  margin-bottom: 17px;
  padding: 0;
  border: 0;
}
.footer-widget .recent-post .post-title {
  color: var(--white-color);
  font-weight: 600;
  margin: 5px 0 0 0;
}
.footer-widget .recent-post:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: 0;
}
.footer-widget .recent-post .recent-post-meta a {
  font-weight: 400;
  line-height: 1.2;
  color: var(--light-color);
  font-size: 12px;
}
.footer-widget .recent-post .recent-post-meta i {
  color: var(--light-color);
  transition: 0.4s;
}
.footer-widget .recent-post .recent-post-meta a:hover {
  color: var(--theme-color);
}
.footer-widget .recent-post .recent-post-meta a:hover i {
  color: var(--theme-color);
}
.footer-widget.widget_shopping_cart .total, .footer-widget.widget_rss ul .rss-date, .footer-widget.widget_rss ul cite, .footer-widget.widget_rss ul .rsswidget,
.footer-widget .product_list_widget li a, .footer-widget.widget_shopping_cart .cart_list a:not(.remove) {
  color: var(--white-color);
}
.footer-widget blockquote:before, .footer-widget .wp-block-quote:before {
  left: 40px;
  top: 40px;
}
.footer-widget .wp-block-search .wp-block-search__inside-wrapper {
  border: 0;
  border-radius: 0;
}
.footer-widget .wp-block-search .wp-block-search__inside-wrapper .wp-block-search__button {
  margin-left: 10px;
}
.footer-widget .wp-block-search .wp-block-search__inside-wrapper .wp-block-search__button:hover {
  background: var(--white-color);
  color: var(--theme-color);
}
.footer-widget .widget_nav_menu a,
.footer-widget .widget_meta a,
.footer-widget .widget_pages a,
.footer-widget .widget_archive a,
.footer-widget .wp-block-page-list a,
.footer-widget .widget_categories a {
  border-bottom: 1px solid #363636;
}
.footer-widget .widget_nav_menu .wp-block-navigation__submenu-container li a, .footer-widget .widget_nav_menu .sub-menu li a, .footer-widget .widget_nav_menu .children li a, .footer-widget .widget_meta .wp-block-navigation__submenu-container li a, .footer-widget .widget_meta .sub-menu li a, .footer-widget .widget_meta .children li a, .footer-widget .widget_pages .wp-block-navigation__submenu-container li a, .footer-widget .widget_pages .sub-menu li a, .footer-widget .widget_pages .children li a, .footer-widget .widget_archive .wp-block-navigation__submenu-container li a, .footer-widget .widget_archive .sub-menu li a, .footer-widget .widget_archive .children li a, .footer-widget .wp-block-page-list .wp-block-navigation__submenu-container li a, .footer-widget .wp-block-page-list .sub-menu li a, .footer-widget .wp-block-page-list .children li a, .footer-widget .widget_categories .wp-block-navigation__submenu-container li a, .footer-widget .widget_categories .sub-menu li a, .footer-widget .widget_categories .children li a {
  border-bottom: 1px solid #363636;
}
.footer-widget.woocommerce .widget_shopping_cart .total, .footer-widget.woocommerce.widget_shopping_cart .total {
  border-top: 3px double #363636;
}

.widget-about .footer-logo {
  margin-bottom: 37px;
}
.widget-about .about-text {
  max-width: 350px;
  margin-top: -0.65em;
  margin-bottom: 27px;
}

.footer-text {
  margin-top: -0.45em;
  margin-bottom: 16px;
  color: var(--body-color);
}

.sidebar-gallery {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}
.sidebar-gallery .gallery-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  color: var(--white-color);
  visibility: hidden;
  opacity: 0;
  transform: translate(-50%, 20px);
}
.sidebar-gallery .gallery-btn:hover {
  color: var(--theme-color);
}
.sidebar-gallery .gallery-thumb {
  overflow: hidden;
  position: relative;
  border-radius: 10px;
}
.sidebar-gallery .gallery-thumb:before {
  content: "";
  height: calc(100% - 14px);
  width: calc(100% - 14px);
  background-color: var(--title-color);
  opacity: 0.8;
  position: absolute;
  top: 7px;
  left: 7px;
  border-radius: 10px;
  transform: scaleX(0);
  transition: 0.4s ease-in-out;
}
.sidebar-gallery .gallery-thumb img {
  width: 100%;
  border-radius: 10px;
}
.sidebar-gallery .gallery-thumb:hover:before {
  transform: scaleX(1);
}
.sidebar-gallery .gallery-thumb:hover .gallery-btn {
  visibility: visible;
  opacity: 1;
  transform: translate(-50%, -50%);
}

.newsletter-form {
  position: relative;
}
.newsletter-form .form-group {
  margin-bottom: 15px;
}
.newsletter-form .form-group > i {
  right: auto;
  left: 24px;
  top: 19px;
}
.newsletter-form .form-group input {
  height: 55px;
  padding: 0 25px 0 55px;
  background: var(--white-color);
  border-radius: 5px;
}

/* Medium Large devices */
@media (max-width: 1299px) {
  .footer-text,
  .widget-about .about-text {
    font-size: 14px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .footer-widget.widget_meta a, .footer-widget.widget_pages a, .footer-widget.widget_archive a, .footer-widget.widget_categories a, .footer-widget.widget_nav_menu a {
    margin-bottom: 16px;
  }
  .footer-info-list li:not(:last-child) {
    margin-bottom: 15px;
  }
  .sidebar-gallery {
    max-width: 350px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .footer-widget .widget_title {
    margin-bottom: 35px;
  }
  .widget-about .about-text {
    margin-bottom: 20px;
  }
  .social-box.mb-30 {
    margin-bottom: 25px;
  }
  .widget_event-location {
    padding: 30px;
  }
}
/*------------------- 4.2. Header  -------------------*/
.nav-header {
  position: relative;
  z-index: 41;
}

.sticky-wrapper {
  transition: 0.4s ease-in-out;
    direction: rtl;
}
.sticky-wrapper.sticky {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  background-color: var(--white-color);
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.07);
  animation: stickyAni 0.4s ease-in-out;
}

@keyframes stickyAni {
  0% {
    transform: translate3d(0, -40px, 0) scaleY(0.8);
    opacity: 0.7;
  }
  100% {
    transform: translate3d(0, 0, 0) scaleY(1);
    opacity: 1;
  }
}
.header-button {
  height: 100%;
  display: flex;
  align-items: center;
  gap: 15px;
}

.main-menu a {
  display: block;
  position: relative;
  font-weight: 600;
  font-size: 18px;
  color: var(--body-color);
  font-family: var(--title-font);
}
.main-menu a:hover {
  color: var(--title-color);
}
.main-menu > ul > li {
  margin: 0 27px;
}
.main-menu > ul > li > a {
  padding: 36px 0;
}
.main-menu > ul > li > a:hover {
  color: var(--title-color);
}
.main-menu ul {
  margin: 0;
  padding: 0;
}
.main-menu ul li {
  list-style-type: none;
  display: inline-block;
  position: relative;
}
.main-menu ul li.menu-item-has-children > a:after {
  content: "\f078";
  position: relative;
  font-family: var(--icon-font);
  margin-left: 4px;
  font-weight: 700;
  top: 0;
  font-size: 12px;
}
.main-menu ul li:last-child {
  margin-right: 0 !important;
}
.main-menu ul li:first-child {
  margin-left: 0 !important;
}
.main-menu ul li:hover > ul.sub-menu {
  visibility: visible;
  opacity: 1;
  transform: scaleY(1);
  z-index: 9;
}
.main-menu ul.sub-menu {
  position: absolute;
  text-align: left;
  top: 100%;
  left: 0;
  background-color: var(--white-color);
  visibility: hidden;
  min-width: 190px;
  width: max-content;
  padding: 7px;
  left: -14px;
  opacity: 0;
  z-index: -1;
  border: 0;
  box-shadow: 0px 4px 15px rgba(1, 15, 28, 0.06);
  border-radius: 0;
  transform: scaleY(0);
  transform-origin: top center;
  transition: all 0.4s ease 0s;
}
.main-menu ul.sub-menu a {
  font-size: 16px;
  line-height: 30px;
}
.main-menu ul.sub-menu {
  padding: 18px 20px 18px 18px;
  left: -27px;
}
.main-menu ul.sub-menu li {
  display: block;
  margin: 0 0;
  padding: 0px 9px;
}
.main-menu ul.sub-menu li.menu-item-has-children > a:after {
  content: "\f105";
  float: right;
  top: 1px;
}
.main-menu ul.sub-menu li a {
  position: relative;
  padding-left: 23px;
  text-transform: capitalize;
}
.main-menu ul.sub-menu li a:before {
  content: "\f57c";
  position: absolute;
  top: 8px;
  left: 0;
  font-family: var(--icon-font);
  width: 11px;
  height: 11px;
  text-align: center;
  border-radius: 50%;
  display: inline-block;
  font-size: 1em;
  line-height: 1;
  color: var(--title-color);
  font-weight: 500;
}
.main-menu ul.sub-menu li ul.sub-menu {
  left: 100%;
  right: auto;
  top: 0;
  margin: 0 0;
  margin-left: 20px;
}
.main-menu ul.sub-menu li ul.sub-menu li ul {
  left: 100%;
  right: auto;
}

/****header-top*****/
.main-menu a {
  display: block;
  position: relative;
  font-weight: 500;
  font-size: 16px;
  color: var(--title-color);
  text-transform: uppercase;
}
.main-menu a:hover {
  color: var(--theme-color);
}
.main-menu > ul > li {
  margin: 0 14px;
}
.main-menu > ul > li > a {
  padding: 31.5px 0;
}
.main-menu > ul > li > a:hover {
  color: var(--theme-color);
}
.main-menu ul {
  margin: 0;
  padding: 0;
}
.main-menu ul li {
  list-style-type: none;
  display: inline-block;
  position: relative;
}
.main-menu ul li.menu-item-has-children > a:after {
  content: "\f107";
  position: relative;
  font-family: var(--icon-font);
  margin-left: 4px;
  font-weight: 600;
  top: 0;
  font-size: 1em;
  display: inline-block;
  transition: 0.4s;
  transform: rotate(0deg);
}
.main-menu ul li.menu-item-has-children:hover > a:after {
  transform: rotate(180deg);
}
.main-menu ul li:last-child {
  margin-right: 0 !important;
}
.main-menu ul li:first-child {
  margin-left: 0 !important;
}
.main-menu ul li:hover > ul.sub-menu {
  visibility: visible;
  opacity: 1;
  transform: scaleY(1);
  z-index: 9;
}
.main-menu ul.sub-menu {
  position: absolute;
  text-align: left;
  top: 100%;
  left: 0;
  background-color: var(--white-color);
  visibility: hidden;
  min-width: 190px;
  width: max-content;
  padding: 7px;
  left: -14px;
  opacity: 0;
  z-index: -1;
  border: 0;
  box-shadow: 0px 4px 15px rgba(1, 15, 28, 0.06);
  border-radius: 0;
  transform: scaleY(0);
  transform-origin: top center;
  transition: all 0.4s ease 0s;
}
.main-menu ul.sub-menu a {
  font-size: 16px;
  line-height: 30px;
}
.main-menu ul.sub-menu a span {
  font-size: 12px;
  padding: 0px 5px;
  margin-left: 4px;
  background-color: var(--theme-color);
  color: var(--white-color);
  padding: 2px 5px;
  border-radius: 4px;
  position: relative;
  top: -1px;
}
.main-menu ul.sub-menu {
  padding: 18px 20px 18px 18px;
  left: -27px;
}
.main-menu ul.sub-menu li {
  display: block;
  margin: 0 0;
  padding: 0px 9px;
}
.main-menu ul.sub-menu li.menu-item-has-children > a:after {
  content: "+";
  float: right;
  top: 1px;
}
.main-menu ul.sub-menu li.menu-item-has-children:hover > a:after {
  content: "\f068";
}
.main-menu ul.sub-menu li a {
  position: relative;
  padding-left: 0px;
  text-transform: capitalize;
}
.main-menu ul.sub-menu li a:before {
  content: "\f57d";
  position: absolute;
  top: 8px;
  left: 10px;
  font-family: var(--icon-font);
  width: 11px;
  height: 11px;
  text-align: center;
  border-radius: 50%;
  display: inline-block;
  font-size: 15px;
  line-height: 1;
  color: var(--theme-color);
  font-weight: 400;
  opacity: 0;
  transition: 0.4s;
}
.main-menu ul.sub-menu li a:hover {
  padding-left: 25px;
}
.main-menu ul.sub-menu li a:hover:before {
  opacity: 1;
  left: 0;
}
.main-menu ul.sub-menu li ul.sub-menu {
  left: 100%;
  right: auto;
  top: 0;
  margin: 0 0;
  margin-left: 20px;
}
.main-menu ul.sub-menu li ul.sub-menu li ul {
  left: 100%;
  right: auto;
}

.simple-icon {
  border: none;
  background-color: transparent;
  color: var(--title-color);
  padding: 0;
  font-size: 22px;
  position: relative;
}
.simple-icon .badge {
  padding: 0.2em 0.4em;
  font-size: 0.5em;
  top: -5px;
  right: 0px;
}
.simple-icon:has(.badge) {
  padding-right: 8px;
}

.header-button {
  height: 100%;
  display: flex;
  align-items: center;
  gap: 20px;
}
.header-button .th-btn {
  margin-left: 10px;
}

.social-links .social-title {
  font-weight: 500;
  font-size: 16px;
  display: inline-block;
  margin: 0 10px 0 0;
  color: var(--body-color);
}
.social-links a {
  font-size: 14px;
  display: inline-block;
  color: var(--body-color);
  margin: 0 0px 0 20px;
}
.social-links a:last-child {
  margin-right: 0;
}
.social-links a:hover {
  color: var(--theme-color);
}

.header-logo {
  padding-top: 15px;
  padding-bottom: 15px;
}

.header-links > ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
  display: flex;
  align-items: center;
}
.header-links li {
  display: inline-block;
  position: relative;
  font-size: 14px;
  font-weight: 400;
}
.header-links li:not(:last-child) {
  margin: 0 0px 0 40px;
}
.header-links li > i {
  margin-left: 10px;
}
.header-links li,
.header-links span,
.header-links p,
.header-links a {
  color: var(--body-color);
  font-family: var(--title-font);
}
.header-links a:hover {
  color: var(--theme-color);
}
.header-links b,
.header-links strong {
  font-weight: 600;
  margin-right: 6px;
}

.header-info {
  display: flex;
  gap: 12px;
  align-items: center;
  --body-color: #6F6E77;
}
.header-info .icon {
  font-size: 35px;
  color: var(--theme-color);
}
.header-info .header-info-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 0;
  color: var(--white-color);
}
.header-info .header-info-link {
  font-size: 14px;
  font-weight: 500;
  color: var(--border-color);
}

/* Header 1 ---------------------------------- */
.header-layout1 .sticky-wrapper {
  padding: 50px 108px 0;
}
.header-layout1 .sticky-wrapper.sticky {
  top: -50px;
}
.header-layout1 .sticky-wrapper.sticky .header-button,
.header-layout1 .sticky-wrapper.sticky .header-logo {
  margin-top: 0;
}
.header-layout1 .header-top {
  background: transparent;
  border-bottom: 1px solid var(--border-color);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  padding: 11.5px 38px 11.5px 48px;
  margin-left: 345px;
  margin-right: 400px;
}
.header-layout1 .header-top:before, .header-layout1 .header-top:after {
  content: "";
  position: absolute;
  top: 0;
  left: -4px;
  background: var(--border-color);
  height: 100%;
  width: 1px;
  transform: rotate(-10deg);
}
.header-layout1 .header-top:before {
  right: -4px;
  left: auto;
  transform: rotate(10deg);
}
.header-layout1 .header-logo {
  margin-top: -50px;
}
.header-layout1 .main-menu {
  margin-left: 28px;
  padding-left: 50px;
    direction: rtl;
  position: relative;
}
.header-layout1 .main-menu:after {
  /*content: "";*/
  position: absolute;
  left: 0;
  top: -2px;
  height: calc(100% + 3px);
  width: 1px;
  background: var(--border-color);
  transform: rotate(-10deg);
}
.header-layout1 .main-menu > ul > li > a {
  padding: 36.5px 0;
}
.header-layout1 .navbar-right-desc {
  font-size: 18px;
  font-weight: 500;
  font-family: var(--title-font);
  color: var(--theme-color);
  position: relative;
  padding: 37px 35px 37px 0;
}
.header-layout1 .navbar-right-desc i {
  margin-right: 10px;

}
.header-layout1 .navbar-right-desc a {
  color: var(--title-color);
}
.header-layout1 .navbar-right-desc a:hover {
  color: var(--theme-color);
}
.header-layout1 .navbar-right-desc:after {
  /*content: "";*/
  position: absolute;
  top: 0;
  right: -4px;
  background: var(--border-color);
  height: 100%;
  width: 1px;
  transform: rotate(10deg);
}
.header-layout1 .header-button {
  height: 100px;
  padding-left: 34px;
  margin-top: -50px;
}
.header-layout1 .header-links li {
  font-weight: 300;
}
.header-layout1 .header-links li:not(:last-child):after {
  width: 1px;
}

@media (max-width: 1600px) {
  .header-layout1 .sticky-wrapper {
    padding: 50px 50px 0;
  }
  .header-layout1 .header-top {
    margin-left: 288px;
    margin-right: 342px;
      direction: rtl;
  }
}
/* Extra large devices */
@media (max-width: 1500px) {
  .header-layout1 .sticky-wrapper {
    padding: 50px 30px 0;
  }
  .header-layout1 .header-top {
    padding: 11.5px 38px 11.5px 38px;
    margin-left: 258px;
    margin-right: 302px;
  }
  .header-layout1 .header-button {
    padding-left: 14px;
  }
  .header-layout1 .main-menu {
    margin-left: 18px;
    padding-left: 40px;
  }
}
/* Medium Large devices */
@media (max-width: 1399px) {
  .header-layout1 .main-menu {
    margin-left: 18px;
    padding-left: 30px;
  }
  .header-layout1 .navbar-right-desc {
    padding: 37px 25px 37px 0;
  }
  .header-layout1 .header-top {
    padding: 11.5px 28px 11.5px 28px;
  }
}
/* Medium Large devices */
@media (max-width: 1299px) {
  .header-layout1 .sticky-wrapper {
    padding: 50px 20px 0;
  }
  .header-layout1 .main-menu {
    margin-left: 8px;
  }
  .header-layout1 .header-logo img {
    max-width: 150px;
  }
  .header-layout1 .navbar-right-desc {
    font-size: 16px;
  }
  .main-menu a {
    font-size: 14px;
  }
  .main-menu > ul > li {
    margin: 0 10px;
  }
  .header-layout1 .header-links li .social-links span {
    display: none;
  }
  .header-layout1 .header-top {
    margin-left: 205px;
    margin-right: 292px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .header-layout1 .header-top {
    margin-left: 205px;
    margin-right: 117px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .header-layout1 .sticky-wrapper {
    padding: 0px 20px 0;
  }
  .header-layout1 .header-logo {
    margin-top: 0;
  }
  .header-layout1 .header-logo img {
    max-width: none;
  }
  .header-layout1 .sticky-wrapper.sticky {
    top: 0;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .header-layout1 .sticky-wrapper {
    padding: 0;
  }
}
/* Header 2 ---------------------------------- */
.header-layout2 .sticky-wrapper {
  padding: 50px 108px 0;
  position: absolute;
  left: 0;
  right: 0;
}
.header-layout2 .sticky-wrapper.sticky {
  top: -50px;
  position: fixed;
  background: rgba(28, 28, 28, 0.9);
  backdrop-filter: blur(10px);
}
.header-layout2 .sticky-wrapper.sticky .header-button,
.header-layout2 .sticky-wrapper.sticky .header-logo {
  margin-top: 0;
}
.header-layout2 .header-top {
  background: rgba(255, 255, 255, 0.11);
  backdrop-filter: blur(10px);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  padding: 11.5px 108px;
  z-index: 1;
  --body-color: #fff;
}
.header-layout2 .main-menu {
  margin-left: 0px;
  padding-left: 0px;
  position: relative;
}
.header-layout2 .main-menu > ul > li > a {
  padding: 43.5px 0;
  color: var(--white-color);
}
.header-layout2 .navbar-right-desc {
  font-size: 18px;
  font-weight: 500;
  font-family: var(--title-font);
  color: var(--theme-color);
  position: relative;
  padding: 37px 18px 37px 0;
}
.header-layout2 .navbar-right-desc i {
  margin-right: 10px;
  transform: rotate(-45deg);
}
.header-layout2 .navbar-right-desc a {
  color: var(--white-color);
}
.header-layout2 .navbar-right-desc a:hover {
  color: var(--theme-color);
}
.header-layout2 .header-button {
  height: 100px;
}
.header-layout2 .header-links li {
  font-weight: 300;
}
.header-layout2 .header-links li:not(:last-child):after {
  width: 1px;
}

/* Extra large devices */
@media (max-width: 1500px) {
  .header-layout2 .navbar-right-desc {
    padding: 37px 0px 37px 0;
  }
}
/* Medium Large devices */
@media (max-width: 1299px) {
  .header-layout2 .sticky-wrapper {
    padding: 50px 38px 0;
  }
  .header-layout2 .header-top {
    padding: 11.5px 38px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .header-layout2 .sticky-wrapper {
    padding: 10px 28px 10px;
  }
  .header-layout2 .sticky-wrapper.sticky {
    top: 0;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .header-layout2 .sticky-wrapper {
    padding: 0;
  }
}
/* Header 3 ---------------------------------- */
.header-layout3 {
  height: 120px;
}
.header-layout3 .sticky-wrapper2 {
  padding: 0px 108px 0;
  background: var(--smoke-color);
  border-bottom: 1px solid var(--title-color);
  position: absolute;
  right: 0;
  left: 0;
}
.header-layout3 .sticky-wrapper2.sticky {
  position: fixed;
  top: 0;
  animation: stickyAni 0.4s ease-in-out;
}
.header-layout3 .sticky-wrapper2.sticky .header-logo {
  transform: none;
  margin-bottom: 0;
  /* Extra large devices */
}
@media (max-width: 1500px) {
  .header-layout3 .sticky-wrapper2.sticky .header-logo {
    padding: 0 48px;
  }
}
.header-layout3 .sticky-wrapper2.sticky .header-logo:after {
  clip-path: polygon(8% 0%, 92% 0%, 100% 100%, 0% 100%);
}
.header-layout3 .sticky-wrapper2.sticky .header-logo a {
  display: none;
}
.header-layout3 .sticky-wrapper2.sticky .header-logo a.sticky-logo {
  display: block;
}
.header-layout3 .sticky-wrapper2.sticky .main-menu ul .header-logo {
  transform: none;
  margin-bottom: 0;
}
.header-layout3 .sticky-wrapper2.sticky .main-menu ul li {
  transform: none;
  margin-bottom: 0;
}
.header-layout3 .sticky-wrapper2.sticky .main-menu ul .header-logo:after {
  clip-path: polygon(7% 0%, 94% 0%, 100% 100%, 0% 100%);
}
.header-layout3 .sticky-wrapper2 .header-logo {
  padding: 0 58px;
  position: relative;
  z-index: 0;
  transform: translateY(-33px);
  margin-bottom: -33px;
}
.header-layout3 .sticky-wrapper2 .header-logo a {
  padding: 37.5px 0;
  display: block;
}
.header-layout3 .sticky-wrapper2 .header-logo .sticky-logo {
  display: none;
}
.header-layout3 .sticky-wrapper2 .header-logo:after {
  content: "";
  position: absolute;
  left: 0;
  top: 0px;
  height: 100%;
  width: 100%;
  background: var(--white-color);
  clip-path: polygon(10% 0%, 90% 0%, 100% 100%, 0% 100%);
  z-index: -1;
}
.header-layout3 .sticky-wrapper2 .header-logo .border-left,
.header-layout3 .sticky-wrapper2 .header-logo .border-right {
  position: absolute;
  left: 10px;
  top: 0;
  height: calc(100% + 2px);
  width: 1px;
  background: var(--title-color);
  transform: rotate(10deg);
}
.header-layout3 .sticky-wrapper2 .header-logo .border-right {
  right: 10px;
  left: auto;
  transform: rotate(-10deg);
}
.header-layout3 .btn {
  padding: 19.5px 35px 19.5px !important;
}
.header-layout3 .header-button {
  gap: 28px;
}
.header-layout3 .header-button .btn {
  width: 55px;
  height: 55px;
  line-height: 55px;
  padding: 0 !important;
  font-size: 18px;
}
.header-layout3 .search-btn {
  border: 0;
  text-transform: uppercase;
  font-size: 16px;
  font-weight: 500;
  font-family: var(--title-font);
  color: var(--title-color);
}
.header-layout3 .search-btn i {
  margin-right: 8px;
}
.header-layout3 .main-menu > ul > li > a {
  padding: 37.5px 0;
}
.header-layout3 .main-menu ul.sub-menu li {
  transform: none;
  margin-bottom: 0;
}

/* Extra large devices */
@media (max-width: 1500px) {
  .header-layout3 .sticky-wrapper2 {
    padding: 0px 78px 0;
  }
  .header-layout3 .sticky-wrapper2.sticky .main-menu ul .header-logo {
    padding: 0 48px;
  }
}
/* Medium Large devices */
@media (max-width: 1399px) {
  .header-layout3 .search-btn {
    display: none;
  }
}
/* Medium Large devices */
@media (max-width: 1299px) {
  .header-layout3 .sticky-wrapper2 {
    padding: 0px 30px 0;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .header-layout3 {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: auto;
  }
  .header-logo {
    padding-top: 19px;
    padding-bottom: 19px;
  }
  .header-layout3 .sticky-wrapper2 .header-logo {
    transform: none;
    margin: 0;
    padding: 19px 0;
  }
  .header-layout3 .sticky-wrapper2 .header-logo::after {
    display: none;
  }
  .header-layout3 .sticky-wrapper2 .header-logo a {
    padding: 0;
  }
  .header-layout3 .sticky-wrapper2.sticky .header-logo {
    padding: 19px 0;
  }
  .header-layout3 .sticky-wrapper2.sticky .header-logo a {
    display: block;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .header-layout3 .sticky-wrapper2 {
    padding: 0px 20px 0;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .header-layout3 .sticky-wrapper2 {
    padding: 0;
  }
}
/*------------------- 4.3. Footer  -------------------*/
.copyright-wrap {
  padding: 26px 0;
  background: #282828;
}

.widget-area {
  padding: 100px 0 50px;
}

.copyright-text {
  margin: 0;
  color: var(--white-color);
  font-weight: 500;
}
.copyright-text a {
  color: var(--white-color);
}
.copyright-text a:hover {
  color: var(--theme-color);
}

.footer-layout1 {
  position: relative;
    direction: rtl;
  background-color: var(--title-color);
}

/* Medium devices */
@media (max-width: 991px) {
  .widget-area {
    padding: 60px 0 30px;
  }
}
/*------------------- 4.4. Breadcumb  -------------------*/
.breadcumb-menu {
  max-width: 100%;
  padding: 0;
  list-style-type: none;
  position: relative;
  margin: 21px 0 -0.4em;
}
.breadcumb-menu li {
  display: inline-block;
  padding-right: 3px;
  list-style: none;
  position: relative;
}
.breadcumb-menu li:after {
  content: "/";
  position: relative;
  margin-left: 7px;
  font-weight: 400;
  font-size: 16px;
  color: var(--white-color);
}
.breadcumb-menu li:last-child {
  padding-right: 0;
  margin-right: 0;
}
.breadcumb-menu li:last-child:after {
  display: none;
}
.breadcumb-menu li,
.breadcumb-menu a,
.breadcumb-menu span {
  white-space: normal;
  color: inherit;
  word-break: break-word;
  font-weight: 500;
  font-size: 16px;
  font-family: var(--title-font);
  color: var(--white-color);
}
.breadcumb-menu a:hover {
  color: var(--theme-color);
}

.breadcumb-title {
  color: var(--white-color);
  margin: -0.25em 0 -0.2em 0;
  line-height: 1.1;
  font-size: 60px;
  font-weight: 600;
  z-index: 1;
  position: relative;
}

.breadcumb-wrapper {
  background-size: cover;
  padding: 158px 0;
  overflow: hidden;
  text-align: center;
  position: relative;
  z-index: auto;
}
.breadcumb-wrapper:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background: rgba(14, 9, 9, 0.55);
}

/* Medium devices */
@media (max-width: 991px) {
  .breadcumb-wrapper {
    padding: 120px 0;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .breadcumb-title {
    font-size: 40px;
  }
  .breadcumb-menu {
    text-align: center;
    margin: 22px 0 -0.45em 0;
  }
  .breadcumb-menu li, .breadcumb-menu a, .breadcumb-menu span {
    font-size: 14px;
  }
  .breadcumb-wrapper {
    padding: 115px 0 115px;
  }
}
/*------------------- 4.5. Pagination  -------------------*/
.pagination {
  margin-bottom: 30px;
  margin-top: 60px;
}
.pagination ul {
  margin: 0;
  padding: 0;
}
.pagination li {
  display: inline-block;
  margin: 0 3px;
  list-style-type: none;
}
.pagination li:last-child {
  margin-right: 0;
}
.pagination li:first-child {
  margin-left: 0;
}
.pagination span,
.pagination a {
  display: inline-block;
  text-align: center;
  position: relative;
  color: var(--title-color);
  background-color: var(--smoke-color3);
  width: 50px;
  height: 50px;
  line-height: 50px;
  z-index: 1;
  font-size: 16px;
  font-weight: 700;
  border-radius: 50%;
}
.pagination span i,
.pagination a i {
  font-size: 14px;
}
.pagination span.active, .pagination span:hover,
.pagination a.active,
.pagination a:hover {
  color: var(--white-color);
  background-color: var(--theme-color);
  border-color: var(--theme-color);
  box-shadow: none;
}

.fitmas-post-navication-thum {
  margin-top: 30px;
}
.fitmas-post-navication-thum .nav-links .nav-previous a,
.fitmas-post-navication-thum .nav-links .nav-next a {
  padding: 15px 30px;
  background-color: var(--white-color);
  color: var(--title-color);
  display: inline-block;
  border: 1px solid #EDEDED;
  box-shadow: 0px 16px 51px rgba(0, 0, 0, 0.07);
  border-radius: 5px;
}
.fitmas-post-navication-thum .nav-links .nav-previous a:hover,
.fitmas-post-navication-thum .nav-links .nav-next a:hover {
  background: var(--theme-color);
  color: var(--white-color);
}
.fitmas-post-navication-thum .nav-links {
  display: flex;
  justify-content: space-between;
}
.fitmas-post-navication-thum .nav-links .nav-thumb a {
  padding: 0;
  box-shadow: none;
  border: 0;
}

/* Small devices */
@media (max-width: 767px) {
  .pagination {
    margin-top: 40px;
  }
  .pagination span,
  .pagination a {
    width: 40px;
    height: 40px;
    line-height: 40px;
    font-size: 14px;
  }
}
@media (max-width: 330px) {
  .pagination li {
    margin: 0 2px;
  }
  .pagination span, .pagination a {
    width: 35px;
    height: 35px;
    line-height: 34px;
    font-size: 14px;
  }
}
.post-footer {
  clear: both;
  margin-top: 30px;
}

/*------------------- 4.6. Blog  -------------------*/
blockquote,
.wp-block-quote {
  font-size: 18px;
  line-height: 1.56;
  padding: 170px 60px 60px 60px;
  font-weight: 500;
  display: block;
  position: relative;
  background-color: var(--smoke-color2);
  overflow: hidden;
  margin: 35px 0;
  color: var(--title-color);
  font-style: italic;
  border-radius: 15px;
  border: 0;
}
blockquote p,
.wp-block-quote p {
  font-size: 24px;
  font-weight: 600;
  font-style: normal;
  margin-top: -0.3em;
  margin-bottom: 20px;
  line-height: 34px;
  color: var(--title-color);
  width: 100%;
  position: relative;
  z-index: 3;
}
blockquote p a,
.wp-block-quote p a {
  color: inherit;
}
blockquote p cite,
.wp-block-quote p cite {
  margin-top: 20px;
}
blockquote:before,
.wp-block-quote:before {
  content: "\f10e";
  font-family: var(--icon-font);
  position: absolute;
  left: 60px;
  top: 60px;
  font-size: 35px;
  font-weight: 300;
  opacity: 1;
  color: var(--white-color);
  font-style: normal;
  background: var(--theme-color);
  height: 80px;
  width: 80px;
  line-height: 80px;
  border-radius: 50%;
  text-align: center;
}
blockquote cite,
.wp-block-quote cite {
  display: block;
  font-size: 18px;
  position: relative;
  border-color: inherit;
  line-height: 1;
  font-weight: 500;
  margin-top: 0;
  margin-bottom: -0.1em;
  font-style: normal;
  color: var(--theme-color);
  font-family: var(--title-font);
}
blockquote cite span,
.wp-block-quote cite span {
  font-size: 12px;
  font-weight: 400;
  display: block;
  margin-bottom: 8px;
}
blockquote .desig,
.wp-block-quote .desig {
  font-size: 12px;
  font-weight: 500;
  font-family: var(--title-font);
  font-style: initial;
  text-transform: uppercase;
  color: var(--body-color);
  margin-top: 8px;
  margin-bottom: -0.4em;
  display: block;
}
blockquote .quote-icon,
.wp-block-quote .quote-icon {
  position: absolute;
  right: 50px;
  bottom: 50px;
  width: 150px;
}
blockquote.style-left-icon, blockquote.is-large, blockquote.is-style-large, blockquote.has-text-align-right,
.wp-block-quote.style-left-icon,
.wp-block-quote.is-large,
.wp-block-quote.is-style-large,
.wp-block-quote.has-text-align-right {
  padding: 170px 50px 50px;
}
blockquote.style-left-icon,
.wp-block-quote.style-left-icon {
  font-size: 18px;
  color: var(--body-color);
  font-weight: 400;
  line-height: 1.556;
  background-color: var(--smoke-color);
  padding-left: 160px;
}
blockquote.style-left-icon:before,
.wp-block-quote.style-left-icon:before {
  right: unset;
  left: 56px;
  top: 60px;
  font-size: 6rem;
  font-weight: 400;
  line-height: 4rem;
  color: var(--theme-color);
  text-shadow: none;
}
blockquote.style-left-icon cite,
.wp-block-quote.style-left-icon cite {
  color: var(--title-color);
}
blockquote.style-left-icon cite:before,
.wp-block-quote.style-left-icon cite:before {
  background-color: var(--title-color);
  top: 8px;
}
blockquote.is-large cite:before, blockquote.is-style-large cite:before,
.wp-block-quote.is-large cite:before,
.wp-block-quote.is-style-large cite:before {
  top: unset;
  bottom: 13px;
}
blockquote.has-text-align-right,
.wp-block-quote.has-text-align-right {
  border: 0;
}
blockquote.has-text-align-right:before,
.wp-block-quote.has-text-align-right:before {
  right: 50px;
  left: unset;
}

.wp-block-pullquote {
  padding: 0;
}

.wp-block-pullquote.is-style-solid-color blockquote p {
  margin-bottom: 20px;
}

.wp-block-pullquote blockquote:before,
.wp-block-pullquote.is-style-solid-color blockquote:before {
  left: 50%;
  transform: translate(-50%, 0px);
}

.wp-block-column blockquote,
.wp-block-column .wp-block-quote {
  padding: 100px 15px 30px 15px;
}
.wp-block-column blockquote:before,
.wp-block-column .wp-block-quote:before {
  width: 50px;
  height: 50px;
  line-height: 50px;
  top: 30px;
  left: 30px;
  font-size: 30px;
}
.wp-block-column blockquote.style-left-icon, .wp-block-column blockquote.is-large:not(.is-style-plain), .wp-block-column blockquote.is-style-large:not(.is-style-plain), .wp-block-column blockquote.has-text-align-right,
.wp-block-column .wp-block-quote.style-left-icon,
.wp-block-column .wp-block-quote.is-large:not(.is-style-plain),
.wp-block-column .wp-block-quote.is-style-large:not(.is-style-plain),
.wp-block-column .wp-block-quote.has-text-align-right {
  padding: 100px 15px 30px 15px;
}

.blog-meta {
  display: block;
}
.blog-meta span,
.blog-meta a {
  display: inline-block;
  font-size: 16px;
  font-weight: 400;
  color: var(--title-color);
  position: relative;
  margin-right: 31px;
}
.blog-meta span:after,
.blog-meta a:after {
  content: "";
  height: 6px;
  width: 6px;
  background-color: var(--body-color);
  position: absolute;
  top: 50%;
  right: 0;
  margin-top: -3px;
}
.blog-meta span i,
.blog-meta a i {
  margin-left: 8px;
  color: var(--theme-color);
}
.blog-meta span:last-child,
.blog-meta a:last-child {
  margin-right: 0 !important;
  padding-right: 0;
}
.blog-meta span:last-child:after,
.blog-meta a:last-child:after {
  display: none;
}
.blog-meta span a {
  margin-right: 0 !important;
}
.blog-meta a:hover {
  color: var(--theme-color);
}

.blog-audio img,
.blog-img img,
.blog-video img {
  transition: 0.4s ease-in-out;
  border-radius: 30px 30px 0 0;
}

.blog-title a {
  color: inherit;
}
.blog-title a:hover {
  color: var(--theme-color);
}

.blog-inner-title {
  margin-top: -0.25em;
  margin-bottom: 25px;
}
.blog-inner-title i {
  color: var(--theme-color);
  margin-right: 4px;
}

.blog-single {
  position: relative;
  margin-bottom: var(--blog-space-y, 40px);
}
.blog-single .blog-thumb {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  margin-bottom: 30px;
}
.blog-single .blog-thumb img {
  width: 100%;
  border-radius: 20px;
}
.blog-single .thumb {
  border-radius: 15px;
  overflow: hidden;
  margin-bottom: 30px;
}
.blog-single .thumb img {
  width: 100%;
  border-radius: 20px;
}
.blog-single .blog-title {
  margin-bottom: 15px;
  font-size: 36px;
  line-height: 1.4;
  font-weight: 500;
}
.blog-single .blog-text {
  margin-bottom: 24px;
}
.blog-single .social-links {
  margin: 0;
  padding: 0;
  list-style-type: none;
  display: inline-block;
}
.blog-single .social-links li {
  display: inline-block;
  margin-right: 3px;
}
.blog-single .social-links li:last-child {
  margin-right: 0;
}
.blog-single .social-links a {
  display: inline-block;
  width: 40px;
  height: 40px;
  line-height: 40px;
  background-color: var(--smoke-color);
  font-size: 14px;
  color: var(--body-color);
  text-align: center;
}
.blog-single .social-links a:hover {
  color: var(--white-color);
  background-color: var(--theme-color);
}
.blog-single .blog-meta {
  margin: -0.35em 0 4px 0;
    direction: rtl;
}
.blog-single .blog-meta a, .blog-single .blog-meta span {
  font-weight: 400;
  font-size: 14px;
  margin-right: 15px;
  color: var(--title-color);
  font-family: var(--title-font);
}
.blog-single .blog-meta a:hover i, .blog-single .blog-meta span:hover i {
  color: var(--theme-color);
}
.blog-single .blog-meta a i, .blog-single .blog-meta span i {
  font-size: 14px;
  color: var(--title-color);
  transition: 0.4s;
}
.blog-single .blog-meta a:after, .blog-single .blog-meta span:after {
  display: none;
}
.blog-single .blog-meta a ~ a, .blog-single .blog-meta span ~ a {
  margin-left: 10px;
}
.blog-single .blog-content {
  margin: 0 0 0 0;
  position: relative;
  padding-top: 0px;
    text-align: justify;
    direction: rtl;
}
.blog-single .blog-audio {
  line-height: 1;
}
.blog-single .blog-audio,
.blog-single .blog-img,
.blog-single .blog-video {
  position: relative;
  overflow: hidden;
  background-color: var(--smoke-color);
}
.blog-single .blog-img .slick-arrow {
  --pos-x: 30px;
  --icon-size: 45px;
  border: none;
  background-color: var(--white-color);
  color: var(--theme-color);
  border-radius: 5px;
}
.blog-single .blog-img .slick-arrow:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
}
.blog-single .blog-img .play-btn {
  --icon-size: 60px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin: calc(var(--icon-size) / -2) 0 0 calc(var(--icon-size) / -2);
}
.blog-single .blog-img .play-btn i {
  --icon-size: 100px;
  background: rgba(255, 255, 255, 0.102);
  font-size: 24px;
  width: var(--icon-size, 120px);
  height: var(--icon-size, 120px);
  line-height: var(--icon-size, 120px);
}
.blog-single .blog-img .play-btn i:after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  border: solid 2px #fff;
  border-radius: 50%;
  animation: rotate 5s infinite linear;
}
.blog-single .blog-img .play-btn:before, .blog-single .blog-img .play-btn:after {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.18);
}
.blog-single .blog-img:after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  border-radius: 30px 30px 0 0;
  background-color: rgba(16, 55, 65, 0.149);
  width: 100%;
  height: 100%;
}
.blog-single .read-more-btn,
.blog-single .line-btn {
  display: block;
  max-width: fit-content;
  margin-bottom: -1px;
}
.blog-single .read-more-btn {
  margin-bottom: -8px;
  color: var(--theme-color);
}
.blog-single .blog-post-wrap {
  background: var(--smoke-color3);
  border-radius: 6px;
  padding: 60px;
  cursor: pointer;
  transition: 0.4s;
}
.blog-single .blog-post-wrap:hover {
  background: var(--theme-color);
}
.blog-single .blog-post-wrap:hover h5 {
  color: var(--white-color);
}
.blog-single .blog-post-wrap h5 {
  margin-bottom: 0;
  transition: 0.4s;
}
.blog-single:hover .blog-img .slick-arrow {
  opacity: 1;
  visibility: visible;
}

.blog-author {
  border-radius: 15px;
  padding: 30px;
  display: flex;
  align-items: center;
  gap: 30px;
  margin-top: var(--blog-space-y, 80px);
}
.blog-author .auhtor-img {
  border-radius: 15px;
  overflow: hidden;
}
.blog-author .author-name {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: -0.1em;
}
.blog-author .author-name a {
  color: var(--title-color);
}
.blog-author .author-desig {
  font-size: 14px;
  font-weight: 500;
  color: var(--theme-color);
  text-transform: uppercase;
  font-family: var(--title-font);
}
.blog-author .author-text {
  margin-top: 9px;
  margin-bottom: -0.3em;
}

.share-links-title {
  font-size: 18px;
  color: var(--title-color);
  font-family: var(--title-font);
  font-weight: 500;
  margin: 0 20px 0 0;
}

.share-links {
  border-bottom: 1px solid var(--border-color);
  padding: 13px 0 40px;
}
.share-links > .row {
  align-items: center;
  --bs-gutter-y: 20px;
}
.share-links .wp-block-tag-cloud,
.share-links .tagcloud {
  display: inline-block;
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .blog-single .blog-title {
    font-size: 28px;
  }
  .blog-single .blog-post-wrap {
    padding: 30px;
  }
  .share-links {
    --blog-space-x: 20px;
  }
  .blog-meta span, .blog-meta a {
    margin-right: 23px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .blog-meta span,
  .blog-meta a {
    font-size: 14px;
    margin-right: 20px;
  }
  .blog-single .blog-post-wrap {
    padding: 20px;
  }
  .blog-single .blog-post-wrap h5 {
    font-size: 18px;
  }
  blockquote, .wp-block-quote {
    padding: 150px 40px 40px 40px;
  }
  blockquote:before, .wp-block-quote:before {
    left: 40px;
    top: 40px;
  }
}
/* Medium devices */
/* Small devices */
@media (max-width: 767px) {
  blockquote cite,
  .wp-block-quote cite {
    font-size: 18px;
  }
  .blog-meta span,
  .blog-meta a {
    margin-right: 6px;
    padding-right: 6px;
  }
  .blog-meta span:after,
  .blog-meta a:after {
    display: none;
  }
  .blog-single .blog-title {
    font-size: 24px;
    line-height: 1.3;
  }
  .blog-single .blog-text {
    margin-bottom: 22px;
  }
  .blog-single .blog-bottom {
    padding-top: 15px;
  }
  .blog-single .blog-content {
    padding-top: 30px;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .blog-meta span,
  .blog-meta a {
    margin-right: 5px;
    padding-right: 5px;
  }
  .blog-meta span:after,
  .blog-meta a:after {
    display: none;
  }
  .share-links-title {
    display: block;
    margin-bottom: 10px;
  }
  .blog-author {
    display: block;
  }
  .blog-author .auhtor-img {
    margin-bottom: 20px;
    display: inline-block;
  }
  blockquote p, .wp-block-quote p {
    font-size: 18px;
    line-height: inherit;
  }
  blockquote, .wp-block-quote {
    padding: 140px 30px 30px 30px;
  }
  blockquote:before, .wp-block-quote:before {
    left: 30px;
    top: 30px;
  }
}
/*------------------- 4.7. Comments  -------------------*/
.comment-form {
  margin-top: var(--blog-space-y, 20px);
  position: relative;
  padding: 60px;
  border-radius: 15px;
  background: var(--smoke-color2);
}
.comment-form .row {
  --bs-gutter-x: 20px;
}
.comment-form .form-title {
  margin-top: -0.35em;
  margin-bottom: 30px;
}
.comment-form .form-title a#cancel-comment-reply-link {
  font-size: 0.7em;
  text-decoration: underline;
}
.comment-form .form-text {
  margin-bottom: 25px;
}
.comment-form .form-group {
  margin-bottom: 30px;
}

.blog-comment-area {
  margin-bottom: 25px;
}

.blog-inner-title {
  font-size: 30px;
  font-weight: 500;
  margin-bottom: 26px;
}

.comments-wrap {
  margin-top: var(--blog-space-y, 80px);
  margin-bottom: 30px;
}
.comments-wrap .description p:last-child {
  margin-bottom: -0.5em;
}
.comments-wrap .comment-respond {
  margin: 30px 0;
}
.comments-wrap pre {
  background: #ededed;
  color: #666;
  font-size: 14px;
  margin: 20px 0;
  overflow: auto;
  padding: 20px;
  white-space: pre-wrap;
  word-wrap: break-word;
}
.comments-wrap li {
  margin: 0;
}
.comments-wrap .post-comment {
  padding: 0;
  position: relative;
  display: flex;
  margin-bottom: 30px;
  padding-bottom: 30px;
  position: relative;
  border-bottom: 1px solid var(--border-color);
}
.comments-wrap .post-comment ol,
.comments-wrap .post-comment ul,
.comments-wrap .post-comment dl {
  margin-bottom: 1rem;
}
.comments-wrap .post-comment ol ol,
.comments-wrap .post-comment ol ul,
.comments-wrap .post-comment ul ol,
.comments-wrap .post-comment ul ul {
  margin-bottom: 0;
}
.comments-wrap ul.comment-list {
  list-style: none;
  margin: 0;
  padding: 0;
  margin-bottom: var(--blog-space-y, 80px);
}
.comments-wrap ul.comment-list ul ul,
.comments-wrap ul.comment-list ul ol,
.comments-wrap ul.comment-list ol ul,
.comments-wrap ul.comment-list ol ol {
  margin-bottom: 0;
}
.comments-wrap .comment-avater {
  margin-right: 25px;
  overflow: hidden;
  border-radius: 15px;
  height: fit-content;
}
.comments-wrap .comment-avater img {
  width: 100%;
}
.comments-wrap .comment-content {
  flex: 1;
  margin-top: -6px;
  position: relative;
}
.comments-wrap .commented-on {
  font-size: 12px;
  display: inline-block;
  margin-bottom: 2px;
  font-weight: 500;
  color: var(--body-color);
  font-family: var(--title-font);
}
.comments-wrap .commented-on i {
  margin-right: 7px;
  font-size: 0.9rem;
}
.comments-wrap .name {
  margin-bottom: 7px;
  font-size: 20px;
  font-weight: 500;
}
.comments-wrap .comment-top {
  display: flex;
  justify-content: space-between;
}
.comments-wrap .text {
  margin-bottom: -0.3em;
}
.comments-wrap .children {
  margin: 0;
  padding: 0;
  list-style-type: none;
  margin-left: 135px;
}
.comments-wrap .reply_and_edit {
  margin-bottom: -0.46em;
  position: absolute;
  right: 0;
  top: 20px;
}
.comments-wrap .reply_and_edit a {
  margin-right: 10px;
  color: var(--theme-color);
  padding: 0;
  font-size: 16px;
  font-weight: 600;
  font-family: var(--body-font);
}
.comments-wrap .reply_and_edit a:hover {
  color: var(--title-color);
}
.comments-wrap .reply_and_edit a i {
  margin-left: 3px;
  margin-right: 0;
}
.comments-wrap .reply_and_edit a:last-child {
  margin-right: 0;
}
.comments-wrap .reply-btn {
  font-weight: 700;
  font-size: 12px;
  color: var(--body-color);
  font-family: var(--title-font);
  display: inline-block;
}
.comments-wrap .reply-btn i {
  margin-right: 7px;
}
.comments-wrap .reply-btn:hover {
  color: var(--theme-color);
}
.comments-wrap .star-rating {
  font-size: 12px;
  margin-bottom: 10px;
  position: absolute;
  top: 5px;
  right: 0;
  width: 80px;
}

ul.comment-list .comment-item:last-child:not(.children .comment-item) > .post-comment {
  border-bottom: none;
  padding-bottom: 0;
}
ul.comment-list .comment-item:first-child:not(.children .comment-item) > .post-comment {
  padding-bottom: 30px;
  border-bottom: 1px solid var(--border-color);
}

.comments-wrap.comment-form {
  margin: 0;
}

/* Large devices */
@media (max-width: 1199px) {
  .blog-single {
    --blog-space-y: 40px;
  }
  .comments-wrap {
    margin-top: 40px;
  }
  .comments-wrap .children {
    margin-left: 40px;
  }
  .blog-inner-title {
    margin-bottom: 30px;
  }
  .comment-form {
    padding: 40px;
    --blog-space-y: 40px;
  }
  .comments-wrap .comment-avater {
    margin-right: 35px;
  }
  ul.comment-list .comment-item:first-child:not(.children .comment-item) > .post-comment {
    padding-bottom: 30px;
  }
  .comments-wrap .post-comment {
    margin-bottom: 30px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .comment-form .btn {
    padding: 20px 40px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .comments-wrap .post-comment {
    display: block;
  }
  .comments-wrap .comment-avater {
    height: 100px;
    width: 100px;
    margin-bottom: 30px;
  }
}
/*--------------------------------------------------------------
# Comment Css
--------------------------------------------------------------*/
.comments-title {
  font-size: 27px;
  border-bottom: 2px solid var(--border-color);
  padding-bottom: 15px;
  margin-bottom: 30px;
}

ol.comment-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.comment ol.children {
  list-style: none;
  padding-left: 50px;
}

.comment span.says {
  display: none;
}

.comment-author.vcard,
footer.comment-meta {
  position: relative;
}

.comment-author.vcard img {
  border-radius: 50%;
  height: 90px;
  width: 90px;
  margin-top: -5px;
}

.comment-author.vcard .fn {
  font-size: 20px;
  position: absolute;
  left: 110px;
  top: -10px;
  text-transform: capitalize;
  font-family: "Poppins", sans-serif;
  font-weight: 600;
}

.comment-author.vcard .fn a {
  font-weight: 600;
}

.bypostauthor {
  display: block;
}

.bypostauthor .comment-author.vcard .fn:after {
  content: "\f02e";
  font-family: "Font Awesome 5 Free";
  font-size: 13px;
  top: 0;
  margin-left: 10px;
  position: relative;
  line-height: 1;
  font-weight: 900;
}

.comment-metadata {
  position: absolute;
  left: 110px;
  top: 20px;
}

.comment-metadata time {
  text-transform: uppercase;
  font-weight: 500;
  font-size: 14px;
}

em.comment-awaiting-moderation {
  font-weight: 500;
  color: #1d1d1d;
  display: block;
  padding-left: 110px;
  margin-top: -25px;
  margin-bottom: 35px;
}

.comment-metadata span.edit-link,
span.edit-link {
  display: none;
}

.comment .comment-content {
  position: relative;
  padding-left: 110px;
  margin-top: -25px;
}

.comment article {
  margin-top: 15px;
  padding: 15px 15px 15px 0;
  border-bottom: 2px solid var(--border-color);
  position: relative;
}

.comment-list li:last-child .comment-body {
  border-bottom: 0;
}

.comment-list li .children .comment-body {
  border-bottom: 2px solid var(--border-color);
}

.comment-content img {
  margin-top: 30px;
  margin-bottom: 30px;
}

.comment-body .reply {
  position: absolute;
  right: 0;
  top: 8px;
}

.comments-area .reply a {
  position: relative;
  padding-left: 25px;
  color: var(--black-color);
  font-weight: 600;
}
.comments-area .reply a:hover {
  color: var(--theme-color);
}

.comment-body .reply a:before {
  content: "\f3e5";
  font-family: Font Awesome\ 6 Pro;
  position: absolute;
  left: 3px;
  top: 0;
}

.comment-content a,
.comment-body a {
  word-wrap: break-word;
  font-weight: 500;
}

.comment-content li {
  font-weight: 500;
  margin: 8px 0;
}

/*--------------------------------------------------------------
# Comment Respond Form
--------------------------------------------------------------*/
.comments-heading {
  font-size: 25px;
  margin-bottom: 5px;
}

#cancel-comment-reply-link {
  margin-left: 10px;
  font-size: 20px;
  font-weight: 700;
}

#cancel-comment-reply-link:hover {
  text-decoration: underline;
}

.comment-respond {
  margin-top: 30px;
  padding: 35px 45px 30px 45px;
  background-color: var(--smoke-color2);
  color: var(--black-color);
  border-radius: 10px;
}
.comment-respond .comment-form {
  padding: 0;
}

.tp-comment-input {
  position: relative;
  margin-bottom: 30px;
}

.tp-comment-input input,
.tp-comment-input textarea {
  margin-bottom: 0;
}

.tp-comment-input i {
  position: absolute;
  right: 20px;
  font-size: 18px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  color: #647589;
}

.comment-message.tp-comment-input i {
  top: 30px;
}

li.comment .comment-respond {
  margin-bottom: 45px;
  margin-top: 45px;
}

.comment-form .comment-form-wrap {
  margin: 25px -45px 0 -45px;
  border-top: 2px solid var(--border-color);
  padding: 35px 30px 0 30px;
}

.comment-form input,
.comment-form textarea {
  background: var(--white-color);
  border-color: var(--border-color);
  color: var(--black-color);
  border-radius: 15px;
}

.comment-message textarea {
  height: auto;
  border-radius: 15px;
}

.comment-form-cookies-consent label {
  display: inline;
  margin-left: 10px;
}

.comment-form .comment-message {
  margin-top: 10px;
}

.comment-form-wrap ::-webkit-input-placeholder {
  /* WebKit, Blink, Edge */
  color: #647589;
}

.comment-form-wrap :-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  color: #647589;
  opacity: 1;
}

.comment-form-wrap ::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: #647589;
  opacity: 1;
}

.comment-form-wrap :-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: #647589;
}

.comment-form-wrap ::-ms-input-placeholder {
  /* Microsoft Edge */
  color: #647589;
}

.comment-form-wrap ::placeholder {
  /* Most modern browsers support this now. */
  color: #647589;
}

#message-cmt::placeholder {
  /* Most modern browsers support this now. */
  color: #647589;
}

.comments-area button.tp-button i {
  font-size: 14px;
}

@media (min-width: 500px) {
  .comment-metadata time {
    margin-top: 15px;
    display: inline-block;
  }
}
li.pingback,
li.trackback {
  border: 2px solid var(--border-color);
  padding: 10px;
  margin-bottom: 20px;
}

/*------------------- 4.8. Hero Area  -------------------*/
/* Hero Global ---------------------------------- */
.hero-wrapper {
  position: relative;
  z-index: 2;
  overflow: hidden;
}

/******here slider******/
.hero-slider {
  position: relative;
  background-position: bottom;
}
.hero-slider .hero-shape1 {
  mix-blend-mode: soft-light;
}

/* Hero 1 ---------------------------------- */
.hero-wrapper .slick-arrow {
  --icon-size: 110px;
  --pos-x: 1;
  margin-left: -55px;
  line-height: 155px;
  opacity: 1;
  letter-spacing: 0.2em;
  text-transform: uppercase;
  font-size: 16px;
  font-weight: 500;
  font-family: var(--title-font);
  color: var(--theme-color);
  transform: rotate(270deg);
  margin-top: calc(var(--icon-size, 110px) / -1);
}
.hero-wrapper .slick-arrow.slick-next {
  --pos-x: 0;
  margin-left: 0;
  margin-right: -55px;
  transform: rotate(90deg);
}

.hero-subtitle {
  font-size: 14px;
  font-family: var(--body-font);
  font-weight: 600;
  color: var(--white-color);
  display: inline-block;
  margin-bottom: 24px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding-left: 23px;
  position: relative;
}
.hero-subtitle:after {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  height: 8px;
  width: 8px;
  transform: translate(0, -50%);
  background: var(--theme-color);
  border-radius: 1px;
}

.hero-title {
  font-size: 80px;
  line-height: 87px;
  margin-bottom: 0;
  color: var(--title-color);
  font-weight: 700;
  letter-spacing: 0.02em;
}
.hero-title span {
  color: transparent;
  -webkit-text-stroke: 2px var(--white-color);
}

.hero-text {
  font-size: 20px;
  margin-bottom: 42px;
}

.hero-style1 {
  position: relative;
  z-index: 6;
  padding: 180px 0 255px;
}
.hero-style1 .btn-group {
  margin-top: 53px;
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .hero-style1 {
    padding: 150px 0 215px;
  }
  .hero-title {
    font-size: 70px;
    line-height: 77px;
  }
  .hero-style1 .btn-group {
    margin-top: 43px;
  }
}
/* Medium Large devices */
@media (max-width: 1299px) {
  .hero-style1 {
    padding: 150px 0 215px 50px;
  }
  .hero-shape1 {
    max-width: 600px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .hero-style1 {
    padding: 150px 0 215px 0px;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .hero-title {
    font-size: 56px;
    line-height: 67px;
  }
  .hero-style1 {
    padding: 120px 0 185px 0px;
  }
}
/* Extra small devices */
@media (max-width: 375px) {
  .hero-title {
    font-size: 46px;
    line-height: 57px;
  }
  .hero-style1 {
    padding: 120px 0 165px 0px;
  }
}
@media (max-width: 320px) {
  .hero-title {
    font-size: 38px;
    line-height: 47px;
  }
}
/* Hero 2 ---------------------------------- */
.hero-2 {
  clip-path: ellipse(105% 93% at 50% 6.9%);
}
.hero-2 .hero-slider {
  background-position: bottom;
}
.hero-2 .slick-arrow {
  margin-top: calc(var(--icon-size, 110px) / -2);
}
.hero-2 .hero-shape2-3,
.hero-2 .hero-shape2-2,
.hero-2 .hero-shape2-1 {
  mix-blend-mode: multiply;
}
.hero-2 .hero-shape2-2 {
  opacity: 0.7;
}

.hero-style2 {
  position: relative;
  z-index: 6;
  padding: 398px 0 230px;
}
.hero-style2 .btn-group {
  margin-top: 62px;
}
.hero-style2 .hero-subtitle {
  padding-left: 0;
  font-size: 48px;
  font-family: var(--title-font);
  text-transform: none;
  margin-bottom: 0;
  letter-spacing: normal;
}
.hero-style2 .hero-subtitle:after {
  display: none;
}
.hero-style2 .hero-title {
  font-size: 160px;
  line-height: 1.167;
  margin-top: -0.08em;
  text-transform: uppercase;
  margin-bottom: 15px;
}

@media (max-width: 1600px) {
  .hero-style2 .hero-title {
    font-size: 130px;
  }
}
/* Extra large devices */
@media (max-width: 1500px) {
  .hero-style2 .hero-title {
    font-size: 100px;
    margin-bottom: 25px;
    margin-top: 10px;
  }
  .hero-style2 .btn-group {
    margin-top: 52px;
  }
  .hero-style2 {
    padding: 368px 0 200px;
  }
}
/* Medium Large devices */
@media (max-width: 1299px) {
  .hero-style2 {
    padding: 338px 0 170px;
  }
  .hero-style2 .hero-title {
    font-size: 90px;
  }
  .hero-style2 .hero-subtitle {
    font-size: 38px;
  }
  .hero-2 .hero-shape2-2, .hero-2 .hero-shape2-1 {
    width: 400px;
  }
  .hero-2 .hero-shape2-3 {
    width: 700px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .hero-style2 {
    padding: 218px 0 160px;
    text-align: center;
  }
  .hero-style2 .hero-title {
    font-size: 80px;
  }
  .hero-style2 .btn-group {
    justify-content: center;
  }
  .hero-2 .hero-shape2-2, .hero-2 .hero-shape2-1 {
    width: 270px;
  }
  .hero-2 .hero-shape2-3 {
    width: 560px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .hero-style2 .hero-title {
    font-size: 70px;
  }
  .hero-style2 .hero-subtitle {
    font-size: 28px;
  }
  .hero-style2 .btn-group {
    margin-top: 42px;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .hero-style2 {
    padding: 158px 0 130px;
  }
  .hero-style2 .hero-title {
    font-size: 60px;
  }
  .hero-2 {
    clip-path: ellipse(155% 93% at 50% 6.9%);
  }
}
/* Extra small devices */
@media (max-width: 375px) {
  .hero-style2 .hero-title {
    font-size: 50px;
  }
  .hero-style2 .hero-subtitle {
    font-size: 24px;
  }
}
/* Hero 3 ---------------------------------- */
.hero-3 {
  padding: 198px 0;
}
.hero-3:after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  background: #1C1C1C;
  mix-blend-mode: hard-light;
}

.hero-style3 {
  position: relative;
  z-index: 6;
}
.hero-style3 .hero-title {
  font-size: 80px;
  line-height: 90px;
  margin-top: -0.26em;
  margin-bottom: -0.2em;
}

.appointment-form {
  position: relative;
  z-index: 6;
  padding: 50px;
}
.appointment-form .form-title {
  margin-top: -0.3em;
  margin-bottom: 22px;
}
.appointment-form .form-group {
  margin-bottom: 10px;
}
.appointment-form .btn {
  padding: 24px 34px 22px !important;
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .hero-style3 .hero-title {
    font-size: 70px;
    line-height: 80px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .hero-3 {
    padding: 150px 0;
  }
  .hero-style3 .hero-title {
    font-size: 65px;
    line-height: 1.3;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .hero-3 {
    padding: 195px 0 120px;
  }
  .hero-style3 {
    text-align: center;
    margin-bottom: 80px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .hero-style3 .hero-title {
    font-size: 60px;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .hero-style3 .hero-title {
    font-size: 46px;
  }
  .appointment-form {
    padding: 40px;
  }
}
/* Extra small devices */
@media (max-width: 375px) {
  .hero-style3 .hero-title {
    font-size: 42px;
  }
}
/*------------------- 4.9. Error  -------------------*/
.error-area .error-thumb {
  margin-bottom: 55px;
}
.error-area .error-content {
  margin-bottom: 45px;
}

.not-found-text-wrapper .text-404 h4 {
  font-size: 48px;
  margin-top: -0.35em;
}
.not-found-text-wrapper .error-dec {
  margin-bottom: 45px;
}

/*------------------- 4.00. Popup Search  -------------------*/
.popup-search-box {
  position: fixed;
  top: 0;
  left: 50%;
  background-color: rgba(0, 0, 0, 0.95);
  height: 0;
  width: 0;
  overflow: hidden;
  z-index: 99999;
  opacity: 0;
  visibility: hidden;
  border-radius: 50%;
  transform: translateX(-50%);
  transition: all ease 0.4s;
}
.popup-search-box button.searchClose {
  width: 50px;
  height: 50px;
  line-height: 52px;
  position: absolute;
  top: 40px;
  right: 40px;
  background-color: var(--theme-color);
  font-size: 22px;
  border-radius: 10px;
  transform: rotate(0);
  transition: all ease 0.4s;
  color: var(--white-color);
  border: 0;
}
.popup-search-box button.searchClose:hover {
  color: var(--body-color);
  background-color: #fff;
  border-color: transparent;
  border-color: transparent;
  transform: rotate(90deg);
}
.popup-search-box form {
  position: absolute;
  top: 50%;
  left: 50%;
  display: inline-block;
  padding-bottom: 40px;
  cursor: auto;
  width: 100%;
  max-width: 700px;
  transform: translate(-50%, 50%) scale(0);
  transition: transform ease 0.4s;
  /* Large devices */
}
@media (max-width: 1199px) {
  .popup-search-box form {
    max-width: 600px;
  }
}
.popup-search-box form input {
  font-size: 18px;
  height: 70px;
  width: 100%;
  border: none;
  background-color: var(--white-color);
  border: 2px solid var(--theme-color);
  padding: 0 80px 0 30px;
  color: var(--title-color);
  border-radius: 50px;
}
.popup-search-box form input::-moz-placeholder {
  color: var(--theme-color);
}
.popup-search-box form input::-webkit-input-placeholder {
  color: var(--theme-color);
}
.popup-search-box form input:-ms-input-placeholder {
  color: var(--theme-color);
}
.popup-search-box form input::placeholder {
  color: var(--theme-color);
}
.popup-search-box form button {
  position: absolute;
  top: 0px;
  background-color: transparent;
  border: none;
  color: var(--theme-color);
  font-size: 24px;
  right: 12px;
  cursor: pointer;
  width: 70px;
  height: 70px;
  transition: all ease 0.4s;
  transform: scale(1.001);
}
.popup-search-box form button:hover {
  transform: scale(1.1);
}
.popup-search-box.show {
  opacity: 1;
  visibility: visible;
  width: 100.1%;
  height: 100%;
  transition: all ease 0.4s;
  border-radius: 0;
}
.popup-search-box.show form {
  transition-delay: 0.5s;
  transform: translate(-50%, -50%) scale(1);
}

/* Small devices */
@media (max-width: 767px) {
  .popup-search-box form {
    width: 80%;
  }
  .popup-search-box form input {
    height: 60px;
  }
  .popup-search-box form button {
    width: 60px;
    line-height: 62px;
    height: 60px;
  }
}
/*------------------- 4.00. Popup Side Menu  -------------------*/
.sidemenu-wrapper {
  position: fixed;
  z-index: 99999;
  right: 0;
  top: 0;
  height: 100%;
  width: 0;
  background-color: rgba(0, 0, 0, 0.75);
  opacity: 0;
  visibility: hidden;
  transition: all ease 0.8s;
}
.sidemenu-wrapper .closeButton {
  display: inline-block;
  border: 2px solid;
  width: 50px;
  height: 50px;
  line-height: 50px;
  font-size: 24px;
  padding: 0;
  position: absolute;
  top: 20px;
  right: 20px;
  background-color: var(--white-color);
  border-radius: 50%;
  transform: rotate(0);
  transition: all ease 0.4s;
}
.sidemenu-wrapper .closeButton:hover {
  color: var(--theme-color);
  border-color: var(--theme-color);
  transform: rotate(90deg);
}
.sidemenu-wrapper .sidemenu-content {
  background-color: var(--title-color);
  width: 450px;
  margin-left: auto;
  padding: 80px 30px;
  height: 100%;
  overflow-y: scroll;
  position: relative;
  right: -500px;
  cursor: auto;
  transition-delay: 1s;
  transition: right ease 1s;
}
.sidemenu-wrapper .sidemenu-content::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 1px rgba(0, 0, 0, 0.1);
  box-shadow: inset 0 0 1px rgba(0, 0, 0, 0.1);
  background-color: #F5F5F5;
}
.sidemenu-wrapper .sidemenu-content::-webkit-scrollbar {
  width: 2px;
  background-color: #F5F5F5;
}
.sidemenu-wrapper .widget {
  padding: 0;
  border: none;
  background-color: transparent;
}
.sidemenu-wrapper.show {
  opacity: 1;
  visibility: visible;
  width: 100%;
  transition: all ease 0.8s;
}
.sidemenu-wrapper.show .sidemenu-content {
  right: 0;
  opacity: 1;
  visibility: visible;
}

/* Small devices */
@media (max-width: 767px) {
  .sidemenu-wrapper .sidemenu-content {
    width: 320px;
    padding: 80px 20px;
  }
}
/*------------------- 4.00. Wocommerce  -------------------*/
.woocommerce-message,
.woocommerce-info {
  position: relative;
  padding: 11px 20px 11px 50px;
  background-color: var(--theme-color);
  color: var(--white-color);
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 15px;
  border-radius: 0;
}
.woocommerce-message a,
.woocommerce-info a {
  color: var(--white-color);
  text-decoration: underline;
}
.woocommerce-message a:hover,
.woocommerce-info a:hover {
  color: var(--title-color);
}
.woocommerce-message:before,
.woocommerce-info:before {
  content: "\f06a";
  font-family: var(--icon-font);
  font-weight: 400;
  margin-right: 10px;
  font-size: 18px;
  position: absolute;
  left: 20px;
  top: 11px;
}

.woocommerce-notices-wrapper .woocommerce-message {
  background-color: var(--theme-color);
  color: var(--white-color);
}
.woocommerce-notices-wrapper .woocommerce-message:before {
  content: "\f14a";
  font-weight: 300;
}

.woocommerce-form-login-toggle .woocommerce-info {
  background-color: var(--theme-color);
  color: var(--white-color);
}
.woocommerce-form-login-toggle .woocommerce-info a {
  color: inherit;
}
.woocommerce-form-login-toggle .woocommerce-info a:hover {
  color: var(--title-color);
}

.woocommerce-form-register,
.woocommerce-form-coupon,
.woocommerce-form-login {
  padding: 35px 40px 35px 40px;
  background-color: var(--white-color);
  box-shadow: 0px 6px 30px rgba(1, 15, 28, 0.1);
  margin-bottom: 0;
  /* Extra small devices */
}
@media (max-width: 575px) {
  .woocommerce-form-register,
  .woocommerce-form-coupon,
  .woocommerce-form-login {
    padding: 40px 20px;
  }
}
.woocommerce-form-register .form-group,
.woocommerce-form-coupon .form-group,
.woocommerce-form-login .form-group {
  margin-bottom: 20px;
}
.woocommerce-form-register .form-group:last-child,
.woocommerce-form-coupon .form-group:last-child,
.woocommerce-form-login .form-group:last-child {
  margin-bottom: 0;
}

.woocommerce-form-login {
  margin-bottom: 30px;
}

.woocommerce-error {
  background-color: var(--error-color);
  color: #fff;
  list-style: none;
  padding: 10px 26px;
  margin: 0 0 30px 0;
  border-radius: 5px;
  font-weight: 700;
  font-size: 14px;
}

nav.woocommerce-MyAccount-navigation li {
  border: 1px solid #ddd;
  margin: 0;
  border-top: none;
}
nav.woocommerce-MyAccount-navigation li:first-child {
  border-top: 1px solid #ddd;
}
nav.woocommerce-MyAccount-navigation li a {
  color: var(--title-color);
  font-weight: 700;
  padding: 7px 17px;
  display: block;
}
nav.woocommerce-MyAccount-navigation li.is-active a,
nav.woocommerce-MyAccount-navigation li a:hover {
  color: var(--white-color);
  background-color: var(--theme-color);
}

.woocommerce-MyAccount-content h3 {
  margin-top: -0.3em;
}
.woocommerce-MyAccount-content .btn {
  background-color: var(--theme-color);
  color: var(--white-color);
  font-size: 14px;
  padding: 10px 25px;
  font-weight: 700;
}
.woocommerce-MyAccount-content .btn:hover {
  background-color: var(--title-color);
  color: var(--white-color);
}

table.variations,
.woocommerce-grouped-product-list-item {
  border-collapse: separate;
  border-spacing: 0 15px;
  margin-bottom: 5px;
  align-items: center;
  border: none;
}
table.variations td,
.woocommerce-grouped-product-list-item td {
  border: none;
  vertical-align: middle;
  padding: 0 5px;
}
table.variations td:first-child,
.woocommerce-grouped-product-list-item td:first-child {
  padding: 0;
}
table.variations label,
.woocommerce-grouped-product-list-item label {
  margin: 0;
  font-size: 14px;
  text-transform: capitalize;
}
table.variations label a,
.woocommerce-grouped-product-list-item label a {
  color: var(--title-color);
}
table.variations label a:hover,
.woocommerce-grouped-product-list-item label a:hover {
  color: var(--theme-color);
}
table.variations .label,
.woocommerce-grouped-product-list-item .label {
  border: none;
}
table.variations__label,
.woocommerce-grouped-product-list-item__label {
  border: none !important;
  font-weight: 600;
}
table.variations__price,
.woocommerce-grouped-product-list-item__price {
  border: none !important;
}
table.variations__price .price,
table.variations__price .amount,
.woocommerce-grouped-product-list-item__price .price,
.woocommerce-grouped-product-list-item__price .amount {
  font-size: 18px !important;
}
table.variations del,
.woocommerce-grouped-product-list-item del {
  margin-left: 12px;
}

.woocommerce-product-attributes th,
.woocommerce-product-attributes td {
  border: 1px solid var(--border-color);
}
.woocommerce-product-attributes th p:last-child,
.woocommerce-product-attributes td p:last-child {
  margin-bottom: 0;
}

.woocommerce-grouped-product-list.group_table {
  border-collapse: collapse;
  margin-bottom: 15px;
}
.woocommerce-grouped-product-list.group_table .woocommerce-Price-amount.amount {
  font-size: 16px;
  color: var(--title-color);
}
.woocommerce-grouped-product-list.group_table label {
  margin: 0 0 0 10px;
  margin: 0 0 0 10px;
  font-family: var(--title-font);
  font-size: 18px;
}
.woocommerce-grouped-product-list.group_table .qty-input {
  border-color: #e3e6e9;
}
.woocommerce-grouped-product-list.group_table tr {
  border-bottom: 1px solid #e3e6e9;
}
.woocommerce-grouped-product-list.group_table tr:last-child {
  border-bottom: none;
}
.woocommerce-grouped-product-list.group_table td {
  padding: 30px 5px;
}

table.variations {
  width: max-content;
  position: relative;
}
table.variations td {
  padding: 0;
}
table.variations td.label {
  padding-right: 10px;
  width: max-content;
}
table.variations select {
  width: max-content;
  font-weight: 400;
  line-height: 1.5;
  vertical-align: middle;
  margin: 0;
  padding-right: 54px;
  padding-left: 20px;
  height: 50px;
}
table.variations .reset_variations {
  margin-left: 16px;
  display: inline-block;
  position: absolute;
  left: 100%;
  bottom: 25px;
}

.woosq-product .product .woocommerce-grouped-product-list-item__quantity,
.woosq-product .product .woocommerce-grouped-product-list-item__label,
.woosq-product .product .woocommerce-grouped-product-list-item__price {
  width: auto !important;
}

.woocommerce-variation.single_variation {
  margin-bottom: 30px;
}
.woocommerce-variation.single_variation .price {
  color: var(--title-color);
  font-weight: 700;
}

.wooscp-table-items td.woocommerce-product-attributes-item__value {
  padding-left: 15px !important;
}
.wooscp-table-items a.added_to_cart.wc-forward {
  margin-left: 15px;
  text-decoration: underline;
}

.tinvwl_added_to_wishlist.tinv-modal.tinv-modal-open {
  z-index: 1111;
}

table.woocommerce-product-attributes {
  margin-bottom: 30px;
}

#woosq-popup .product_meta {
  margin-top: 20px;
}
#woosq-popup .product_title {
  font-size: 24px;
  margin-bottom: 5px;
}
#woosq-popup .single-product .product .actions {
  align-items: center;
  display: flex;
  gap: 20px;
}
#woosq-popup .single-product .product .actions > div {
  height: auto;
  overflow: visible;
  width: max-content;
}
#woosq-popup .single-product .product .actions > div .quantity.style2.woocommerce-grouped-product-list-item__quantity {
  width: max-content;
}

.login-tab {
  margin-bottom: 30px;
  justify-content: center;
}
.login-tab button.nav-link {
  background-color: var(--smoke-color);
  color: var(--title-color);
  padding: 11px 39px;
  font-size: 18px;
  font-weight: 500;
  border-radius: 15px ​15px 0;
}
.login-tab button.nav-link.active {
  background-color: var(--theme-color);
  color: var(--white-color);
}

.star-rating {
  overflow: hidden;
  position: relative;
  width: 100px;
  height: 1.2em;
  line-height: 1.2em;
  display: block;
  font-family: var(--icon-font);
  font-weight: 700;
  font-size: 12px;
}
.star-rating:before {
  content: "\f005\f005\f005\f005\f005";
  color: #e1e1e1;
  float: left;
  top: 0;
  left: 0;
  position: absolute;
  letter-spacing: 3px;
}
.star-rating span {
  overflow: hidden;
  float: left;
  top: 0;
  left: 0;
  position: absolute;
  padding-top: 1.5em;
}
.star-rating span:before {
  content: "\f005\f005\f005\f005\f005";
  top: 0;
  position: absolute;
  left: 0;
  color: #feb62a;
  letter-spacing: 3px;
}

.rating-select label {
  margin: 0;
  margin-right: 10px;
}
.rating-select p.stars {
  margin-bottom: 0;
  line-height: 1;
}
.rating-select p.stars a {
  position: relative;
  height: 14px;
  width: 18px;
  text-indent: -999em;
  display: inline-block;
  text-decoration: none;
}
.rating-select p.stars a::before {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 18px;
  height: 14px;
  line-height: 1;
  font-family: var(--icon-font);
  content: "\f005";
  font-weight: 400;
  text-indent: 0;
  color: var(--yellow-color);
}
.rating-select p.stars a:hover ~ a::before {
  content: "\f005";
  font-weight: 400;
}
.rating-select p.stars:hover a::before {
  content: "\f005";
  font-weight: 700;
}
.rating-select p.stars.selected a.active::before {
  content: "\f005";
  font-weight: 700;
}
.rating-select p.stars.selected a.active ~ a::before {
  content: "\f005";
  font-weight: 400;
}
.rating-select p.stars.selected a:not(.active)::before {
  content: "\f005";
  font-weight: 700;
}

/* Small devices */
@media (max-width: 767px) {
  .woocommerce-message,
  .woocommerce-info {
    font-size: 14px;
    line-height: 22px;
    padding: 10px 15px 10px 37px;
  }
  .woocommerce-message:before,
  .woocommerce-info:before {
    font-size: 16px;
    top: 10px;
    left: 15px;
  }
}
.woocommerce .widget_shopping_cart .cart_list li, .woocommerce.widget_shopping_cart .cart_list li {
  padding-left: 3em;
}

.woocommerce a.remove {
  height: 1em;
  width: 1em;
  line-height: 18px;
}

/*------------------- 4.00. Subscribe  -------------------*/
.subscribe-sec-shape {
  position: relative;
  z-index: -1;
  top: -50px;
}

.subscribe-area {
  border-radius: 6px;
  position: relative;
  z-index: 2;
  box-shadow: 0px 10px 60px 0px rgba(0, 0, 0, 0.07);
  border-radius: 0 0 6px 6px;
  padding: 0 30px 30px;
  margin: 0 -30px 0px;
  background: var(--white-color);
}
.subscribe-area .subscribe-wrapper {
  padding: 50px 90px;
  border-radius: 6px;
  transform: translate(0, -30px);
  margin-bottom: -30px;
}
.subscribe-area .subscribe-title {
  font-size: 30px;
  margin-top: -0.25em;
}

.subscribe-form {
  position: relative;
}
.subscribe-form .form-group .form-control {
  height: 70px;
  padding-right: 220px;
}
.subscribe-form .form-group .input-icon {
  top: 0;
  height: 100%;
  line-height: 70px;
}
.subscribe-form .btn {
  position: absolute;
  right: 10px;
  top: 10px;
  padding: 18px 40px;
}

/* Large devices */
@media (max-width: 1199px) {
  .subscribe-area .subscribe-wrapper {
    padding: 50px;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .subscribe-area {
    margin: 0 0px 0px;
    padding: 0 20px 20px;
  }
  .subscribe-area .subscribe-wrapper {
    padding: 50px 20px;
  }
  .subscribe-form .form-group .form-control {
    height: 60px;
    padding-right: 30px;
  }
  .subscribe-form .form-group .input-icon {
    line-height: 60px;
  }
  .subscribe-form .btn {
    position: relative;
    margin-top: 15px;
    width: 100%;
    right: 0;
    top: 0;
  }
  .subscribe-area .subscribe-title {
    font-size: 24px;
  }
}
/* Extra small devices */
@media (max-width: 375px) {
  .subscribe-area {
    padding: 0;
    box-shadow: none;
  }
}
/*------------------- 4.00. Cart  -------------------*/
.woocommerce-cart-form {
  text-align: center;
}

.cart_table {
  border: 1px solid #eaf0f2;
  margin-bottom: 45px;
}
.cart_table thead {
  background-color: #ecf0f1;
}
.cart_table thead th {
  border: none !important;
}
.cart_table td:before,
.cart_table th {
  font-family: var(--title-font);
  color: var(--title-color);
  font-weight: 800;
  border: none;
  padding: 27px 15px;
}
.cart_table td:before {
  content: attr(data-title);
  position: absolute;
  left: 15px;
  top: 50%;
  vertical-align: top;
  padding: 0;
  transform: translateY(-50%);
  display: none;
}
.cart_table td {
  border: none;
  border-bottom: 1px solid #f3f3f3;
  color: #8b8b8b;
  padding: 20px 10px;
  position: relative;
  vertical-align: middle;
}
.cart_table .product-quantity {
  color: var(--title-color);
}
.cart_table .product-quantity input {
  position: relative;
  top: -2px;
}
.cart_table .cart-productname {
  font-weight: 400;
  font-family: var(--body-font);
  color: var(--body-color);
}
.cart_table .cart-productimage {
  display: inline-block;
  border: 2px solid var(--smoke-color);
}
.cart_table .remove {
  color: var(--theme-color);
  font-size: 18px;
}
.cart_table .quantity {
  display: inline-flex;
  align-items: center;
}
.cart_table .qty-btn {
  border: 2px solid var(--smoke-color) !important;
  background-color: transparent;
  color: #b8c6d0;
  padding: 0;
  width: 30px;
  height: 30px;
  line-height: 28px;
  font-size: 16px;
  border-radius: 4px;
}
.cart_table .qty-btn:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
}
.cart_table .qty-input {
  vertical-align: middle;
  border: 2px solid var(--smoke-color);
  width: 70px;
  height: 30px;
  font-size: 14px;
  text-align: center;
  color: var(--title-color);
  font-weight: 700;
  margin: 0 10px;
  border-radius: 4px;
  padding: 0;
  /* Firefox */
}
.cart_table .qty-input::-moz-placeholder {
  color: var(--title-color);
}
.cart_table .qty-input::-webkit-input-placeholder {
  color: var(--title-color);
}
.cart_table .qty-input:-ms-input-placeholder {
  color: var(--title-color);
}
.cart_table .qty-input::placeholder {
  color: var(--title-color);
}
.cart_table .qty-input::-webkit-outer-spin-button, .cart_table .qty-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.cart_table .qty-input[type=number] {
  -moz-appearance: textfield;
}
.cart_table .actions {
  text-align: right;
  vertical-align: middle;
}
.cart_table .actions > .as-btn {
  font-size: 16px;
  padding: 20px 28px;
  margin-right: 15px;
}
.cart_table .actions > .as-btn:last-child {
  margin-right: 0;
}
.cart_table .as-cart-coupon {
  float: left;
  margin: 0;
  width: 455px;
  max-width: 100%;
  display: flex;
}
.cart_table .as-cart-coupon input {
  width: calc(100% - 200px);
  margin-right: 10px;
}
.cart_table .as-cart-coupon .as-btn {
  font-size: 16px;
  padding: 20px 25px;
  width: max-content;
}

.cart_totals {
  border: 1px solid #ecf0f1;
}
.cart_totals th,
.cart_totals td {
  vertical-align: top;
  padding: 20px 20px;
  border: none;
  border-bottom: 1px solid #ecf0f1;
  font-size: 14px;
  color: var(--title-color);
  width: 55%;
}
.cart_totals th:first-child,
.cart_totals td:first-child {
  width: 45%;
  background-color: #f9fbfb;
  font-weight: 700;
  font-size: 14px;
  color: #333333;
}
.cart_totals .shipping-calculator-button {
  display: inline-block;
  border-bottom: 1px solid;
  color: var(--title-color);
  font-weight: 700;
}
.cart_totals .shipping-calculator-button:hover {
  color: var(--theme-color);
}
.cart_totals .woocommerce-shipping-destination {
  margin-bottom: 10px;
}
.cart_totals .woocommerce-shipping-methods {
  margin-bottom: 0;
}
.cart_totals .shipping-calculator-form {
  display: none;
  margin-top: 20px;
}
.cart_totals .shipping-calculator-form .form-control,
.cart_totals .shipping-calculator-form .single-select {
  margin-bottom: 20px;
}
.cart_totals .shipping-calculator-form .as-btn {
  padding: 5px 30px;
}
.cart_totals .amount {
  font-weight: 700;
}
.cart_totals .order-total .amount {
  color: var(--theme-color);
}

/* Medium devices */
@media (max-width: 991px) {
  .cart_table th {
    padding: 23px 8px;
    font-size: 14px;
  }
  .cart_table .cart-productname {
    font-size: 14px;
  }
  .cart_table .as-cart-coupon {
    width: 100%;
    margin-bottom: 20px;
    justify-content: center;
  }
  .cart_table .actions {
    text-align: center;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .cart_table {
    text-align: left;
    min-width: auto;
    border-collapse: separate;
    border-spacing: 0 20px;
    border: none;
  }
  .cart_table thead {
    display: none;
  }
  
  .cart_table td {
    padding: 15px;
    display: block;
    width: 100%;
    padding-left: 25%;
    text-align: right;
    border: 1px solid #f3f3f3;
    border-bottom: none;
  }
  .cart_table td::before {
    display: block;
  }
  .cart_table td:last-child {
    border-bottom: 1px solid #f3f3f3;
  }
  .cart_table td.actions {
    padding-left: 15px;
    text-align: center;
  }
  .cart_table td.actions > .as-btn {
    margin-top: 10px;
    margin-right: 0;
    display: block;
    width: max-content;
    margin-left: auto;
    margin-right: auto;
  }
  .cart_table td.actions > .as-btn:last-child {
    margin-right: auto;
  }
  .cart_table .as-cart-coupon {
    width: 100%;
    text-align: center;
    float: none;
    justify-content: center;
    display: block;
    padding-bottom: 10px;
  }
  .cart_table .as-cart-coupon input {
    width: 100%;
    margin-bottom: 10px;
  }
  .cart_totals th,
  .cart_totals td {
    padding: 15px 10px;
  }
  .cart_totals th:first-child,
  .cart_totals td:first-child {
    width: 17%;
    line-height: 1.4;
  }
}
/*------------------- 4.00. Checkout  -------------------*/
.woocommerce-checkout .form-group,
.woocommerce-checkout .form-row {
  margin-bottom: 0;
}
.woocommerce-checkout .form-select,
.woocommerce-checkout .select2-container,
.woocommerce-checkout .form-control {
  margin-bottom: 0;
}
.woocommerce-checkout .select2-container--open .select2-dropdown--below {
  margin-top: -35px;
}
.woocommerce-checkout .select2-container--open .select2-dropdown--above {
  position: relative;
  bottom: -30px;
}
.woocommerce-checkout .select2-dropdown {
  border: 1px solid #e3e6e9;
  border-top: none;
}
.woocommerce-checkout .select2-container--default .select2-selection--single {
  border-radius: 0;
}
.woocommerce-checkout .select2-container--default .select2-selection--single .select2-selection__rendered,
.woocommerce-checkout .select2-container--default .select2-selection--single .form-control:focus {
  color: var(--body-color);
}

.select2-container--default .select2-search--dropdown .select2-search__field {
  border: 1px solid #eee;
  padding: 0;
}

.woocommerce-form-login select,
.woocommerce-form-login .form-select,
.woocommerce-form-login .form-control,
.woocommerce-form-login .select2,
.woocommerce-form-login .select2-container,
.woocommerce-form-coupon select,
.woocommerce-form-coupon .form-select,
.woocommerce-form-coupon .form-control,
.woocommerce-form-coupon .select2,
.woocommerce-form-coupon .select2-container,
.woocommerce-checkout select,
.woocommerce-checkout .form-select,
.woocommerce-checkout .form-control,
.woocommerce-checkout .select2,
.woocommerce-checkout .select2-container {
  margin-bottom: var(--bs-gutter-x);
}

#ship-to-different-address {
  margin-top: 15px;
}

.select2-container--default .select2-selection--single {
  height: 60px;
  border: 1px solid #e3e6e9;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  line-height: 60px;
  padding-left: 30px;
  padding-right: 25px;
}

.woocommerce-billing-fields .form-row {
  margin-bottom: 0;
}

.select2-container--default .select2-selection--single .select2-selection__arrow b:before {
  content: "\f107";
  font-family: var(--icon-font);
}

.select2-container--default .select2-selection--single .select2-selection__arrow b {
  margin: 0;
  border: none;
  top: 0;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 60px;
  line-height: 60px;
  margin-right: 30px;
}

span.select2-selection.select2-selection--single:focus {
  outline: none;
}

.shipping-calculator-form .form-select,
.shipping-calculator-form .form-control {
  height: 40px;
  padding-left: 15px;
  font-size: 16px;
  line-height: 40px;
  border-radius: 0;
  background-position: right 13px center;
}
.shipping-calculator-form .as-btn {
  font-size: 14px;
  padding: 0 20px;
  width: max-content;
  height: 40px;
}

.checkout-ordertable th,
.checkout-ordertable td {
  border: 1px solid #ededed;
  text-align: right;
  padding: 5px 20px;
  vertical-align: top;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}
.checkout-ordertable th {
  font-weight: 800;
  text-align: left;
}
.checkout-ordertable ul {
  margin: 0;
  padding: 0;
}
.checkout-ordertable .order-total .amount {
  color: var(--theme-color);
}
.checkout-ordertable input[type=hidden] ~ label {
  color: var(--theme-color);
}

.woocommerce-checkout .form-group input:not(:last-child) {
  margin-bottom: var(--bs-gutter-x);
}

.woocommerce-checkout-payment {
  text-align: left;
}
.woocommerce-checkout-payment ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}
.woocommerce-checkout-payment ul li {
  padding-top: 12px;
  border-bottom: 1px solid #d8d8d8;
  border-radius: 4px;
  font-size: 16px;
}
.woocommerce-checkout-payment ul input[type=radio] ~ label {
  margin-bottom: 17px;
  color: var(--body-color);
}
.woocommerce-checkout-payment ul input[type=radio] ~ label img {
  margin-bottom: -2px;
  margin-left: 10px;
}
.woocommerce-checkout-payment .place-order {
  padding-top: 30px;
}
.woocommerce-checkout-payment .payment_box {
  color: #a1b1bc;
  background-color: #ecf0f1;
  border: 1px solid #d8d8d8;
  border-bottom: none;
  font-size: 14px;
  padding: 10px 20px;
  border-radius: 4px;
  display: none;
}
.woocommerce-checkout-payment .payment_box p {
  margin: 0;
}

.as-checkout-wrapper form.woocommerce-form {
  margin-bottom: 25px;
}

/* Small devices */
@media (max-width: 767px) {
  tfoot.checkout-ordertable th {
    display: none;
  }
  .woocommerce-checkout-payment ul input[type=radio] ~ label img {
    max-width: 150px;
  }
  .checkout-ordertable th,
  .checkout-ordertable td {
    padding: 5px 20px 5px 60px;
  }
}
/*------------------- 4.00. Wishlist  -------------------*/
.tinv-wishlist input[type=checkbox] {
  display: inline-block;
  opacity: 1;
  visibility: visible;
  vertical-align: middle;
  width: auto;
  height: auto;
}
.tinv-wishlist .tinv-header {
  margin-top: -0.8rem;
}
.tinv-wishlist .cart-empty {
  padding: 12px 25px;
  background-color: #eee;
  border-radius: 5px;
  font-weight: 700;
  font-size: 14px;
}
.tinv-wishlist p.return-to-shop .button {
  display: inline-block;
  background-color: var(--theme-color);
  color: #fff;
  font-size: 14px;
  padding: 10px 25px;
  margin-top: 10px;
  font-weight: 700;
}
.tinv-wishlist p.return-to-shop .button:Hover {
  background-color: var(--title-color);
  color: var(--white-color);
}
.tinv-wishlist table {
  border: none;
}
.tinv-wishlist table th {
  color: var(--title-color);
}
.tinv-wishlist table td, .tinv-wishlist table th {
  padding: 15.3px 10px;
  border-bottom: 1px solid var(--border-color);
  text-align: center;
}
.tinv-wishlist table thead {
  background-color: var(--smoke-color);
}
.tinv-wishlist .product-cb,
.tinv-wishlist .product-remove {
  width: 40px;
  text-align: center;
}
.tinv-wishlist .product-thumbnail {
  width: 110px;
}
.tinv-wishlist .stock.in-stock {
  margin-bottom: 0;
}
.tinv-wishlist ins {
  text-decoration: none;
}
.tinv-wishlist .product-remove button {
  border: none;
  height: 22px;
  width: 22px;
  text-align: center;
  font-size: 12px;
  line-height: 22px;
  border-radius: 0;
  padding-top: 0;
}
.tinv-wishlist .product-remove button i {
  line-height: 22px;
  font-size: 16px;
}
.tinv-wishlist .tinvwl-mobile {
  display: none;
}
.tinv-wishlist .social-buttons {
  display: flex;
  max-width: 295px;
  margin-left: auto;
  align-items: center;
}
.tinv-wishlist .social-buttons ul {
  padding-left: 0;
  margin-bottom: 0;
  margin-left: auto;
  display: flex;
  gap: 6px;
}
.tinv-wishlist table.tinvwl-table-manage-list {
  font-size: 16px;
}
.tinv-wishlist .product-stock .stock {
  display: block;
}
.tinv-wishlist .product-stock span {
  display: inline;
}
.tinv-wishlist .product-stock i {
  margin-right: 5px;
}
.tinv-wishlist .tinv-modal .icon_big_times {
  margin-bottom: 5px;
  color: var(--theme-color);
}
.tinv-wishlist button.button {
  border: none;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  font-weight: 600;
  background-color: var(--theme-color);
  color: #fff;
  padding: 1px 15px;
  min-width: 140px;
}
.tinv-wishlist button.button.mask-btn {
  padding: 0;
}
.tinv-wishlist button.button .btn-text-mask {
  padding: 0.5px 21px;
}
.tinv-wishlist button.button:hover {
  background-color: var(--title-color);
  color: #fff;
}
.tinv-wishlist button.button i {
  font-size: 14px !important;
  margin-right: 3px !important;
}
.tinv-wishlist th,
.tinv-wishlist td.product-name {
  font-size: 16px;
  font-weight: 700;
  font-family: var(--title-font);
}
.tinv-wishlist td.product-name a {
  color: var(--body-color);
}
.tinv-wishlist td.product-name a:hover {
  color: var(--theme-color);
}
.tinv-wishlist td.product-price del {
  margin-left: 8px;
  font-size: 0.9em;
}
.tinv-wishlist .social-buttons > span {
  font-weight: 700;
  margin-right: 10px;
  font-family: var(--title-font);
  color: var(--title-color);
}
.tinv-wishlist .social-buttons li {
  display: inline-block;
  margin-right: 0;
}
.tinv-wishlist .social-buttons li a.social {
  background-color: var(--theme-color);
  color: #fff;
  width: 30px;
  height: 30px;
  line-height: 30px;
  font-size: 14px;
  display: inline-block;
  text-align: center;
  border-radius: 50px;
  margin-left: 3px;
}
.tinv-wishlist .social-buttons li a.social:first-child {
  margin-left: 0;
}
.tinv-wishlist .social-buttons li a.social i {
  line-height: inherit;
}
.tinv-wishlist .social-buttons li a.social:hover {
  background-color: var(--title-color);
  color: var(--white-color);
}

/* Medium devices */
@media (max-width: 991px) {
  .tinvwl-full {
    display: none;
  }
  .tinv-wishlist .tinvwl-mobile {
    display: block;
  }
  .tinvwl-txt {
    display: none !important;
  }
  .product-stock {
    width: 40px;
    text-align: center;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .tinv-wishlist table {
    table-layout: fixed;
    border-bottom: 1px solid var(--border-color);
  }
  .tinv-wishlist table.tinvwl-table-manage-list tbody td.product-remove, .tinv-wishlist table.tinvwl-table-manage-list thead th:not(.product-name) {
    display: none;
  }
  .tinv-wishlist table td, .tinv-wishlist table th {
    border: 1px solid var(--border-color);
  }
  .tinv-wishlist table.tinvwl-table-manage-list tbody td {
    display: block;
    width: 100% !important;
    text-align: center;
  }
  .product-name {
    text-align: center;
  }
  .tinv-wishlist table td,
  .tinv-wishlist table th {
    border-bottom: none;
  }
  .tinv-wishlist table tfoot {
    border-bottom: 1px solid var(--border-color);
  }
  .tinv-wishlist .social-buttons {
    max-width: 100%;
    margin-left: unset;
    flex-direction: column;
  }
  .tinv-wishlist .social-buttons ul {
    margin-left: unset;
    margin-top: 5px;
  }
  .tinvwl-txt {
    display: inline-block !important;
  }
}
/*------------------- 4.00. Contact  -------------------*/
.contact-card {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #2F2F2F;
}
.contact-card .info-card {
  padding: 60px 0;
  display: flex;
  gap: 20px;
  align-items: center;
}
.contact-card .info-card:not(:first-child) {
  border-left: 1px solid #2F2F2F;
  padding-left: 100px;
}
.contact-card .info-card_icon {
  background: #1B1B1B;
  border: 1px solid #2B2B2B;
  box-shadow: 0px 7px 64px #090909;
  display: inline-block;
  flex: none;
  height: 70px;
  width: 70px;
  line-height: 70px;
  text-align: center;
  border-radius: 50%;
  font-size: 24px;
  color: var(--white-color);
}
.contact-card .info-card_text {
  font-weight: 500;
  font-size: 14px;
  font-family: var(--title-font);
  color: var(--white-color);
  text-transform: uppercase;
  margin-bottom: 4px;
}
.contact-card .info-card_link {
  font-weight: 500;
  font-size: 22px;
  font-family: var(--title-font);
  color: var(--white-color);
}
.contact-card .info-card_link:hover {
  color: var(--theme-color);
}
.contact-card.style2 {
  background: var(--white-color);
  border: 0;
  box-shadow: 0px 17px 38px rgba(0, 0, 0, 0.16);
  padding: 0 90px 0 0;
  transform: translate(0, 50%);
  position: relative;
  z-index: 1;
  margin-top: -70.5px;
}
.contact-card.style2 .info-card {
  padding: 35px 0;
  margin-left: 40px;
}
.contact-card.style2 .info-card .info-card_icon {
  box-shadow: none;
}
.contact-card.style2 .info-card.style2 {
  display: block;
  padding: 35px 150px;
  text-align: center;
}
.contact-card.style2 .info-card .info-card_title {
  font-size: 14px;
  font-weight: 500;
  color: var(--white-color);
  text-transform: uppercase;
}
.contact-card.style2 .info-card:not(:first-child) {
  border: 0;
}
.contact-card.style2 .info-card_text {
  color: var(--title-color);
}
.contact-card.style2 .info-card_link {
  color: var(--title-color);
}
.contact-card.style2 .info-card_link:hover {
  color: var(--theme-color);
}

.contact-social-wrap {
  border-top: 1px solid var(--border-color);
  margin-top: 50px;
  padding-top: 50px;
  display: flex;
  align-items: center;
}
.contact-social-wrap p {
  margin-bottom: 0;
}
.contact-social-wrap .social-btn {
  flex: none;
  margin-left: auto;
}

.map-sec {
  line-height: 0;
  filter: grayscale(1);
}
.map-sec iframe {
  width: 100%;
  height: 400px;
  /* Small devices */
}
@media (max-width: 767px) {
  .map-sec iframe {
    height: 300px;
  }
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .contact-card.style2 {
    padding: 0 40px 0 0;
  }
}
/* Medium Large devices */
@media (max-width: 1299px) {
  .contact-card .info-card_icon {
    height: 60px;
    width: 60px;
    line-height: 60px;
    font-size: 20px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .contact-card .info-card:not(:first-child) {
    padding-left: 30px;
  }
  .contact-card.style2 .info-card:not(:first-child) {
    padding-left: 0;
  }
  .contact-card.style2 .info-card.style2 {
    padding: 35px 70px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .contact-card {
    display: block;
    padding: 35px 0;
    text-align: center;
  }
  .contact-card .info-card {
    padding: 15px 0;
    display: inline-flex;
    padding-right: 50px;
    text-align: left;
  }
  .contact-card .info-card:not(:first-child) {
    border-left: 0;
    padding-left: 0;
  }
  .contact-card.style2 {
    transform: none;
    margin-top: 0;
    margin-bottom: 80px;
    padding: 0;
    text-align: center;
  }
  .contact-card.style2 .info-card {
    margin-left: 0;
  }
  .contact-card.style2 .info-card .social-btn {
    justify-content: center;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .contact-card {
    text-align: left;
  }
}
/* Extra small devices */
@media (max-width: 375px) {
  .contact-card .info-card {
    padding-right: 0;
  }
}
/****contact page*****/
.contact-info {
  display: flex;
  gap: 20px;
  background: #FFFFFF;
  border: 1px solid #EAEAEA;
  border-radius: 10px;
  padding: 70px;
}
.contact-info_icon {
  flex: none;
}
.contact-info .contact-info_title {
  font-size: 14px;
  font-weight: 600;
  font-family: var(--title-font);
  color: var(--theme-color);
  text-transform: uppercase;
  display: block;
  margin-top: -0.3em;
}
.contact-info .contact-info_text {
  margin-bottom: -0.4em;
}
.contact-info .contact-info_text a {
  font-size: 24px;
  font-weight: 500;
  color: var(--title-color);
}
.contact-info .contact-info_text a:hover {
  color: var(--theme-color);
}

.map-sec2 {
  line-height: 0;
  filter: grayscale(1);
}
.map-sec2 iframe {
  width: 100%;
  height: 750px;
  /* Small devices */
}
@media (max-width: 767px) {
  .map-sec2 iframe {
    height: 500px;
  }
}

.contact-form-area {
  border-radius: 20px;
  margin-top: -300px;
  position: relative;
  z-index: 1;
}
.contact-form-area .contact-form-thumb {
  border-radius: 20px 0 0 20px;
  overflow: hidden;
  height: 100%;
}
.contact-form-area .contact-form-thumb img {
  height: 100%;
  object-fit: cover;
}
.contact-form-area .contact-form-wrap {
  padding: 80px 80px 80px 60px;
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .contact-info {
    padding: 50px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .contact-info {
    padding: 40px;
  }
  .contact-info .contact-info_text a {
    font-size: 20px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .contact-form-area .contact-form-thumb {
    border-radius: 15px 15px 0 0;
    height: auto;
  }
  .contact-form-area .contact-form-thumb img {
    width: 100%;
    border-radius: 15px 15px 0 0;
  }
  .contact-form-area .contact-form-wrap {
    padding: 60px;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .contact-form-area .contact-form-wrap {
    padding: 40px;
  }
}
/* Extra small devices */
@media (max-width: 375px) {
  .contact-form-area .contact-form-wrap {
    padding: 30px;
  }
}
/*------------------- 4.00. About  -------------------*/
/*----------------------------------------------
    # about style here
----------------------------------------------*/
.about-thumb {
  position: relative;
  padding-right: 68px;
  display: inline-block;
  height: 100%;
}
.about-thumb .about-img-1 {
  border-radius: 20px;
  height: 100%;
  object-fit: cover;
}
.about-thumb .about-img-2 {
  border-radius: 20px;
  position: absolute;
  bottom: 100px;
  left: 0;
  z-index: 1;
  filter: drop-shadow(0px 6px 42px rgba(0, 0, 0, 0.19));
}

.about-thumb-2 {
  position: relative;
}
.about-thumb-2 .about-thumb-num {
  position: absolute;
  left: 40px;
  bottom: 0;
  font-size: 120px;
  font-weight: 700;
  font-family: var(--title-font);
  color: transparent;
  -webkit-text-stroke: 1px var(--white-color);
  line-height: initial;
}
.about-thumb-2.style2 .about-thumb-num {
  bottom: auto;
  top: 0;
}

.goal-thumb-1 {
  position: relative;
  padding-right: 103px;
  height: 100%;
}
.goal-thumb-1 .goal-badge-wrap {
  position: absolute;
  right: 0;
  top: 92px;
  height: 234px;
  width: 234px;
  line-height: 256px;
  text-align: center;
  border-radius: 50%;
  background: var(--title-color);
}
.goal-thumb-1 .goal-badge-wrap .goal-badge {
  line-height: initial;
  display: inline-block;
  font-size: 16px;
  font-weight: 500;
  color: var(--white-color);
  font-family: var(--title-font);
  text-transform: uppercase;
  padding: 0 20px;
}
.goal-thumb-1 img {
  border-radius: 20px;
  height: 100%;
  object-fit: cover;
}

.about-tab-1 {
  margin-top: 41px;
}
.about-tab-1 .filter-menu-active {
  gap: 10px;
  display: flex;
  border-bottom: 1px solid #E6E6E6;
  padding-bottom: 25px;
}
.about-tab-1 button {
  border-radius: 5px;
  border: 0;
  background: var(--smoke-color);
  font-weight: 600;
  font-size: 14px;
  padding: 5.5px 16px;
}
.about-tab-1 button.active, .about-tab-1 button:hover {
  background: var(--theme-color);
  color: var(--white-color);
}
.about-tab-1 .filter-item {
  display: flex;
  gap: 30px;
  border-bottom: 1px solid #E6E6E6;
  padding: 30px 0;
}
.about-tab-1 .filter-item .about-tab-icon {
  flex: none;
}
.about-tab-1 .filter-item .about-tab-text {
  margin-bottom: -0.4em;
  margin-top: -0.4em;
  font-weight: 500;
}

.about-info-wrap {
  display: flex;
  gap: 20px;
}
.about-info-wrap .icon {
  height: 56px;
  width: 56px;
  line-height: 56px;
  border: 1px solid #dddddd;
  font-size: 18px;
  color: var(--theme-color);
  border-radius: 50%;
  text-align: center;
}
.about-info-wrap .icon .fa-phone-volume {
  transform: rotate(-30deg);
}
.about-info-wrap .about-info-title {
  font-size: 14px;
  font-weight: 500;
  font-family: var(--body-font);
  color: var(--title-color);
  margin-bottom: 0;
}
.about-info-wrap .about-info-link {
  font-size: 18px;
  font-weight: 700;
  font-family: var(--body-font);
  color: var(--title-color);
}
.about-info-wrap .about-info-link:hover {
  color: var(--theme-color);
}
.about-info-wrap.style2 {
  gap: 15px;
  align-items: center;
}
.about-info-wrap.style2 .icon {
  box-shadow: 0px 10px 21px rgba(0, 0, 0, 0.12);
  border: 0;
}
.about-info-wrap.style3 {
  gap: 20px;
  align-items: center;
}
.about-info-wrap.style3 .icon {
  color: var(--white-color);
}
.about-info-wrap.style3 .about-info-link {
  color: var(--white-color);
}
.about-info-wrap.style3 .about-info-link:hover {
  color: var(--theme-color);
}

.about-grid {
  display: flex;
  gap: 20px;
}
.about-grid:not(:last-child) {
  margin-bottom: 40px;
}
.about-grid_icon {
  width: 60px;
  height: 60px;
  border-radius: 10px;
  background: var(--theme-color);
  line-height: 60px;
  flex: none;
  text-align: center;
}
.about-grid_title {
  font-size: 22px;
  font-weight: 500;
  margin-bottom: 8px;
  margin-top: -0.3em;
}
.about-grid_text {
  margin-bottom: -0.3em;
}
.about-grid.style2 {
  align-items: center;
  gap: 15px;
  margin-bottom: 0;
}
.about-grid.style2 .about-grid_icon {
  background: var(--white-color);
  border-radius: 0;
  border: 1px solid #EDEDED;
  box-shadow: 0px 10px 21px rgba(0, 0, 0, 0.1);
}
.about-grid.style2 .about-grid_title {
  margin-bottom: 0;
}
.about-grid.style3 .about-grid_icon {
  border-radius: 0;
}
.about-grid.style4 {
  align-items: center;
  gap: 20px;
  margin-bottom: 0;
}
.about-grid.style4 ~ .about-grid.style4 {
  position: relative;
}
.about-grid.style4 ~ .about-grid.style4:after {
  content: "";
  position: absolute;
  left: -65px;
  top: 50%;
  height: 60px;
  width: 1px;
  background: #D9D9D9;
  transform: translate(0, -50%);
}
.about-grid.style4 .about-grid_icon {
  background: var(--white-color);
  border-radius: 0;
  border: 1px solid var(--white-color);
  height: 80px;
  width: 80px;
  line-height: 80px;
}
.about-grid.style4 .about-grid_icon img {
  width: 40px;
}
.about-grid.style4 .about-grid_counter {
  margin-bottom: -0.1em;
  font-size: 40px;
  font-weight: 600;
  margin-top: -0.3em;
}
.about-grid.style4 .about-grid_text {
  font-size: 14px;
  font-weight: 500;
  font-family: var(--title-font);
  color: var(--title-color);
  text-transform: uppercase;
}

.about-grid-wrap {
  display: flex;
    flex-wrap: wrap;
  gap: 75px;
  padding-bottom: 40px;
  border-bottom: 1px solid #EEEEEE;
}
.about-grid-wrap.style2 {
  border-bottom: 0;
  padding-bottom: 0;
  gap: 130px;
}

.goal-grid-wrap {
  max-width: 518px;
}
.goal-grid-wrap .btn-wrap {
  gap: 20px;
  margin-top: 50px;
}
.goal-grid-wrap .btn-wrap .btn.btn-border {
  padding: 19.5px 41px 19.5px 45px;
}

.goal-thumb-2 {
  position: relative;
  padding-left: 144px;
  height: 100%;
}
.goal-thumb-2 .img-1 {
  height: 100%;
}
.goal-thumb-2 .img-1 img {
  border-radius: 20px;
  height: 100%;
  object-fit: cover;
}
.goal-thumb-2 .img-2 {
  position: absolute;
  left: 0px;
  top: 60px;
  filter: drop-shadow(0px 6px 42px rgba(0, 0, 0, 0.19));
}
.goal-thumb-2 .img-2 img {
  border-radius: 20px;
}
.goal-thumb-2 .wcu-grid {
  position: absolute;
  left: 174px;
  bottom: 30px;
}

.goal-thumb-3 {
  position: relative;
  display: inline-block;
}
.goal-thumb-3 .wcu-grid {
  position: absolute;
  left: 30px;
  bottom: 30px;
}

.goal-thumb-4 {
  margin-left: 75px;
  margin-right: -246px;
}

@media (max-width: 1600px) {
  .goal-thumb-4 {
    margin-left: 40px;
    margin-right: -90px;
  }
}
/* Extra large devices */
@media (max-width: 1500px) {
  .about-grid-wrap {
    gap: 25px;
  }
  .goal-thumb-4 {
    margin-left: 0;
    margin-right: 0;
  }
}
/* Medium Large devices */
@media (max-width: 1399px) {
  .about-grid-wrap {
    gap: 15px;
    justify-content: space-between;
    margin-bottom: 25px;
  }
  .goal-thumb-2 .wcu-grid {
    left: 0;
  }
  .about-content-wrap .checklist li {
    font-size: 15px;
  }
  .about-grid-wrap.style2 {
    gap: 100px;
  }
  .about-grid.style4 ~ .about-grid.style4:after {
    left: -50px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .about-content-wrap .btn-wrap {
    gap: 20px 25px;
  }
  .goal-thumb-1 .goal-badge-wrap {
    height: 204px;
    width: 204px;
    line-height: 236px;
  }
  .goal-thumb-1 .goal-badge-wrap .goal-badge {
    font-size: 15px;
  }
  .about-grid-wrap {
    gap: 50px;
    justify-content: start;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .goal-thumb-2,
  .goal-thumb-1,
  .about-thumb {
    height: auto;
    display: inline-block;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .about-grid-wrap {
    gap: 40px;
  }
  .goal-thumb-2 {
    padding-left: 0;
  }
  .goal-thumb-2 .img-2 {
    left: 30px;
  }
  .goal-grid-wrap .btn-wrap .btn.btn-border {
    padding: 17px 41px 17px 45px;
  }
  .about-grid-wrap.style2 {
    gap: 70px;
  }
  .about-grid.style4 ~ .about-grid.style4:after {
    left: -35px;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .about-thumb .about-img-2 {
    bottom: 50px;
  }
  .about-grid-wrap {
    display: block;
  }
  .about-grid-wrap .about-grid ~ .about-grid {
    margin-top: 30px;
  }
  .goal-thumb-2 .img-2 {
    display: none;
  }
  .goal-thumb-3 .wcu-grid {
    position: initial;
    animation: none;
    display: flex;
    margin-top: 30px;
  }
  .about-thumb-2 .about-thumb-num {
    font-size: 100px;
  }
  .about-grid.style4 ~ .about-grid.style4:after {
    display: none;
  }
}
/* Extra small devices */
@media (max-width: 375px) {
  .about-thumb {
    padding-right: 38px;
  }
  .about-thumb .about-img-2 {
    width: 180px;
  }
  .about-tab-1 button {
    padding: 10px 16px;
    line-height: 1.3;
  }
  .about-tab-1 .filter-item {
    display: block;
  }
  .about-tab-1 .filter-item .about-tab-icon {
    margin-bottom: 20px;
  }
  .goal-thumb-1 {
    position: relative;
    padding-right: 73px;
  }
  .goal-thumb-1 .goal-badge-wrap {
    height: 154px;
    width: 154px;
    line-height: 196px;
    top: 50px;
  }
  .goal-thumb-1 .goal-badge-wrap .goal-badge {
    font-size: 13px;
  }
  .goal-thumb-2 .wcu-grid {
    margin-top: 20px;
    position: initial;
    animation: none;
  }
}
/*------------------- 4.00. Team  -------------------*/
/* Team global ---------------------------------- */
.team-card {
  background: var(--smoke-color2);
  border-radius: 20px;
  padding: 30px;
  transition: 0.4s;
  border: 1px solid transparent;
  margin-bottom: 60px;
}
.team-card_img {
  position: relative;
  border-radius: 20px;
}
.team-card_img img {
  border-radius: 20px;
  width: 100%;
}
.team-card_content {
  text-align: center;
  position: relative;
}
.team-card_content .social-btn {
  justify-content: center;
  margin-top: 0px;
  opacity: 0;
  transition: 0.4s;
  margin-bottom: -45px;
}
.team-card_title {
  font-size: 22px;
  font-weight: 500;
  margin-top: -0.2em;
  margin-bottom: -5px;
  margin-top: 23px;
}
.team-card_title a {
  color: var(--title-color);
}
.team-card_title a:hover {
  color: var(--theme-color);
}
.team-card_desig {
  font-weight: 300;
  font-size: 14px;
  font-family: var(--title-font);
  color: var(--body-color);
}
.team-card:hover {
  background: var(--white-color);
  box-shadow: 0px 16px 51px rgba(0, 0, 0, 0.07);
  border: 1px solid rgba(237, 237, 237, 0.89);
  margin-bottom: 0;
}
.team-card:hover .team-card_content .social-btn {
  margin-top: 15px;
  opacity: 1;
  margin-bottom: 0;
}
.team-card.style2 {
  padding: 0;
  border-radius: 0;
  border: 0;
  background: transparent;
  position: relative;
  margin-bottom: 0;
}
.team-card.style2 .team-card_img {
  border-radius: 0;
}
.team-card.style2 .team-card_img img {
  border-radius: 0;
}
.team-card.style2 .team-card_content {
  position: absolute;
  left: 0;
  bottom: 0;
  text-align: left;
}
.team-card.style2 .team-card_content .social-btn {
  opacity: 1;
  transform: none;
  margin-bottom: 0;
  justify-content: start;
  gap: 5px;
}
.team-card.style2 .team-card_content .social-btn a {
  border-radius: 0;
}
.team-card.style2 .team-card_title {
  background: var(--white-color);
  font-size: 20px;
  font-weight: 700;
  text-transform: uppercase;
  padding: 14px 26px;
  margin-bottom: 0;
  margin-top: 0;
}
.team-card.style2 .team-card_desig {
  background: var(--theme-color);
  display: inline-block;
  color: var(--white-color);
  font-size: 14px;
  font-weight: 400;
  text-transform: uppercase;
  font-family: var(--title-font);
  padding: 5.5px 25px 3.5px;
}
.team-card.style2:hover {
  background: transparent;
  box-shadow: none;
  border: 0;
  margin-bottom: 0;
}
.team-card.style2:hover .team-card_content .social-btn {
  margin-top: 0;
}
.team-card.style3 {
  margin-bottom: 0px;
  padding: 30px 30px 20px;
}
.team-card.style3 .team-card_img {
  position: relative;
}
.team-card.style3 .team-card_img .social-btn {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translate(-50%, 0%);
  opacity: 0;
  transition: 0.4s;
}
.team-card.style3:hover .social-btn {
  opacity: 1;
  transform: translate(-50%, -30px);
}

.team-slider-1 .slick-current .team-card {
  background: var(--white-color);
  box-shadow: 0px 16px 51px rgba(0, 0, 0, 0.07);
  border: 1px solid rgba(237, 237, 237, 0.89);
  margin-bottom: 0;
}
.team-slider-1 .slick-current .team-card .team-card_content .social-btn {
  margin-top: 15px;
  opacity: 1;
  margin-bottom: 0;
}

.team-card2 {
  display: flex;
  gap: 30px;
  align-items: center;
}
.team-card2 .team-card_img {
  flex: none;
  border-radius: 50%;
}
.team-card2 .team-card_img img {
  border-radius: 50%;
}
.team-card2 .team-card_content {
  text-align: start;
}
.team-card2 .team-card_subtitle {
  text-transform: uppercase;
  color: var(--theme-color);
  font-size: 14px;
  font-weight: 400;
  font-family: var(--title-font);
}
.team-card2 .team-card_title {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 10px;
  margin-top: 0;
}
.team-card2 .team-card_title a {
  color: var(--white-color);
  background-image: linear-gradient(to left, var(--white-color), var(--white-color));
  background-repeat: no-repeat;
  background-position: bottom left;
  background-size: 0 2px;
}
.team-card2 .team-card_title a:hover {
  background-size: 100% 2px;
}
.team-card2 .link-btn {
  color: var(--white-color);
}
.team-card2 .link-btn i {
  transform: rotate(-25deg);
  color: var(--theme-color);
}
.team-card2 .link-btn:before {
  display: none;
}
.team-card2 .link-btn:hover {
  color: var(--theme-color);
}
.team-card2.style2 {
  background: var(--white-color);
  border: 1px solid rgba(237, 237, 237, 0.89);
  box-shadow: 0px 16px 51px rgba(0, 0, 0, 0.07);
  border-radius: 15px;
  padding: 40px;
  gap: 20px;
}
.team-card2.style2 .team-card_title {
  margin-bottom: 3px;
}
.team-card2.style2 .team-card_title a {
  color: var(--title-color);
}
.team-card2.style2 .team-card_title a:hover {
  color: var(--theme-color);
}
.team-card2.style2 .team-card_link {
  font-size: 16px;
  font-weight: 400;
  font-family: var(--title-font);
  color: var(--title-color);
}
.team-card2.style2 .team-card_link i {
  color: var(--theme-color);
  transform: rotate(-25deg);
  margin-right: 8px;
}
.team-card2.style2 .team-card_link:hover {
  color: var(--theme-color);
}

.team-slider-2 {
  margin-left: auto;
  margin-right: -38px;
  max-width: 1660px;
}

/* Hight Resoulation devices */
@media (min-width: 1922px) {
  .team-slider-2 {
    max-width: 1980px;
  }
}
@media (max-width: 1600px) {
  .team-slider-2 {
    max-width: 1635px;
    margin-right: -170px;
  }
}
/* Extra large devices */
@media (max-width: 1500px) {
  .team-slider-2 {
    max-width: 1575px;
    margin-right: -190px;
  }
}
/* Medium Large devices */
@media (max-width: 1399px) {
  .team-slider-2 {
    max-width: 1398px;
    margin-right: -150px;
  }
  .team-card2.style2 {
    padding: 30px;
  }
  .team-card2.style2 .team-card_title {
    font-size: 22px;
  }
}
/* Medium Large devices */
@media (max-width: 1299px) {
  .team-slider-2 {
    max-width: 1290px;
    margin-right: -120px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .team-slider-2 {
    max-width: 1230px;
    margin-right: -241px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .team-slider-2 {
    max-width: 860px;
    margin-right: -120px;
  }
  .team-card2.style2 .team-card_title {
    font-size: 20px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .team-slider-2 {
    max-width: 567px;
    margin-right: 0;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .team-slider-2 {
    margin: 0 3px;
  }
}
/* Extra small devices */
@media (max-width: 375px) {
  .team-card2 {
    display: block;
    text-align: center;
  }
  .team-card2 .team-card_img img {
    width: auto;
  }
  .team-card2 .team-card_content {
    text-align: center;
    margin-top: 20px;
  }
  .team-card2.style2 .team-card_content {
    margin-top: 15px;
  }
}
/* Team Details ---------------------------------- */
.team-details-page .contact-form-wrap {
  border-radius: 6px;
}
.team-details-page .contact-form-wrap .default-title {
  margin-bottom: 24px;
}

.team-about-card {
  background-color: var(--title-color);
  border-radius: 20px;
}
.team-about-card_img {
  border-radius: 20px 0 0 20px;
  overflow: hidden;
}
.team-about-card_img img {
  border-radius: 20px 0 0 20px;
  object-fit: cover;
}
.team-about-card_box {
  padding: 90px 95px 90px 60px;
  border-radius: 0 6px 6px 0;
}
.team-about-card_title {
  margin-top: -0.3em;
  margin-bottom: 6px;
  font-size: 40px;
  font-weight: 500;
  color: var(--white-color);
}
.team-about-card_desig {
  font-size: 16px;
  font-weight: 400;
  color: var(--body-color);
  margin-bottom: 25px;
}
.team-about-card .social-btn {
  border-bottom: 1px solid #232325;
  padding-bottom: 40px;
  margin-bottom: 30px;
}
.team-about-card .social-btn a {
  border-radius: 50%;
}
.team-about-card_text {
  margin: 14px 0 27px;
}
.team-about-card_info {
  margin-bottom: 0px;
  display: flex;
  align-items: center;
}
.team-about-card_info .icon {
  height: 50px;
  width: 50px;
  line-height: 50px;
  background: var(--white-color);
  border-radius: 50%;
  display: inline-block;
  text-align: center;
  color: var(--theme-color);
  box-shadow: 0px 9px 73px 0px rgba(90, 86, 86, 0.15);
  margin-right: 15px;
  margin-bottom: -5px;
}
.team-about-card_info p {
  font-size: 18px;
  font-weight: 600;
  color: var(--white-color);
  margin-bottom: -0.4em;
}
.team-about-card_info span {
  display: block;
  font-family: var(--body-font);
  color: var(--body-color);
  font-size: 16px;
  font-weight: 400;
  margin-top: -0.3em;
}
.team-about-card_info:last-child {
  margin-bottom: 0;
}
.team-about-card .team-about-card_info ~ .team-about-card_info {
  margin-top: 15px;
}

/* Extra small devices */
@media (max-width: 575px) {
  .team-card {
    background: var(--white-color);
    box-shadow: 0px 16px 51px rgba(0, 0, 0, 0.07);
    border: 1px solid rgba(237, 237, 237, 0.89);
    margin-bottom: 0;
  }
  .team-card .team-card_content .social-btn {
    margin-top: 15px;
    opacity: 1;
    margin-bottom: 0;
  }
}
/*------------------- 4.00. Testimonial  -------------------*/
/* Testimonial 1 ---------------------------------- */
.testimonial-bg-thumb1 {
  position: relative;
}
.testimonial-bg-thumb1 .thumb {
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 0 30px 30px 0;
  z-index: -1;
  min-height: 784px;
}
.testimonial-bg-thumb1 .thumb img {
  border-radius: 0 30px 30px 0;
}

.testi-box-wrap1 {
  border-radius: 30px;
  padding: 100px 95px;
}

.testi-box {
  position: relative;
  padding-top: 15px;
}
.testi-box_thumb {
  border-radius: 6px;
  position: relative;
  display: inline-block;
}
.testi-box_thumb img {
  border-radius: 50%;
}
.testi-box_thumb .block-quote {
  position: absolute;
  top: 0;
  left: 0;
  height: 50px;
  width: 50px;
  line-height: 46px;
  border: 4px solid var(--title-color);
  background: var(--theme-color);
  color: var(--white-color);
  font-size: 18px;
  border-radius: 50%;
}
.testi-box_text {
  margin-top: 33px;
  margin-bottom: 25px;
  line-height: 1.667;
  font-size: 18px;
  font-weight: 500;
}
.testi-box .rating {
  color: var(--theme-color);
  font-size: 16px;
}
.testi-box_profile .testi-box_name {
  margin-bottom: -0.15em;
  margin-top: -0.35em;
  font-size: 20px;
  font-weight: 500;
  font-family: var(--title-font);
  color: var(--white-color);
}
.testi-box_profile .testi-box_desig {
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 500;
  font-family: var(--title-font);
  color: var(--body-color);
  display: block;
  margin-bottom: -0.65em;
}

.testi-slider-controller {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-top: 60px;
}
.testi-slider-controller .indicatior-btn {
  background: #202020;
  border: 1px solid #303030;
  border-radius: 10px;
  padding: 25px 30px;
  position: relative;
  min-width: 183px;
}
.testi-slider-controller .indicatior-btn:after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  border-radius: 10px;
  border: 1px dashed rgba(255, 255, 255, 0.29);
  margin: 7px;
  opacity: 0;
  transition: 0.4s;
}
.testi-slider-controller .indicatior-btn.active, .testi-slider-controller .indicatior-btn:hover {
  background: var(--theme-color);
  border-color: var(--theme-color);
}
.testi-slider-controller .indicatior-btn.active .testi-box_desig, .testi-slider-controller .indicatior-btn:hover .testi-box_desig {
  color: var(--white-color);
}
.testi-slider-controller .indicatior-btn.active:after, .testi-slider-controller .indicatior-btn:hover:after {
  opacity: 1;
}
.testi-slider-controller .indicator {
  display: none;
}

/* Extra large devices */
@media (max-width: 1500px) {
  .testimonial-bg-thumb1 .thumb {
    width: 65%;
  }
  .testi-slider-controller {
    margin-top: 40px;
  }
  .testi-box {
    padding-top: 0px;
  }
}
/* Medium Large devices */
@media (max-width: 1399px) {
  .testi-box-wrap1 {
    padding: 75px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .testi-box-wrap1 {
    padding: 50px;
  }
  .testimonial-bg-thumb1 .thumb img {
    height: 674px;
    object-fit: cover;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .testi-box-wrap1 {
    padding: 60px 50px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .testi-slider-controller {
    display: inline-block;
    margin: -10px;
    margin-top: 30px;
  }
  .testi-slider-controller .indicatior-btn {
    margin: 10px;
    display: block;
  }
  .testi-box_text {
    font-size: 16px;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .testi-box-wrap1 {
    padding: 50px 30px;
  }
  .testi-slider-controller {
    margin: 0;
    margin-top: 30px;
  }
  .testi-slider-controller .indicatior-btn {
    min-width: auto;
    width: 100%;
    margin: 10px 0;
  }
}
.testi-box-wrap2 {
  position: relative;
  padding: 0 37px;
}
.testi-box-wrap2:after {
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) rotate(-13.84deg);
  width: 671px;
  height: 351px;
  border: 1px solid rgba(27, 27, 27, 0.08);
  border-radius: 50%;
  z-index: -1;
}
.testi-box-wrap2 .testi-arrow .slick-arrow {
  background: transparent;
  box-shadow: none;
  opacity: 1;
  top: 50%;
  left: -100px;
  font-size: 40px;
  color: var(--body-color);
  margin-top: 0;
}
.testi-box-wrap2 .testi-arrow .slick-arrow.slick-next {
  margin-top: 0;
  left: auto;
  right: -100px;
}
.testi-box-wrap2 .testi-arrow .slick-arrow:hover {
  color: var(--title-color);
}

.testi-box.style2 {
  padding: 0;
}
.testi-box.style2 .testi-box_text {
  font-size: 24px;
  font-weight: 500;
  font-style: italic;
  font-family: var(--title-font);
  text-transform: uppercase;
  color: var(--title-color);
  margin-top: 28px;
  margin-bottom: 32px;
}
.testi-box.style2 .testi-box_profile .testi-box_name {
  color: var(--theme-color);
  font-size: 20px;
  font-weight: 600;
  text-transform: uppercase;
}
.testi-box.style2 .testi-box_profile .testi-box_desig {
  font-weight: 400;
}

/* Large devices */
@media (max-width: 1199px) {
  .testi-box.style2 .testi-box_text {
    font-size: 20px;
  }
  .testi-box-wrap2 .testi-arrow .slick-arrow {
    left: -80px;
  }
  .testi-box-wrap2 .testi-arrow .slick-arrow.slick-next {
    right: -80px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .testi-box-wrap2 {
    padding: 0;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .testi-box.style2 .testi-box_text {
    font-size: 16px;
  }
}
/* Extra small devices */
@media (max-width: 375px) {
  .testi-box.style2 .testi-box_text {
    font-size: 14px;
  }
}
/*------------------- 4.00. Counter  -------------------*/
/* Counter 1 ---------------------------------- */
.counter-area-1 {
  background-attachment: fixed;
}

.counter-area-2 {
  border-top: 1px solid #2C2C2C;
  border-bottom: 1px solid #2C2C2C;
  margin-bottom: 100px;
}

.counter-sec-shape-top {
  margin-top: -45px;
}

.counter-sec-shape-bottom {
  transform: rotateY(180deg) translateY(1px);
  margin-top: -2px;
  margin-bottom: -18px;
}

.counter-card {
  position: relative;
  display: inline-flex;
  gap: 25px;
  align-items: center;
}
.counter-card_wrap ~ .counter-card_wrap {
  border-left: 1px solid #2C2C2C;
}
.counter-card_wrap ~ .counter-card_wrap .counter-card.style2 {
  padding-left: 45px;
}
.counter-card_wrap:last-child .counter-card.style2 {
  padding-right: 0;
}
.counter-card_number {
  margin-top: 0px;
  margin-bottom: 2px;
  font-weight: 600;
  font-size: 44px;
  color: var(--white-color);
}
.counter-card_text {
  font-weight: 500;
  font-size: 14px;
  font-family: var(--title-font);
  color: #E1E1E1;
  margin-bottom: -0.4em;
}
.counter-card_icon {
  border-radius: 50px;
  width: 90px;
  height: 90px;
  line-height: 90px;
  background-color: var(--theme-color);
  text-align: center;
  position: relative;
  margin: auto;
}
.counter-card_icon img {
  transition: 0.4s all ease-in-out;
}
.counter-card:hover .counter-card_icon:after {
  margin: 0;
  border-width: 10px;
  border-color: var(--theme-color);
}
.counter-card:hover .counter-card_icon img {
  transform: rotateY(180deg);
}
.counter-card.style2 {
  padding: 60px 0;
  padding-right: 30px;
}
.counter-card.style2 .counter-card_icon {
  background: var(--title-color);
}
.counter-card.style2 .counter-card_number {
  font-size: 48px;
  font-weight: 600;
  margin-bottom: -4px;
}
.counter-card.style2 .counter-card_text {
  text-transform: uppercase;
  color: #656565;
}
.counter-card.style3 {
  display: block;
  text-align: center;
  padding: 60px 30px;
}
.counter-card.style3 .counter-circle {
  position: relative;
  margin-bottom: 23px;
}
.counter-card.style3 .circle-num {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 30px;
  font-weight: 600;
  font-family: var(--title-font);
  color: var(--white-color);
}
.counter-card.style3 .counter-card_text {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: -0.6em;
}

.ad-slider {
  overflow: hidden;
}
.ad-slider .global-carousel {
  margin: 0 -220px;
}
.ad-slider_title {
  font-size: 200px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.07);
  display: flex;
  margin-top: -0.05em;
  margin-bottom: 0;
  line-height: 0.7;
}
.ad-slider_title span {
  color: transparent;
  -webkit-text-stroke: 1px rgba(255, 255, 255, 0.07);
  display: inline-block;
  margin: 0 30px;
}
.ad-slider_title ~ .ad-slider_title {
  margin-left: 50px;
}

/* Hight Resoulation devices */
@media (min-width: 1922px) {
  .counter-sec-shape-bottom,
  .counter-sec-shape-top {
    width: 100%;
  }
  .counter-sec-shape-bottom img,
  .counter-sec-shape-top img {
    width: 100%;
  }
  .counter-sec-shape-top {
    margin-top: -60px;
    transform: translateY(-1px);
    margin-bottom: -3px;
  }
  .counter-sec-shape-bottom {
    margin-top: -3px;
  }
}
@media (max-width: 1600px) {
  .ad-slider .global-carousel {
    margin: 0 -370px;
  }
}
/* Extra large devices */
@media (max-width: 1500px) {
  .ad-slider_title {
    font-size: 180px;
  }
}
/* Medium Large devices */
@media (max-width: 1399px) {
  .counter-sec-shape-top {
    margin-top: -35px;
  }
  .counter-sec-shape-bottom {
    margin-bottom: -15px;
  }
  .counter-card.style2 {
    padding-right: 20px;
    gap: 20px;
  }
  .counter-card_wrap ~ .counter-card_wrap .counter-card.style2 {
    padding-left: 20px;
  }
  .counter-card_icon {
    width: 80px;
    height: 80px;
    line-height: 80px;
  }
  .counter-card.style2 .counter-card_number {
    font-size: 40px;
    margin-bottom: 0;
  }
  .ad-slider_title {
    font-size: 170px;
  }
}
/* Medium Large devices */
@media (max-width: 1299px) {
  .ad-slider_title {
    font-size: 160px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .counter-wrap1 {
    text-align: center;
  }
  .counter-wrap1 .counter-card .media-body {
    text-align: start;
  }
  .counter-card_wrap:nth-child(3) {
    border-left: 0;
    border-top: 1px solid #2C2C2C;
  }
  .counter-card_wrap:nth-child(3) .counter-card.style2 {
    padding-left: 0px;
  }
  .counter-card_wrap:nth-child(4) {
    border-top: 1px solid #2C2C2C;
  }
  .ad-slider_title {
    font-size: 140px;
  }
  .counter-card.style3 {
    padding: 50px 30px;
  }
  .counter-card.style3 .counter-card_text {
    font-size: 16px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .counter-sec-shape-top {
    margin-top: -20px;
  }
  .counter-sec-shape-bottom {
    margin-bottom: -5px;
  }
  .ad-slider_title {
    font-size: 120px;
  }
  .counter-area-2 {
    margin-bottom: 80px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .counter-area-2 {
    padding: 30px 0;
  }
  .counter-card_wrap ~ .counter-card_wrap {
    border: 0;
  }
  .counter-card_wrap ~ .counter-card_wrap .counter-card.style2 {
    padding-left: 0;
  }
  .counter-card.style2 {
    padding: 20px 0;
  }
  .counter-card.style2 .counter-card_number {
    margin-top: -0.3em;
    font-size: 36px;
  }
  .ad-slider_title {
    font-size: 100px;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .counter-sec-shape-top {
    transform: translateY(-1px);
    margin-top: -25px;
    margin-bottom: -2px;
  }
  .counter-sec-shape-bottom {
    margin-top: -6px;
  }
  .counter-card_number {
    font-size: 34px;
  }
  .counter-card_wrap {
    text-align: center;
  }
  .counter-card.style2 {
    display: block;
  }
  .counter-card.style2 .counter-card_icon {
    margin-bottom: 20px;
  }
  .ad-slider_title {
    font-size: 90px;
  }
}
/* Extra small devices */
@media (max-width: 375px) {
  .counter-sec-shape-top {
    transform: translateY(-2px);
  }
}
@media (max-width: 320px) {
  .counter-sec-shape-top {
    transform: translateY(-3px);
    margin-top: -23px;
  }
  .counter-sec-shape-bottom {
    transform: rotateY(180deg) translateY(4px);
    margin-top: -12px;
  }
  .ad-slider_title {
    font-size: 80px;
  }
}
/*------------------- 4.00. Blog  -------------------*/
/* Blog Card ---------------------------------- */
.blog-card {
  background: var(--title-color);
  position: relative;
  border-radius: 15px;
}
.blog-card .blog-title {
  font-weight: 500;
  color: var(--white-color);
  margin-bottom: 10px;
}
.blog-card .blog-title a {
  background-image: linear-gradient(to left, var(--white-color), var(--white-color));
  background-repeat: no-repeat;
  background-position: bottom left;
  background-size: 0 2px;
}
.blog-card .blog-title a:hover {
  color: var(--white-color);
}
.blog-card .blog-title:hover a {
  background-size: 100% 2px;
}
.blog-card .blog-img {
  width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 3;
  border-radius: 15px 15px 0 0;
}
.blog-card .blog-img img {
  max-width: 100%;
  border-radius: 15px 15px 0 0;
  transition: 0.4s ease-in-out;
}
.blog-card .blog-content {
  border-radius: 0 0 15px 15px;
  padding: 40px;
  position: relative;
  z-index: 2;
  transition: 0.4s;
    direction: rtl;
}
.blog-card .blog-meta {
  margin-top: -0.4em;
  margin-bottom: 12px;
}
.blog-card .blog-meta a {
  font-size: 14px;
  font-family: var(--title-font);
  color: var(--white-color);
  margin-right: 0;
  text-transform: uppercase;
}
.blog-card .blog-meta a ~ a {
  margin-left: 22px;
}
.blog-card .blog-meta i, .blog-card .blog-meta svg {
  font-size: 14px;
  transition: 0.4s;
}
.blog-card .blog-meta a:hover {
  color: var(--theme-color);
}
.blog-card .blog-meta a:hover i, .blog-card .blog-meta a:hover svg {
  color: var(--theme-color);
}
.blog-card .blog-meta span:after,
.blog-card .blog-meta a:after {
  display: none;
}
.blog-card .blog-text {
  margin-bottom: -0.3em;
}
.blog-card:hover .blog-img img {
  transform: scale(1.08);
}
.blog-card.style2 {
  display: flex;
  background: var(--smoke-color2);
  border-radius: 20px;
  padding: 30px 60px 30px 30px;
  gap: 30px;
  align-items: center;
}
.blog-card.style2 ~ .blog-card.style2 {
  margin-top: 24px;
}
.blog-card.style2 .blog-img {
  border-radius: 20px;
  width: auto;
  flex: none;
}
.blog-card.style2 .blog-img img {
  border-radius: 20px;
}
.blog-card.style2 .blog-content {
  padding: 0;
}
.blog-card.style2 .blog-meta {
  margin-bottom: 18px;
}
.blog-card.style2 .blog-meta a {
  background: transparent;
  border: 1px solid #DFDFDF;
  border-radius: 50px;
  font-size: 14px;
  font-weight: 300;
  font-family: var(--title-font);
  color: var(--title-color);
  text-transform: uppercase;
  padding: 3.5px 25px;
  margin: 0 13px 10px 0;
}
.blog-card.style2 .blog-meta a:last-child {
  margin-right: 0;
}
.blog-card.style2 .blog-meta a:hover {
  box-shadow: 0px 16px 51px rgba(0, 0, 0, 0.07);
  background: var(--white-color);
}
.blog-card.style2 .blog-title {
  font-size: 20px;
  font-weight: 500;
  color: var(--title-color);
  line-height: 30px;
  margin-bottom: 22px;
}
.blog-card.style2 .blog-title a {
  background-image: linear-gradient(to left, var(--theme-color), var(--theme-color));
}
.blog-card.style2 .blog-title a:hover {
  color: var(--theme-color);
}
.blog-card.style3 {
  display: flex;
  padding: 40px;
  border-radius: 0;
  gap: 30px;
}
.blog-card.style3 ~ .blog-card.style3 {
  margin-top: 20px;
}
.blog-card.style3 .blog-date {
  border: 1px solid #313131;
  height: 90px;
  width: 90px;
  flex: none;
  text-align: center;
  font-size: 16px;
  font-weight: 400;
  color: var(--body-color);
  font-family: var(--title-font);
  padding-top: 17px;
}
.blog-card.style3 .blog-date span {
  font-size: 30px;
  font-weight: 500;
  color: var(--white-color);
  display: block;
  margin-bottom: 5px;
}
.blog-card.style3 .blog-content {
  padding: 0;
}
.blog-card.style3 .blog-category {
  color: var(--theme-color);
  font-size: 12px;
  font-weight: 600;
  font-family: var(--body-font);
  text-transform: uppercase;
  margin-top: -0.5em;
  display: block;
  margin-bottom: 11px;
}
.blog-card.style3 .blog-title {
  margin-bottom: 21px;
}
.blog-card.style3 .blog-meta {
  margin-bottom: -0.4em;
}
.blog-card.style3 .blog-meta i {
  color: var(--white-color);
  margin-right: 6px;
}
.blog-card.style4 {
  background: var(--smoke-color2);
  border-radius: 20px;
}
.blog-card.style4 .blog-img {
  border-radius: 20px 20px 0 0;
}
.blog-card.style4 .blog-meta a {
  color: var(--title-color);
  font-weight: 400;
}
.blog-card.style4 .blog-meta a i {
  color: var(--title-color);
}
.blog-card.style4 .blog-meta a:hover {
  color: var(--theme-color);
}
.blog-card.style4 .blog-title {
  color: var(--title-color);
}
.blog-card.style4 .blog-title:hover a {
  color: var(--theme-color);
  background: transparent;
}
.blog-card.style4 ~ .blog-card.style4 {
  margin-top: 24px;
}

.blog_sec_title_static {
  position: absolute;
  height: 100%;
  display: block;
}
.blog_sec_title_static .blog_sec_title_wrap {
  position: sticky;
  top: 200px;
  flex-direction: column;
  flex-wrap: wrap;
  align-items: flex-start;
  margin: auto;
  margin-right: 40px;
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .blog_sec_title_static .blog_sec_title_wrap {
    margin-right: 0;
  }
  .blog-card.style2 {
    padding: 30px;
  }
  .blog-card.style2 .blog-img {
    width: 300px;
  }
  .blog-card.style4 .blog-title {
    font-size: 30px;
  }
}
/* Medium Large devices */
@media (max-width: 1299px) {
  .blog-card .blog-content {
    padding: 35px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .blog-card.style2 {
    display: block;
  }
  .blog-card.style2 .blog-img {
    width: 100%;
    margin-bottom: 40px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .blog-card .blog-title {
    font-size: 22px;
  }
  .blog-card.style2 .blog-title {
    font-size: 22px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .blog-card .blog-title {
    font-size: 20px;
  }
  .blog_sec_title_static {
    position: initial;
    height: auto;
    text-align: center;
    margin-bottom: 40px;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .blog-card.style2 {
    padding: 25px;
  }
  .blog-card.style3 {
    padding: 30px;
  }
}
/* Extra small devices */
@media (max-width: 375px) {
  .blog-card.style3 {
    display: block;
  }
  .blog-card.style3 .blog-date {
    margin-bottom: 25px;
  }
}
@media (max-width: 320px) {
  .blog-card.style2 {
    padding: 20px;
  }
  .blog-card.style2 .blog-meta a {
    padding: 3.5px 20px;
  }
  .blog-card.style4 .blog-content {
    padding: 30px;
  }
  .blog-card.style4 .blog-meta a ~ a {
    margin-left: 0;
  }
  .blog-card.style4 .blog-meta span, .blog-card.style4 .blog-meta a {
    padding-right: 12px;
  }
}
/*------------------- 4.00. Client  -------------------*/
/* Client 1 ---------------------------------- */
.client-area {
  border-top: 1px solid var(--border-color);
  padding: 86px 0;
}

.client-box {
  text-align: center;
  display: grid;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  padding: 33.5px 0;
  position: relative;
  transition: 0.4s;
  cursor: pointer;
  opacity: 0.3;
}
.client-box:hover {
  background: var(--smoke-color3);
  opacity: 1;
}

.client-sec {
  padding-top: 337px;
}

/* Medium devices */
@media (max-width: 991px) {
  .client-area {
    padding: 80px 0;
  }
  .client-sec {
    padding-top: 317px;
  }
}
/* Extra small devices */
/*------------------- 4.00. Simple Sections  -------------------*/
.checklist ul {
  padding-left: 0;
  list-style: none;
  text-align: left;
  margin-bottom: 0;
}
.checklist li {
  font-size: 16px;
  font-weight: 400;
  margin-bottom: 5px;
  display: flex;
  color: var(--body-color);
}
.checklist li:last-child {
  margin-bottom: 0;
}
.checklist li i, .checklist li svg {
  font-size: 16px;
  margin-right: 10px;
  color: var(--theme-color);
  margin-top: 6px;
  width: 16px;
}
.checklist.style2 ul li {
  font-weight: 700;
  border-radius: 6px;
  background-color: rgb(247, 244, 239);
  padding: 17px 30px;
  font-family: var(--title-font);
}
.checklist.style2 ul li:not(:last-child) {
  margin-bottom: 20px;
}
.checklist.style2 ul li i {
  margin-right: 20px;
}
.checklist.style3 ul {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 8px;
}
.checklist.style3 ul li {
  margin-bottom: 0;
}
.checklist.style4 ul li:not(:last-child) {
  margin-bottom: 9px;
}
.checklist.style5 ul li {
  font-weight: 400;
  font-size: 16px;
  color: var(--body-color);
}
.checklist.mb-40 {
  /* Large devices */
}
@media (max-width: 1199px) {
  .checklist.mb-40 {
    margin-bottom: 32px;
  }
}
.checklist.mb-45 {
  /* Large devices */
}
@media (max-width: 1199px) {
  .checklist.mb-45 {
    margin-bottom: 35px;
  }
}

.img-half {
  position: absolute;
  top: 0;
  width: 50%;
  height: 100%;
  z-index: -1;
}
.img-half img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.img-half.img-right {
  right: 0;
}

.icon-box {
  display: inline-flex;
  gap: 10px;
}

.btn-wrap {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 20px 30px;
}
.btn-wrap.style2 {
  gap: 30px 40px;
}

.read-more-btn {
  font-size: 14px;
  font-weight: 500;
  color: var(--body-color);
  position: relative;
}
.read-more-btn i, .read-more-btn svg {
  font-size: 12px;
}
.read-more-btn:after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  height: 1px;
  background: var(--theme-color);
  width: 0;
  transition: 0.4s ease-in-out;
}
.read-more-btn:hover {
  color: var(--theme-color);
}
.read-more-btn:hover:after {
  width: 100%;
}

.category-tag {
  background: var(--theme-color);
  height: 25px;
  line-height: 25px;
  border-radius: 30px;
  text-transform: uppercase;
  color: var(--white-color);
  font-size: 10px;
  font-weight: 700;
  display: inline-block;
  padding: 0 19px;
  letter-spacing: 1px;
}

/* Large devices */
@media (max-width: 1199px) {
  p.mb-40 {
    margin-bottom: 35px;
  }
  p.mb-45 {
    margin-bottom: 38px;
  }
  .checklist li {
    margin-bottom: 6px;
    font-size: 16px;
  }
  .checklist li i, .checklist li svg {
    font-size: 14px;
    margin-right: 5px;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .checklist.style2 ul li i {
    margin-right: 10px;
  }
}
/*------------------- 4.00. Video -------------------*/
/* Why 1 ---------------------------------- */
.video-area-1 .video-wrap {
  display: block;
  text-align: center;
  margin-bottom: 0;
  margin-top: -119px;
}
.video-area-1 .video-wrap .play-btn {
  border-radius: 50%;
  position: relative;
  transform: translate(0, 50%);
}
.video-area-1 .video-wrap .play-btn > i {
  background: transparent;
  color: var(--white-color);
  --icon-size: 238px;
}
.video-area-1 .video-wrap .play-btn > i::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background: var(--theme-color);
  mix-blend-mode: multiply;
  border-radius: 50%;
  z-index: -1;
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .video-area-1 .video-wrap .play-btn > i {
    --icon-size: 150px;
  }
  .video-area-1 .video-wrap {
    margin-top: -76px;
  }
}
/*------------------- 4.00. Category Menu -------------------*/
.category-card {
  border-radius: 6px;
  background: var(--white-color);
  padding: 30px;
  display: flex;
  justify-content: space-between;
}
.category-card_content {
  max-width: 150px;
}
.category-card_title {
  font-size: 20px;
  margin-top: -0.3em;
  margin-bottom: 26px;
}
.category-card_title a {
  color: var(--title-color);
}
.category-card_title a:hover {
  color: var(--theme-color);
}
.category-card_icon {
  flex: none;
  align-self: end;
  position: relative;
  z-index: 1;
}
.category-card_icon:after {
  position: absolute;
  content: "";
  border-radius: 50%;
  background-color: rgb(247, 244, 239);
  height: 60px;
  width: 60px;
  left: -30px;
  top: 0;
  z-index: -1;
}
.category-card .more-btn {
  font-size: 12px;
  color: var(--body-color);
  font-weight: 500;
  letter-spacing: 1px;
  line-height: initial;
}
.category-card .more-btn:hover {
  color: var(--theme-color);
}
.category-card .more-btn i {
  font-size: 14px;
  display: block;
}

.category-search-form {
  margin-top: 40px;
  position: relative;
}
.category-search-form .form-control {
  padding: 14.5px 280px 14.5px 30px;
}
.category-search-form .btn {
  position: absolute;
  right: 0;
  top: 0;
  padding: 20px 50px;
}

.category-card2 {
  border-radius: 6px;
  background-color: rgb(255, 255, 255);
  box-shadow: 0px 10px 60px 0px rgba(0, 0, 0, 0.07);
  text-align: center;
  padding: 30px 30px 23px;
}
.category-card2_title {
  font-size: 20px;
  margin-top: -0.3em;
  margin-bottom: 0px;
}
.category-card2_title a {
  color: var(--title-color);
}
.category-card2_title a:hover {
  color: var(--theme-color);
}
.category-card2_icon {
  display: inline-block;
  height: 66px;
  width: 66px;
  line-height: 64px;
  border-radius: 50%;
  background: var(--smoke-color3);
  margin-bottom: 20px;
  transition: 0.5s;
}
.category-card2_icon img {
  transition: 0.5s;
}
.category-card2_icon:after {
  display: none;
}
.category-card2:hover .category-card2_icon {
  background: var(--theme-color);
}
.category-card2:hover .category-card2_icon img {
  filter: brightness(99);
}

.category-slider {
  transform: translateY(-50px);
  margin-bottom: -50px;
}

.category-area2 {
  padding-bottom: 100px;
  z-index: 2;
}
.category-area2 .category-search-form .form-control {
  border-color: var(--theme-color);
  color: var(--white-color);
}

/* Small devices */
@media (max-width: 767px) {
  .category-search-form {
    text-align: center;
  }
  .category-search-form .btn {
    position: inherit;
    margin-top: 20px;
  }
  .category-search-form .form-control {
    padding: 14.5px 30px;
  }
}
/*------------------- 4.00. Faq -------------------*/
/* Faq 1 ---------------------------------- */
.accordion-card {
  margin-bottom: 20px;
  transition: 0.4s ease-in-out;
  overflow: hidden;
  background: #202020;
  border: 1px solid #303030;
  border-radius: 10px;
}
.accordion-card .accordion-button {
  font-size: 18px;
  font-weight: 500;
  font-family: var(--title-font);
  color: var(--white-color);
  border: 0;
  background-color: transparent;
  padding: 12px 65px 12px 30px;
  min-height: 80px;
  gap: 10px;
  margin-bottom: 0;
  text-align: left;
  transition: 0.3s;
  position: relative;
}
.accordion-card .accordion-button:after {
  content: "\f061";
  height: 40px;
  width: 40px;
  border-radius: 7px;
  line-height: 38px;
  background: #171717;
  border: 1px solid #303030;
  font-family: var(--icon-font);
  color: var(--white-color);
  font-weight: 900;
  font-size: 16px;
  display: grid;
  justify-content: center;
  align-items: center;
  text-align: center;
  position: absolute;
  top: 20px;
  right: 20px;
  transition: 0.3s ease-in-out;
}
.accordion-card .accordion-button:focus {
  outline: none;
  box-shadow: none;
}
.accordion-card .accordion-button:not(.collapsed) {
  color: var(--white-color);
  padding: 0px 65px 0px 30px;
}
.accordion-card .accordion-button:not(.collapsed):after {
  content: "\f063";
  transform: rotate(0);
  background: var(--theme-color);
  border-color: var(--theme-color);
}
.accordion-card .accordion-collapse {
  border: none;
}
.accordion-card .accordion-body {
  border-radius: 0;
  background-color: #202020;
  border: none;
  padding: 0px 30px 30px 30px;
  margin-top: -0.5em;
}
.accordion-card .accordion-body p {
  margin-bottom: -0.3em;
}
.accordion-card:last-child {
  margin-bottom: 0;
}
.accordion-card.style2 {
  background: #F3F3F4;
  font-size: 16px;
  font-weight: 600;
  border: 0;
}
.accordion-card.style2 .accordion-button {
  color: var(--title-color);
  min-height: 60px;
  padding: 0px 65px 0px 35px;
}
.accordion-card.style2 .accordion-button:after {
  background: transparent;
  color: var(--theme-color);
  border: 0;
  top: 10px;
}
.accordion-card.style2 .accordion-body {
  background: #F3F3F4;
  padding: 30px 35px 30px 35px;
  font-weight: 400;
}
.accordion-card.style2 .accordion-button:not(.collapsed):after {
  background: transparent;
  color: var(--white-color);
  border: 0;
}
.accordion-card.style2 .accordion-button:not(.collapsed) {
  background: var(--theme-color);
  color: var(--white-color);
}

/* Extra small devices */
@media (max-width: 575px) {
  .accordion-card .accordion-button {
    font-size: 16px;
  }
  .accordion-card .accordion-body {
    padding: 10px 30px 30px 30px;
  }
}
/*------------------- 4.00. feature -------------------*/
/***********feature area**********/
.feature-area {
  background: #F4F4F4;
  border-radius: 15px;
  overflow: hidden;
}

.feature-card {
  padding: 60px 65px 60px 40px;
  transition: 0.4s;
  position: relative;
  z-index: 0;
}
.feature-card_bg {
  position: absolute;
  left: 0;
  top: 0;
  height: 0%;
  width: 100%;
  z-index: -1;
  transition: 0.4s;
  opacity: 0;
}
.feature-card_bg img {
  height: 100%;
  width: 100%;
  object-fit: cover;
}
.feature-card_icon {
  margin-bottom: 30px;
}
.feature-card_subtitle {
  letter-spacing: 0.1em;
  text-transform: uppercase;
  color: var(--theme-color);
  font-weight: 400;
  font-size: 14px;
  font-family: var(--title-font);
  position: relative;
  padding-right: 18px;
  margin-bottom: 5px;
}
.space-bottom{
    direction: rtl;
}
.feature-card_subtitle:after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  transform: translate(0, -50%);
  background: var(--theme-color);
  border-radius: 1px;
  width: 8px;
  height: 8px;
}
.feature-card_title {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 15px;
}
.feature-card_title a {
  color: var(--title-color);
  background-image: linear-gradient(to left, var(--white-color), var(--white-color));
  background-repeat: no-repeat;
  background-position: bottom left;
  background-size: 0 2px;
}
.feature-card_title a:hover {
  background-size: 100% 2px;
}
.feature-card_text {
  font-size: 14px;
  margin-bottom: 26px;
}
.feature-card-active {
  background: var(--title-color);
}
.feature-card-active .feature-card_bg {
  opacity: 1;
  height: 100%;
}
.feature-card-active .feature-card_icon {
  filter: brightness(99);
}
.feature-card-active .feature-card_title a {
  color: var(--white-color);
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .feature-area {
    margin-top: -15px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .feature-area {
    margin-top: -30px;
  }
  .feature-card {
    padding: 40px 35px;
  }
  .feature-card_title {
    font-size: 22px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .feature-area {
    margin-top: -45px;
  }
  .feature-card_text {
    max-width: 500px;
  }
}
@media (max-width: 320px) {
  .feature-card {
    padding: 30px 25px;
  }
}
.feature-card2 .feature-card_img {
  position: relative;
  display: inline-block;
  margin-bottom: 44px;
}
.feature-card2 .feature-card_img img {
  border-radius: 50%;
}
.feature-card2 .feature-card_img .feature-card_icon {
  height: 60px;
  width: 60px;
  line-height: 60px;
  border-radius: 50%;
  display: inline-block;
  position: absolute;
  background: var(--white-color);
  left: 50%;
  top: 50%;
  transform: translate(-50%, -30%);
  z-index: 1;
  opacity: 0;
}
.feature-card2 .feature-card_img .feature-card_icon:hover {
  background: var(--theme-color);
  color: var(--white-color);
}
.feature-card2 .feature-card_img:after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  border-radius: 50%;
  background: var(--title-color);
  transition: 0.4s;
  opacity: 0;
}
.feature-card2 .feature-card_title {
  font-size: 20px;
  font-weight: 500;
  text-transform: uppercase;
  margin-bottom: 12px;
}
.feature-card2 .feature-card_title a {
  background: transparent;
}
.feature-card2 .feature-card_title a:hover {
  color: var(--theme-color);
}
.feature-card2 .feature-card_text {
  font-size: 16px;
  max-width: 330px;
  margin: auto;
  margin-bottom: -0.3em;
}
.feature-card2:hover .feature-card_img .feature-card_icon {
  opacity: 1;
  transform: translate(-50%, -50%);
}
.feature-card2:hover .feature-card_img img {
  mix-blend-mode: luminosity;
}
.feature-card2:hover .feature-card_img:after {
  opacity: 0.8;
}

/* Medium Large devices */
@media (max-width: 1299px) {
  .feature-card2 .feature-card_img {
    margin-bottom: 34px;
  }
}
.feature-tab-button {
  background: var(--white-color);
}
.feature-tab-button button {
  border: 0;
  background: transparent;
  font-size: 16px;
  font-weight: 600;
  font-family: var(--title-font);
  color: var(--title-color);
  text-transform: uppercase;
  padding: 17px 52px 17px 30px;
  display: block;
  width: 100%;
  text-align: left;
  position: relative;
}
.feature-tab-button button i {
  position: absolute;
  right: 30px;
  top: 22px;
  color: var(--body-color);
}
.feature-tab-button button ~ button {
  border-top: 1px solid #EEEEEE;
}
.feature-tab-button button.active {
  background: var(--theme-color);
  color: var(--white-color);
}
.feature-tab-button button.active i {
  color: var(--white-color);
}

.feature-tab-content .filter-item {
  display: flex;
  gap: 30px;
}
.feature-tab-content .tab-thumb {
  flex: none;
}
.feature-tab-content .tab-content_grid {
  display: flex;
  gap: 30px 60px;
  margin-top: 10px;
}
.feature-tab-content .tab-content_grid .media-left {
  align-self: flex-end;
  margin-bottom: -0.3em;
}
.feature-tab-content .tab-content_grid .media-body {
  margin-bottom: -0.3em;
}
.feature-tab-content .tab-content_grid-title {
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
}
.feature-tab-content .counter-number {
  color: var(--theme-color);
  font-size: 60px;
  font-weight: 700;
  font-family: var(--title-font);
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .feature-tab-button button {
    font-size: 15px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .feature-tab-content .tab-content_grid {
    gap: 30px 40px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .feature-tab-content .filter-item {
    display: block;
  }
  .feature-tab-content .filter-item .tab-thumb {
    margin-bottom: 30px;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .feature-tab-content .tab-content_grid {
    display: block;
    margin-top: 20px;
  }
  .feature-tab-content .tab-content_grid .media-left {
    margin-bottom: 10px;
  }
}
/*------------------- 4.00. CTA -------------------*/
.cta-area {
  position: relative;
  z-index: 0;
}
.cta-area:after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  background: linear-gradient(262.57deg, rgba(28, 28, 28, 0.99) 34.91%, rgba(28, 28, 28, 0) 81.55%);
  opacity: 0.8;
  z-index: -1;
}

.cta-btn-wrap .arrow-icon {
  margin-top: -50px;
}

/* Medium devices */
@media (max-width: 991px) {
  .cta-wrap .btn-wrap {
    justify-content: center;
  }
  .cta-wrap .btn-wrap .about-info-wrap.style3 {
    text-align: left;
  }
}
/*------------------- 4.00. Service -------------------*/
/* Service Card ---------------------------------- */
.service-bg-area {
  margin-top: -45px;
  background-attachment: fixed;
  margin-bottom: -15px;
}

.service-slider-1 {
  margin-left: auto;
  margin-right: -18px;
  max-width: 1593px;
}

.service-card {
  background: #202020;
  border: 1px solid #303030;
  border-radius: 15px;
  padding: 40px 40px 35px;
  display: flex;
    direction: rtl;
  gap: 20px;
}
.service-card_icon {
  flex: none;
}
.service-card_title {
  margin-top: -0.3em;
  margin-bottom: 5px;
  font-weight: 500;
}
.service-card_title a {
  color: var(--white-color);
  background-image: linear-gradient(to left, var(--white-color), var(--white-color));
  background-repeat: no-repeat;
  background-position: bottom left;
  background-size: 0 2px;
}
.service-card_title a:hover {
  background-size: 100% 2px;
}
.service-card_text {
  font-size: 14px;
  margin-bottom: 10px;
}
.service-card .link-btn {
  color: var(--white-color);
}
.service-card .link-btn:before {
  background: var(--white-color);
}
.service-card .link-btn i {
  margin-left: 3px;
}
.service-card.style2 {
  background: var(--smoke-color2);
  border: 1px solid var(--smoke-color2);
  display: block;
  border-radius: 15px;
  padding: 40px;
  transition: 0.4s;
}
.service-card.style2 .service-card_icon {
  height: 100px;
  width: 100px;
  box-shadow: 0px 16px 51px rgba(0, 0, 0, 0.07);
  border: 1px solid rgba(237, 237, 237, 0.89);
  border-radius: 15px;
  text-align: center;
  line-height: 100px;
  background: var(--white-color);
  margin-bottom: 40px;
  filter: grayscale(9) brightness(1);
  transition: 0.4s;
}
.service-card.style2 .service-card_title {
  font-size: 24px;
  margin-bottom: 13px;
}
.service-card.style2 .service-card_title a {
  color: var(--title-color);
  background-image: linear-gradient(to left, var(--title-color), var(--title-color));
}
.service-card.style2 .service-card_text {
  font-size: 16px;
  margin-bottom: 22px;
}
.service-card.style2 .link-btn {
  font-weight: 500;
  font-size: 14px;
  font-family: var(--title-font);
  color: var(--title-color);
}
.service-card.style2 .link-btn:before {
  background-color: var(--title-color);
  bottom: 12px;
  left: 55px;
}
.service-card.style2 .link-btn i {
  height: 45px;
  width: 45px;
  line-height: 44px;
  border-radius: 50%;
  background: var(--white-color);
  border: 1px solid rgba(237, 237, 237, 0.89);
  box-shadow: 0px 16px 51px rgba(0, 0, 0, 0.07);
  text-align: center;
  color: var(--title-color);
  margin-right: 7px;
  margin-left: 0;
  transition: 0.4s;
}
.service-card.style2 .link-btn:hover:before {
  width: calc(100% - 55px);
}
.service-card.style2:hover {
  background: var(--white-color);
  border: 1px solid rgba(237, 237, 237, 0.89);
  box-shadow: 0px 16px 51px rgba(0, 0, 0, 0.07);
}
.service-card.style2:hover .service-card_icon {
  filter: none;
}
.service-card.style2:hover .link-btn i {
  background: var(--theme-color);
  color: var(--white-color);
}

/* Hight Resoulation devices */
@media (min-width: 1922px) {
  .sec-shape-bottom,
  .sec-shape-top {
    transform: translateY(-1px);
  }
  .sec-shape-bottom img,
  .sec-shape-top img {
    width: 100%;
  }
  .sec-shape-bottom {
    transform: translateY(1px);
  }
  .service-bg-area {
    margin-top: -60px;
    margin-bottom: -20px;
  }
  .service-slider-1 {
    overflow: hidden;
    max-width: 1582px;
    margin-right: 300px;
  }
}
@media (max-width: 1600px) {
  .service-slider-1 {
    max-width: 1438px;
  }
}
/* Extra large devices */
@media (max-width: 1500px) {
  .service-slider-1 {
    max-width: 1358px;
  }
}
/* Medium Large devices */
@media (max-width: 1399px) {
  .service-slider-1 {
    max-width: 1285px;
    margin: 0 -38px 0 auto;
  }
}
@media (max-width: 1200px) {
  .service-slider-1 {
    max-width: 1207px;
  }
  .service-bg-area {
    margin-top: -35px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .service-slider-1 {
    max-width: 1029px;
  }
  .service-card {
    padding: 30px 30px 25px;
  }
  .service-card.style2 {
    padding: 30px;
  }
  .service-card.style2 .service-card_icon {
    margin-bottom: 30px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .service-slider-1 {
    max-width: none;
    margin: 30px 25px 0px;
  }
  .service-bg-area {
    margin-top: -20px;
    margin-bottom: -10px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .service-card {
    padding: 40px 40px 35px;
  }
  .service-slider-2 {
    margin: 0 -20px;
  }
  .service-card.style2 {
    margin: 0 10px;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .service-slider-1 {
    margin: 30px 0px 0px;
  }
  .service-bg-area {
    margin-top: -12px;
    margin-bottom: -6px;
  }
  .sec-shape-bottom {
    margin-top: -5px;
  }
}
/* Extra small devices */
@media (max-width: 375px) {
  .service-bg-area {
    margin-top: -10px;
    margin-bottom: -4px;
  }
  .service-bg-area .sec-shape-top {
    transform: translateY(-2px);
  }
  .service-bg-area .sec-shape-bottom {
    margin-top: -10px;
    transform: translateY(2px);
  }
  .service-card {
    padding: 30px 30px 25px;
  }
}
@media (max-width: 320px) {
  .service-bg-area .sec-shape-top {
    transform: translateY(-4px);
    margin-bottom: -5px;
  }
  .service-bg-area .sec-shape-bottom {
    margin-top: -12px;
    transform: translateY(3px);
  }
}
/*******body mass index*******/
.service-bg2-area {
  margin-top: -120px;

}

.sec-shape2-bottom {
  margin-bottom: -116px;
}
.sec-shape2-bottom img {
  width: 100%;
}

.bmi-table {
  --border-color: #2D2D2D;
}
.bmi-table table {
  border-color: var(--border-color);
  margin-bottom: 0;
}
.bmi-table thead th {
  font-size: 16px;
  font-weight: 600;
  font-family: var(--title-font);
  color: var(--white-color);
  padding-left: 30px;
  line-height: 33px;
}
.bmi-table thead th ~ th {
  padding-left: 40px;
}
.bmi-table tbody th {
  color: var(--body-color);
  padding-left: 30px;
  line-height: 33px;
}
.bmi-table tbody td {
  color: var(--body-color);
  padding-left: 40px;
  line-height: 33px;
}

.bmi-calculator-form {
  background: var(--title-color);
  border: 1px solid #2D2D2D;
  box-shadow: 0px 7px 78px rgba(0, 0, 0, 0.18);
  padding: 60px;
    direction:rtl; 
}
.tabless{
    direction: rtl;
}
.bmi-calculator-form .form-title {
  color: var(--white-color);
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 32px;
  text-transform: uppercase;
  margin-top: -0.35em;
}
.bmi-calculator-form .btn {
  display: inline-block;
  padding: 0;
}
.bmi-calculator-form .btn input {
  background: transparent;
  color: var(--white-color);
  padding: 16px 34px 18px;
  font-weight: 500;
}
.bmi-calculator-form .btn:hover input {
  color: var(--title-color);
}

/* Hight Resoulation devices */
@media (min-width: 1922px) {
  .sec-shape2-bottom {
    margin-bottom: -154px;
    transform: translate(0px, 1px);
  }
}
@media (max-width: 1600px) {
  .sec-shape2-bottom {
    margin-bottom: -96px;
  }
}
/* Extra large devices */
@media (max-width: 1500px) {
  .sec-shape2-bottom {
    margin-bottom: -87px;
  }
}
/* Medium Large devices */
@media (max-width: 1399px) {
  .sec-shape2-bottom {
    margin-bottom: -82px;
  }
}
/* Medium Large devices */
@media (max-width: 1299px) {
  .sec-shape2-bottom {
    margin-bottom: -73px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .sec-shape2-bottom {
    margin-bottom: -62px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .sec-shape2-bottom {
    margin-bottom: -47px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .sec-shape2-bottom {
    margin-bottom: -36px;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .sec-shape2-bottom {
    margin-bottom: -26px;
  }
  .bmi-calculator-form {
    padding: 40px;
  }
}
/* Extra small devices */
@media (max-width: 375px) {
  .sec-shape2-bottom {
    margin-bottom: -23px;
  }
}
@media (max-width: 320px) {
  .sec-shape2-bottom {
    margin-bottom: -20px;
    transform: translate(0px, 1px);
    margin-top: -6px;
  }
  .bmi-table tbody td,
  .bmi-table thead th ~ th {
    padding-left: 20px;
  }
}
/****service details page****/
.service-page-single .page-img {
  border-radius: 20px;
}
.service-page-single .page-img img {
  border-radius: 20px;
}
.service-page-single .service-wrap {
  background: #FFFFFF;
  border: 1px solid rgba(237, 237, 237, 0.89);
  box-shadow: 0px 16px 51px rgba(0, 0, 0, 0.07);
  border-radius: 15px;
  padding: 40px;
}
.service-page-single .service-wrap h6 {
  font-size: 18px;
  font-weight: 500;
  margin-top: -0.4em;
  margin-bottom: 10px;
}
.service-page-single .service-wrap p {
  margin-bottom: -0.4em;
}
.service-page-single .service-page-list {
  border-radius: 20px;
  padding: 53px 60px;
  margin-top: 20px;
}
.service-page-single .service-page-list li {
  color: var(--white-color);
  margin-bottom: 10px;
}
.service-page-single .service-page-list li i {
  color: var(--white-color);
}
.service-page-single .service-page-list li:last-child {
  margin-bottom: 0;
}

/* Medium Large devices */
@media (max-width: 1299px) {
  .service-page-single .service-wrap {
    padding: 30px;
  }
}
/*------------------- 4.00. Why Choose Us -------------------*/
/*----------------------------------------------
    # why choose us area style here
----------------------------------------------*/
.wcu-thumb {
  position: relative;
  padding: 0 43px 40px 0;
  margin-right: 10px;
}
.wcu-thumb .img-1 {
  border-radius: 20px;
}
.wcu-thumb .img-2 {
  position: absolute;
  left: -105px;
  top: 94px;
  border-radius: 20px;
}
.wcu-thumb .wcu-grid {
  position: absolute;
  bottom: 0px;
  right: 0px;
}

.wcu-grid {
  background: var(--theme-color);
  border-radius: 20px;
  padding: 40px 68px 40px 40px;
  display: inline-flex;
  align-items: center;
}
.wcu-grid .icon {
  padding-right: 20px;
  border-right: 1px solid rgba(255, 255, 255, 0.31);
  margin-right: 20px;
}
.wcu-grid_year {
  font-size: 48px;
  font-weight: 500;
  color: var(--white-color);
  margin-bottom: -5px;
  margin-top: -0.3em;
}
.wcu-grid_text {
  font-size: 16px;
  font-weight: 600;
  font-family: var(--title-font);
  color: var(--white-color);
  text-transform: uppercase;
  margin-bottom: -0.4em;
  display: block;
}
.wcu-grid.style2 {
  border-radius: 0;
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .wcu-thumb .img-2 {
    left: -75px;
  }
}
/* Medium Large devices */
@media (max-width: 1299px) {
  .wcu-thumb {
    padding: 0 40px 40px 40px;
    height: 100%;
  }
  .wcu-thumb .img-1 {
    height: 100%;
    object-fit: cover;
  }
  .wcu-thumb .img-2 {
    left: 0;
  }
}
/* Medium Large devices */
@media (max-width: 1399px) {
  .wcu-thumb {
    height: auto;
    margin: 0;
    margin-bottom: 10px;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .wcu-grid {
    padding: 30px 30px 30px 30px;
  }
  .wcu-grid_year {
    font-size: 38px;
  }
  .wcu-thumb .img-2 {
    width: 200px;
  }
}
/* Extra small devices */
@media (max-width: 375px) {
  .wcu-thumb {
    padding: 0;
    margin-bottom: 40px;
  }
  .wcu-thumb .wcu-grid {
    position: initial;
    animation: none;
    margin-top: 20px;
    display: flex;
  }
}
@media (max-width: 320px) {
  .wcu-grid {
    padding: 20px;
  }
}
.wcu-thumb2 img {
  border-radius: 20px;
}

.wcu-grid2 {
  border-radius: 20px;
  padding: 30px;
  height: 200px;
  display: flex;
  align-items: end;
}
.wcu-grid2 .wcu-grid_text {
  font-size: 18px;
  display: inline-block;
}

/* Large devices */
@media (max-width: 1199px) {
  .wcu-thumb2 img {
    width: 100%;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .wcu-grid {
    padding: 40px;
  }
}
@media (max-width: 320px) {
  .wcu-grid {
    padding: 19px;
  }
}
/*------------------- 4.00. Pricing -------------------*/
.pricing-card {
  background: var(--smoke-color2);
  border-radius: 15px;
  padding: 0 40px 50px;
  margin-top: 66px;
  position: relative;
  z-index: 0;
}
.pricing-card_bg {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  z-index: -1;
  opacity: 0;
  transition: 0.4s;
}
.pricing-card_bg img {
  height: 100%;
  width: 100%;
  object-fit: cover;
  border-radius: 15px;
}
.pricing-card_icon {
  display: inline-block;
  height: 141px;
  width: 134px;
  line-height: 134px;
  transform: translateY(-66px);
  text-align: center;
  position: relative;
  z-index: 1;
  margin-bottom: -46px;
}
.pricing-card_icon:before, .pricing-card_icon:after {
  content: "";
  position: absolute;
  left: 0px;
  top: 0px;
  background: transparent;
  width: 141px;
  height: 134px;
  z-index: -1;
  transform: skewX(-10deg);
  border-radius: 15px;
}
.pricing-card_icon:after {
  background: var(--theme-color);
  width: 115px;
  height: 110px;
  top: 11px;
  left: 13px;
}
.pricing-card_icon img {
  transition: 0.4s;
}
.pricing-card_title {
  font-size: 30px;
  font-weight: 500;
  margin-bottom: 7px;
  transition: 0.4s;
}
.pricing-card_price {
  font-size: 72px;
  font-weight: 500;
  color: var(--theme-color);
  border-bottom: 1px solid #E2E2E4;
  padding-bottom: 18px;
  transition: 0.4s;
  margin-bottom: 22px;
}
.pricing-card_price .currency {
  font-size: 24px;
  position: relative;
  top: -30px;
  margin-right: 6px;
  display: inline-block;
}
.pricing-card_price .duration {
  font-size: 16px;
  font-weight: 400;
}
.pricing-card_content {
  margin-bottom: 27px;
}
.pricing-card .checklist {
  margin-bottom: 35px;
}
.pricing-card .checklist li {
  font-size: 16px;
  color: var(--body-color);
  font-weight: 400;
}
.pricing-card .checklist li:not(:last-child) {
  margin-bottom: 7px;
}
.pricing-card .checklist li i, .pricing-card .checklist li svg {
  color: var(--body-color);
}
.pricing-card.pricing-card_active .pricing-card_bg, .pricing-card:hover .pricing-card_bg {
  opacity: 1;
}
.pricing-card.pricing-card_active .pricing-card_icon:before, .pricing-card:hover .pricing-card_icon:before {
  background: var(--white-color);
}
.pricing-card.pricing-card_active .pricing-card_icon img, .pricing-card:hover .pricing-card_icon img {
  transform: rotateY(180deg);
}
.pricing-card.pricing-card_active .pricing-card_title, .pricing-card:hover .pricing-card_title {
  color: var(--white-color);
}
.pricing-card.pricing-card_active .pricing-card_price, .pricing-card:hover .pricing-card_price {
  color: var(--white-color);
  border-color: #2E2E2E;
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .pricing-card {
    padding: 0 30px 40px;
  }
  .pricing-card_price {
    font-size: 60px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .pricing-card_title {
    font-size: 24px;
  }
  .pricing-card_price {
    font-size: 50px;
  }
  .pricing-card_price .currency {
    top: -15px;
  }
  .pricing-card {
    margin-top: 56px;
  }
  .pricing-card_icon {
    height: 121px;
    width: 114px;
    line-height: 112px;
    transform: translateY(-56px);
    margin-bottom: -36px;
  }
  .pricing-card_icon:before {
    width: 121px;
    height: 114px;
  }
  .pricing-card_icon:after {
    width: 95px;
    height: 90px;
  }
}
/*------------------- 4.00. Product -------------------*/
.product-card {
  transition: all ease 0.4s;
  border-radius: 6px;
}
.product-card .product-img {
  background: var(--smoke-color3);
  border-radius: 15px;
  margin-bottom: 25px;
}
.product-card .product-img img {
  border-radius: 15px;
}
.product-card .product-title {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 1px;
}
.product-card .product-title a {
  color: inherit;
}
.product-card .product-title a:hover {
  color: var(--theme-color);
}
.product-card .price {
  display: block;
  color: var(--title-color);
  font-weight: 400;
  font-size: 16px;
  font-family: var(--title-font);
  margin-bottom: -0.4em;
}
.product-card .price del {
  margin-right: 10px;
  color: #a9a9a9;
}
.product-card .product-img {
  --space: 20px;
  background-color: var(--smoke-color);
  overflow: hidden;
  position: relative;
  text-align: center;
  background-color: var(--smoke-color);
  border-radius: 15px;
  z-index: 2;
}
.product-card .product-img:before {
  --space: 0px;
  content: "";
  height: calc(100% - var(--space) * 2);
  width: calc(100% - var(--space) * 2);
  position: absolute;
  top: var(--space);
  left: var(--space);
  background-color: rgb(34, 35, 40);
  z-index: 1;
  visibility: hidden;
  opacity: 0;
  transition: 0.4s ease-in-out;
}
.product-card .product-img img {
  width: 100%;
  transition: all ease 0.4s;
  transform: scale(1);
}
.product-card .star-rating {
  width: 93px;
  font-size: 14px;
}
.product-card .actions {
  height: 100%;
  position: absolute;
  left: 0;
  top: 50%;
  right: 0;
  text-align: center;
  transform: translateY(-50%);
  z-index: 3;
  margin-top: 0;
  opacity: 0;
  visibility: hidden;
  transition: 0.4s ease-in-out;
  display: flex;
  justify-content: center;
  align-items: center;
}
.product-card .actions .icon-btn {
  --btn-size: 40px !important;
  line-height: 42px !important;
  font-size: 14px;
  border-color: var(--white-color);
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.05);
  color: var(--white-color);
  background: var(--theme-color);
  border-radius: 10px;
}
.product-card .actions .icon-btn i {
  margin-right: 1px;
}
.product-card .actions .icon-btn:hover {
  color: var(--theme-color);
  background: var(--white-color);
  border-color: var(--white-color);
}
.product-card .actions > * {
  margin: 0 var(--icon-gap-x, 5px);
}
.product-card .actions > * > a {
  margin: 0;
}
.product-card .product-tag {
  height: 25px;
  font-size: 14px;
  font-weight: 500;
  background-color: var(--theme-color);
  line-height: 25px;
  color: var(--white-color);
  border-radius: 5px;
  position: absolute;
  padding: 0 10px;
  top: 23px;
  left: 23px;
  z-index: 9;
}
.product-card .icon-btn {
  transform: translateY(30px);
  transition: 0.4s ease-in-out;
}
.product-card .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart-plus.no-txt,
.product-card .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart.no-txt {
  width: 40px;
  height: 40px;
  line-height: 40px;
  display: inline-block;
}
.product-card .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart-plus.no-txt::before,
.product-card .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart.no-txt::before {
  position: relative;
  top: -1px;
  left: 0;
  line-height: inherit;
  margin: 0;
  font-size: 24px;
}
.product-card .tinv-wishlist a {
  display: inline-block;
  width: 40px;
  height: 40px;
  line-height: 40px;
  background-color: var(--white-color);
  color: var(--title-color);
  border-radius: 50%;
}
.product-card .tinv-wishlist a:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
}
.product-card .add_to_cart_button.added {
  display: none;
}
.product-card .added_to_cart {
  width: 40px;
  height: 40px;
  line-height: 40px;
  background-color: var(--theme-color);
  color: var(--white-color);
  font-size: 0;
  text-align: center;
  border-radius: 10px;
}
.product-card .added_to_cart:after {
  content: "\f07a";
  position: relative;
  font-family: var(--icon-font);
  font-size: 16px;
  font-weight: 700;
}
.product-card .added_to_cart:hover {
  background-color: var(--title-color);
  color: var(--white-color);
}
.product-card .action-btn {
  background-color: var(--white-color);
  font-size: 14px;
  font-family: var(--title-font);
  text-transform: uppercase;
  font-weight: bold;
  display: inline-block;
  padding: 13px 25px;
}
.product-card:hover .product-img img {
  transform: scale(1.1);
}
.product-card:hover .product-img:before {
  visibility: visible;
  opacity: 0.7;
}
.product-card:hover .actions {
  margin-top: 0;
  opacity: 1;
  visibility: visible;
}
.product-card:hover .icon-btn {
  transform: translateY(0);
}
.product-card .media {
  display: flex;
  margin-top: 21px;
  margin-bottom: 6px;
}
.product-card .media .media-body {
  text-align: right;
}
.product-card.list-view {
  display: flex;
  text-align: left;
  height: 100%;
}
.product-card.list-view .product-img {
  width: 100%;
  max-width: 200px;
  margin: 0;
}
.product-card.list-view .star-rating {
  width: 93px;
}
.product-card.list-view .product-content {
  flex: 1;
  border: 1px solid var(--border-color);
  border-left: none;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 0 25px;
}
.product-card.list-view .actions {
  --btn-size: 35px;
  --btn-font-size: 13px;
  --icon-gap-x: 2px;
}
.product-card.list-view .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart-plus.no-txt,
.product-card.list-view .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart.no-txt {
  width: 35px;
  height: 35px;
  line-height: 35px;
}
.product-card.list-view .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart-plus.no-txt::before,
.product-card.list-view .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart.no-txt::before {
  font-size: 20px;
}
.product-card.list-view .tinv-wishlist a {
  width: 35px;
  height: 35px;
  line-height: 35px;
}
.product-card.list-view .added_to_cart {
  width: 35px;
  height: 35px;
  line-height: 35px;
}
.product-card.list-view .added_to_cart:after {
  font-size: 16px;
}
.product-card.list-view .action-btn {
  padding: 8px 15px;
}
.product-card.list-view .tag {
  top: 8px;
  right: 8px;
  padding: 0px 15px;
}
.product-card.list-view .product-title {
  font-size: 18px;
  margin: 0 0 5px 0;
}
.product-card.list-view .product-price {
  font-size: 14px;
}

#productCarousel .slick-arrow {
  top: 37.5%;
}

.mfp-content {
  margin: 1.5rem auto;
}
.mfp-content .product-details-img {
  padding-top: 15px;
}
.mfp-content .product-about {
  padding-top: 40px;
  padding-bottom: 40px;
}
.mfp-content .container {
  position: relative;
}
.mfp-content .product-big-img {
  margin-top: 15px;
  margin-bottom: 15px;
}

.mfp-fade.mfp-bg {
  opacity: 0;
  transition: all 0.15s ease-out;
}

.mfp-fade.mfp-bg.mfp-ready {
  opacity: 0.8;
}

.mfp-fade.mfp-bg.mfp-removing {
  opacity: 0;
}

.mfp-fade.mfp-wrap .mfp-content {
  opacity: 0;
  transition: all 0.4s ease-out;
}

.mfp-fade.mfp-wrap.mfp-ready .mfp-content {
  opacity: 1;
}

.mfp-fade.mfp-wrap.mfp-removing .mfp-content {
  opacity: 0;
}

.woosq-popup .product_meta > span > a:after,
.woosq-popup .product_meta > span > span:after {
  display: none;
}

.woosq-product > .product > div {
  height: auto;
}

.shop-sort-bar {
  padding: 0;
  margin: 0 0 24px 0;
}
.shop-sort-bar .row {
  --bs-gutter-x: 0;
  --bs-gutter-y: 15px;
}
.shop-sort-bar .single-select,
.shop-sort-bar select {
  height: 60px;
  line-height: 60px;
  padding: 0 30px;
  border: 1px solid var(--border-color);
  background-color: var(--white-color);
  width: fit-content;
  min-width: 250px;
  font-size: 16px;
  margin: 0;
  border-radius: 30px;
  color: var(--body-color);
  background-image: none;
}
.shop-sort-bar .woocommerce-result-count {
  margin-bottom: 0;
  color: var(--body-color);
}
.shop-sort-bar .nav a {
  display: inline-block;
  height: 50px;
  width: 50px;
  line-height: 50px;
  border: 1px solid var(--border-color);
  background-color: var(--white-color);
  text-align: center;
  position: relative;
  font-family: var(--title-font);
  font-weight: 600;
  font-size: 16px;
  text-transform: capitalize;
  color: var(--body-color);
  margin: 0 0 0 10px;
}
.shop-sort-bar .nav a.active, .shop-sort-bar .nav a:hover {
  color: var(--theme-color);
}

.product-thumb-area {
  position: relative;
}
.product-thumb-area .product-thumb-tab {
  position: absolute;
  bottom: 50px;
  left: 0;
}

.product-thumb-tab {
  --thumb: 87px;
  --gap: 0px;
  position: relative;
  z-index: 3;
  display: inline-flex;
  border-radius: 20px;
  align-items: end;
  justify-content: center;
  width: 100%;
}
.product-thumb-tab .tab-btn {
  background-color: var(--white-color);
  cursor: pointer;
  height: var(--thumb);
  width: var(--thumb);
  padding: var(--gap);
  border-radius: 20px;
}
.product-thumb-tab .tab-btn img {
  max-width: 100%;
  width: 100%;
  border-radius: inherit;
}
.product-thumb-tab .tab-btn:not(:last-of-type) {
  margin-right: 15px;
}

.product-big-img {
  background-color: #f4f4f4;
  text-align: center;
  border-radius: 20px;
  position: relative;
}
.product-big-img .img {
  width: 100%;
  border-radius: 20px;
}
.product-big-img .img img {
  width: 100%;
  border-radius: 20px;
  height: 100%;
  object-fit: cover;
}
.product-big-img .add_to_wishlist {
  position: absolute;
  top: 48px;
  right: 48px;
}

.product-thumb,
.product-big-img {
  background-color: var(--smoke-color3);
  text-align: center;
  border-radius: 15px;
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.product-thumb .img img,
.product-big-img .img img {
  object-fit: cover;
  border-radius: 15px;
}
.product-thumb .add_to_wishlist,
.product-big-img .add_to_wishlist {
  position: absolute;
  top: 48px;
  right: 48px;
  color: var(--theme-color);
}
.product-thumb .add_to_wishlist:hover i,
.product-big-img .add_to_wishlist:hover i {
  font-weight: 700;
}
.product-thumb .product-tag,
.product-big-img .product-tag {
  height: 25px;
  font-size: 14px;
  font-weight: 500;
  background-color: var(--theme-color);
  line-height: 25px;
  color: var(--white-color);
  border-radius: 5px;
  position: absolute;
  padding: 0 10px;
  top: 23px;
  left: 23px;
  z-index: 9;
}

.quantity {
  position: relative;
  display: inline-flex;
  vertical-align: middle;
}
.quantity > .screen-reader-text {
  display: inline-block;
  font-weight: 600;
  color: var(--title-color);
  font-family: var(--title-font);
  margin: 0;
  align-self: center;
  margin-right: 10px;
}
.quantity .qty-btn,
.quantity .qty-input {
  display: inline-block;
  width: 50px;
  height: 50px;
  border: none;
  border-right: none;
  background-color: transparent;
  padding: 0;
  border-radius: 0;
  text-align: center;
  color: var(--body-color);
  font-size: 18px;
  font-weight: 600;
}
.quantity .qty-btn:last-child,
.quantity .qty-input:last-child {
  border-right: none;
}
.quantity .qty-btn {
  font-size: 16px;
}

.product_meta {
  font-weight: 700;
  font-size: 16px;
  font-family: var(--body-font);
  margin: 35px 0 0 0;
}
.product_meta > span {
  display: block;
  margin-bottom: 5px;
  color: var(--title-color);
  font-weight: bold;
}
.product_meta > span:last-child {
  margin-bottom: 0;
}
.product_meta > span a {
  color: inherit;
}
.product_meta > span a:hover {
  color: var(--theme-color);
}
.product_meta > span > a,
.product_meta > span > span {
  position: relative;
  color: var(--body-color);
  font-weight: 400;
}
.product_meta > span > a:after,
.product_meta > span > span:after {
  content: ",";
  margin-right: 5px;
}
.product_meta > span > a:last-child:after,
.product_meta > span > span:last-child:after {
  display: none;
}
.product_meta > span > a:first-child,
.product_meta > span > span:first-child {
  margin-left: 7px;
}

.product-tab-area {
  margin-top: 80px;
}

.product-tab-style1 {
  border-bottom: 2px solid #EFEFEF;
  margin: 0px auto 40px auto;
  padding-bottom: 20px;
  justify-content: center;
  gap: 30px;
    direction: rtl;
}
.product-tab-style1 .nav-link {
  background-color: transparent;
  color: var(--title-color);
  padding: 0;
  font-size: 16px;
  font-weight: 500;
  font-family: var(--title-font);
  position: relative;
  text-transform: uppercase;
}
.product-tab-style1 .nav-link:after {
  content: "";
  height: 2px;
  background: var(--theme-color);
  width: 0;
  left: 0;
  bottom: -22px;
  position: absolute;
  transition: 0.4s all;
}
.product-tab-style1 .nav-link:hover, .product-tab-style1 .nav-link.active {
  color: var(--theme-color);
}
.product-tab-style1 .nav-link:hover:after, .product-tab-style1 .nav-link.active:after {
  width: 100%;
}

.woocommerce-table {
  margin-bottom: 0;
}

.woocommerce-Reviews .comments-wrap {
  padding: 0;
  box-shadow: none;
}
.woocommerce-Reviews .comment-form {
  background-color: transparent;
  box-shadow: none;
  margin-bottom: 0;
  margin-top: var(--blog-space-y, 30px);
}
.woocommerce-Reviews .comment-form input,
.woocommerce-Reviews .comment-form .form-control {
  background-color: var(--white-color);
}
.woocommerce-Reviews .comment-form .blog-inner-title {
  margin-bottom: 10px;
}
.woocommerce-Reviews .comment-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0 24px;
  margin-bottom: 40px;
}
.woocommerce-Reviews .post-comment {
  padding: 40px !important;
  margin-bottom: 24px;
  background: var(--white-color);
  border: 0 !important;
  margin-bottom: 0;
}
.woocommerce-Reviews .post-comment .name {
  margin-bottom: 3px;
}
.woocommerce-Reviews .post-comment .commented-on {
  margin-bottom: 12px;
}
.woocommerce-Reviews .post-comment .text {
  margin-bottom: -0.5em;
}

/* Large devices */
@media (max-width: 1199px) {
  .woocommerce-Reviews .post-comment {
    padding: 30px !important;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .woocommerce-Reviews .comment-list {
    grid-template-columns: repeat(1, 1fr);
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .woocommerce-Reviews .comment-form {
    padding: 40px 20px;
  }
}
.product-inner-list > ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}
.product-inner-list li {
  position: relative;
  padding-left: 15px;
}
.product-inner-list li:before {
  content: "-";
  position: absolute;
  left: 0;
}

.share-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--title-color);
  margin-bottom: 8px;
}
.share-title i {
  color: var(--theme-color);
}

.product-slider-wrap {
  background-color: var(--smoke-color3);
  text-align: center;
  border-radius: 6px;
  height: 100%;
  padding: 0 0 50px;
}
.product-slider-wrap .product-thumb {
  padding-top: 100px;
}
.product-slider-wrap .product-indicator {
  display: inline-flex;
  gap: 15px;
  margin-top: 40px;
}
.product-slider-wrap .product-indicator .indicator-btn {
  z-index: 99;
  position: relative;
  background-color: rgb(255, 255, 255);
  border-radius: 6px;
  width: 87px;
  height: 87px;
  display: inline-block;
  line-height: 85px;
  cursor: pointer;
  border: 1px solid var(--white-color);
}
.product-slider-wrap .product-indicator .indicator-btn.active {
  border: 1px solid var(--theme-color);
}
.product-slider-wrap .product-indicator .indicator {
  display: none;
}

.summary-content,
.product-about {
  padding-left: 24px;
}
.summary-content .product-title,
.product-about .product-title {
  margin: -0.2em 0 15px 0;
  font-weight: 500;
  font-size: 36px;
}
.summary-content > .price,
.product-about > .price {
  font-size: 24px;
  font-weight: 600;
  color: var(--theme-color);
  display: block;
  margin-bottom: 25px;
  margin-right: 10px;
  font-family: var(--title-font);
  line-height: inherit;
}
.summary-content > .price del,
.product-about > .price del {
  color: #D3D3D3;
  font-weight: 400;
  margin-left: 13px;
}
.summary-content .product-rating,
.product-about .product-rating {
  display: inline-flex;
  gap: 5px;
  align-items: center;
  position: relative;
  top: 2px;
  font-size: 16px;
  line-height: 20px;
  padding: 0 0 0 0;
  margin: 0 0 0 0;
  margin-bottom: 25px;
}
.summary-content .product-rating .star-rating,
.product-about .product-rating .star-rating {
  width: 80px;
  font-size: 12px;
  margin-right: 8px;
}
.summary-content .product-rating .star-rating span:before,
.product-about .product-rating .star-rating span:before {
  color: var(--theme-color);
}
.summary-content .woocommerce-review-link,
.product-about .woocommerce-review-link {
  color: var(--body-color);
}
.summary-content .checklist,
.product-about .checklist {
  margin: 30px 0 40px 0;
}
.summary-content .checklist li,
.product-about .checklist li {
  font-weight: 400;
}
.summary-content .actions,
.product-about .actions {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  margin: 33px 0 27px 0;
  align-items: center;
}
.summary-content .actions .btn,
.product-about .actions .btn {
  padding: 18px 30px;
}
.summary-content .actions .btn .btn-icon,
.product-about .actions .btn .btn-icon {
  padding: 10.5px 15px 10.5px 15px;
}
.summary-content .actions .icon-btn,
.product-about .actions .icon-btn {
  border-color: var(--border-color);
}
.summary-content .actions .icon-btn:hover,
.product-about .actions .icon-btn:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
  border-color: var(--theme-color);
}
.summary-content .share,
.product-about .share {
  margin-top: 25px;
}
.summary-content .social-btn a,
.product-about .social-btn a {
  --icon-size: 40px;
  line-height: 38px;
  font-size: 14px;
  border: 1px solid var(--border-color);
  border-radius: 0;
}
.summary-content .social-btn a:hover,
.product-about .social-btn a:hover {
  border-color: var(--theme-color);
}
.summary-content .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart-plus.no-txt,
.summary-content .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart.no-txt,
.product-about .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart-plus.no-txt,
.product-about .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart.no-txt {
  width: 50px;
  height: 50px;
  line-height: 50px;
  display: inline-block;
  border-radius: 10px;
}
.summary-content .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart-plus.no-txt::before,
.summary-content .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart.no-txt::before,
.product-about .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart-plus.no-txt::before,
.product-about .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart.no-txt::before {
  position: relative;
  top: 0;
  left: 0;
  line-height: inherit;
  margin: 0;
  font-size: 24px;
}
.summary-content .tinv-wishlist a,
.product-about .tinv-wishlist a {
  display: inline-block;
  width: 60px;
  height: 60px;
  line-height: 60px;
  border-radius: 0;
}
.summary-content .tinv-wishlist a:hover,
.product-about .tinv-wishlist a:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
}
.summary-content .quantity,
.product-about .quantity {
  position: relative;
  background: var(--title-color);
  border-radius: 5px;
  margin-right: 0px;
  height: 50px;
}
.summary-content .quantity .qty-input,
.product-about .quantity .qty-input {
  width: 50px;
  height: 50px;
  background-color: transparent;
  color: var(--white-color);
  text-align: center;
  font-weight: 500;
  border-left: 1px solid #3c3c3c;
  border-right: 1px solid #3c3c3c;
}
.summary-content .quantity .qty-btn,
.product-about .quantity .qty-btn {
  color: var(--white-color);
  background-color: transparent;
  right: 21px;
  border: none;
  line-height: 50px;
}

.product-details .comments-wrap {
  margin-top: 0;
}
.product-details .border-title {
  position: relative;
  padding-bottom: 20px;
  margin-bottom: 40px;
}
.product-details .border-title:before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  height: 2px;
  width: 80px;
  background-color: var(--theme-color);
}

.product-inner-title {
  font-size: 32px;
  border-bottom: 1px solid var(--border-color);
  padding: 0 0 7px 0;
  margin: 0 0 30px 0;
}

.related-product-wrapper {
  padding-top: 115px;
}

.woosq-product .thumbnails img {
  padding: 10px;
}

.woosq-product .thumbnails .slick-dots {
  bottom: auto;
  margin-top: 30px;
}
.woosq-product .thumbnails .slick-dots li ~ li {
  margin-left: 5px;
}
.woosq-product .thumbnails .slick-dots li button {
  background: var(--theme-color);
  position: relative;
}
.woosq-product .thumbnails .slick-dots li button:before {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #fff;
  top: 14px;
  left: 14px;
}

#woosq-popup .single-product .product .tinvwl-shortcode-add-to-cart {
  display: none;
}

.mfp-woosq .mfp-close {
  width: 44px;
  height: 44px;
  line-height: initial;
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .product.list-view .product-img {
    max-width: 150px;
  }
  .woocommerce-Reviews .post-comment {
    display: block;
  }
  .woocommerce-Reviews .post-comment .comment-avater img {
    width: auto;
    margin: 0 0 30px;
  }
}
/* Medium Large devices */
@media (max-width: 1299px) {
  .product-thumb-tab {
    --thumb: 100px;
    margin-left: -40px;
  }
  .product-thumb-area {
    margin-right: 0;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .shop-sort-bar select {
    min-width: auto;
  }
  .summary-content,
  .product-about {
    padding-left: 0;
    margin-top: 35px;
  }
  .summary-content .actions, .product-about .actions {
    margin: 0;
  }
  .product-big-img .img {
    height: 540px;
  }
  .product-slider-wrap .product-indicator .indicator-btn {
    width: 70px;
    height: 70px;
    line-height: 70px;
  }
  .product-slider-wrap {
    padding: 0 0 30px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .product-big-img {
    margin-bottom: 40px;
    margin-left: 50px;
  }
  .product-thumb-tab {
    margin-left: -10px;
  }
  .product-box.list-view .product-img {
    max-width: 150px;
  }
  .shop-sort-bar .row {
    --bs-gutter-x: 20px;
  }
  .shop-sort-bar .nav a:last-child {
    margin-right: 0;
    padding-right: 0;
  }
  .shop-sort-bar .nav a:last-child:before {
    display: none;
  }
  .woosq-product > .product .thumbnails {
    max-height: 400px;
    min-height: 200px;
    padding: 10px;
  }
  .woocommerce-Reviews .comment-item ~ .comment-item {
    margin-top: 20px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .shop-sort-bar {
    text-align: center;
  }
  .shop-sort-bar .nav {
    justify-content: center;
  }
  .shop-sort-bar .single-select,
  .shop-sort-bar select {
    margin: 0 auto;
    width: 100%;
  }
  .shop-sort-bar .woocommerce-result-count {
    padding-left: 0;
  }
  .product-box.list-view .product-img {
    max-width: 130px;
  }
  .product-box.list-view .actions {
    --btn-size: 30px;
    --btn-font-size: 10px;
    --icon-gap-x: 2px;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .product-about .actions {
    gap: 15px 15px;
  }
  .product-thumb-tab {
    --thumb: 74px;
    --gap: 6px;
  }
  .product-thumb-tab .tab-btn {
    border-radius: 6px;
  }
  .product-thumb-tab .tab-btn:not(:last-of-type) {
    margin-bottom: 10px;
  }
  .product-thumb-tab .indicator {
    border-radius: 6px;
  }
  .product-big-img {
    margin-left: 28px;
  }
  .product-big-img .img {
    height: 370px;
  }
  .summary-content .product-title, .product-about .product-title {
    font-size: 32px;
  }
  .summary-content > .price, .product-about > .price {
    font-size: 24px;
    margin-bottom: 9px;
  }
  .product-tab-area {
    margin-top: 40px;
  }
  .product-tab-style1 {
    border: 0;
    gap: 15px;
  }
  .product-tab-style1 .nav-link:after {
    bottom: -4px;
  }
}
/* Extra small devices */
@media (max-width: 375px) {
  .product.list-view .product-img {
    max-width: 130px;
  }
  .product-slider-wrap .product-indicator .indicator-btn {
    width: 60px;
    height: 60px;
    line-height: 60px;
  }
  .product-slider-wrap .product-indicator {
    gap: 10px;
  }
  .summary-content .product-title, .product-about .product-title {
    font-size: 28px;
  }
}
/*------------------- 4.00. Portfolio -------------------*/
/* Gallery 1 ---------------------------------- */
.portfolio-area-1 {
  padding: 239px 0 180px;
  clip-path: ellipse(110% 100% at 50% 100%);
  background-position: bottom;
}

.gallery-card {
  position: relative;
  border-radius: 20px;
}
.gallery-card:after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  background: var(--theme-color);
  mix-blend-mode: multiply;
  height: 100%;
  width: 100%;
  border-radius: 20px;
  transition: 0.4s;
  opacity: 0;
}
.gallery-card .gallery-img {
  position: relative;
}
.gallery-card .gallery-img img {
  border-radius: 20px;
}
.gallery-card .gallery-content {
  position: absolute;
  z-index: 1;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--white-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 60px;
  border-radius: 10px;
  padding: 40px;
  transition: 0.4s;
  opacity: 0;
}
.gallery-card .gallery-content_subtitle {
  letter-spacing: 0.05em;
  text-transform: uppercase;
  color: var(--theme-color);
  font-size: 14px;
  font-weight: 500;
  margin-top: -0.3em;
  margin-bottom: 5px;
}
.gallery-card .gallery-content_title {
  font-weight: 500;
  font-size: 24px;
  color: var(--title-color);
  margin-bottom: -0.3em;
}
.gallery-card .gallery-content .icon-btn {
  --btn-size: 50px;
  background: #FFFFFF;
  border: 1px solid rgba(237, 237, 237, 0.89);
  box-shadow: 0px 16px 51px rgba(0, 0, 0, 0.07);
  border-radius: 50%;
  color: var(--theme-color);
}

/* Medium Large devices */
@media (max-width: 1299px) {
  .gallery-card .gallery-img img {
    width: 100%;
  }
}
/* Flip Slider --------------------------------------*/
.flipster--carousel .flipster__item--past-1 .flipster__item__content {
  -webkit-transform: rotateY(45deg) scale(0.8);
  transform: rotateY(0deg) scale(0.8);
}

.flipster--carousel .flipster__item--future-1 .flipster__item__content {
  -webkit-transform: rotateY(-45deg) scale(0.8);
  transform: rotateY(0deg) scale(0.8);
}

.flipster--carousel .flipster__item--past-2 .flipster__item__content {
  -webkit-transform: translateX(25%) rotateY(40deg) scale(0.65);
  transform: translateX(150%) rotateY(0deg) scale(0.8);
}

.flipster--carousel .flipster__item--future-2 .flipster__item__content {
  -webkit-transform: translateX(-25%) rotateY(-40deg) scale(0.65);
  transform: translateX(-150%) rotateY(0deg) scale(0.8);
}

.flipster__item--current .gallery-card:after {
  opacity: 1;
}
.flipster__item--current .gallery-card .gallery-content {
  opacity: 1;
    text-align: right;
    direction: rtl;
}

.flip-gallery {
  overflow: hidden;
}
.flip-gallery-area {
  position: relative;
}
.flip-gallery .flipster__nav {
  display: none;
}
.flip-gallery .flipster__nav__item--current .flipster__nav__child {
  display: none;
}
.flip-gallery .flipster__button {
  top: 46%;
}

.flipster__button {
  height: 115px;
  width: 115px;
  line-height: 115px;
  border: 0;
  font-size: 30px;
  background-color: var(--title-color);
  text-align: center;
  opacity: 1;
  border-radius: 50%;
  color: var(--white-color);
  transition: 0.4s;
}
.flipster__button:after {
  content: "";
  position: absolute;
  right: 47px;
  top: 50%;
  width: 22px;
  height: 3px;
  transform: translateY(-1px);
  background: var(--white-color);
}
.flipster__button svg {
  max-width: 20px;
  stroke-width: 2px;
  margin-top: -5px;
}
.flipster__button--prev {
  left: 21%;
}
.flipster__button--prev svg {
  margin-right: 20px;
}
.flipster__button--next {
  right: 21%;
}
.flipster__button--next svg {
  margin-left: 20px;
}
.flipster__button:hover {
  background: var(--theme-color);
}

@media (max-width: 1600px) {
  .flip-gallery-area {
    margin: 0 100px;
  }
  .flipster--carousel .flipster__item--past-1 .flipster__item__content {
    transform: rotateY(0deg) scale(0.7) translateX(76px);
  }
  .flipster--carousel .flipster__item--future-1 .flipster__item__content {
    transform: translateX(-53px) rotateY(0deg) scale(0.7);
  }
  .flipster--carousel .flipster__item--past-2 .flipster__item__content {
    transform: translateX(144%) rotateY(0deg) scale(0.7);
  }
  .flipster--carousel .flipster__item--future-2 .flipster__item__content {
    transform: translateX(-144%) rotateY(0deg) scale(0.7);
  }
  .flipster__button--prev {
    left: 9.8%;
  }
  .flipster__button--next {
    right: 9.8%;
  }
}
/* Extra large devices */
@media (max-width: 1500px) {
  .portfolio-area-1 {
    clip-path: ellipse(130% 100% at 50% 100%);
  }
  .flipster--carousel .flipster__item--past-1 .flipster__item__content {
    transform: rotateY(0deg) scale(0.7) translateX(190px);
  }
  .flipster--carousel .flipster__item--future-1 .flipster__item__content {
    transform: translateX(-132px) rotateY(0deg) scale(0.7);
  }
  .flipster--carousel .flipster__item--past-2 .flipster__item__content {
    transform: translateX(134.8%) rotateY(0deg) scale(0.7);
  }
  .flipster--carousel .flipster__item--future-2 .flipster__item__content {
    transform: translateX(-134.8%) rotateY(0deg) scale(0.7);
  }
  .flipster__button--prev {
    left: 4.8%;
  }
  .flipster__button--next {
    right: 4.8%;
  }
  .portfolio-area-1 {
    padding: 239px 0 150px;
  }
}
/* Medium Large devices */
@media (max-width: 1399px) {
  .flip-gallery-area {
    margin: 0 50px;
  }
  .flipster__button {
    height: 85px;
    width: 85px;
    line-height: 85px;
  }
  .flipster__button:after {
    right: 30px;
  }
  .flipster__button--prev svg {
    margin-right: 13px;
  }
  .flipster__button--prev {
    left: 6.2%;
  }
  .flipster__button--next {
    right: 6.2%;
  }
  .flipster__button--next:after {
    right: 33px;
  }
  .flipster__button--next svg {
    margin-left: 13px;
  }
  .portfolio-area-1 {
    padding: 195px 0 150px;
  }
}
/* Medium Large devices */
@media (max-width: 1299px) {
  .flipster--carousel .flipster__item--past-1 .flipster__item__content {
    transform: rotateY(0deg) scale(0.7) translateX(290px);
  }
  .flipster--carousel .flipster__item--future-1 .flipster__item__content {
    transform: translateX(-203px) rotateY(0deg) scale(0.7);
  }
  .flipster--carousel .flipster__item--past-2 .flipster__item__content {
    transform: translateX(126.7%) rotateY(0deg) scale(0.7);
  }
  .flipster--carousel .flipster__item--future-2 .flipster__item__content {
    transform: translateX(-126.7%) rotateY(0deg) scale(0.7);
  }
  .flipster__button--next {
    right: 0;
  }
  .flipster__button--prev {
    left: 0;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .portfolio-area-1 {
    clip-path: ellipse(160% 100% at 50% 100%);
  }
  .flipster--carousel .flipster__item--past-2 .flipster__item__content,
  .flipster--carousel .flipster__item--future-2 .flipster__item__content,
  .flipster--carousel .flipster__item--past-1 .flipster__item__content,
  .flipster--carousel .flipster__item--future-1 .flipster__item__content {
    transform: scale(0);
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .portfolio-area-1 {
    padding: 155px 0 120px;
  }
  .flip-gallery-area {
    margin: -65px 20px;
  }
  .flipster--carousel .flipster__item--current .flipster__item__content {
    transform: translateX(0) rotateY(0deg) scale(0.8);
  }
  .flipster__button {
    height: 75px;
    width: 75px;
    line-height: 75px;
  }
  .flipster__button--prev {
    left: -28px;
  }
  .flipster__button--next {
    right: -28px;
  }
  .flipster__button:after {
    right: 27px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .portfolio-area-1 {
    clip-path: ellipse(190% 100% at 50% 100%);
  }
  .flipster--carousel .flipster__item--current .flipster__item__content {
    transform: translateX(-35px) rotateY(0deg) scale(0.6);
  }
  .flipster__button {
    display: none;
  }
  .gallery-card .gallery-content_title {
    font-size: 34px;
  }
  .gallery-card .gallery-content_subtitle {
    font-size: 24px;
  }
  .gallery-card .gallery-content .icon-btn {
    --btn-size: 70px;
    font-size: 20px;
  }
  .flip-gallery-area {
    margin: -131px 20px;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .flip-gallery-area {
    margin: -82px 0px;
  }
  .flipster--carousel .flipster__item--current .flipster__item__content {
    transform: translateX(-50px) rotateY(0deg) scale(0.55);
  }
  .gallery-card .gallery-content .icon-btn {
    flex: none;
  }
  .flip-gallery-area {
    margin: -118px 0px -82px;
  }
}
/* Extra small devices */
@media (max-width: 375px) {
  .flipster--carousel .flipster__item--current .flipster__item__content {
    transform: translateX(-43px) rotateY(0deg) scale(0.55);
  }
  .gallery-card .gallery-content {
    margin: 40px;
  }
}
@media (max-width: 320px) {
  .flipster--carousel .flipster__item--current .flipster__item__content {
    transform: translateX(-37px) rotateY(0deg) scale(0.55);
  }
  .flip-gallery-area {
    margin: -86px 0px -82px;
  }
}
.portfolio-slider2 {
  transform: translate(0, 227px);
  margin-top: -227px;
  margin-bottom: 227px;
  padding: 0 25px;
}

.project-card {
  position: relative;
}
.project-card .project-img img {
  width: 100%;
}
.project-card .project-content {
  position: absolute;
  bottom: -30px;
  left: 0;
  right: 0;
  background: var(--white-color);
  margin: 30px;
  padding: 30px;
  transition: 0.4s;
  opacity: 0;
}
.project-card .project-content .project-subtitle {
  font-size: 16px;
  font-family: var(--body-color);
  font-weight: 500;
  color: var(--theme-color);
  margin-bottom: 6px;
  margin-top: -0.3em;
}
.project-card .project-content .project-title {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: -0.35em;
}
.project-card .project-content .project-title a {
  color: var(--title-color);
}
.project-card .project-content .project-title a:hover {
  color: var(--theme-color);
}
.project-card:hover .project-content {
  opacity: 1;
  bottom: 0;
}

/* Extra large devices */
@media (max-width: 1500px) {
  .project-card .project-content .project-title {
    font-size: 22px;
  }
}
/* Medium Large devices */
@media (max-width: 1299px) {
  .portfolio-slider2 {
    transform: translate(0, 185px);
    margin-top: -185px;
    margin-bottom: 185px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .portfolio-slider2 {
    transform: translate(0, 150px);
    margin-top: -150px;
    margin-bottom: 150px;
  }
  .project-card .project-content {
    margin: 25px;
    padding: 25px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .portfolio-slider2 {
    transform: translate(0, 270px);
    margin-top: -270px;
    margin-bottom: 270px;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .portfolio-slider2 {
    transform: translate(0, 200px);
    margin-top: -200px;
    margin-bottom: 200px;
    padding: 0;
  }
}
.portfolio-thumb {
  position: relative;
}
.portfolio-thumb img {
  border-radius: 20px;
  width: 100%;
}
.portfolio-thumb .icon-btn {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -30%);
  background: var(--white-color);
  border-radius: 50%;
  color: var(--theme-color);
  height: 50px;
  width: 50px;
  line-height: 50px;
  opacity: 0;
  z-index: 1;
}
.portfolio-thumb:after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  border-radius: 20px;
  background: var(--theme-color);
  mix-blend-mode: multiply;
  z-index: 0;
  opacity: 0;
  transition: 0.4s;
}
.portfolio-thumb:hover .icon-btn, .portfolio-thumb:hover:after {
  opacity: 1;
}
.portfolio-thumb:hover .icon-btn {
  transform: translate(-50%, -50%);
}

/*****single-project-details-wrap*****/
.project-details-single-page .thumb {
  border-radius: 20px 20px 0 0;
  overflow: hidden;
}
.project-details-single-page .thumb img {
  width: 100%;
}
.project-details-single-page .thumb2 {
  border-radius: 15px;
  overflow: hidden;
}
.project-details-single-page .thumb2 img {
  width: 100%;
}

.project-details-content {
  background: var(--title-color);
  display: flex;
  justify-content: space-between;
  padding: 70px 60px;
  border-radius: 0 0 20px 20px;
  margin-bottom: 70px;
}

.single-project-details-wrap {
  display: flex;
  gap: 25px;
}
.single-project-details-wrap .icon {
  height: 70px;
  width: 70px;
  line-height: 70px;
  border-radius: 50%;
  background: var(--white-color);
  font-size: 22px;
  color: var(--theme-color);
  text-align: center;
  flex: none;
}
.single-project-details-wrap .subtitle {
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  color: var(--white-color);
  margin-bottom: 6px;
  margin-top: -0.3em;
}
.single-project-details-wrap .title {
  font-size: 18px;
  font-weight: 500;
  line-height: 24px;
  color: var(--white-color);
  max-width: 180px;
  margin-bottom: 0;
}

/* Large devices */
@media (max-width: 1199px) {
  .project-details-content {
    padding: 50px 40px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .project-details-content {
    display: block;
  }
  .project-details-content .single-project-details-wrap ~ .single-project-details-wrap {
    margin-top: 30px;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .project-details-content {
    padding: 40px 30px;
  }
}
/*------------------- 4.00. Schedule -------------------*/
.schedule-area-1 {
  clip-path: ellipse(195% 133% at 50% 137.9%);
}

.schedule-tab-1 .btn {
  margin: 0 3px;
}
.schedule-tab-1 .btn:before {
  transform: skewX(-10deg);
}
.schedule-tab-1 .btn.active:before {
  background: var(--theme-color);
}

.schedule-table {
  border: 0;
  max-width: 1304px;
  text-align: center;
  margin-bottom: 0;
}
.schedule-table thead th {
  border-top: 0;
  border-bottom: 0;
  padding: 21.5px 0;
  font-size: 14px;
  font-weight: 300;
  font-family: var(--title-font);
  color: var(--title-color);
}
.schedule-table thead th:last-child {
  border-right: 0;
}
.schedule-table thead th:first-child {
  border-left: 0;
}
.schedule-table tbody tr th {
  border-left: 0;
  font-size: 14px;
  font-weight: 300;
  font-family: var(--title-font);
  color: var(--title-color);
  min-width: 142px;
  vertical-align: middle;
  padding: 0 38px 0 15px;
}
.schedule-table tbody tr td {
  font-size: 14px;
  font-weight: 300;
  font-family: var(--title-font);
  color: var(--body-color);
  padding: 36.5px 27px;
  min-width: 147px;
  vertical-align: middle;
}
.schedule-table tbody tr td.active {
  background: var(--title-color);
}
.schedule-table tbody tr td.active span {
  color: var(--white-color);
}
.schedule-table tbody tr td span {
  display: block;
  font-size: 16px;
  font-weight: 500;
  font-family: var(--title-font);
  color: var(--title-color);
  text-transform: uppercase;
  min-width: 110px;
}
.schedule-table tbody tr td:last-child {
  border-right: 0;
}
.schedule-table tbody tr:last-child td,
.schedule-table tbody tr:last-child th {
  border-bottom: 0;
}

/* Medium Large devices */
@media (max-width: 1299px) {
  .schedule-tab-1 .table-responsive {
    max-width: 1100px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .schedule-tab-1 .table-responsive {
    max-width: 940px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .schedule-tab-1 .table-responsive {
    max-width: 695px;
  }
  .schedule-tab-1 .filter-menu-active {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
  }
  .schedule-tab-1 .btn {
    margin: 6px 3px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .schedule-tab-1 .table-responsive {
    max-width: 515px;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .schedule-tab-1 .table-responsive {
    max-width: 400px;
  }
  .schedule-tab-1 .filter-menu-active {
    grid-template-columns: repeat(2, 1fr);
  }
}
/* Extra small devices */
@media (max-width: 375px) {
  .schedule-tab-1 .table-responsive {
    max-width: 350px;
  }
}
@media (max-width: 320px) {
  .schedule-tab-1 .table-responsive {
    max-width: 295px;
  }
}
/*=================================
    05. Spacing
==================================*/
/*-- Padding Left And Right --*/
.px-5 {
  padding-right: 5px;
  padding-left: 5px;
}

.px-10 {
  padding-right: 10px;
  padding-left: 10px;
}

.px-15 {
  padding-right: 15px;
  padding-left: 15px;
}

.px-20 {
  padding-right: 20px;
  padding-left: 20px;
}

.px-25 {
  padding-right: 25px;
  padding-left: 25px;
}

.px-30 {
  padding-right: 30px;
  padding-left: 30px;
}

.px-35 {
  padding-right: 35px;
  padding-left: 35px;
}

.px-40 {
  padding-right: 40px;
  padding-left: 40px;
}

.px-45 {
  padding-right: 45px;
  padding-left: 45px;
}

.px-50 {
  padding-right: 50px;
  padding-left: 50px;
}

/*-- Padding Top And Bottom --*/
.py-5 {
  padding-top: 5px;
  padding-bottom: 5px;
}

.py-10 {
  padding-top: 10px;
  padding-bottom: 10px;
}

.py-15 {
  padding-top: 15px;
  padding-bottom: 15px;
}

.py-20 {
  padding-top: 20px;
  padding-bottom: 20px;
}

.py-25 {
  padding-top: 25px;
  padding-bottom: 25px;
}

.py-30 {
  padding-top: 30px;
  padding-bottom: 30px;
}

.py-35 {
  padding-top: 35px;
  padding-bottom: 35px;
}

.py-40 {
  padding-top: 40px;
  padding-bottom: 40px;
}

.py-45 {
  padding-top: 45px;
  padding-bottom: 45px;
}

.py-50 {
  padding-top: 50px;
  padding-bottom: 50px;
}

/*-- Padding Top --*/
.pt-5 {
  padding-top: 5px;
}

.pt-10 {
  padding-top: 10px;
}

.pt-15 {
  padding-top: 15px;
}

.pt-20 {
  padding-top: 20px;
}

.pt-25 {
  padding-top: 25px;
}

.pt-30 {
  padding-top: 30px;
}

.pt-35 {
  padding-top: 35px;
}

.pt-40 {
  padding-top: 40px;
}

.pt-45 {
  padding-top: 45px;
}

.pt-50 {
  padding-top: 50px;
}

/*-- Padding Bottom --*/
.pb-5 {
  padding-bottom: 5px;
}

.pb-10 {
  padding-bottom: 10px;
}

.pb-15 {
  padding-bottom: 15px;
}

.pb-20 {
  padding-bottom: 20px;
}

.pb-25 {
  padding-bottom: 25px;
}

.pb-30 {
  padding-bottom: 30px;
}

.pb-35 {
  padding-bottom: 35px;
}

.pb-40 {
  padding-bottom: 40px;
}

.pb-45 {
  padding-bottom: 45px;
}

.pb-50 {
  padding-bottom: 50px;
}

/*-- Padding Left --*/
.pl-5 {
  padding-left: 5px;
}

.pl-10 {
  padding-left: 10px;
}

.pl-15 {
  padding-left: 15px;
}

.pl-20 {
  padding-left: 20px;
}

.pl-25 {
  padding-left: 25px;
}

.pl-30 {
  padding-left: 30px;
}

.pl-35 {
  padding-left: 35px;
}

.pl-40 {
  padding-left: 40px;
}

.pl-45 {
  padding-left: 45px;
}

.pl-50 {
  padding-left: 50px;
}

/*-- Padding Right --*/
.pr-5 {
  padding-right: 5px;
}

.pr-10 {
  padding-right: 10px;
}

.pr-15 {
  padding-right: 15px;
}

.pr-20 {
  padding-right: 20px;
}

.pr-25 {
  padding-right: 25px;
}

.pr-30 {
  padding-right: 30px;
}

.pr-35 {
  padding-right: 35px;
}

.pr-40 {
  padding-right: 40px;
}

.pr-45 {
  padding-right: 45px;
}

.pr-50 {
  padding-right: 50px;
}

.pb-60 {
  padding-bottom: 60px;
}

.pb-105 {
  padding-bottom: 105px;
}

.pt-105 {
  padding-top: 105px;
}

/*-- margin Left And Right --*/
.mx-5 {
  margin-right: 5px;
  margin-left: 5px;
}

.mx-10 {
  margin-right: 10px;
  margin-left: 10px;
}

.mx-15 {
  margin-right: 15px;
  margin-left: 15px;
}

.mx-20 {
  margin-right: 20px;
  margin-left: 20px;
}

.mx-25 {
  margin-right: 25px;
  margin-left: 25px;
}

.mx-30 {
  margin-right: 30px;
  margin-left: 30px;
}

.mx-35 {
  margin-right: 35px;
  margin-left: 35px;
}

.mx-40 {
  margin-right: 40px;
  margin-left: 40px;
}

.mx-45 {
  margin-right: 45px;
  margin-left: 45px;
}

.mx-50 {
  margin-right: 50px;
  margin-left: 50px;
}

/*-- margin Top And Bottom --*/
.my-5 {
  margin-top: 5px;
  margin-bottom: 5px;
}

.my-10 {
  margin-top: 10px;
  margin-bottom: 10px;
}

.my-15 {
  margin-top: 15px;
  margin-bottom: 15px;
}

.my-20 {
  margin-top: 20px;
  margin-bottom: 20px;
}

.my-25 {
  margin-top: 25px;
  margin-bottom: 25px;
}

.my-30 {
  margin-top: 30px;
  margin-bottom: 30px;
}

.my-35 {
  margin-top: 35px;
  margin-bottom: 35px;
}

.my-40 {
  margin-top: 40px;
  margin-bottom: 40px;
}

.my-45 {
  margin-top: 45px;
  margin-bottom: 45px;
}

.my-50 {
  margin-top: 50px;
  margin-bottom: 50px;
}

/*-- margin Top --*/
.mt-5 {
  margin-top: 5px;
}

.mt-10 {
  margin-top: 10px;
}

.mt-15 {
  margin-top: 15px;
}

.mt-20 {
  margin-top: 20px;
}

.mt-25 {
  margin-top: 25px;
}

.mt-30 {
  margin-top: 30px;
}

.mt-35 {
  margin-top: 35px;
}

.mt-40 {
  margin-top: 40px;
}

.mt-45 {
  margin-top: 45px;
}

.mt-50 {
  margin-top: 50px;
}

/*-- margin Bottom --*/
.mb-5 {
  margin-bottom: 5px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-15 {
  margin-bottom: 15px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-25 {
  margin-bottom: 25px;
}

.mb-30 {
  margin-bottom: 30px;
}

.mb-35 {
  margin-bottom: 35px;
}

.mb-40 {
  margin-bottom: 40px;
}

.mb-45 {
  margin-bottom: 45px;
}

.mb-50 {
  margin-bottom: 50px;
}

/*-- margin Left --*/
.ml-5 {
  margin-left: 5px;
}

.ml-10 {
  margin-left: 10px;
}

.ml-15 {
  margin-left: 15px;
}

.ml-20 {
  margin-left: 20px;
}

.ml-25 {
  margin-left: 25px;
}

.ml-30 {
  margin-left: 30px;
}

.ml-35 {
  margin-left: 35px;
}

.ml-40 {
  margin-left: 40px;
}

.ml-45 {
  margin-left: 45px;
}

.ml-50 {
  margin-left: 50px;
}

/*-- margin Right --*/
.mr-5 {
  margin-right: 5px;
}

.mr-10 {
  margin-right: 10px;
}

.mr-15 {
  margin-right: 15px;
}

.mr-20 {
  margin-right: 20px;
}

.mr-25 {
  margin-right: 25px;
}

.mr-30 {
  margin-right: 30px;
}

.mr-35 {
  margin-right: 35px;
}

.mr-40 {
  margin-right: 40px;
}

.mr-45 {
  margin-right: 45px;
}

.mr-50 {
  margin-right: 50px;
}

.mb-60 {
  margin-bottom: 60px;
}

.mt-n1 {
  margin-top: -0.25rem;
}

.mt-n2 {
  margin-top: -0.65rem;
}

.mt-n3 {
  margin-top: -0.8rem;
}

.mt-n4 {
  margin-top: -1.5rem;
}

.mt-n5 {
  margin-top: -3rem;
}

.mb-n1 {
  margin-bottom: -0.25rem;
}

.mb-n2 {
  margin-bottom: -0.6rem;
}

.mb-n3 {
  margin-bottom: -0.8rem;
}

.mb-n4 {
  margin-bottom: -1.5rem;
}

.mb-n5 {
  margin-bottom: -3rem;
}

.space,
.space-top {
  padding-top: var(--section-space);
}

.space,
.space-bottom {
  padding-bottom: var(--section-space);
}

.space-extra,
.space-extra-top {
  padding-top: calc(var(--section-space) - 30px);
}

.space-extra,
.space-extra-bottom {
  padding-bottom: calc(var(--section-space) - 30px);
}

.space-extra2,
.space-extra2-top {
  padding-top: calc(var(--section-space) - 40px);
}

.space-extra2,
.space-extra2-bottom {
  padding-bottom: calc(var(--section-space) - 40px);
}

/* Medium devices */
@media (max-width: 991px) {
  .space,
  .space-top {
    padding-top: var(--section-space-mobile);
  }
  .space,
  .space-bottom {
    padding-bottom: var(--section-space-mobile);
  }
  .space-extra,
  .space-extra-top {
    padding-top: calc(var(--section-space-mobile) - 30px);
  }
  .space-extra,
  .space-extra-bottom {
    padding-bottom: calc(var(--section-space-mobile) - 30px);
  }
  .space-top-md-none {
    padding-top: 0;
  }
  .space-extra2,
  .space-extra2-top {
    padding-top: 70px;
  }
  .space-extra2,
  .space-extra2-bottom {
    padding-bottom: 70px;
  }
  .pb-105 {
    padding-bottom: 65px;
  }
  .pt-105 {
    padding-top: 65px;
  }
}
:root{
    --text-color:rgba(34,42,66,.7);
    --success-text:#0d6832;
    --primary-text:#273e63;
    --warning-text:#73510d;
    --danger-text: #A61001;
    --success-bg:#d6f0e0;
    --primary-bg:#dfe7f6;
    --warning-bg:#fbf0da;
    --danger-bg: #FFEBE9;
    --primary-btn-text:#3b71ca;
}

*{
    /* color:var(--text-color); */
    font-family: 'Open Sans', sans-serif;
    font-family: 'Poppins', sans-serif;
}

.main{
    background-color: #f5f6f8bb !important;
    height: 100vh;
    
}

.text-primary{
    color:var(--primary-btn-text) !important;
}

.fw-bold{
    font-weight: 500 !important;
}

h2{
    font-weight: 400;
    margin-bottom: unset;
    
}

.action-icon{
    font-size: 1.08rem;
}

/* //Badge color overwirte */



.badge-success{
    color:var(--success-text) !important;
    background-color:var(--success-bg);
    border: 1px solid;
}

.badge-primary{
    color:var(--primary-text) !important;;
    background-color:var(--primary-bg);
    border: 1px solid;
}

.badge-warning{
    color:var(--warning-text) !important;;
    background-color:var(--warning-bg);
    border: 1px solid;
}

.badge-danger{
    color:var(--danger-text) !important;;
    background-color: var(--danger-bg);
    border: 1px solid;
}

.time{
  font-size:.75rem;
}



/* table styling */

/* primary table container  */
.table-container{
    box-shadow: 0px 1px 2px 0px rgb(60 64 67 / 25%), 0px 2px 6px 2px rgb(60 64 67 / 10%);
    padding:1rem;
    border-radius: 12px;
    background-color: white;
}


th{
    padding:1rem .5rem !important;   
   font-size: .875rem;
    margin-bottom: 1rem !important;
    background-color: white !important;
    color:var(--text-color) !important;
    font-weight: 600 !important;
}

th:last-child  {
   border-top-right-radius: 12px;
}

 th:first-child  {
   border-top-left-radius: 12px;
}

/* //Removes border from last table row */

 


/* Changing TH bottom border color*/
.table>:not(:last-child)>:last-child>* {
     border-bottom-color: rgba(128, 128, 128, 0.277) !important;
    
}
#mytable{
    border-radius: 20px;
}
.myprofile{
    color:white;
    display:flex;
      justify-content: center;
}
ul{
    margin-bottom: 0rem !important;
}

  .schedule-table{
        filter: blur(3px);
    } 
.topp{
    position: absolute;
    width: 11%;
    left: 45%;
    height: 61px;
    top: 251%;
    background: black;
    border-radius: 30px;
}
@media (max-width: 575px) {
.topp{
    width: 35%;
    }}
.sectionOne{
    direction:rtl;
}
  .iconsNav{
        margin-right:337px;
    }