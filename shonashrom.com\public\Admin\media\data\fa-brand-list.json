["-500px", "-accessible-icon", "-accusoft", "-acquisitions-incorporated", "-adn", "-adobe", "-adversal", "-affiliatetheme", "-airbnb", "-algolia", "-alipay", "-amazon", "-amazon-pay", "-amilia", "-android", "-angellist", "-angrycreative", "-angular", "-app-store", "-app-store-ios", "-apper", "-apple", "-apple-pay", "-artstation", "-asymmetrik", "-atlassian", "-audible", "-autoprefixer", "-avianex", "-aviato", "-aws", "-bandcamp", "-battle-net", "-behance", "-behance-square", "-bimobject", "-bitbucket", "-bitcoin", "-bity", "-black-tie", "-blackberry", "-blogger", "-blogger-b", "-bluetooth", "-bluetooth-b", "-bootstrap", "-btc", "-buffer", "-buromobelexperte", "-buy-n-large", "-buysellads", "-canadian-maple-leaf", "-cc-amazon-pay", "-cc-amex", "-cc-apple-pay", "-cc-diners-club", "-cc-discover", "-cc-jcb", "-cc-mastercard", "-cc-paypal", "-cc-stripe", "-cc-visa", "-centercode", "-centos", "-chrome", "-chromecast", "-cloudscale", "-cloudsmith", "-cloudversify", "-codepen", "-codie<PERSON>", "-confluence", "-connectdevelop", "-contao", "-cotton-bureau", "-c<PERSON>el", "-creative-commons", "-creative-commons-by", "-creative-commons-nc", "-creative-commons-nc-eu", "-creative-commons-nc-jp", "-creative-commons-nd", "-creative-commons-pd", "-creative-commons-pd-alt", "-creative-commons-remix", "-creative-commons-sa", "-creative-commons-sampling", "-creative-commons-sampling-plus", "-creative-commons-share", "-creative-commons-zero", "-critical-role", "-css3", "-css3-alt", "-cuttlefish", "-d-and-d", "-d-and-d-beyond", "-dailymotion", "-dashcube", "-delicious", "-deploydog", "-deskpro", "-dev", "-deviantart", "-dhl", "-diaspora", "-digg", "-digital-ocean", "-discord", "-discourse", "-do<PERSON>b", "-docker", "-draft2digital", "-dribbble", "-dribbble-square", "-dropbox", "-drupal", "-dyalog", "-earlybirds", "-ebay", "-edge", "-elementor", "-ello", "-ember", "-empire", "-envira", "-er<PERSON>", "-ethereum", "-etsy", "-evernote", "-expeditedssl", "-facebook", "-facebook-f", "-facebook-messenger", "-facebook-square", "-fantasy-flight-games", "-fedex", "-fedora", "-figma", "-firefox", "-firefox-browser", "-first-order", "-first-order-alt", "-firstdraft", "-flickr", "-flipboard", "-fly", "-font-awesome", "-font-awesome-alt", "-font-awesome-flag", "-fonticons", "-fonticons-fi", "-fort-awesome", "-fort-awesome-alt", "-forumbee", "-foursquare", "-free-code-camp", "-freebsd", "-fulcrum", "-galactic-republic", "-galactic-senate", "-get-pocket", "-gg", "-gg-circle", "-git", "-git-alt", "-git-square", "-gith<PERSON>", "-github-alt", "-github-square", "-gitkraken", "-git<PERSON>b", "-gitter", "-glide", "-glide-g", "-gofore", "-goodreads", "-goodreads-g", "-google", "-google-drive", "-google-play", "-google-plus", "-google-plus-g", "-google-plus-square", "-google-wallet", "-grat<PERSON><PERSON>", "-grav", "-gripfire", "-grunt", "-gulp", "-hacker-news", "-hacker-news-square", "-hackerrank", "-hips", "-hire-a-helper", "-hooli", "-hornbill", "-hotjar", "-<PERSON><PERSON>z", "-html5", "-hubspot", "-ideal", "-imdb", "-instagram", "-instagram-square", "-intercom", "-internet-explorer", "-invision", "-ioxhost", "-itch-io", "-itunes", "-itunes-note", "-java", "-jedi-order", "-jenkins", "-jira", "-joget", "-j<PERSON><PERSON>", "-js", "-js-square", "-jsfiddle", "-kaggle", "-keybase", "-keycdn", "-kickstarter", "-kickstarter-k", "-kor<PERSON>", "-la<PERSON>l", "-lastfm", "-lastfm-square", "-lean<PERSON>b", "-less", "-line", "-linkedin", "-linkedin-in", "-linode", "-linux", "-lyft", "-magento", "-mailchimp", "-mandalorian", "-markdown", "-mastodon", "-maxcdn", "-mdb", "-medapps", "-medium", "-medium-m", "-medrt", "-meetup", "-megaport", "-men<PERSON><PERSON>", "-microblog", "-microsoft", "-mix", "-mixcloud", "-mixer", "-mi<PERSON><PERSON>", "-modx", "-monero", "-napster", "-neos", "-nimblr", "-node", "-node-js", "-npm", "-ns8", "-nutritionix", "-odnoklassniki", "-odnoklassniki-square", "-old-republic", "-opencart", "-openid", "-opera", "-optin-monster", "-orcid", "-osi", "-page4", "-pagelines", "-palfed", "-patreon", "-paypal", "-penny-arcade", "-periscope", "-phabricator", "-phoenix-framework", "-phoenix-squadron", "-php", "-pied-piper", "-pied-piper-alt", "-pied-piper-hat", "-pied-piper-pp", "-pied-piper-square", "-pinterest", "-pinterest-p", "-pinterest-square", "-playstation", "-product-hunt", "-pushed", "-python", "-qq", "-quinscape", "-quora", "-r-project", "-raspberry-pi", "-ravelry", "-react", "-reacteurope", "-readme", "-rebel", "-red-river", "-reddit", "-reddit-alien", "-reddit-square", "-redhat", "-renren", "-replyd", "-researchgate", "-resolving", "-rev", "-rocketchat", "-rockrms", "-safari", "-salesforce", "-sass", "-schlix", "-scribd", "-searchengin", "-sellcast", "-sellsy", "-servicestack", "-shirtsinbulk", "-shopify", "-shopware", "-simplybuilt", "-sistrix", "-sith", "-sketch", "-skyatlas", "-skype", "-slack", "-slack-hash", "-slideshare", "-snapchat", "-snapchat-ghost", "-snapchat-square", "-soundcloud", "-sourcetree", "-speakap", "-speaker-deck", "-spotify", "-squarespace", "-stack-exchange", "-stack-overflow", "-stackpath", "-staylinked", "-steam", "-steam-square", "-steam-symbol", "-sticker-mule", "-strava", "-stripe", "-stripe-s", "-<PERSON><PERSON><PERSON>", "-stumbleupon", "-stumbleupon-circle", "-superpowers", "-supple", "-suse", "-swift", "-symfony", "-teamspeak", "-telegram", "-telegram-plane", "-tencent-weibo", "-the-red-yeti", "-themeco", "-<PERSON><PERSON><PERSON>", "-think-peaks", "-trade-federation", "-trello", "-tripadvisor", "-tumblr", "-tumblr-square", "-twitch", "-twitter", "-twitter-square", "-typo3", "-uber", "-ubu<PERSON><PERSON>", "-u<PERSON>t", "-umbraco", "-uniregistry", "-unity", "-untappd", "-ups", "-usb", "-usps", "-<PERSON><PERSON><PERSON>", "-vaadin", "-viacoin", "-viadeo", "-viadeo-square", "-viber", "-vimeo", "-vimeo-square", "-vimeo-v", "-vine", "-vk", "-vnv", "-v<PERSON><PERSON>s", "-waze", "-weebly", "-we<PERSON>o", "-weixin", "-whatsapp", "-whatsapp-square", "-whmcs", "-wikipedia-w", "-windows", "-wix", "-wizards-of-the-coast", "-wolf-pack-battalion", "-wordpress", "-wordpress-simple", "-wpbe<PERSON>ner", "-wpexplorer", "-wpforms", "-wpressr", "-xbox", "-xing", "-xing-square", "-y-combinator", "-yahoo", "-yammer", "-yandex", "-yandex-international", "-yarn", "-yelp", "-yoast", "-youtube", "-youtube-square", "-zhihu"]