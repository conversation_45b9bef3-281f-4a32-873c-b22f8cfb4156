<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAdderssesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('addersses', function (Blueprint $table) {
            $table->id();
            
                $table->longText('Address_Name')->nullable();
                $table->longText('User')->nullable();
                $table->longText('Gov')->nullable();
                $table->longText('City')->nullable();
                $table->longText('Place')->nullable();
                $table->longText('Street')->nullable();
                $table->longText('Bulliding')->nullable();
                $table->longText('Floor')->nullable();
                $table->longText('Flat')->nullable();
                $table->longText('Special_Mark')->nullable();
                $table->longText('Location')->nullable();
                $table->longText('Address_Details')->nullable();
                $table->longText('Address_ID')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('addersses');
    }
}
