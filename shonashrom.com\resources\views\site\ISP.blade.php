@extends('site.index')
@section('content')

<title>ISP-ONE</title>
     <main>        

<!-- page__title-start -->
         <div class="page__title align-items-center" style="background-image: url({{asset('Front/assets/img/bg/Background2.png')}});">
            <div class="container">
               <div class="row">
                  <div class="col-xl-12">
                     <div class="page__title-content text-center">
                        <h3 class="breadcrumb-title breadcrumb-title-sd mb-15">ISP-ONE</h3>
                        <div class="page_title__bread-crumb">
                        <nav>
                           <nav class="breadcrumb-trail breadcrumbs">
                              <ul class="breadcrumb-menu">
                                 <li class="breadcrumb-trail">
                                    <a href="{{url('/')}}"><span>{{trans('admin.Home')}}</span></a>
                                 </li>
                                 <li class="trail-item">
                                    <span>ISP-ONE</span>
                                 </li>
                              </ul>
                           </nav> 
                        </nav>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
         <!-- page__title-end -->


       <!------------------------ BMI start ------------------->
       
       <div class="company-static-area mt-150"  > 
         <div class="schedule-shape-bg fix">
            <img src="{{asset('Front/assets/img/features/schedule-shape.png')}}"  class="img-fluid" alt="">
         </div>
         <div class="schedule-shape-one fix">
            <img src="{{asset('Front/assets/img/bg/BgAnimi3.png')}}"   class="img-fluid img-animi" alt="">
         </div>
         <div class="schedule-shape-two fix">
            <img src="{{asset('Front/assets/img/bg/BgAnimi3.png')}}"  class="img-fluid img-animi" alt="">
         </div>
         <div class="container custome-container">
            <div class="row justify-content-center align-items-center">
               <div class="col-sm-12 col-lg-9 col-md-12">
                  <div class="tp-calculate mb-30">
                     <form action="{{url('CalculateISP')}}" method="post" class="calculate-form-wrapper">
                         @csrf
                        <h3 class="tp-calculate-form-title mb-30">ISP-ONE</h3>
                         
                         
                         @if(!empty(auth()->user()->id))
                    <input type="hidden" name="Name" value="{{auth()->user()->name}}">
                         @else
                                     <div class="input-field mb-15 mt-15">
                           <input type="text" step="any" placeholder="{{trans('admin.Name')}}" name="Name" required>
                        </div>   
                         @endif
                         
                        <div class="input-field mb-15  mt-15">
                             <input type="number" step="any" placeholder="{{trans('admin.Height')}}" name="Height" required>
                        </div>
                        <div class="input-field select-field-arrow mb-15  mt-15 p-relative">
                             <select class="form-control" name="HeightUnit" required>
                                 <option value="">{{trans('admin.HeightUnit')}}</option>
                                 <option value="CM">{{trans('admin.CM')}}</option>
                                 <option value="M">{{trans('admin.Meter')}}</option>
                                 <option value="Inch">{{trans('admin.Inch')}}</option>
                                 <option value="Feet">{{trans('admin.Feet')}}</option>
                             </select>
                        </div>
            
                        <div class="input-field mb-15  mt-15">
                             <input type="number" step="any" placeholder="{{trans('admin.Weight')}}" required name="Weight">
                        </div>
                          <div class="input-field select-field-arrow mb-15  mt-15 p-relative">
                             <select class="form-control" name="WightUnit" required>
                                 <option value="">{{trans('admin.WightUnit')}}</option>
                                 <option value="KG">{{trans('admin.KG')}}</option>
                                 <option value="Pound">{{trans('admin.Pound')}}</option>
                            
                             </select>
                        </div>
                            <div class="input-field mb-15  mt-15">
                           <input type="number" step="any" placeholder="{{trans('admin.Age')}}" name="Age" required>
                        </div> 
                         
                        <div class="input-field select-field-arrow mb-15 p-relative">
                             <select class="form-control" name="Gender" required>
                                 <option value="">{{trans('admin.Gender')}}</option>
                                 <option value="Male">{{trans('admin.Male')}}</option>
                                 <option value="Female">{{trans('admin.Female')}}</option>
                             </select>
                        </div>
                         
                                <div class="input-field select-field-arrow mb-15  mt-15  p-relative">
                             <select class="form-control" name="Sports" required>
                                 <option value="">{{trans('admin.Sports')}}</option>
                                 <option value="0">{{trans('admin.No')}}</option>
                                 <option value="4">{{trans('admin.OneToFourTimes')}}</option>
                                 <option value="5">{{trans('admin.MoreThanFiveTimes')}}</option>
                             </select>
                        </div>
                    
      
                        <div class="input-field">
                             <button type="submit" class="calculate-btn" >{{trans('admin.Calculate')}}   <i class="fal fa-chevron-double-right"></i></button>
                        </div>
                     </form>
                 </div>
               </div>
            </div>
         </div>
      </div>
       <!------------------------ BMI End ------------------->


      </main>

   



@endsection
      