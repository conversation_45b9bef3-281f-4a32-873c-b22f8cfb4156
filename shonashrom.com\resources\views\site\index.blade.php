@php
use App\Helpers\DesignHelper;
use App\Models\ShonaDesign;

$design = DesignHelper::getCurrentDesign();
$company = DesignHelper::getCompanyData();
$fonts = DesignHelper::getFontConfiguration($design);
$isRTL = DesignHelper::isRTL();
@endphp

@include('site.layouts.header')
@include('site.layouts.navbar')

   @yield('content')
     <span id="ex"> @include('admin.layouts.messages')</span>
@include('site.layouts.footer')

@include('components.design-styles', ['design' => $design, 'fonts' => $fonts, 'isRTL' => $isRTL])

<!-- Additional site-specific styles -->
<style>
    /* Hide slider arrows */
    .tp-rightarrow, .tp-leftarrow {
        display: none !important;
    }
</style>
