@include('site.layouts.header')
@include('site.layouts.navbar')
   @yield('content')

@include('site.layouts.footer')

@php
use App\Models\MainEComDesign;
$main=MainEComDesign::orderBy('id','desc')->first();
@endphp

<style>
    .switch-box{
        display: none !important;
    }
    .tp-rightarrow, .tp-leftarrow {
        display: none !important;
    }
</style>

@if($main->Font_Type == 1)
   <!-- google font cairo -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;800;1000&display=swap" rel="stylesheet">
    <style>
        body , h1,h2,h3,h4,h5,h6,div,span,strong,p,a,th,button {
            font-family: 'Cairo', sans-serif  !important;
        }
    </style>
@elseif($main->Font_Type == 2)
   <!-- google font Roboto -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body , h1,h2,h3,h4,h5,h6,div,span,strong,p,a,th,button {
            font-family: 'Roboto', sans-serif  !important;
        }
    </style>
@elseif($main->Font_Type == 3)
   <!-- google font Open Sans -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body , h1,h2,h3,h4,h5,h6,div,span,strong,p,a,th,button {
            font-family: 'Open Sans', sans-serif  !important;
        }
    </style>
@elseif($main->Font_Type == 4)
   <!-- google font Poppins -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body , h1,h2,h3,h4,h5,h6,div,span,strong,p,a,th,button {
            font-family: 'Poppins', sans-serif  !important;
        }
    </style>
@else
   <!-- Default font Alexandria -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,700;1,400;1,700&family=Alexandria:wght@200;300;400;500;800;1000&family=Alexandria:wght@300;500;700&family=Alexandria:wght@200;500;800;900&display=swap" rel="stylesheet">
    <style>
        body , h1,h2,h3,h4,h5,h6,div,span,strong,p,a,th,button {
            font-family: 'Alexandria', sans-serif  !important;
        }
    </style>
@endif

<!-- Apply ERP Design System Styles -->
<style>
    /* Body Background */
    @if($main->Body_BG_Type == 1)
        body {
            background-color: {{$main->Body_BG_Color}} !important;
        }
    @else
        body {
            background-image: url('{{URL::to($main->Body_BG_Image)}}') !important;
            background-size: cover;
            background-repeat: no-repeat;
            background-attachment: fixed;
        }
    @endif

    /* Breadcrumb Styling */
    .page-title-area, .breadcumb-wrapper {
        background-color: {{$main->Breadcumb_BG_Color}} !important;
    }

    .breadcumb-title, .page-title {
        color: {{$main->Breadcumb_Txt_Color}} !important;
    }

    /* Footer Styling */
    .footer-area, .footer-wrapper {
        background-color: {{$main->Sub_Page_BG_Color}} !important;
    }

    .footer-area h2, .footer-area h3, .footer-area h4, .footer-area h5,
    .footer-wrapper h2, .footer-wrapper h3, .footer-wrapper h4, .footer-wrapper h5 {
        color: {{$main->Footer_Title_Color}} !important;
    }

    .footer-area p, .footer-area span, .footer-area div,
    .footer-wrapper p, .footer-wrapper span, .footer-wrapper div {
        color: {{$main->Footer_Txt_Color}} !important;
    }

    .footer-area a, .footer-wrapper a {
        color: {{$main->Footer_Txt_Color}} !important;
    }

    .footer-area a:hover, .footer-wrapper a:hover {
        color: {{$main->Footer_Txt_Hover_Color}} !important;
    }

    /* Social Media Styling */
    .social-links a, .th-social a {
        background-color: {{$main->Footer_Social_BG_Color}} !important;
        color: {{$main->Footer_Social_Color}} !important;
    }

    .social-links a:hover, .th-social a:hover {
        background-color: {{$main->Footer_Social_Hover_BG_Color}} !important;
        color: {{$main->Footer_Social_Hover_Txt_Color}} !important;
    }

    /* Preloader Styling */
    .preloader {
        background-color: {{$main->Preloader_BG_Color}} !important;
    }

    .preloader .loader {
        border-color: {{$main->Preloader_Small_Circle_Color}} !important;
    }

    /* Modal Styling */
    .modal-content {
        background-color: {{$main->Modal_BG_Color}} !important;
        color: {{$main->Modal_Txt_Color}} !important;
    }

    .modal-content .btn-primary {
        background-color: {{$main->Modal_Button_BG_Color}} !important;
        color: {{$main->Modal_Button_Txt_Color}} !important;
        border-color: {{$main->Modal_Button_BG_Color}} !important;
    }

    /* Table Styling */
    .table thead th {
        background-color: {{$main->Table_Header_BG_Color}} !important;
        color: {{$main->Table_Header_Txt_Color}} !important;
    }

    .table tbody td {
        background-color: {{$main->Table_Body_BG_Color}} !important;
        color: {{$main->Table_Body_Txt_Color}} !important;
    }

    .table .btn {
        background-color: {{$main->Table_Button_BG_Color}} !important;
        color: {{$main->Table_Button_Txt_Color}} !important;
        border-color: {{$main->Table_Button_BG_Color}} !important;
    }

    /* Pagination Styling */
    .pagination .page-link {
        background-color: {{$main->Pagination_BG_Color}} !important;
        color: {{$main->Pagination_Txt_Color}} !important;
        border-color: {{$main->Pagination_BG_Color}} !important;
    }

    .pagination .page-item.active .page-link {
        background-color: {{$main->Pagination_Active_BG_Color}} !important;
        color: {{$main->Pagination_Active_Txt_Color}} !important;
        border-color: {{$main->Pagination_Active_BG_Color}} !important;
    }

    /* Copyright Styling */
    .copyright-text, .copy-right {
        color: {{$main->CopyRights_Txt_Color}} !important;
    }

    .copyright-text a, .copy-right a {
        color: {{$main->CopyRights_Klar_Txt_Color}} !important;
    }

    .copyright-text a:hover, .copy-right a:hover {
        color: {{$main->CopyRights_Klar_Hover_Txt_Color}} !important;
    }
</style>
