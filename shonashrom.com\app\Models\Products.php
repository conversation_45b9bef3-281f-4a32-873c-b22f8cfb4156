<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Products extends Model
{
    use HasFactory;
         protected $table = 'products';
     protected $fillable = [
   
     
                'Pro_ID',
                'Group_ID',
                'P_Ar_Name',
                'P_En_Name',
                'Ar_Desc',
                'En_Desc',
                'Ar_Spec',
                'En_Spec',
                'Ar_Sup_Desc',
                'En_Sup_Desc',
                'Image',
                'Price',
                'Offer_Price',
                'Symbol',
                'Type',


    ];
    
               public function Group_ID()
    {
        return $this->belongsTo(Groups::class,'Group_ID');
    }
}
