@extends('site.index')
@section('content')
@php
use App\Models\Rate;
use App\Models\Comments;
@endphp
<title>{{app()->getLocale() == 'ar' ?$Pro->P_Ar_Name :$Pro->P_En_Name}}</title>

    <!--==============================
    Breadcumb
    ============================== -->
    <div class="breadcumb-wrapper" data-bg-src="{{asset('Front/assets/img/bg/breadcrumb-bg.png')}}">
        <!-- bg animated image/ -->
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="breadcumb-content">
               
                        <h1 class="breadcumb-title">{{app()->getLocale() == 'ar' ?$Pro->P_Ar_Name :$Pro->P_En_Name}}</h1>
                        <ul class="breadcumb-menu">
                            <li><a href="{{url('/')}}">{{trans('admin.Home')}}</a></li>
                            <li><a href="{{url('/')}}">{{trans('admin.Shop')}}</a></li>
                            <li class="active">{{app()->getLocale() == 'ar' ?$Pro->P_Ar_Name :$Pro->P_En_Name}}</li>
                        </ul>
                    </div>
                </div>
            </div>

        </div>
    </div>






    <!--==============================
    Product Details
    ==============================-->
    <section class="product-details space-top">
        <div class="container">
                        <form method="post" action="{{url('AddToCart')}}" class="product-form product-form-product-template hidedropdown" enctype="multipart/form-data">
              @csrf
    <input type="hidden"  name="Product" value="{{$Pro->id}}">  
            <div class="row">
                <div class="col-lg-5">
                    <div class="product-thumb">
                        <div class="img"><img src="{{URL::to($pro->Image)}}" alt="Product Image"></div>
                                                 @if($pro->Type == 0)
                                       
                               
                                   <div class="product-tag"> {{trans('admin.Recently')}}</div>
                                          @elseif($pro->Type == 1)
                                 
                                   <div class="product-tag"> {{trans('admin.Special')}}</div>
                                          @elseif($pro->Type == 2)
                                       
                          
                                   <div class="product-tag">{{trans('admin.Finaly')}}</div>
                                  
                                         @endif
                    </div>
                </div>
                <div class="col-xl-6 col-lg-7 align-self-center">
                    <div class="product-about">
                        
                           @if(!empty($Pro->Offer_Price)) 
                      <input type="hidden" name="OfferPrice" value="{{$Pro->Offer_Price}}"> 
                        <input type="hidden" name="Price" value="{{$Pro->Price}}">     
                        <p class="price">{{$Pro->Offer_Price}}  <del>{{$Pro->Price}}</del> {{$Pro->Symbol}}</p>
                        @else
                        
                           <input type="hidden" name="OfferPrice" value="0"> 
                        <input type="hidden" name="Price" value="{{$Pro->Price}}">  
                            <p class="price">{{$Pro->Price}}  {{$Pro->Symbol}}</p>
                        @endif
                        
                        
                        <h2 class="product-title">      {{app()->getLocale() == 'ar' ?$Pro->P_Ar_Name :$Pro->P_En_Name}}</h2> 
                        

                        <div class="product-rating">
                          <!--  <div class="star-rating" role="img" aria-label="Rated 5.00 out of 5"></div> -->
                    
                             @php     
                            $RateCount=Rate::where('Product',$Pro->id)->count();   
                            $RateSum=Rate::where('Product',$Pro->id)->get()->sum('Rate');   
                               
                               if($RateCount == 0){
                               $RateCount=1;
                               }
                               $RATE=$RateSum / $RateCount ;
                               @endphp
                               
                               
                               @if(round($RATE) == 1)
                              <a href="#"><i class="fas fa-star"></i></a>
                               @elseif(round($RATE) == 2)
                              <a href="#"><i class="fas fa-star"></i></a>
                              <a href="#"><i class="fas fa-star"></i></a>
                                @elseif(round($RATE) == 3)
                              <a href="#"><i class="fas fa-star"></i></a>
                              <a href="#"><i class="fas fa-star"></i></a>
                              <a href="#"><i class="fas fa-star"></i></a>
                                @elseif(round($RATE) == 4)
                              <a href="#"><i class="fas fa-star"></i></a>
                              <a href="#"><i class="fas fa-star"></i></a>
                              <a href="#"><i class="fas fa-star"></i></a>
                              <a href="#"><i class="fas fa-star"></i></a>
                                @elseif(round($RATE) == 5)
                              <a href="#"><i class="far fa-star"></i></a>
                              <a href="#"><i class="far fa-star"></i></a>
                              <a href="#"><i class="far fa-star"></i></a>
                              <a href="#"><i class="far fa-star"></i></a>
                              <a href="#"><i class="far fa-star"></i></a>
                               @endif
                        </div>
                     
               
                        <div class="actions">
                            <div class="quantity">
                                <button class="quantity-minus qty-btn"><i class="fa-solid fa-minus"></i></button>
                                <input type="number" class="qty-input" step="1" min="1" max="100" name="Qty" value="1" title="Qty">
                                <button class="quantity-plus qty-btn"><i class="fa-solid fa-plus"></i></button>                                
                            </div>
                            <button class="btn" type="submit"><i class="fas fa-shopping-basket"></i></button>
                        </div>
              
                    </div>
                </div>
            </div>
            </form>
            
            
            <div class="product-tab-area">
                <ul class="nav product-tab-style1" id="productTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <a class="nav-link active" id="description-tab" data-bs-toggle="tab" href="#description" role="tab" aria-controls="description" aria-selected="false">{{trans('admin.Desc')}}</a>
                    </li>
                    <li class="nav-item" role="presentation">
                        <a class="nav-link" id="info-tab" data-bs-toggle="tab" href="#add_info" role="tab" aria-controls="add_info" aria-selected="false">{{trans('admin.AdditionalInformation')}}</a>
                    </li>
                    <li class="nav-item" role="presentation">
                        <a class="nav-link" id="reviews-tab" data-bs-toggle="tab" href="#reviews" role="tab" aria-controls="reviews" aria-selected="true">{{trans('admin.Reviews')}}</a>
                    </li>
                </ul>
                <div class="tab-content" id="productTabContent">
                    
                    <div class="tab-pane fade show active" id="description" role="tabpanel" aria-labelledby="description-tab">
                     
                              {!!app()->getLocale() == 'ar' ?$Pro->Ar_Desc :$Pro->En_Desc!!}    
       
                    </div>
                    
                    <div class="tab-pane fade" id="add_info" role="tabpanel" aria-labelledby="add_info">
                          {!!app()->getLocale() == 'ar' ?$Pro->Ar_Spec :$Pro->En_Spec!!}    
                    </div>
                    <div class="tab-pane fade" id="reviews" role="tabpanel" aria-labelledby="reviews-tab">
                        <div class="comments-wrap mt-0">
                            <ul class="comment-list">
                              
                                    @foreach($Comments as $comment)   

     
                                <li class="comment-item">
                                    <div class="post-comment">
                                        <div class="comment-avater">
                                            <img src="{{asset('Front/assets/img/blog/blog_comment1.png')}}" alt="Comment Author">
                                        </div>
                                        <div class="comment-content">
                                     
                                            <h3 class="name">@if(!empty($comment->User()->first()->name)){{$comment->User()->first()->name}}@endif</h3>
                                            <p class="text">    {{$comment->Comment}}</p>
                                            
                                            @if(!empty(auth()->user()->id))  
                                            @if($comment->User == auth()->user()->id)  
                                            <div class="reply_and_edit">
                                                <a href="{{url('DeleteComment/'.$comment->id)}}" class="reply-btn"> <i class="far fa-trash"></i></a>
                                                <a data-bs-toggle="collapse" data-bs-target="#multiCollapseExample{{$comment->id}}" aria-expanded="false" aria-controls="multiCollapseExample{{$comment->id}}" class="reply-btn"> <i class="far fa-edit"></i></a>
                                            </div>
                                            
                                            
                                    <div class="row">
  <div class="col">
    <div class="collapse multi-collapse" id="multiCollapseExample{{$comment->id}}">
      <div class="card card-body">
      
          
          
               <form method="post" action="{{url('EditComment')}}">
                                           @csrf
                 <input type="hidden" name="ID" value="{{$comment->id}}">          
                                        <div class="row">
                                         <input type="hidden" name="Product" value="{{$Pro->id}}"> 
                                            <div class="col-lg-12 col-md-12">
                                                <div class="form-group">
                                                    <textarea  name="Comment" id="review-body" cols="30" rows="6" placeholder="{{trans('admin.Write_Comment')}}" class="form-control" required>      {{$comment->Comment}}</textarea>
                                                </div>
                                            </div>

                                            <div class="col-lg-12 col-md-12">
                                                <button type="submit" class="btn btn-primary">
                                               {{trans('admin.Edit')}}
                                                    <span></span>
                                                </button>
                                            </div>
                                        </div>
                                    </form>
      </div>
    </div>
  </div>

</div>              
                                            
                                            
                                            
                                                @endif 
                                            @endif  
                                        </div>
                                    </div>
                                </li>
                                  @endforeach  
                              
                            </ul>
                        </div> <!-- Comment end -->
                        <!-- Comment Form -->
                             @if(!empty(auth()->user()->id))   
                        <div class="comment-form bg-smoke2">
                          
                             
            <div class="row">
                                                                 
       @php   $rate=Rate::where('User',auth()->user()->id)->where('Product',$Pro->id)->first();  @endphp                      
                                                        @if(!empty($rate))
                                        <!-- Edit Rate -->                       
                      <div class="spr-summary">
                                            <form action="{{url('EditRate')}}" method="post">    
                                                @csrf
                                                <input type="hidden" name="ID" value="{{$rate->id}}">
                                                    <input type="hidden" name="Product" value="{{$Pro->id}}">
                                                            <span class="product-review">
                                                            
                                                    <div class="rate">
                <input type="radio" id="star5" name="Rate" value="5" @if($rate->Rate == 5) checked @endif />
                <label for="star5" title="text">5 stars</label>
                <input type="radio" id="star4" name="Rate" value="4" @if($rate->Rate == 4) checked @endif/>
                <label for="star4" title="text">4 stars</label>
                <input type="radio" id="star3" name="Rate" value="3" @if($rate->Rate == 3) checked @endif/>
                <label for="star3" title="text">3 stars</label>
                <input type="radio" id="star2" name="Rate" value="2" @if($rate->Rate == 2) checked @endif/>
                <label for="star2" title="text">2 stars</label>
                <input type="radio" id="star1" name="Rate" value="1" @if($rate->Rate == 1) checked @endif/>
                <label for="star1" title="text">1 star</label>
                            </div>
                                                                
                                                            </span>
                                                            <span class="spr-summary-actions">
                              <button type="submit" class=" btn btn-primary"><i class="fal fa-check"></i></button>
                                                            </span>
                                            </form>    
                                                        </div>                                     
                                                        @else                                    
                                                          <!-- Add Rate -->  
                                                        <div class="spr-summary">
                                            <form action="{{url('AddRate')}}" method="post">    
                                                @csrf
                                                <input type="hidden" name="Product" value="{{$Pro->id}}">
                                                            <span class="product-review">
                                                            
                                                    <div class="rate">
                <input type="radio" id="star5" name="Rate" value="5" />
                <label for="star5" title="text">5 stars</label>
                <input type="radio" id="star4" name="Rate" value="4" />
                <label for="star4" title="text">4 stars</label>
                <input type="radio" id="star3" name="Rate" value="3" />
                <label for="star3" title="text">3 stars</label>
                <input type="radio" id="star2" name="Rate" value="2" />
                <label for="star2" title="text">2 stars</label>
                <input type="radio" id="star1" name="Rate" value="1" />
                <label for="star1" title="text">1 star</label>
                            </div>
                                                                
                                                            </span>
                                                            <span class="spr-summary-actions">
                      <button type="submit" class=" btn btn-primary"><i class="fal fa-check"></i></button>
                                                            </span>
                                            </form>    
                                                        </div> 
                                                    @endif    
                                              
                                </div>

 
                            
                                    <form method="post" action="{{url('AddComment')}}">
                                           @csrf
      <input type="hidden" name="Product" value="{{$Pro->id}}">    

 
                            <div class="row">
                           
                                <div class="col-12 form-group">
                                    <textarea placeholder="{{trans('admin.YourCommentHere')}}" name="Comment" class="form-control style-white"></textarea>
                                    <i class="fal fa-pencil"></i>
                                </div>                          
                                <div class="col-12 form-group mb-0">
                                    <button class="btn" type="submit">{{trans('admin.Submit')}}</button>
                                </div>
                            </div>
                            </form>
                        </div>
                                             @else
               <div class="comment-form bg-smoke2">
                          <span>{{trans('admin.MustLogin')}}</span>
                <a class="btn btn-primary" href="{{url('LoginSite')}}">{{trans('admin.Login')}}</a>         
                            </div>                
                        @endif  
                    </div>
                </div>
            </div>

            <!--==============================
            Related Product  
            ==============================-->
            <div class="space">
                <div class="row justify-content-center">
                    <div class="col-xl-6 col-lg-8 text-center">
                        <div class="title-area text-center">
                            <span class="sub-title text-theme2">{{trans('admin.RelatedProduct')}}</span>
                      
                        </div>
                    </div>
                </div>
                <div class="row global-carousel" id="productCarousel" data-slide-show="4" data-lg-slide-show="4" data-md-slide-show="3" data-sm-slide-show="2" data-xs-slide-show="1">

                        @foreach($Products as $pro)
                    <div class="col-lg-3 col-md-6">
                        <div class="product-card">
                            <div class="product-img">
                                <img src="{{URL::to($pro->Image)}}" alt="Product Image">
                                <div class="actions">
                                    <a href="{{url('ProDetails/'.$pro->id)}}" class="btn style2"><i class="fal fa-shopping-cart"></i></a>
                                    <a href="{{url('PrescriptionPro/'.$pro->id)}}" class="btn style2"><i class="fas fa-file"></i></a>
                                </div>
                                                              @if($pro->Type == 0)

                                   <div class="product-tag"> {{trans('admin.Recently')}}</div>
                                          @elseif($pro->Type == 1)
                                 
                                   <div class="product-tag"> {{trans('admin.Special')}}</div>
                                          @elseif($pro->Type == 2)
                                       
                          
                                   <div class="product-tag">{{trans('admin.Finaly')}}</div>
                                  
                                         @endif
                            </div>
                            <div class="product-content">
                                <h3 class="product-title"><a href="{{url('ProDetails/'.$pro->id)}}">   {{app()->getLocale() == 'ar' ?$pro->P_Ar_Name :$pro->P_En_Name}}  </a></h3>
                                
                                   @if(!empty($pro->Offer_Price))
                       
                                     <span class="price"><del>{{$pro->Price}}</del>{{$pro->Offer_Price}} {{$pro->Symbol}}</span>
                                  @else
                            
                                     <span class="price">{{$pro->Price}}  {{$pro->Symbol}}</span>
                                  @endif
                           
                            </div>
                        </div>
                    </div>  
                    
                @endforeach

                </div>
            </div>
        </div>
    </section>    
    


   <style>

.rate {
    float: left;
    height: 46px;
    padding: 0 10px;
}
.rate:not(:checked) > input {
    position:absolute;
    top:-9999px;
}
.rate:not(:checked) > label {
    float:right;
    width:1em;
    overflow:hidden;
    white-space:nowrap;
    cursor:pointer;
    font-size:30px;
    color:#ccc;
}
.rate:not(:checked) > label:before {
    content: '★ ';
}
.rate > input:checked ~ label {
    color: red !important;   
}
.rate:not(:checked) > label:hover,
.rate:not(:checked) > label:hover ~ label {
    color: red !important;  
}
.rate > input:checked + label:hover,
.rate > input:checked + label:hover ~ label,
.rate > input:checked ~ label:hover,
.rate > input:checked ~ label:hover ~ label,
.rate > label:hover ~ input:checked ~ label {
    color: red !important;
}    

</style> 



@endsection