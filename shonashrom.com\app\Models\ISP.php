<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ISP extends Model
{
    use HasFactory;
      protected $table = 'i_s_p_s';
     protected $fillable = [
   
                'Name',
                'Height',
                'HeightUnit',
                'Weight',
                'WightUnit',
                'Age',
                'Gender',
                'Sports',
                'Moshr_Kotlt_Gsm',
                'ZaydtTool',   
                'ZaydtToolToInch',
                'WaznMsaly',
                'mkdar_wazn_mtlop_fkdanh',
                'saarat_hararya_mtlop_fkdnah',
                'waktMatwkaaLlwsolWaznMasaly',
                '3dd3bwatOstegy',
                'So3ratYomen',
                'so3ratPoortinat',
                'gramProtinat',
                'User',

    ];
    
               public function User()
    {
        return $this->belongsTo(User::class,'User');
    }
}
