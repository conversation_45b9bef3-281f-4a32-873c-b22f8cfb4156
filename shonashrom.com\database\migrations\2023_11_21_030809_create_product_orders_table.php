<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProductOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_orders', function (Blueprint $table) {
            $table->id();
            $table->longText('P_Ar_Name')->nullable();
            $table->longText('P_En_Name')->nullable();
            $table->longText('Qty')->nullable();
            $table->longText('Price')->nullable();
            $table->longText('Total')->nullable();
            $table->longText('Product')->nullable();
            $table->longText('Order')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_orders');
    }
}
