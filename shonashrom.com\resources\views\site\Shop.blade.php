@extends('site.index')
@section('content')
    
<title>{{trans('admin.Shop')}}</title>



    <!--==============================
    Breadcumb
    ============================== -->
    <div class="breadcumb-wrapper" data-bg-src="{{asset('Front/assets/img/bg/breadcrumb-bg.png')}}">
        <!-- bg animated image/ -->
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="breadcumb-content">
                        <h1 class="breadcumb-title">{{trans('admin.Shop')}}</h1>
                        <ul class="breadcumb-menu">
                            <li><a href="{{url('/')}}">{{trans('admin.Home')}}</a></li>
                            <li class="active">{{trans('admin.Shop')}}</li>
                        </ul>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!--==============================
    Shop Area  
    ==============================-->
    <div class="shop-area container space">
        <div class="row">
        <div class="col-lg-8">
          <div class="container">
            <div class="row gy-40">
                
                
                   @foreach($Products as $pro)
                <div class="col-lg-3 col-md-6">
                    <div class="product-card">
                        <div class="product-img">
                            <img src="{{URL::to($pro->Image)}}" alt="Product Image">
                            <div class="actions">
                                <a href="{{url('ProDetails/'.$pro->id)}}" class="btn style2">   <i class="fal fa-shopping-cart"></i></a>
                                <a href="{{url('PrescriptionPro/'.$pro->id)}}" class="btn style2">   <i class="fas fa-file"></i></a>
                            </div>
                        </div>
                        <div class="product-content">
                            <h3 class="product-title"><a href="{{url('ProDetails/'.$pro->id)}}"> {{app()->getLocale() == 'ar' ?$pro->P_Ar_Name :$pro->P_En_Name}}</a></h3>
                                 @if(!empty($pro->Offer_Price))
                            <span class="price"><del>{{$pro->Price}}</del>{{$pro->Offer_Price}} {{$pro->Symbol}}</span>
                            @else
                           <span class="price">{{$pro->Price}} {{$pro->Symbol}}</span>  
                            @endif
                        </div>
                    </div>
                </div>  
   
                @endforeach
            
            </div>
        </div>
           <div class="pagination justify-content-center">
                <ul>
                      {{$Products->Links()}} 
                </ul>
            </div>
        
        </div>
       
           <div class="col-lg-4">
          <div class="widget widget_categories">
                            <h3 class="widget_title">{{trans('admin.Search')}}</h3>
                        <form action="{{url('FilterSearchName')}}" method="get">
                                     
                                    <div class="sidebar__search-input-2">
                                       <input type="text" placeholder="{{trans('admin.Search')}}" name="search" style="width: 70%" required>
                                       <button class="btn style2" style="padding: 18.5px 20px 20.5px;" type="submit"><i class="fal fa-search"></i></button>
                                    </div>
                                 </form>
                        </div>    
               
               <div class="widget widget_categories">
                            <h3 class="widget_title">{{trans('admin.Categories')}}</h3>
                            <ul>
                                
                               @foreach($Groups as $group)           
                                <li>
                                    <a href="{{url('FilterProGroup/'.$group->id)}}"><i class="fa-solid fa-arrow-right"></i>{{app()->getLocale() == 'ar' ?$group->Name :$group->NameEn}}</a>
                                </li>
                                @endforeach
                                                        
                            </ul>
                        </div>
               
                 

    
        </div> 
       </div>
    </div>
    

    
@endsection
      