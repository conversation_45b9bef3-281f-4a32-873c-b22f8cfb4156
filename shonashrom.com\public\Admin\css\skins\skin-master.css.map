{"version": 3, "sources": ["_include.scss", "_skin-light.scss", "skin-master.css", "_skin-dark.scss", "_skin-dark-modules/variables.scss", "_skin-dark-modules/_accordion.scss", "_skin-dark-modules/_modal.scss", "_skin-dark-modules/_pagination.scss", "_skin-dark-modules/_panel.scss", "_skin-dark-modules/_plugins/_plugin_prettyprint.scss", "_skin-dark-modules/_alerts.scss", "_skin-dark-modules/_alt.scss", "_skin-dark-modules/_border.scss", "_skin-dark-modules/_breadcrumb.scss", "_skin-dark-modules/_button.scss", "_skin-dark-modules/_chat-segment.scss", "_skin-dark-modules/_colors.scss", "_skin-dark-modules/_demo.scss", "_skin-dark-modules/_demo-settings.scss", "_skin-dark-modules/_dropdown.scss", "_skin-dark-modules/_forms.scss", "_skin-dark-modules/_misc.scss", "_skin-dark-modules/_nav.scss", "_skin-dark-modules/_navbar.scss", "_skin-dark-modules/_navtabs.scss", "_skin-dark-modules/_notitfication.scss", "_skin-dark-modules/_page_etc.scss", "_skin-dark-modules/_progressbar.scss", "_skin-dark-modules/_tables.scss", "_skin-dark-modules/_toast.scss", "_skin-dark-modules/_tooltip.scss", "_skin-dark-modules/_plugins/_plugin_datatables.scss", "_skin-dark-modules/_plugins/_plugin_other.scss", "_skin-dark-modules/_plugins/_plugin_select2.scss", "_skin-dark-modules/_mobile.scss"], "names": [], "mappings": "AAAA;;;EAGC,kBAAiB,EAAA;EAHlB;;;IAME,YAAY;IACZ,eAAe,EAAA;EAPjB;;;IAWE,iCAAiC;IACjC,gBAAgB;IAChB,eAAe;IACf,kBAAkB;IAClB,aAAa;IACb,WAAW;IACX,YAAY;IACZ,yBAAmB;QAAnB,sBAAmB;YAAnB,mBAAmB;IACnB,wBAAuB;QAAvB,qBAAuB;YAAvB,uBAAuB;IACvB,qBAAqB;IACrB,oBAAoB;IACpB,yBAAwB,EAAA;;AAI1B;EAGG,oBAAa;EAAb,oBAAa;EAAb,aAAa,EAAA;;AC7BhB;4EC6B4E;ADzB5E;EAIG,oBAAY;EAAZ,oBAAY;EAAZ,aAAY,EAAA;;AAJf;EASG,yBAAyB,EAAA;;AAT5B;EAaG,mBAAmB;EACnB,cAAc;EACd,8BAA8B,EAAA;EAfjC;IAkBE,qBAAqB;IACrB,+BAA+B,EAAA;;AAnBjC;EAwBG,wDAAgD;UAAhD,gDAAgD,EAAA;;AAxBnD;EA4BG,+BAA+B,EAAA;;AA5BlC;EAiCE,cAAc,EAAA;EAjChB;IAsCI,yBAAyB;IACzB,iBAAiB,EAAA;EAvCrB;IA4CI,+BAAuB;YAAvB,uBAAuB;IACvB,aAAa,EAAA;EA7CjB;IAiDI,cAAc;IACd,iBAAiB,EAAA;;AAlDrB;EAuDI,mBAAmB,EAAA;;AAvDvB;EA2DI,cAAc,EAAA;;AA3DlB;EA+DE,yBAAyB;EACzB,+BAA+B,EAAA;;AAhEjC;EAoEE,sBAAsB,EAAA;;AApExB;EAwEE,gCAAgC;EAChC,wBAAgB;UAAhB,gBAAgB,EAAA;;AAzElB;EA8EI,yBAAyB;EACzB,8BAA8B,EAAA;;AA/ElC;EAwFI,yBAAyB;EACzB,8BAA8B,EAAA;EAzFlC;IAoFG,yBAAyB;IACzB,8BAA8B,EAAA;;AArFjC;EA8FE,yBAAyB;EACzB,8BAA8B,EAAA;;AA/FhC;EAqGG,yBAAyB;EACzB,8BAA8B;EAC9B,gBAAgB,EAAA;;AAvGnB;EA2GG,qCAAqC,EAAA;;AA3GxC;EAgHK,+BAA+B,EAAA;;AAhHpC;EAqHG,8BAA8B,EAAA;;AArHjC;EA0HI,yBAAyB;EACzB,6BAA6B,EAAA;;AA3HjC;EAgIE,cAAc,EAAA;;AAhIhB;EAoIE,mBAAmB,EAAA;;AApIrB;;;;;EA8II,mBAAmB,EAAA;EA9IvB;;;;;IAiJK,sBAAsB,EAAA;;AAY3B;EAEC;IAIG,8BAA8B;IAC9B,yBAAyB,EAAA;EAL5B;IAUE,oCAAoC,EAAA;IAVtC;MAaI,yBAAyB,EAAA;EAb7B;IAkBE,yBAAyB,EAAA;EAlB3B;IAyBG,mBAAmB,EAAA;IAzBtB;MA4BE,qCAA6B;cAA7B,6BAA6B,EAAA;IA5B/B;MAiCI,yBAAyB,EAAA;EAjC7B;IAuCG,cAAc,EAAA,EACf;;AE3MH;4EDuJ4E;AClJ5E;EAEC,yBAAyB;EACzB,cCL2B;ECH5B,cAAA;ECAA,UAAA;ECAA;;;;;;;;ELqKE;EMpIF,gBAAA;EChCA,mHAAA,EAAoH;ENIpH;IAOG,oBAAa;IAAb,oBAAa;IAAb,aAAa,EAAA;EAPhB;IEHC,gCDUmB,EAAA;EDPpB;IECE,6BAA6B,EAAA;EFD/B;IEIE,uBAAuB,EAAA;EFJzB;;;;IOAC,2CNOmB;IMNnB,uCNMmB;IMLnB,cNKmB,EAAA;EDPpB;IOKC,cAAc;IACd,+BAA+B;IAC/B,0CNGqB;IMFrB,qDAAqD;IACrD,sCNCqB;IMArB,iDAAiD,EAAA;EPVlD;IOaC,cAAc;IACd,+BAA+B;IAC/B,yCNJqB;IMKrB,qDAAqD;IACrD,qCNNqB;IMOrB,iDAAiD,EAAA;EPlBlD;IOqBC,cAAc;IACd,8BAA8B;IAC9B,yCNToB;IMUpB,oDAAoD;IACpD,qCNXoB;IMYpB,gDAAgD,EAAA;EP1BjD;IO6BC,cAAc;IACd,+BAA+B;IAC/B,yCNlBqB;IMmBrB,qDAAqD;IACrD,qCNpBqB;IMqBrB,iDAAiD,EAAA;EPlClD;IOqCC,cAAc;IACd,4BAA4B;IAC5B,yCN3BkB;IM4BlB,kDAAkD;IAClD,qCN7BkB;IM8BlB,8CAA8C,EAAA;EP1C/C;IO6CC,cAAc;IACd,yBAAyB;IACzB,uCNhCoB;IMiCpB,oDAAoD;IACpD,mCNlCoB;IMmCpB,gDAAgD,EAAA;EPlDjD;;;;;IQEE,mBAAmB,EAAA;ERFrB;ISLgN,4CAAsC,EAAA;ETKtP;ISJkB,oDAAwD,EAAA;ETI1E;ISHkB,sDAA0D,EAAA;ETG5E;ISFkB,uDAA2D,EAAA;ETE7E;ISDkB,qDAAyD,EAAA;ETC3E;ISEC,4CAA4C,EAAA;ETF7C;IUHC,mBAAgD,EAAA;EVGjD;IUAC,yBAA6B,EAAA;EVA9B;IWFE,cAAc,EAAA;EXEhB;IWGC,kDAA0C;YAA1C,0CAA0C;IAC1C,iCAAiC,EAAA;EXJlC;IWQC,cAAc;IACd,qBAAqB,EAAA;EXTtB;IWaC,yBAAyB;IACzB,cAAc;IACd,qBAAqB,EAAA;EXftB;IWmBI,mBAAmB;IAEnB,cAAc;IACd,yBAAyB,EAAA;IXtB7B;MWyBE,yBAAyB;MACzB,cAAc,EAAA;IX1BhB;MW8BE,gCAA4C,EAAA;IX9B9C;MWkCE,gDAAiD;MACjD,cV5BkB,EAAA;EDPpB;IWyCE,yBAAqC,EAAA;EXzCvC;IW6CE,yBAAmD;IACnD,oCAAiC,EAAA;EX9CnC;IYFE,8BAA2B;IAC3B,+BAA+B,EAAA;EZCjC;IYIE,oCXOoB;IWNpB,gDAAgD,EAAA;EZLlD;IYUC,8BAA8B,EAAA;EZV/B;IYaC,cAAc,EAAA;EZbf;;IaDC,cAAc;IACd,+BZH4C,EAAA;EDG7C;IaKC,0CAA0C,EAAA;EbL3C;IaSC,yCAAyC,EAAA;EbT1C;IaaC,0CAA0C,EAAA;Ebb3C;IaiBC,uCAAuC,EAAA;EbjBxC;IaqBC,2CAA2C,EAAA;EbrB5C;IawBC,0CAA2C,EAAA;EbxB5C;Ia2BC,cZpBmB,EAAA;EDPpB;Ia8BC,0CAAwC,EAAA;Eb9BzC;IaiCC,cAAc;IACd,0CAA0C,EAAA;EblC3C;;IasCC,cAAc;IACd,0CAA0C,EAAA;EbvC3C;Ia4CC,yBAAyB;IACzB,cZ/C2B,EAAA;EDE5B;IagDC,yBAAyB,EAAA;EbhD1B;IamDC,oCAAoC,EAAA;EbnDrC;IasDC,mBAAmB;IACnB,8CAA8C,EAAA;EbvD/C;Ia0DC,0GAA6G,EAAA;Eb1D9G;Ia6DC,0CZhDqB;IYiDrB,sDAAsD,EAAA;Eb9DvD;IaiEC,oCAAoC;IACpC,cAAc,EAAA;IblEf;MaqEE,cAAc,EAAA;EbrEhB;IayEC,0CZ/DqB;IYgErB,qDAAsD;IACtD,cAAc,EAAA;Eb3Ef;Ia8EC,0CZpEqB;IYqErB,qDAAsD;IACtD,cAAc,EAAA;EbhFf;IamFC,0CZzEqB;IY0ErB,qDAAsD;IACtD,cAAc,EAAA;EbrFf;IawFC,0CZ9EqB;IY+ErB,qDAAsD;IACtD,cAAc,EAAA;Eb1Ff;Ia6FC,0CZnFqB;IYoFrB,qDAAsD,EAAA;Eb9FvD;IaiGC,0CZvFqB;IYwFrB,qDAAsD,EAAA;EblGvD;IaqGC,0CZ3FqB;IY4FrB,qDAAsD,EAAA;EbtGvD;IayGC,0CZ/FqB;IYgGrB,qDAAsD,EAAA;Eb1GvD;Ia6GC,0CZnGqB;IYoGrB,qDAAsD,EAAA;Eb9GvD;IaiHC,mDAAmD,EAAA;EbjHpD;IaoHC,yCZzGqB;IY0GrB,qDAAqD;IACrD,cAAc,EAAA;EbtHf;IayHC,yCZ9GqB;IY+GrB,qDAAqD;IACrD,cAAc,EAAA;Eb3Hf;Ia8HC,yCZnHqB;IYoHrB,qDAAqD;IACrD,cAAc,EAAA;EbhIf;IamIC,yCZxHqB;IYyHrB,qDAAqD;IACrD,cAAc,EAAA;EbrIf;IawIC,yCZ7HqB;IY8HrB,qDAAqD,EAAA;EbzItD;Ia4IC,yCZjIqB;IYkIrB,qDAAqD,EAAA;Eb7ItD;IagJC,yCZrIqB;IYsIrB,qDAAqD,EAAA;EbjJtD;IaoJC,yCZzIqB;IY0IrB,qDAAqD,EAAA;EbrJtD;IawJC,yCZ7IqB;IY8IrB,qDAAqD,EAAA;EbzJtD;Ia4JC,yBZjJqB;IYkJrB,mDAAmD,EAAA;Eb7JpD;IagKC,yCZlJoB;IYmJpB,oDAAoD;IACpD,cAAc,EAAA;EblKf;IaqKC,yCZvJoB;IYwJpB,oDAAoD;IACpD,cAAc,EAAA;EbvKf;Ia0KC,yCZ5JoB;IY6JpB,oDAAoD;IACpD,cAAc,EAAA;Eb5Kf;Ia+KC,yCZjKoB;IYkKpB,oDAAoD;IACpD,cAAc,EAAA;EbjLf;IaoLC,yCZtKoB;IYuKpB,oDAAoD;IACpD,cAAc,EAAA;EbtLf;IayLC,yCZ3KoB;IY4KpB,oDAAoD,EAAA;Eb1LrD;Ia6LC,yCZ/KoB;IYgLpB,oDAAoD,EAAA;Eb9LrD;IaiMC,yCZnLoB;IYoLpB,oDAAoD,EAAA;EblMrD;IaqMC,yCZvLoB;IYwLpB,oDAAoD,EAAA;EbtMrD;IayMC,yBZ3LoB;IY4LpB,kDAAkD,EAAA;Eb1MnD;Ia6MC,yCZhMqB;IYiMrB,qDAAqD;IACrD,cAAc,EAAA;Eb/Mf;IakNC,yCZrMqB;IYsMrB,qDAAqD;IACrD,cAAc,EAAA;EbpNf;IauNC,yCZ1MqB;IY2MrB,qDAAqD;IACrD,cAAc,EAAA;EbzNf;Ia4NC,yCZ/MqB;IYgNrB,qDAAqD;IACrD,cAAc,EAAA;Eb9Nf;IaiOC,yCZpNqB;IYqNrB,qDAAqD;IACrD,cAAc,EAAA;EbnOf;IasOC,yCZzNqB;IY0NrB,qDAAqD,EAAA;EbvOtD;Ia0OC,yCZ7NqB;IY8NrB,qDAAqD,EAAA;Eb3OtD;Ia8OC,yCZjOqB;IYkOrB,qDAAqD,EAAA;Eb/OtD;IakPC,yCZrOqB;IYsOrB,qDAAqD,EAAA;EbnPtD;IasPC,yBZzOqB;IY0OrB,mDAAmD,EAAA;EbvPpD;Ia0PC,yCZ9OkB;IY+OlB,kDAAkD;IAClD,cAAc,EAAA;Eb5Pf;Ia+PC,yCZnPkB;IYoPlB,kDAAkD;IAClD,cAAc,EAAA;EbjQf;IaoQC,yCZxPkB;IYyPlB,kDAAkD;IAClD,cAAc,EAAA;EbtQf;IayQC,yCZ7PkB;IY8PlB,kDAAkD;IAClD,cAAc,EAAA;Eb3Qf;Ia8QC,yCZlQkB;IYmQlB,kDAAkD,EAAA;Eb/QnD;IakRC,yCZtQkB;IYuQlB,kDAAkD,EAAA;EbnRnD;IasRC,yCZ1QkB;IY2QlB,kDAAkD,EAAA;EbvRnD;Ia0RC,yCZ9QkB;IY+QlB,kDAAkD,EAAA;Eb3RnD;Ia8RC,yCZlRkB;IYmRlB,kDAAkD,EAAA;Eb/RnD;IakSC,cAAc;IACd,yBZvRkB;IYwRlB,gDAAgD,EAAA;EbpSjD;IawSE,8BAA8B,EAAA;EbxShC;Ia2SE,oCAAoC,EAAA;Eb3StC;IaiTC,mBAAmB;IACnB,cAAc,EAAA;IblTf;MaqTE,mBAAmB;MACnB,cAAc,EAAA;EbtThB;Ia4TE,8BAA8B,EAAA;Eb5ThC;Ia+TE,8BAA0C,EAAA;Eb/T5C;IamUE,cZrU0B,EAAA;EDE5B;IcHC,wCAAwC;IACxC,yCAAyC,EAAA;EdE1C;IcCC,yCAAyC;IACzC,0CAA0C,EAAA;EdF3C;IcKC,yCbSoB;IaRpB,oDAAoD,EAAA;EdNrD;IcSC,yCbGkB;IaFlB,kDAAkD,EAAA;EdVnD;IccC,mBAAmB,EAAA;EddpB;IciBC,uBAAkC,EAAA;EdjBnC;IcoBC,uBAAuB,EAAA;EdpBxB;Ic0BE,mBAAmB,EAAA;Ed1BrB;Ic8BG,kDAA+C,EAAA;Ed9BlD;IeFE,+BAA+B,EAAA;EfEjC;IeCE,cAAc,EAAA;IfDhB;MeGG,cAAc,EAAA;IfHjB;MeMG,qCAAqC;MACrC,cAAc,EAAA;EfPjB;IeWE,mBAAmB,EAAA;EfXrB;IgBHC,oDAA4C;YAA5C,4CAA4C;IAC5C,yBAAyB;IACzB,cAAc,EAAA;EhBCf;IgBEC,yBAAqC,EAAA;IhBFtC;MgBME,yBAAyB,EAAA;IhBN3B;MgBWE,yBAAyB;MACzB,yBAAyB;MACzB,sDAAsD,EAAA;EhBbxD;IgBiBC,yBAAyB,EAAA;EhBjB1B;IgBoBC,yCAAsC,EAAA;EhBpBvC;IgB0BE,mBAAmB,EAAA;EhB1BrB;IgB6BE,mCAA2B;YAA3B,2BAA2B,EAAA;EhB7B7B;IiBH8B,SAAA;IAC7B,cAAc,EAAA;EjBEf;IiBCyB,4BAAA;IACxB,cAAc,EAAA;EjBFf;IiBMC,cAAc,EAAA;EjBNf;IiBMC,cAAc,EAAA;EjBNf;IiBMC,cAAc,EAAA;EjBNf;IiBWI,oDAA4C;YAA5C,4CAA4C;IAC5C,yBAAyB;IACzB,cAAc;IACd,uCAAuC,EAAA;EjBd3C;IiBsBC,chBxB2B,EAAA;EDE5B;IiBsBC,chBxB2B,EAAA;EDE5B;IiBsBC,chBxB2B,EAAA;EDE5B;IiBsBC,chBxB2B,EAAA;EDE5B;;;IiBsBC,chBxB2B,EAAA;EDE5B;IiByBC,cAAc;IACd,yBAAyB,EAAA;IjB1B1B;MiB4BE,yBAAyB,EAAA;EjB5B3B;IiBiCC,chBnC2B,EAAA;EDE5B;;IiBqCC,cAAc;IACd,qCAAqC;IACrC,iCAAiC,EAAA;IjBvClC;;MiByCE,iDAAiD,EAAA;IjBzCnD;;;MiB6CE,yBAAiC;MACjC,uCAAuC,EAAA;EjB9CzC;IiBkDC,cAAc;IACd,yBAAyB;IACzB,yBAAyB,EAAA;EjBpD1B;IiBuDC,0ChB7CqB;IgB8CrB,qDAAsD;IACtD,qBAAqB,EAAA;EjBzDtB;IiB4DC,0ChBlDqB;IgBmDrB,qDAAsD;IACtD,qBhBpDqB;IgBqDrB,+CAA+C,EAAA;EjB/DhD;;IiBqEC,yBAAyB,EAAA;EjBrE1B;;;IiB0EC,yBAAyB;IACzB,sDAAsD,EAAA;EjB3EvD;;;;;;;IkBGI,qCAAqC,EAAA;ElBHzC;;;;;;;IkBaI,sCAAsC,EAAA;ElBb1C;IkBkBC,WAAW;IACX,yBAAyB,EAAA;ElBnB1B;IkBuBC,+CAA6C;IAC7C,wDAAwD,EAAA;ElBxBzD;IkB6BC,oCjBhBqB;IiBiBrB,gDAAgD,EAAA;ElB9BjD;IkBiCC,cjB1BmB,EAAA;EDPpB;IkBqCC,gCAAgC,EAAA;ElBrCjC;IkByCC,qCAAqC,EAAA;ElBzCtC;IkB8CC,wDAAwD,EAAA;ElB9CzD;IkBkDC,8BAA8B;IAC9B,cAAc,EAAA;ElBnDf;IkBwDC,uBAAuB,EAAA;ElBxDxB;IkB6DE,oCAAiC,EAAA;ElB7DnC;IkBkEC,gDAAgD,EAAA;ElBlEjD;IGAI,oDAA4C;YAA5C,4CAA4C;IAC5C,yBAAyB;IACzB,cAAc,EAAA;EHFlB;IGSC,oDAA4C;YAA5C,4CAA4C;IAC5C,yBAAyB;IACzB,cAAc,EAAA;EHXf;IGeC,cAAc,EAAA;EHff;IGmBC,+BAA+B;IAC/B,wBAAgB;YAAhB,gBAAgB,EAAA;EHpBjB;IGwBC,cF1B2B,EAAA;EDE5B;ImBJC,mBAA6C;IAC7C,cAAc,EAAA;InBGf;MmBAE,sCAAsC;MACtC,cACD,EAAA;InBFD;MmBKE,sCAAsC,EAAA;EnBLxC;ImBUC,clBZ2B;IkBmB3B;;GpBuqBE,EoBrqBC;InBnBJ;MmBeE,iBAAiB,EAAA;InBfnB;MmBqBE,cAAc;MACd,iBAAiB,EAAA;EnBtBnB;ImB0BC,mBAAmB,EAAA;EnB1BpB;ImB8BC,gCAAsE,EAAA;EnB9BvE;ImBkCC,cAAc,EAAA;EnBlCf;ImBqCC,+BAA+B;IAC/B,+BAA+B,EAAA;EnBtChC;ImB0CE,+BAA+B;IAC/B,clBvCkC,EAAA;EDJpC;ImBkDE,+BAA+B;IAC/B,clBrD0B,EAAA;IDE5B;MmB+CG,+BAA+B;MAC/B,clBlDyB,EAAA;EDE5B;ImBuDC,+BAA+B;IAC/B,clB1D2B,EAAA;EDE5B;ImBkEE;;IpBopBE,EoBlpBC;InBpEL;MmB6DG,+BAA+B;MAC/B,clB1DiC;MkB2DjC,gBAAgB;MAChB,oCAAoC,EAAA;InBhEvC;;MmBwEI,+BAA+B,EAAA;InBxEnC;MmB4EG,+BAA+B,EAAA;EnB5ElC;ImBgFE,+BAA+B;IAC/B,8BAA8B,EAAA;EnBjFhC;IoBFI,+BAA+B,EAAA;EpBEnC;IoBGM,+BAA+B,EAAA;EpBHrC;IoBeM,+BAA+B,EAAA;IpBfrC;MoBQQ,+BAA+B,EAAA;IpBRvC;MoBYQ,+BAA+B,EAAA;EpBZvC;IoBoBI,+BAA+B,EAAA;EpBpBnC;;IoB2BE,uBAAuB;IACvB,yBAAyB,EAAA;EpB5B3B;IoBiCI,uBAAuB,EAAA;EpBjC3B;;IqBHE,yEAAyE,EAAA;ErBG3E;IqBAE,8CAA8C,EAAA;ErBAhD;;IqBKI,6EAA6E,EAAA;ErBLjF;IqBSE,kDAAkD,EAAA;ErBTpD;;;IsBAE,yBAAqC,EAAA;EtBAvC;IsBIE,yBrBR+B,EAAA;IDIjC;MsBOG,sDAAuD,EAAA;EtBP1D;;IsBgBE,mBrBpB+B,EAAA;EDIjC;;IuBHC,ctBC2B,EAAA;EDE5B;;IuBGC,cAAc,EAAA;IvBHf;;MuBME,cAA4B,EAAA;EvBN9B;IuBUC,+BAA6B;IAC7B,gDAAgD,EAAA;EvBXjD;;IuBeE,yBAAyB,EAAA;EvBf3B;IuBmBC,qCtBd4B;YsBc5B,6BtBd4B,EAAA;EDL7B;;;IuB0BC,sBAAsB,EAAA;EvB1BvB;IuB6BC,gCAAqE,EAAA;EvB7BtE;;IuBiCC,gCAAsE;IACtE,wBAAgB;YAAhB,gBAAgB,EAAA;EvBlCjB;;;IuBwCC,yBtB5CgC,EAAA;EDIjC;IuB2CC,6BAAmE;IACnE,mBAAmB,EAAA;EvB5CpB;IuB+CC,ctBxCmB,EAAA;EDPpB;;;IuBoDC,mBtB/C4B,EAAA;EDL7B;IuBwDC,wBAAgB;YAAhB,gBAAgB,EAAA;EvBxDjB;IuB4DC,cAAc,EAAA;EvB5Df;IuB+DC,0BAA8B,EAAA;EvB/D/B;IuBoEC,yBAAyB,EAAA;IvBpE1B;MuBuEE,cAAc,EAAA;EvBvEhB;IuB4EC,ctB9E2B;IsB+E3B,0BAA0B,EAAA;IvB7E3B;MuB+EE,cAA+C,EAAA;EvB/EjD;IwBJC,0CAA2C,EAAA;ExBI5C;IKJC,yBAAyB,EAAA;ELI1B;;IKCC,yBAAyB,EAAA;ELD1B;IKIC,uCAAuC,EAAA;ELJxC;;IKQC,+BAA4B;IAC5B,8CAA8C,EAAA;ILT/C;;MKWE,cJJkB,EAAA;EDPpB;IKeC,sBAAsB,EAAA;ELfvB;;IKoBC,mBAAmB,EAAA;ELpBpB;IKuBC,oCJZqB;IIarB,gDAAgD;IAChD,cAAc,EAAA;ELzBf;IK8BC,sCAAsC;IACtC,YAAY,EAAA;IL/Bb;MKkCE,oCAAoC,EAAA;ELlCtC;;;;;;IyBEC,uCAAuC,EAAA;EzBFxC;IyBKC,cAAwC,EAAA;IzBLzC;MyBQE,cAAc;MACd,yBAAyB,EAAA;IzBT3B;MyBaE,qCAAqC,EAAA;EzBbvC;IyBiBC,sDAAsD;IACtD,iEAAiE;IACjE,cAAc,EAAA;EzBnBf;IyBsBC,qDAAsD;IACtD,uJAAqH;IAArH,qHAAqH,EAAA;EzBvBtH;;;IyB4BC,6BAA6B,EAAA;EzB5B9B;I0BHC,wCAAwC,EAAA;I1BGzC;M0BAE,cAAc;MACd,wCAAwC,EAAA;E1BD1C;;I2BFC,oCAAoC,EAAA;E3BErC;;I2BEC,sCAAsC,EAAA;E3BFvC;;I2BMC,qCAAqC,EAAA;E3BNtC;;I2BUC,uCAAuC,EAAA;E3BVxC;I2BaC,oCAAoC,EAAA;E3BbrC;I2BkBC,0CAA0C,EAAA;E3BlB3C;I4BHC,sGAAkE;IAAlE,kEAAkE,EAAA;E5BGnE;I4BAC,yBAAyB,EAAA;E5BA1B;I4BGC,yBAAqC,EAAA;E5BHtC;I4BOC,uBAAuB,EAAA;E5BPxB;;;I4BYC,mBAAmB,EAAA;E5BZpB;I4BeC,gCAAgC,EAAA;E5BfjC;;;I4BqBC,0C3BXqB;I2BYrB,qDAAqD,EAAA;E5BtBtD;I4ByBC,0C3BfqB;I2BgBrB,qDAAqD,EAAA;E5B1BtD;I4B+BG,cAAc;IACd,yBAAyB;IACzB,qBAAqB,EAAA;E5BjCxB;I4BsCC,+BAA+B,EAAA;E5BtChC;I4ByCC,yBAAyB,EAAA;E5BzC1B;;I6BFC,6FAA2D;IAA3D,2DAA2D;IAC3D,cAAc,EAAA;E7BCf;I6BEC,oCAAoC,EAAA;E7BFrC;I6BMC,mBAAmB;IACnB,qBAAqB,EAAA;E7BPtB;I6BWC,yBAAyB,EAAA;E7BX1B;;I6BeC,2C5BLqB;I4BMrB,sDAAsD,EAAA;E7BhBvD;;I6BqBC,mBAAmB,EAAA;E7BrBpB;;;;I6B2BC,uBAAuB,EAAA;E7B3BxB;I6B8BC,qBAAqB,EAAA;E7B9BtB;I6BmCC,yBAAyB,EAAA;E7BnC1B;I6BsCC,4BAA4B,EAAA;E7BtC7B;I6B0CC,mBAAmB,EAAA;I7B1CpB;M6B+CE,mBAAiC;MACjC,cAAc,EAAA;E7BhDhB;;I6BqDC,qCAAqC;IACrC,WAAW,EAAA;E7BtDZ;;I6B0DC,6BAA6B;IAC7B,yBAAyB,EAAA;E7B3D1B;IMHa,mBAAkB;IAAC,sFAAqF;IAAC,oBAAkB,EAAA;ENGxI;IMH8I,cAAa,EAAA;ENG3J;IMHwK,aAAY;IAAC,gBAAe;IAAC,cAAa,EAAA;ENGlN;IMH+Q,iBAAgB;IAAC,yBAAwB;IAAC,wBAAuB,EAAA;EAAC;INGjV;MMHoW,cAAa,EAAA;INGjX;MMHuX,cAAa,EAAA;INGpY;MMH0Y,cAAa,EAAA;INGvZ;MMH6Z,cAAa,EAAA;ING1a;MMHgb,cAAa,EAAA;ING7b;MMHmc,cAAa,EAAA;INGhd;MMHsd,cAAa,EAAA;INGne;MMHye,cAAa,EAAA;INGtf;MMH4f,cAAa,EAAA;INGzgB;MMH+gB,cAAa,EAAA;ING5hB;MMHkiB,cAAa,EAAA;ING/iB;MMHqjB,cAAa,EAAA;INGlkB;MMHwkB,cAAa,EAAA;INGrlB;MMH2lB,cAAa,EAAA,EAAE;ENG1mB;;;;;I8BCC,cAAc;IACX,qCAAqC;IACrC,iCAAiC,EAAA;E9BHrC;I8BOC,cAAc,EAAA;E9BPf;;;;;;I8BiBC,mBAAmB,EAAA;E9BjBpB;;I8BuBC,yBAAyB;IACzB,uCAAuC,EAAA;;AC7BxC;EACC;IAGG,8BAA8B;IAC9B,yBAAyB,EAAA;EAJ5B;IAQI,oCAAiD,EAAA;IARrD;MAUK,yBAAsC,EAAA;EAV3C;IAcI,yBAAyB,EAAA;EAd7B;IAoBG,mB9BXyB,EAAA;I8BT5B;;MAwBK,yBAAyB,EAAA;EAxB9B;IA6BG,cAAc,EAAA;EA7BjB;IAkCE,yBAAyB,EAAA,EACzB", "file": "skin-master.css", "sourcesContent": ["#skin-default,\r\n#skin-light,\r\n#skin-dark {\r\n\tposition:relative;\r\n\r\n\t&:hover {\r\n\t\topacity: 0.8;\r\n\t\tcursor: pointer;\r\n\t}\r\n\r\n\t&:before {\r\n\t\tfont-family: 'Font Awesome 5 Pro';\r\n\t\tcontent: \"\\f058\";\r\n\t\tfont-size: 3rem;\r\n\t\tposition: absolute;\r\n\t\tdisplay: none;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tpadding-left: 0.75rem;\r\n\t\tpadding-top: 0.25rem;\r\n\t\tcolor:var(--success-500);\r\n\t}\t\r\n}\r\n\r\nbody:not(.mod-skin-light):not(.mod-skin-dark) {\r\n\t#skin-default {\r\n\t\t&:before {\r\n\t\t\tdisplay: flex;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n", "/* #LIGHT MODE\r\n========================================================================== */\r\n\r\n\r\n.mod-skin-light:not(.mod-skin-dark) {\r\n\r\n\t#skin-light {\r\n\t\t&:before {\r\n\t\t\tdisplay:flex;\r\n\t\t}\r\n\t}\r\n\r\n\t.page-content-wrapper {\r\n\t  background-color: #f9f9f9;\r\n\t}\r\n\r\n\t.nav-filter input[type=\"text\"] {\r\n\t  background: #ffffff;\r\n\t  color: #333333;\r\n\t  color: var(--theme-fusion-500);\r\n\r\n\t  &:focus {\r\n\t\tborder-color: #333333;\r\n\t\tcolor: var(--theme-primary-500);\r\n\t  }\r\n\t}\r\n\r\n\t.page-sidebar {\r\n\t  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.07);\r\n\t}\r\n\r\n\t&.mod-nav-link:not(.nav-function-top):not(.nav-function-minify):not(.mod-hide-nav-icons) ul.nav-menu:not(.nav-menu-compact) > li > ul:before {\r\n\t  border-left: 1px dashed #cecece;\r\n\t}\r\n\r\n\t&:not(.mod-nav-dark) {\r\n\t  .info-card {\r\n\t\tcolor: #333333;\r\n\t\t//align-items: flex-end;\r\n\t\t//height: 5rem;\r\n\r\n\t\t.text-white {\r\n\t\t  color: #333333 !important;\r\n\t\t  text-shadow: none;\r\n\t\t}\r\n\r\n\t\timg.cover {\r\n\t\t  //display: none;\r\n\t\t  filter: grayscale(100%);\r\n\t\t  opacity: 0.25;\r\n\t\t}\r\n\r\n\t\t.info-card-text > span {\r\n\t\t  color: #333333;\r\n\t\t  text-shadow: none;\r\n\t\t}\r\n\t  }\r\n\r\n\t  &.nav-function-top .page-sidebar .primary-nav .nav-menu > li ul {\r\n\t  \tbackground: #ffffff;\r\n\t  }\r\n\r\n\t  &.nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul:before {\r\n\t  \tcolor: #ffffff;\r\n\t  }\r\n\r\n\t  &.nav-function-top .page-sidebar .primary-nav .nav-menu > li a {\r\n\t\tcolor: rgba(0, 0, 0, 0.8);\r\n\t\tcolor: var(--theme-primary-700);\r\n\t  }\r\n\r\n\t  .page-logo, .page-sidebar, .nav-footer {\r\n\t\tbackground-image: none;\r\n\t  }\r\n\r\n\t  .page-logo, .page-header {\r\n\t\tborder-bottom: 1px solid #eaeaea;\r\n\t\tbox-shadow: none;\r\n\t  }\r\n\r\n\t  .nav-menu li {\r\n\t\t> ul li.active > a {\r\n\t\t  color: rgba(0, 0, 0, 0.8);\r\n\t\t  color: var(--theme-fusion-500);\r\n\t\t}\r\n\r\n\t\ta {\r\n\t\t  &:focus {\r\n\t\t\tcolor: rgba(0, 0, 0, 0.8);\r\n\t\t\tcolor: var(--theme-fusion-500);\r\n\t\t  }\r\n\r\n\t\t  color: rgba(0, 0, 0, 0.8);\r\n\t\t  color: var(--theme-fusion-500);\r\n\t\t}\r\n\t  }\r\n\r\n\t  &.nav-function-top .page-sidebar .primary-nav .nav-menu > li a {\r\n\t\tcolor: rgba(0, 0, 0, 0.8);\r\n\t\tcolor: var(--theme-fusion-500);\r\n\t  }\r\n\r\n\t  .nav-menu {\r\n\t\tli {\r\n\t\t  &.active > a {\r\n\t\t\tcolor: rgba(0, 0, 0, 0.8);\r\n\t\t\tcolor: var(--theme-fusion-500);\r\n\t\t\tfont-weight: 500;\r\n\t\t  }\r\n\r\n\t\t  > ul {\r\n\t\t\tbackground-color: rgba(0, 0, 0, 0.03);\r\n\t\t  }\r\n\r\n\t\t  a > {\r\n\t\t\t[class*='fa-'], .ni {\r\n\t\t\t  color: var(--theme-primary-700);\r\n\t\t\t}\r\n\t\t  }\r\n\r\n\t\t  > ul li a:hover {\r\n\t\t\tcolor: var(--theme-fusion-500);\r\n\t\t  }\r\n\t\t}\r\n\r\n\t\t.nav-title {\r\n\t\t  color: rgba(0, 0, 0, 0.6);\r\n\t\t  color: var(--theme-fusion-50);\r\n\t\t}\r\n\t  }\r\n\r\n\t  .page-logo-text {\r\n\t\tcolor: #333333;\r\n\t  }\r\n\r\n\t  .page-logo, .page-sidebar, .nav-footer {\r\n\t\tbackground: #ffffff;\r\n\t  }\r\n\r\n\t  .page-wrapper {\r\n\t\t\t&.alt,\r\n\t\t\t&.auth,\r\n\t\t\t&.auth .page-inner,\r\n\t\t\t&.alt .page-inner,\r\n\t\t\t&.auth .page-content-wrapper > div,\r\n\t\t\t&.alt .page-content-wrapper > div {\r\n\t\t\t\tbackground: #ffffff;\r\n\r\n\t\t\t\t.text-white {\r\n\t\t\t\t\tcolor: #000 !important;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\r\n\r\n\t\t}\r\n\r\n\t}\r\n\r\n}\r\n\r\n@media (min-width: 992px) {\r\n\r\n\t.mod-skin-light {\r\n\r\n\t  &.nav-function-minify:not(.nav-function-top) {\r\n\t\t&:not(.mod-nav-dark) .page-sidebar .primary-nav:hover .nav-menu > li:hover > a {\r\n\t\t  background: inherit !important;\r\n\t\t  color: inherit !important;\r\n\t\t}\r\n\r\n\t\t.page-sidebar .primary-nav .nav-menu > li > a {\r\n\t\t  + ul {\r\n\t\t\tbackground-color: #ffffff !important;\r\n\r\n\t\t\t&:before {\r\n\t\t\t  color: #ffffff !important;\r\n\t\t\t}\r\n\t\t  }\r\n\r\n\t\t  > .nav-link-text {\r\n\t\t\tcolor: inherit !important;\r\n\t\t  }\r\n\t\t}\r\n\t  }\r\n\r\n\t  &.nav-function-top:not(.mod-nav-dark) {\r\n\t\t.page-header {\r\n\t\t  background: #ffffff;\r\n\r\n\t\t  .badge.badge-icon {\r\n\t\t\tbox-shadow: 0 0 0 1px #ffffff;\r\n\t\t  }\r\n\r\n\t\t  .header-icon:not(.btn) > {\r\n\t\t\t[class*='fa-']:first-child:hover, .ni:first-child:hover {\r\n\t\t\t  color: #333333 !important;\r\n\t\t\t}\r\n\t\t  }\r\n\t\t}\r\n\r\n\t\t#search-field {\r\n\t\t  color: #333333;\r\n\t\t}\r\n\t  }\r\n\t}\r\n\r\n}\r\n\r\n", "@import '_include';\r\n@import '_skin-light';\r\n@import '_skin-dark';", "/* #DARK MODE\r\n========================================================================== */\r\n\r\n@import '_skin-dark-modules/variables';\r\n\r\n.mod-skin-dark:not(.mod-skin-light) {\r\n\r\n\tbackground-color: #303133;\r\n\tcolor: $dark-content-color;\r\n\r\n\t#skin-dark {\r\n\t\t&:before {\r\n\t\t\tdisplay: flex;\r\n\t\t}\r\n\t}\r\n\r\n\t@import '_skin-dark-modules/_accordion';\r\n\t@import '_skin-dark-modules/_alerts';\r\n\t@import '_skin-dark-modules/_alt';\r\n\t@import '_skin-dark-modules/_border';\r\n\t@import '_skin-dark-modules/_breadcrumb';\r\n\t@import '_skin-dark-modules/_button';\r\n\t@import '_skin-dark-modules/_chat-segment';\r\n\t@import '_skin-dark-modules/_colors';\r\n\t@import '_skin-dark-modules/_demo';\r\n\t@import '_skin-dark-modules/_demo-settings';\r\n\t@import '_skin-dark-modules/_dropdown';\r\n\t@import '_skin-dark-modules/_forms';\r\n\t@import '_skin-dark-modules/_misc';\r\n\t@import '_skin-dark-modules/_modal';\r\n\t@import '_skin-dark-modules/_nav';\r\n\t@import '_skin-dark-modules/_navbar';\r\n\t@import '_skin-dark-modules/_navtabs';\r\n\t@import '_skin-dark-modules/_notitfication';\r\n\t@import '_skin-dark-modules/_page_etc';\r\n\t@import '_skin-dark-modules/_pagination';\r\n\t@import '_skin-dark-modules/_progressbar';\r\n\t@import '_skin-dark-modules/_panel';\r\n\t@import '_skin-dark-modules/_tables';\r\n\t@import '_skin-dark-modules/_toast';\r\n\t@import '_skin-dark-modules/_tooltip';\r\n\t@import '_skin-dark-modules/_plugins/_plugin_datatables';\r\n\t@import '_skin-dark-modules/_plugins/_plugin_other';\r\n\t@import '_skin-dark-modules/_plugins/_plugin_prettyprint';\r\n\t@import '_skin-dark-modules/_plugins/_plugin_select2';\r\n\r\n}\r\n\r\n@import '_skin-dark-modules/_mobile';\r\n", "//Variables for Dark MODE\r\n$dark-content-background: #37393e;\r\n$dark-content-links: var(--theme-primary-200); //defaults to #ffffff\r\n$dark-content-color: #a5abb1;\r\n$border-width: 1px;\r\n$border-color: rgba(0,0,0,0.15);\r\n$dark-subheader-title-color: $dark-content-color;\r\n$dark-header-bottom-border-color: #252525;\r\n$dark-nav-link-color: $dark-content-color;\r\n$dark-nav-link-active-color: #ffffff;\r\n$dark-nav-background: #212225;\r\n$dark-black: #000000;\r\n$dark-white: #ffffff;\r\n\r\n\r\n$rgba-primary: #886ab5;\r\n$rgba-success: #1dc9b7;\r\n$rgba-info: #2196F3;\r\n$rgba-warning: #ffc241;\r\n$rgba-danger: #fd3995;\r\n$rgba-fusion: #505050;", "/* accordion */\r\n.accordion .card .card-header .card-title {\r\n\tcolor: rgba($dark-white, 0.85);\r\n}\r\n.accordion.accordion-clean {\r\n\t.card {\r\n\t\tbackground-color: transparent;\r\n\t}\r\n\t.card-header {\r\n\t\tbackground: transparent;\r\n\t}\r\n}", "/* modal */\r\n:not(.modal-alert) {\r\n\t.modal-dialog {\r\n\t\t&:not(.modal-transparent) {\r\n\t\t\t.modal-content {\r\n\t\t\t\tbox-shadow: 0 0 15px 1px rgba(0, 0, 0, 0.25);\r\n\t\t\t\tbackground-color: #383b40;\r\n\t\t\t\tcolor: #a3acb5;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.swal2-popup {\r\n\tbox-shadow: 0 0 15px 1px rgba(0, 0, 0, 0.25);\r\n\tbackground-color: #383b40;\r\n\tcolor: #a3acb5;\r\n}\r\n\r\n.swal2-title {\r\n\tcolor: #ffffff;\r\n}\r\n\r\n.modal-transparent .modal-content {\r\n\tbackground: rgba(0, 0, 0, 0.75);\r\n\tbox-shadow: none;\r\n}\r\n\r\n.modal-title {\r\n\tcolor: $dark-content-color;\r\n}", "/*//pagination\r\n.page-link {\r\n\tcolor: #ffffff;\r\n\tbackground-color: #37393e;\r\n\r\n\t&:hover {\r\n\r\n\t}\r\n}*/", ".panel .card {\r\n\tbackground-color: #37383e;\r\n}\r\n//panel\r\n.panel,\r\n.card {\r\n\tbackground-color: #26272b;\r\n}\r\n.panel {\r\n\tborder-bottom-color: rgba(0, 0, 0, 0.3)\r\n}\r\n.panel-hdr,\r\n.card-header {\r\n\tbackground: rgba(0,0,0,0.03);\r\n\tbackground: rgba(var(--theme-rgb-fusion), 0.5);\r\n\th2 {\r\n\t\tcolor: $dark-white;\r\n\t}\r\n}\r\n.panel-toolbar .btn-panel {\r\n\tborder: 1px solid #333;\r\n}\r\n\r\n.panel-hdr,\r\n.accordion:not(.accordion-clean) .card .card-header {\r\n\tbackground: #303136;\r\n}\r\n.panel-tag {\r\n\tbackground: rgba($rgba-success, 0.15);\r\n\tbackground: rgba(var(--theme-rgb-success), 0.15);\r\n\tcolor: #ffffff;\r\n}\r\n\r\n/* placeholder */\r\n.panel-placeholder {\r\n\tbackground-color: var(--theme-primary);\r\n\topacity: 0.2;\r\n\r\n\t&:before {\r\n\t\tbackground: var(--theme-primary-900);\r\n\t}\r\n}", "//prettify\r\n/*! Color themes for Google Code Prettify | MIT License | github.com/jmblog/color-themes-for-google-code-prettify */\r\n.prettyprint{background:#22221b;font-family:<PERSON><PERSON>,Bitstream Vera Sans Mono,<PERSON>ja<PERSON><PERSON> Sans Mono,Monaco,Consolas,monospace;border:0!important}.pln{color:#f4f3ec}ol.linenums{margin-top:0;margin-bottom:0;color:#6c6b5a}li.L0,li.L1,li.L2,li.L3,li.L4,li.L5,li.L6,li.L7,li.L8,li.L9{padding-left:1em;background-color:#22221b;list-style-type:decimal}@media screen{.str{color:#7d9726}.kwd{color:#5f9182}.com{color:#6c6b5a}.typ{color:#36a166}.lit{color:#ae7313}.pun{color:#f4f3ec}.opn{color:#f4f3ec}.clo{color:#f4f3ec}.tag{color:#ba6236}.atn{color:#ae7313}.atv{color:#5b9d48}.dec{color:#ae7313}.var{color:#ba6236}.fun{color:#36a166}}\r\n", "//alert corrections \r\n.alert-primary,\r\n.alert-sucess,\r\n.alert-danger .alert-warning,\r\n.alert-info .alert-secondary {\r\n\tbackground-color: rgba($dark-white, 0.06);\r\n\tborder-color: rgba($dark-white, 0.09);\r\n\tcolor: $dark-white;\r\n}\r\n.alert-primary {\r\n\tcolor: #ffffff;\r\n\tcolor: var(--theme-primary-100);\r\n\tbackground-color: rgba($rgba-primary, 0.2);\r\n\tbackground-color: rgba(var(--theme-rgb-primary), 0.2);\r\n\tborder-color: rgba($rgba-primary, 0.6);\r\n\tborder-color: rgba(var(--theme-rgb-primary), 0.6);\r\n}\r\n.alert-success {\r\n\tcolor: #ffffff;\r\n\tcolor: var(--theme-success-100);\r\n\tbackground-color: rgba($rgba-success, 0.2);\r\n\tbackground-color: rgba(var(--theme-rgb-success), 0.2);\r\n\tborder-color: rgba($rgba-success, 0.6);\r\n\tborder-color: rgba(var(--theme-rgb-success), 0.6);\r\n}\r\n.alert-danger {\r\n\tcolor: #ffffff;\r\n\tcolor: var(--theme-danger-100);\r\n\tbackground-color: rgba($rgba-danger, 0.2);\r\n\tbackground-color: rgba(var(--theme-rgb-danger), 0.2);\r\n\tborder-color: rgba($rgba-danger, 0.6);\r\n\tborder-color: rgba(var(--theme-rgb-danger), 0.6);\r\n}\r\n.alert-warning {\r\n\tcolor: #ffffff;\r\n\tcolor: var(--theme-warning-100);\r\n\tbackground-color: rgba($rgba-warning, 0.2);\r\n\tbackground-color: rgba(var(--theme-rgb-warning), 0.2);\r\n\tborder-color: rgba($rgba-warning, 0.6);\r\n\tborder-color: rgba(var(--theme-rgb-warning), 0.6);\r\n}\r\n.alert-info {\r\n\tcolor: #ffffff;\r\n\tcolor: var(--theme-info-100);\r\n\tbackground-color: rgba($rgba-info, 0.2);\r\n\tbackground-color: rgba(var(--theme-rgb-info), 0.2);\r\n\tborder-color: rgba($rgba-info, 0.6);\r\n\tborder-color: rgba(var(--theme-rgb-info), 0.6);\r\n}\r\n.alert-secondary {\r\n\tcolor: #ffffff;\r\n\tcolor: var(--theme-white);\r\n\tbackground-color: rgba($rgba-fusion, 0.2);\r\n\tbackground-color: rgba(var(--theme-rgb-fusion), 0.2);\r\n\tborder-color: rgba($rgba-fusion, 0.6);\r\n\tborder-color: rgba(var(--theme-rgb-fusion), 0.6);\r\n}", ".page-wrapper {\r\n\t&.alt,\r\n\t&.auth,\r\n\t&.auth .page-inner,\r\n\t&.alt .page-inner,\r\n\t&.auth .page-content-wrapper > div,\r\n\t&.alt .page-content-wrapper > div {\r\n\t\tbackground: #37393e;\r\n\t}\r\n\r\n}", ".border:not(.border-primary):not(.tab-content):not(.border-secondary):not(.border-success):not(.border-danger):not(.border-warning):not(.border-info):not(.border-light):not(.border-dark):not(.border-white) { border-color: $border-color !important; }\r\n.border-top     { border-top: $border-width solid $border-color !important; }\r\n.border-right   { border-right: $border-width solid $border-color !important; }\r\n.border-bottom  { border-bottom: $border-width solid $border-color !important; }\r\n.border-left    { border-left: $border-width solid $border-color !important; }\r\n\r\n.border-faded {\r\n\tborder-color: rgba(0, 0, 0, 0.15) !important;\r\n}\r\n", "//breadcrumb\r\n.breadcrumb:not(.breadcrumb-arrow):not(.page-breadcrumb):not([class*=\"bg-\"]) {\r\n\tbackground: darken($dark-content-background, 8%);\r\n}\r\n.breadcrumb-arrow li a {\r\n\tcolor: $dark-white !important;\r\n}", "//buttons\r\n.btn {\r\n\t&.btn-panel.bg-transparent {\r\n\t\tcolor: #ffffff;\r\n\t}\r\n}\r\n\r\n.btn-light {\r\n\tbox-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.1);\r\n\tborder-color: rgba(0, 0, 0, 0.35);\r\n}\r\n\r\n.btn-outline-dark {\r\n\tcolor: #989898;\r\n\tborder-color: #000000;\r\n}\r\n\r\n.btn-outline-default {\r\n\tbackground-color: #37393e;\r\n\tcolor: #c5c5c5;\r\n\tborder-color: #1d1f23;\r\n}\r\n\r\n.btn-default {\r\n    background: #5e646b;\r\n    //background-image: linear-gradient(to top, #35393e, #565a61);\r\n    color: #cccccc;\r\n    border-color: transparent;\r\n\r\n\t&:hover {\r\n\t\tborder-color: transparent;\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\r\n\t&:focus {\r\n\t\tborder-color: $dark-content-color !important;\r\n\t}\r\n\r\n\t&.active {\r\n\t\tbackground:  rgba(var(--theme-rgb-primary), 0.85);\r\n\t\tcolor: $dark-white;\r\n\t}\r\n}\t\r\n\r\n.btn-icon:not([class*=\"-primary\"]):not([class*=\"-secondary\"]):not([class*=\"-default\"]):not([class*=\"-success\"]):not([class*=\"-info\"]):not([class*=\"-warning\"]):not([class*=\"-danger\"]):not([class*=\"-dark\"]):not([class*=\"-light\"]):not(.nav-item) {\r\n\t&:not(.active):not(:active):not(:hover) {\r\n\t\tcolor: $dark-content-color !important;\r\n\t}\r\n\r\n\t&:hover {\r\n\t\tcolor: lighten($dark-content-color, 10%) !important;\r\n\t\tbackground: rgba(255,255,255,0.1);\r\n\t}\r\n}", "//chat window\r\n.chat-segment-get {\r\n\t.chat-message {\r\n\t\tbackground: rgba(0,0,0,0.3);\r\n\t\tcolor: rgba(255, 255, 255, 0.8);\r\n\t}\r\n}\r\n.chat-segment-sent {\r\n\t.chat-message {\r\n\t\tbackground: rgba($rgba-success, 0.35);\r\n\t\tbackground: rgba(var(--theme-rgb-success), 0.35);\r\n\t}\r\n}\r\n\r\n.msgr-list + .msgr:before {\r\n\tbackground: rgba(0, 0, 0, 0.5);\r\n}\r\n.time-stamp {\r\n\tcolor: #bdbdbd;\r\n}\r\n", "\r\n//Links or pagelinks\r\n.page-content-wrapper a:not(.btn):not(.badge):not(.dropdown-item):not(.nav-link):not(.navbar-brand):not(.card-title):not([class*=\"fc-\"]):not([class*=\"text-\"]):not(.btn-search-close),\r\n.modal-body a:not(.btn):not(.badge):not(.dropdown-item):not(.nav-link):not(.page-link):not(.navbar-brand):not(.card-title) {\r\n\tcolor: #ffffff;\r\n\tcolor: $dark-content-links;\r\n}\r\n//color correction\r\n.text-success  {\r\n\t//color: $dark-white;\r\n\tcolor: var(--theme-success-300) !important;\r\n}\r\n.text-danger  {\r\n\t//color: $dark-white;\r\n\tcolor: var(--theme-danger-300) !important;\r\n}\r\n.text-warning  {\r\n\t//color: $dark-white;\r\n\tcolor: var(--theme-warning-300) !important;\r\n}\r\n.text-info {\r\n\t//color: $dark-white;\r\n\tcolor: var(--theme-info-300) !important;\r\n}\r\n\r\n.text-dark {\r\n\tcolor: rgba(255, 255, 255, 0.75) !important;\r\n}\r\n.text-muted {\r\n\tcolor: rgba(255, 255, 255, 0.80) !important;\r\n}\r\n.text-contrast {\r\n\tcolor: $dark-white;\r\n}\r\n.text-secondary {\r\n\tcolor: rgba($dark-white, 0.9) !important;\r\n}\r\n.text-primary {\r\n\tcolor: #ffffff;\r\n\tcolor: var(--theme-primary-300) !important;\r\n}\r\na.text-primary:hover, \r\na.text-primary:focus {\r\n\tcolor: #ffffff;\r\n\tcolor: var(--theme-primary-200) !important;\r\n}\r\n\r\n//backgrounds\r\n.bg-faded {\r\n\tbackground-color: #3c3f48;\r\n\tcolor: $dark-content-color;\r\n}\r\n.bg-gray-50 {\r\n\tbackground-color: #47484c;\r\n}\r\n.bg-light {\r\n\tbackground-color: #565656 !important;\r\n}\r\n.bg-subtlelight-fade {\r\n\tbackground: #2d2f32;\r\n\tbackground: rgba(var(--theme-rgb-fusion), 0.2);\r\n}\t\r\n.bg-trans-gradient {\r\n\tbackground: linear-gradient(250deg,  rgba(var(--theme-rgb-info), 0.50), rgba(var(--theme-rgb-primary), 0.50));\r\n}\r\n.bg-highlight {\r\n\tbackground-color: rgba($rgba-warning, 0.15);\r\n\tbackground-color: rgba(var(--theme-rgb-warning), 0.15);\r\n}\r\n.bg-white:not([class*='popover']) {\r\n\tbackground-color: #383b44 !important;\r\n\tcolor: #ffffff;\r\n\r\n\t&.popover {\r\n\t\tcolor: inherit;\r\n\t}\r\n}\r\n.bg-primary-50:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-primary, 0.10);\r\n\tbackground-color: rgba(var(--theme-rgb-primary), 0.10);\r\n\tcolor: #ffffff;\r\n}\r\n.bg-primary-100:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-primary, 0.20);\r\n\tbackground-color: rgba(var(--theme-rgb-primary), 0.20);\r\n\tcolor: #ffffff;\r\n}\r\n.bg-primary-200:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-primary, 0.30);\r\n\tbackground-color: rgba(var(--theme-rgb-primary), 0.30);\r\n\tcolor: #ffffff;\r\n}\r\n.bg-primary-300:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-primary, 0.40);\r\n\tbackground-color: rgba(var(--theme-rgb-primary), 0.40);\r\n\tcolor: #ffffff;\r\n}\r\n.bg-primary-400:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-primary, 0.50);\r\n\tbackground-color: rgba(var(--theme-rgb-primary), 0.50);\r\n}\r\n.bg-primary-500:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-primary, 0.60);\r\n\tbackground-color: rgba(var(--theme-rgb-primary), 0.60);\r\n}\r\n.bg-primary-600:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-primary, 0.70);\r\n\tbackground-color: rgba(var(--theme-rgb-primary), 0.70);\r\n}\r\n.bg-primary-700:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-primary, 0.80);\r\n\tbackground-color: rgba(var(--theme-rgb-primary), 0.80);\r\n}\r\n.bg-primary-800:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-primary, 0.90);\r\n\tbackground-color: rgba(var(--theme-rgb-primary), 0.90);\r\n}\r\n.bg-primary-900:not([class*='popover']) {\r\n\tbackground-color: rgba(var(--theme-rgb-primary), 1);\r\n}\r\n.bg-success-50:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-success, 0.10);\r\n\tbackground-color: rgba(var(--theme-rgb-success), 0.1);\r\n\tcolor: #ffffff;\r\n}\r\n.bg-success-100:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-success, 0.20);\r\n\tbackground-color: rgba(var(--theme-rgb-success), 0.2);\r\n\tcolor: #ffffff;\r\n}\r\n.bg-success-200:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-success, 0.30);\r\n\tbackground-color: rgba(var(--theme-rgb-success), 0.3);\r\n\tcolor: #ffffff;\r\n}\r\n.bg-success-300:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-success, 0.40);\r\n\tbackground-color: rgba(var(--theme-rgb-success), 0.4);\r\n\tcolor: #ffffff;\r\n}\r\n.bg-success-400:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-success, 0.50);\r\n\tbackground-color: rgba(var(--theme-rgb-success), 0.5);\r\n}\r\n.bg-success-500:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-success, 0.60);\r\n\tbackground-color: rgba(var(--theme-rgb-success), 0.6);\r\n}\r\n.bg-success-600:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-success, 0.70);\r\n\tbackground-color: rgba(var(--theme-rgb-success), 0.7);\r\n}\r\n.bg-success-700:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-success, 0.80);\r\n\tbackground-color: rgba(var(--theme-rgb-success), 0.8);\r\n}\r\n.bg-success-800:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-success, 0.90);\r\n\tbackground-color: rgba(var(--theme-rgb-success), 0.9);\r\n}\r\n.bg-success-900:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-success, 1);\r\n\tbackground-color: rgba(var(--theme-rgb-success), 1);\r\n}\r\n.bg-danger-50:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-danger, 0.1);\r\n\tbackground-color: rgba(var(--theme-rgb-danger), 0.1);\r\n\tcolor: #ffffff;\r\n}\r\n.bg-danger-100:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-danger, 0.2);\r\n\tbackground-color: rgba(var(--theme-rgb-danger), 0.2);\r\n\tcolor: #ffffff;\r\n}\r\n.bg-danger-200:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-danger, 0.3);\r\n\tbackground-color: rgba(var(--theme-rgb-danger), 0.3);\r\n\tcolor: #ffffff;\r\n}\r\n.bg-danger-300:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-danger, 0.4);\r\n\tbackground-color: rgba(var(--theme-rgb-danger), 0.4);\r\n\tcolor: #ffffff;\r\n}\r\n.bg-danger-400:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-danger, 0.5);\r\n\tbackground-color: rgba(var(--theme-rgb-danger), 0.5);\r\n\tcolor: #ffffff;\r\n}\r\n.bg-danger-500:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-danger, 0.6);\r\n\tbackground-color: rgba(var(--theme-rgb-danger), 0.6);\r\n}\r\n.bg-danger-600:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-danger, 0.7);\r\n\tbackground-color: rgba(var(--theme-rgb-danger), 0.7);\r\n}\r\n.bg-danger-700:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-danger, 0.8);\r\n\tbackground-color: rgba(var(--theme-rgb-danger), 0.8);\r\n}\r\n.bg-danger-800:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-danger, 0.9);\r\n\tbackground-color: rgba(var(--theme-rgb-danger), 0.9);\r\n}\r\n.bg-danger-900:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-danger, 1);\r\n\tbackground-color: rgba(var(--theme-rgb-danger), 1);\r\n}\r\n.bg-warning-50:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-warning, 0.1);\r\n\tbackground-color: rgba(var(--theme-rgb-warning), 0.1);\r\n\tcolor: #ffffff;\r\n}\r\n.bg-warning-100:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-warning, 0.2);\r\n\tbackground-color: rgba(var(--theme-rgb-warning), 0.2);\r\n\tcolor: #ffffff;\r\n}\r\n.bg-warning-200:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-warning, 0.3);\r\n\tbackground-color: rgba(var(--theme-rgb-warning), 0.3);\r\n\tcolor: #ffffff;\r\n}\r\n.bg-warning-300:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-warning, 0.4);\r\n\tbackground-color: rgba(var(--theme-rgb-warning), 0.4);\r\n\tcolor: #ffffff;\r\n}\r\n.bg-warning-400:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-warning, 0.5);\r\n\tbackground-color: rgba(var(--theme-rgb-warning), 0.5);\r\n\tcolor: #ffffff;\r\n}\r\n.bg-warning-500:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-warning, 0.6);\r\n\tbackground-color: rgba(var(--theme-rgb-warning), 0.6);\r\n}\r\n.bg-warning-600:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-warning, 0.7);\r\n\tbackground-color: rgba(var(--theme-rgb-warning), 0.7);\r\n}\r\n.bg-warning-700:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-warning, 0.8);\r\n\tbackground-color: rgba(var(--theme-rgb-warning), 0.8);\r\n}\r\n.bg-warning-800:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-warning, 0.9);\r\n\tbackground-color: rgba(var(--theme-rgb-warning), 0.9);\r\n}\r\n.bg-warning-900:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-warning, 1);\r\n\tbackground-color: rgba(var(--theme-rgb-warning), 1);\r\n}\r\n.bg-info-50:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-info, 0.1);\r\n\tbackground-color: rgba(var(--theme-rgb-info), 0.1);\r\n\tcolor: #ffffff;\r\n}\r\n.bg-info-100:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-info, 0.2);\r\n\tbackground-color: rgba(var(--theme-rgb-info), 0.2);\r\n\tcolor: #ffffff;\r\n}\r\n.bg-info-200:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-info, 0.3);\r\n\tbackground-color: rgba(var(--theme-rgb-info), 0.3);\r\n\tcolor: #ffffff;\r\n}\r\n.bg-info-300:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-info, 0.4);\r\n\tbackground-color: rgba(var(--theme-rgb-info), 0.4);\r\n\tcolor: #ffffff;\r\n}\r\n.bg-info-400:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-info, 0.5);\r\n\tbackground-color: rgba(var(--theme-rgb-info), 0.5);\r\n}\r\n.bg-info-500:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-info, 0.6);\r\n\tbackground-color: rgba(var(--theme-rgb-info), 0.6);\r\n}\r\n.bg-info-600:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-info, 0.7);\r\n\tbackground-color: rgba(var(--theme-rgb-info), 0.7);\r\n}\r\n.bg-info-700:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-info, 0.8);\r\n\tbackground-color: rgba(var(--theme-rgb-info), 0.8);\r\n}\r\n.bg-info-800:not([class*='popover']) {\r\n\tbackground-color: rgba($rgba-info, 0.9);\r\n\tbackground-color: rgba(var(--theme-rgb-info), 0.9);\r\n}\r\n.bg-info-900:not([class*='popover']) {\r\n\tcolor: #000000;\r\n\tbackground-color: rgba($rgba-info, 1);\r\n\tbackground-color: rgba(var(--theme-rgb-info), 1);\r\n}\r\n[data-replaceclass] {\r\n\t.bg-white {\r\n\t\tbackground: #ffffff !important;\r\n\t}\r\n\t.bg-faded {\r\n\t\tbackground-color: #f7f9fa !important;\r\n\t}\r\n}\r\n\r\n//hover bg\r\n.hover-bg {\r\n\tbackground: #303136;\r\n\tcolor: inherit;\r\n\r\n\t&:hover {\r\n\t\tbackground: inherit;\r\n\t\tcolor: inherit;\r\n\t}\r\n}\r\n\r\n.hover-white {\r\n\t&:hover {\r\n\t\tbackground: #313438 !important;\r\n\t}\r\n\t&:active {\r\n\t\tbackground: darken(#313438, 5%) !important;\r\n\t}\r\n\r\n\t.app-list-name {\r\n\t\tcolor: $dark-content-color;\r\n\t}\r\n}\r\n", "//demo\r\n.bd-highlight {\r\n\tbackground-color: rgba(86, 61, 124, 0.6);\r\n\tborder: 1px solid rgba(121, 95, 160, 0.8);\r\n}\r\n.bd-example-row .row > .col, .bd-example-row .row > [class^=col-] {\r\n\tbackground-color: rgba(86, 61, 124, 0.75);\r\n\tborder: 1px solid rgba(133, 106, 175, 0.5);\r\n}\r\n.card .d-block.bg-danger-50 {\r\n\tbackground-color: rgba($rgba-danger, 0.7);\r\n\tbackground-color: rgba(var(--theme-rgb-danger), 0.7);\r\n}\r\n.card .d-block.bg-info-50 {\r\n\tbackground-color: rgba($rgba-info, 0.7);\r\n\tbackground-color: rgba(var(--theme-rgb-info), 0.7);\r\n}\r\n//demo window\r\n.demo-window:before {\r\n\tbackground: #000000;\r\n}\r\n.app-body-demo {\r\n\tborder: 1px solid rgba(0, 0, 0, 1);\r\n}\r\n.frame-wrap {\r\n\tbackground: transparent;\r\n}\r\n\r\n//events\r\n#app-eventlog {\r\n\t&:empty {\r\n\t\tbackground: #2c2e31;\r\n\t}\r\n\t& > div {\r\n\t\t&:not(:last-child) {\r\n\t\t\tborder-bottom: 1px dashed rgba(255,255,255,0.1);\r\n\t\t}\r\n\t}\r\n}\r\n", "\r\n.settings-panel {\r\n\th5 {\r\n\t\tcolor: rgba(255, 255, 255, 0.5);\r\n\t}\r\n\t.list {\r\n\t\tcolor: #d0d0d0;\r\n\t\t.onoffswitch-title-desc {\r\n\t\t\tcolor: #8c8c8c;\r\n\t\t}\r\n\t\t&:hover {\r\n\t\t\tbackground: rgba(255, 255, 255, 0.03);\r\n\t\t\tcolor: #ffffff;\r\n\t\t}\r\n\t}\r\n\t.expanded:not(.theme-colors) {\r\n\t\tbackground: #2f323b;\r\n\t}\r\n}", "//dropdown\r\n.dropdown-menu {\r\n\tbox-shadow: 0 0 15px 1px rgba(0, 0, 0, 0.25);\r\n\tbackground-color: #383b40;\r\n\tcolor: #a3acb5;\r\n}\r\n.dropdown-item {\r\n\tcolor: $dark-content-color !important;\r\n\r\n\t&:hover,\r\n\t&:focus, {\r\n\t\tbackground-color: #3e4146;\r\n\t}\r\n\r\n\t&.active, \r\n\t&:active {\r\n\t\tcolor: #ffffff !important;\r\n\t\tbackground-color: #594677;\r\n\t\tbackground-color: rgba(var(--theme-rgb-primary), 0.35);\r\n\t}\r\n}\r\n.dropdown-multilevel:hover > .dropdown-item:not(.disabled) {\r\n\tbackground-color: #3e4146;\r\n}\r\n.dropdown-divider {\r\n\tborder-top: 1px solid rgba(0,0,0,0.25);\r\n}\r\n\r\n//dropdown shortcut\r\n.dropdown-icon-menu  {\r\n\t& > ul {\r\n\t\tbackground: #202225;\r\n\t}\r\n\ta {\r\n\t\tbox-shadow: none !important;\r\n\t}\r\n}\r\n", "\r\n//placeholder color\r\n::-webkit-input-placeholder { /* Edge */\r\n\tcolor: #ffffff;\r\n}\r\n\r\n:-ms-input-placeholder { /* Internet Explorer 10-11 */\r\n\tcolor: #ffffff;\r\n}\r\n\r\n::placeholder {\r\n\tcolor: #ffffff;\r\n}\r\n\r\n//select\r\nselect option {\r\n    box-shadow: 0 0 15px 1px rgba(0, 0, 0, 0.25);\r\n    background-color: #383b40;\r\n    color: #ffffff;\r\n    text-shadow: 0 1px 0 rgba(0, 0, 0, 0.4);\r\n}\r\n\r\n\r\n//forms\r\ninput::placeholder,\r\ntextarea::placeholder,\r\nselect::placeholder {\r\n\tcolor: $dark-content-color;\r\n}\r\n.custom-select {\r\n\tcolor: #FFFFFF;\r\n\tbackground-color: #1f2125;\r\n\t&:not(:focus) {\r\n\t\tborder: 1px solid #19191c;\r\n\t}\r\n}\r\n\r\n.form-label {\r\n\tcolor: $dark-content-color;\r\n}\r\n.form-control,\r\n.custom-file-label {\r\n\tcolor: #ffffff;\r\n\tbackground-color: rgba(0, 0, 0, 0.15);\r\n\tborder-color: rgba(0, 0, 0, 0.35);\r\n\t&:focus {\r\n\t\tborder-color: rgba(var(--theme-rgb-primary), 0.7);\r\n\t}\r\n\t&[readonly],\r\n\t&:disabled {\r\n\t\tbackground-color: rgb(63, 66, 70);\r\n\t\tborder-color: rgba(132, 132, 132, 0.35);\r\n\t}\r\n}\r\n.input-group:not(.has-length) .input-group-text {\r\n\tcolor: #838386;\r\n\tbackground-color: #37393e;\r\n\tborder: 1px solid #232528;\r\n}\r\n.custom-control-label::before {\r\n\tbackground-color:  rgba($rgba-primary, 0.1);\r\n\tbackground-color:  rgba(var(--theme-rgb-primary), 0.2);\r\n\tborder-color: #37393e;\r\n}\r\n.custom-control-input:checked ~ .custom-control-label::before {\r\n\tbackground-color:  rgba($rgba-primary, 0.8);\r\n\tbackground-color:  rgba(var(--theme-rgb-primary), 0.8);\r\n\tborder-color: rgba($rgba-primary, 1);\t\r\n\tborder-color: rgba(var(--theme-rgb-primary), 1);\t\r\n}\r\n\r\n\r\n.custom-control-input[disabled] ~ .custom-control-label::before, \r\n.custom-control-input:disabled ~ .custom-control-label::before {\r\n\tbackground-color: #565656;\r\n}\r\n.custom-checkbox .custom-control-input:disabled:checked ~ .custom-control-label::before,\r\n.custom-radio .custom-control-input:disabled:checked ~ .custom-control-label::before,\r\n.custom-switch .custom-control-input:disabled:checked ~ .custom-control-label::before {\r\n\tbackground-color: #565656;\r\n\tbackground-color: rgba(var(--theme-rgb-primary), 0.35)\r\n}\r\n", ".row-grid > {\r\n  .col:before, \r\n  [class^=\"col-\"]:before, \r\n  [class*=\" col-\"]:before, \r\n  [class^=\"col \"]:before, \r\n  [class*=\" col \"]:before, \r\n  [class$=\" col\"]:before, \r\n  [class=\"col\"]:before {\r\n    border-top-color: rgba(0, 0, 0, 0.15);\r\n  }\r\n\r\n  .col:after, \r\n  [class^=\"col-\"]:after, \r\n  [class*=\" col-\"]:after, \r\n  [class^=\"col \"]:after, \r\n  [class*=\" col \"]:after, \r\n  [class$=\" col\"]:after, \r\n  [class=\"col\"]:after {\r\n    border-left-color: rgba(0, 0, 0, 0.15);\r\n  }\r\n}\r\n//close icon\r\n.close {\r\n\tcolor: #fff;\r\n\ttext-shadow: 0 1px 0 #000;\r\n}\r\n\r\n.state-selected {\r\n\tbackground: rgba($rgba-info, 0.15) !important;\r\n\tbackground: rgba(var(--theme-rgb-info), 0.15) !important;\r\n}\r\n\r\n\r\n.notes {\r\n\tbackground: rgba($rgba-warning, 0.15);\r\n\tbackground: rgba(var(--theme-rgb-warning), 0.15);\r\n}\r\npre {\r\n\tcolor: $dark-white;\r\n}\r\n//status\r\n.status:before {\r\n\tborder-color: rgba(0, 0, 0, 0.5)\r\n}\r\n//progress \r\n.progress {\r\n\tbackground-color: rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n\r\nhr {\r\n\tborder-bottom-color: rgba(var(--theme-rgb-fusion), 0.85);\r\n}\r\n\r\ncode {\r\n\tbackground: rgba(0, 0, 0, 0.3);\r\n\tcolor: #ffffff;\r\n}\r\n\r\n//list-group\r\n.list-group-item {\r\n\tbackground: transparent;\r\n}\r\n\r\n//jumbotron\r\n.jumbotron {\r\n  background-color: rgba(0,0,0,0.2);\r\n}\r\n\r\n\r\n&.mod-nav-link:not(.nav-function-top):not(.nav-function-minify):not(.mod-hide-nav-icons) ul.nav-menu:not(.nav-menu-compact)>li>ul:before {\r\n\tborder-left: 1px dashed rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n", ".nav-filter input[type=\"text\"] {\r\n\tbackground: lighten($dark-nav-background, 5%);\r\n\tcolor: #ffffff;\r\n\r\n\t&:focus {\r\n\t\tborder-color: rgba(255, 255, 255, 0.5);\r\n\t\tcolor: #ffffff\r\n\t}\r\n\r\n\t&:not(:focus) {\r\n\t\tborder-color: rgba(255, 255, 255, 0.1);\r\n\t}\r\n}\r\n\r\n.info-card {\r\n\tcolor: $dark-content-color;\r\n\t//height: 5rem;\r\n\t//align-items: flex-end;\r\n\t.text-white {\r\n\t\t//color: $dark-content-color !important;\r\n\t\ttext-shadow: none;\r\n\t}\r\n\t/*img.cover {\r\n\t\topacity: 0.6;\r\n\t}*/\r\n\t.info-card-text>span {\r\n\t\tcolor: #fafafa;\r\n\t\ttext-shadow: none;\r\n\t}\r\n}\r\n&.nav-function-top .page-sidebar .primary-nav .nav-menu > li ul {\r\n\tbackground: #212225;\r\n}\r\n\r\n&.nav-function-top .page-logo {\r\n\tborder-bottom: 1px solid lighten($dark-header-bottom-border-color, 3%);\r\n}\r\n\r\n&.nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul:before {\r\n\tcolor: #212225;\r\n}\r\n&.nav-function-top .page-sidebar .primary-nav .nav-menu>li a {\r\n\tcolor: rgba(255, 255, 255, 0.8);\r\n\tcolor: var(--theme-primary-200);\r\n}\r\n.nav-menu li {\r\n\t>ul li.active>a {\r\n\t\tcolor: rgba(255, 255, 255, 0.8);\r\n\t\tcolor: $dark-nav-link-active-color;\r\n\t}\r\n\ta {\r\n\t\t&:focus {\r\n\t\t\tcolor: rgba(255, 255, 255, 0.8);\r\n\t\t\tcolor: $dark-nav-link-color;\r\n\t\t}\r\n\t\tcolor: rgba(255, 255, 255, 0.8);\r\n\t\tcolor: $dark-nav-link-color;\r\n\t}\r\n}\r\n&.nav-function-top .page-sidebar .primary-nav .nav-menu>li a {\r\n\tcolor: rgba(255, 255, 255, 0.8);\r\n\tcolor: $dark-nav-link-color;\r\n}\r\n.nav-menu {\r\n\tli {\r\n\t\t&.active>a {\r\n\t\t\tcolor: rgba(255, 255, 255, 0.8);\r\n\t\t\tcolor: $dark-nav-link-active-color;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tbackground-color: rgba(0, 0, 0, 0.1);\r\n\t\t}\r\n\t\t/* > ul {\r\n\tbackground-color: rgba(255, 255, 255, 0.03);\r\n  }*/\r\n\t\ta> {\r\n\t\t\t[class*='fa-'],\r\n\t\t\t.ni {\r\n\t\t\t\tcolor: var(--theme-primary-300);\r\n\t\t\t}\r\n\t\t}\r\n\t\t>ul li a:hover {\r\n\t\t\tcolor: var(--theme-primary-100);\r\n\t\t}\r\n\t}\r\n\t.nav-title {\r\n\t\tcolor: rgba(255, 255, 255, 0.6);\r\n\t\tcolor: var(--theme-primary-50);\r\n\t}\r\n}\r\n", "//navbar\r\n.navbar-light {\r\n  .navbar-brand {\r\n    color: rgba(255, 255, 255, 0.9);\r\n  }\r\n\r\n  .navbar-nav {\r\n    .show > .nav-link, .active > .nav-link {\r\n      color: rgba(255, 255, 255, 0.9);\r\n    }\r\n\r\n    .nav-link {\r\n      &.show, &.active {\r\n        color: rgba(255, 255, 255, 0.9);\r\n      }\r\n\r\n      &:hover, &:focus {\r\n        color: rgba(255, 255, 255, 0.7);\r\n      }\r\n\r\n      color: rgba(255, 255, 255, 0.5);\r\n    }\r\n  }\r\n\r\n  .navbar-text {\r\n    color: rgba(255, 255, 255, 0.5);\r\n  }\r\n}\r\n\r\n//Nav\r\n.nav-tabs .nav-item .nav-link.active:not(:hover), \r\n.nav-tabs-clean .nav-item .nav-link:hover {\r\n  background: transparent;\r\n  color: #ffffff !important;\r\n}\r\n\r\n.nav-tabs {\r\n  .nav-link.active, .nav-item.show .nav-link {\r\n    background: transparent;\r\n  }\r\n}", ".nav-tabs:not(.nav-tabs-clean) .nav-link.active, \r\n.nav-tabs:not(.nav-tabs-clean) .nav-item.show .nav-link {\r\n  border-color: rgba(255, 255, 255, 0.15) rgba(255, 255, 255, 0.15) #25272b;\r\n}\r\n.nav-tabs {\r\n  border-bottom-color: rgba(255, 255, 255, 0.15);\r\n}\r\n\r\n.nav-tabs .nav-link:not(.active):hover, \r\n.nav-tabs .nav-link:not(.active):focus {\r\n    border-color: rgba(255, 255, 255, 0.07) rgba(255, 255, 255, 0.07) transparent;\r\n}\r\n\r\n.tab-content.border {\r\n  border-color: rgba(255, 255, 255, 0.15) !important;\r\n}", "//notification\r\n.notification {\r\n\t.msg-a,\r\n\t.msg-b,\r\n\t.name {\r\n\t\tcolor: $dark-content-color !important;\r\n\t}\r\n\r\n\tli {\r\n\t\tbackground-color: $dark-content-background;\r\n\r\n\t\t&.unread > :first-child {\r\n\t\t\tbackground-color:  rgba(var(--theme-rgb-primary), 0.15);\r\n\t\t}\r\n\t}\r\n}\t\r\n\r\n\r\n.notification.notification-layout-2 {\r\n\tli,\r\n\tli.unread {\r\n\t\tbackground: $dark-content-background;\r\n\t}\r\n}\r\n", "body,\r\n.page-content {\r\n\tcolor: $dark-content-color;\r\n}\r\n\r\n//header\r\n.header-icon:not(.btn) > [class*='fa-']:first-child, \r\n.header-icon:not(.btn) > .ni:first-child {\r\n\tcolor: #888888;\r\n\r\n\t&:hover {\r\n\t\tcolor: lighten(#888888, 10%);\r\n\t}\r\n}\r\n.header-icon:not(.btn)[data-toggle=\"dropdown\"][data-toggle=\"dropdown\"]:after {\r\n\tbackground: rgba(0,0,0, 0.75);\r\n\tbackground: rgba(var(--theme-rgb-primary), 0.75);\r\n}\r\n.header-icon:not(.btn)[data-toggle=\"dropdown\"][aria-expanded=\"true\"] > [class*='fa-']:first-child, \r\n.header-icon:not(.btn)[data-toggle=\"dropdown\"][aria-expanded=\"true\"] > .ni:first-child {\r\n \tcolor: #ffffff !important;\r\n}\r\n\r\n.badge.badge-icon {\r\n\tbox-shadow: 0 0 0 1px $dark-nav-background;\r\n}\r\n\r\n\r\n.page-logo,\r\n.page-sidebar,\r\n.nav-footer {\r\n\tbackground-image: none;\r\n}\r\n.page-logo {\r\n\tborder-bottom: 1px solid darken($dark-header-bottom-border-color, 7%);\r\n}\r\n.page-header,\r\n.page-footer {\r\n\tborder-bottom: 1px solid lighten($dark-header-bottom-border-color, 3%);\r\n\tbox-shadow: none;\r\n\t//background: #37393e;\r\n}\r\n.page-header,\r\n.page-content-wrapper,\r\n.page-footer {\r\n\tbackground-color: $dark-content-background;\r\n}\r\n.page-footer {\r\n\tborder-top: 1px solid lighten($dark-header-bottom-border-color, 3%);\r\n\tborder-bottom: none;\r\n}\r\n.page-logo-text {\r\n\tcolor: $dark-white;\r\n}\r\n.page-logo,\r\n.page-sidebar,\r\n.nav-footer {\r\n\tbackground: $dark-nav-background;\r\n}\r\n\r\n.page-sidebar {\r\n\tbox-shadow: none;\r\n}\r\n\r\n.page-breadcrumb .breadcrumb-item.active {\r\n\tcolor: #bfbfbf;\r\n}\r\n.page-breadcrumb {\r\n\ttext-shadow: $dark-black 0 1px;\r\n}\r\n\r\n//page error\r\n.page-error {\r\n\tcolor: #ffffff !important;\r\n\r\n\tsmall {\r\n\t\tcolor: #c1c1c1;\r\n\t}\r\n}\r\n\r\n.subheader-title {\r\n\tcolor: $dark-subheader-title-color;\r\n\ttext-shadow: #505050 0 1px;\r\n\tsmall {\r\n\t\tcolor: darken($dark-subheader-title-color, 10%);\r\n\t}\r\n}", ".progress-bar {\r\n\tbackground-color:  var(--theme-primary-500);\r\n}", "//table\r\n.table,\r\n.table-bordered th,\r\n.table-bordered td,\r\n.table thead th,\r\n.table th,\r\n.table td {\r\n\tborder-color: rgba(255, 255, 255, 0.25);\r\n}\r\n.table {\r\n\tcolor: lighten($dark-content-color, 10%);\r\n\r\n\t&.table-dark {\r\n\t\tcolor: #ffffff;\r\n\t\tbackground-color: #202225;\r\n\t}\r\n\r\n\t&.table-striped tbody tr:nth-of-type(odd) {\r\n\t\tbackground-color: rgba(0, 0, 0, 0.15);\r\n\t}\r\n}\t\r\n.table-hover tbody tr:hover {\r\n\tbackground-color: rgba($rgba-primary, 0.15) !important;\r\n\tbackground-color: rgba(var(--theme-rgb-primary), 0.15) !important;\r\n\tcolor: inherit;\r\n}\r\n.thead-themed {\r\n\tbackground-color: rgba(var(--theme-rgb-primary), 0.10);\r\n\tbackground-image: linear-gradient(to top, rgba(var(--theme-rgb-primary), 0.15), rgba(var(--theme-rgb-primary), 0.55))\r\n}\r\n.table-active,\r\n.table-active>th,\r\n.table-active>td {\r\n\tbackground-color: transparent;\r\n}", "//toast\r\n.toast:not([class*=\"toast-\"]) {\r\n\tbackground-color: rgba(31, 31, 31, 0.85);\r\n\r\n\t.toast-header {\r\n\t\tcolor: #ffffff;\r\n\t\tbackground-color: rgba(35, 35, 35, 0.85);\r\n\t}\r\n}\r\n", "//tooltip\r\n.bs-tooltip-top .arrow::before, \r\n.bs-tooltip-auto[x-placement^=\"top\"] .arrow::before {\r\n\tborder-top-color: rgba(0, 0, 0, 0.9);\r\n}\r\n.bs-tooltip-right .arrow::before, \r\n.bs-tooltip-auto[x-placement^=\"right\"] .arrow::before {\r\n\tborder-right-color: rgba(0, 0, 0, 0.9);\r\n}\r\n.bs-tooltip-left .arrow::before, \r\n.bs-tooltip-auto[x-placement^=\"left\"] .arrow::before {\r\n\tborder-left-color: rgba(0, 0, 0, 0.9);\r\n}\r\n.bs-tooltip-bottom .arrow::before, \r\n.bs-tooltip-auto[x-placement^=\"bottom\"] .arrow::before {\r\n\tborder-bottom-color: rgba(0, 0, 0, 0.9);\r\n}\r\n.tooltip-inner {\r\n\tbackground-color: rgba(0, 0, 0, 0.9);\r\n}\r\n\r\n\r\n.popover:not([class*=\"bg-\"]) {\r\n\tborder: 3px solid rgba(255, 255, 255, 0.4);\r\n}", "//datatables\r\n.fc-head-container thead tr {\r\n\tbackground-image: linear-gradient(to top, #35393e 0, #565a61 100%);\r\n}\r\n.dt-autofill-list {\r\n\tbackground-color: #383b40;\r\n}\r\ndiv.dt-autofill-list ul li:hover {\r\n\tbackground-color: darken(#383b40, 5%);\r\n}\r\n\r\ntable.DTFC_Cloned tr {\r\n\tbackground: transparent;\r\n}\r\ndiv.DTFC_LeftHeadWrapper table,\r\ntable.dataTable.table-striped.DTFC_Cloned tbody, \r\ndiv.DTFC_RightHeadWrapper table {\r\n\tbackground: #25272b;\r\n}\r\ntable.dataTable > tbody > tr.child ul.dtr-details > li {\r\n\tborder-bottom: 1px solid #373a40;\r\n}\r\n\r\n.sorting_asc,\r\n.sorting_desc,\r\n.even .sorting_1 {\r\n\tbackground-color: rgba($rgba-primary, 0.1);\r\n\tbackground-color: rgba(var(--theme-rgb-primary), 0.1);\r\n}\r\n.odd .sorting_1 {\r\n\tbackground-color: rgba($rgba-primary, 0.2);\r\n\tbackground-color: rgba(var(--theme-rgb-primary), 0.2);\r\n}\r\n.dataTables_filter {\r\n\t&:not(.has-length) {\r\n\t\t.input-group-text {\r\n\t\t\tcolor: #a7a7a7;\r\n\t\t\tbackground-color: #474950;\r\n\t\t\tborder-color: #262627;\r\n\t\t}\r\n\t}\r\n}\r\ntable.dataTable:not(.table-dark) tr.dtrg-group td {\r\n\tbackground: rgba(0, 0, 0, 0.13);\r\n}\r\ndiv.DTS div.dataTables_scrollBody table {\r\n\tbackground-color: #3a3c45;\r\n}", "//jqvmap\r\n.jqvmap-zoomin,\r\n.jqvmap-zoomout {\r\n\tbackground-image: linear-gradient(to top, #2f323b, #2a2d35);\r\n\tcolor: #c3c3c3;\r\n}\r\n.jqvmap-bg-ocean {\r\n\tbackground-color: #374344 !important;\r\n}\r\n//ion range slider \r\n.irs-line {\r\n\tbackground: #3c3e44;\r\n\tborder-color: #28292d;\r\n}\r\n//calendar & dropzone\r\n.fc a {\r\n\tcolor: #ffffff !important;\r\n}\r\n.dropzone,\r\n.fc td.fc-other-month {\r\n\tbackground-color: rgba($rgba-primary, 0.15);\r\n\tbackground-color: rgba(var(--theme-rgb-primary), 0.15);\r\n}\r\n//date range picker\r\n.daterangepicker,\r\n.daterangepicker .calendar-table {\r\n\tbackground: #383b40;\r\n}\r\n.daterangepicker td.off, \r\n.daterangepicker td.off.in-range, \r\n.daterangepicker td.off.start-date,\r\n.daterangepicker td.off.end-date {\r\n\tbackground: transparent;\r\n}\r\n.daterangepicker .calendar-table {\r\n\tborder-color: #383b40;\r\n}\r\n\r\n//datepicker\r\n.datepicker-dropdown.datepicker-orient-top:after {\r\n\tborder-top-color: #383b40;\r\n}\r\n.datepicker-dropdown:after {\r\n\tborder-bottom-color: #383b40;\r\n}\r\n//summernote \r\n.note-toolbar .note-btn {\r\n\tbackground: #25272b;\r\n\r\n\t&:hover,\r\n\t&:focus,\r\n\t&:active {\r\n\t\tbackground: lighten(#25272b, 10%);\r\n\t\tcolor: #ffffff;\r\n\t}\r\n}\r\n.note-editor.note-frame .note-editing-area .note-editable, \r\n.note-editor.note-airframe .note-editing-area .note-editable {\r\n\tbackground-color: rgba(0, 0, 0, 0.15);\r\n\tcolor: #fff;\r\n}\r\n.note-editor.note-frame .note-statusbar, \r\n.note-editor.note-airframe .note-statusbar {\r\n\tborder-top: 1px solid #25272b;\r\n\tbackground-color: #434548;\r\n}", ".select2-container--default .select2-selection--single, \r\n.select2-container--default.select2-container--disabled .select2-selection--single, \r\n.select2-container--default .select2-selection--multiple, \r\n.select2-container--default.select2-container--disabled .select2-selection--multiple,\r\n.select2-container--default .select2-search--dropdown .select2-search__field {\r\n\t//background: #383b40;\r\n\tcolor: #ffffff;\r\n    background-color: rgba(0, 0, 0, 0.15);\r\n    border-color: rgba(0, 0, 0, 0.35);\r\n}\r\n\r\n.select2-container--default .select2-selection--single .select2-selection__rendered {\r\n\tcolor: #ffffff;\r\n}\r\n\r\n\r\n.select2-container--default.select2-container--open.select2-container--below .select2-selection--single, \r\n.select2-container--default.select2-container--open.select2-container--below .select2-selection--multiple, \r\n.select2-container--default.select2-container--open.select2-container--above .select2-selection--single, \r\n.select2-container--default.select2-container--open.select2-container--above .select2-selection--multiple,\r\n.select2-container--open .select2-dropdown--above,\r\n.select2-container--open .select2-dropdown--below {\r\n\tbackground: #383b40;\r\n}\r\n\r\n\r\n.select2-container--disabled .select2-selection.select2-selection--single,\r\n.select2-container--disabled .select2-selection.select2-selection--multiple {\r\n\tbackground-color: #3f4246;\r\n\tborder-color: rgba(132, 132, 132, 0.35);\r\n}\r\n", "@media (min-width: 992px) {\r\n\t.mod-skin-dark {\r\n\t\t&.nav-function-minify:not(.nav-function-top) {\r\n\t\t\t&:not(.mod-nav-dark) .page-sidebar .primary-nav:hover .nav-menu>li:hover>a {\r\n\t\t\t\tbackground: inherit !important;\r\n\t\t\t\tcolor: #d0d0d0 !important;\r\n\t\t\t}\r\n\t\t\t.page-sidebar .primary-nav .nav-menu>li>a {\r\n\t\t\t\t+ul {\r\n\t\t\t\t\tbackground-color: $dark-nav-background !important;\r\n\t\t\t\t\t&:before {\r\n\t\t\t\t\t\tcolor: $dark-nav-background !important;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t>.nav-link-text {\r\n\t\t\t\t\tcolor: inherit !important;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t&.nav-function-top {\r\n\t\t\t.page-header {\r\n\t\t\t\tbackground: $dark-nav-background;\r\n\t\t\t\t.header-icon:not(.btn)> {\r\n\t\t\t\t\t[class*='fa-']:first-child:hover,\r\n\t\t\t\t\t.ni:first-child:hover {\r\n\t\t\t\t\t\tcolor: #ffffff !important;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t#search-field {\r\n\t\t\t\tcolor: #ffffff;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.subheader-title {\r\n\t\t\tcolor: #ffffff !important;\r\n\t\t}\r\n\t}\r\n}"]}