<?php

namespace App\Helpers;

use App\Models\ShonaDesign;
use App\Models\CompanyData;

class DesignHelper
{
    /**
     * Get current design configuration
     */
    public static function getCurrentDesign()
    {
        return ShonaDesign::getCurrentDesign();
    }
    
    /**
     * Get company data
     */
    public static function getCompanyData()
    {
        return CompanyData::orderBy('id', 'desc')->first();
    }
    
    /**
     * Generate CSS variables for the current design
     */
    public static function generateCSSVariables($design = null)
    {
        if (!$design) {
            $design = self::getCurrentDesign();
        }
        
        $css = ":root {\n";
        
        // Font Configuration
        $css .= "    --font-type: {$design->Font_Type};\n";
        
        // Body Styling
        $css .= "    --body-bg-color: {$design->Body_BG_Color};\n";
        $css .= "    --body-title-color: {$design->Body_Title_Color};\n";
        
        // Header Styling
        $css .= "    --header-bg-color: {$design->Header_BG_Color};\n";
        $css .= "    --header-txt-color: {$design->Header_Txt_Color};\n";
        $css .= "    --header-logo-size: {$design->Header_Logo_Size};\n";
        $css .= "    --header-height: {$design->Header_Height};\n";
        
        // Navigation Styling
        $css .= "    --navbar-bg-color: {$design->Navbar_BG_Color};\n";
        $css .= "    --navbar-txt-color: {$design->Navbar_Txt_Color};\n";
        $css .= "    --navbar-hover-bg-color: {$design->Navbar_Hover_BG_Color};\n";
        $css .= "    --navbar-hover-txt-color: {$design->Navbar_Hover_Txt_Color};\n";
        $css .= "    --navbar-active-bg-color: {$design->Navbar_Active_BG_Color};\n";
        $css .= "    --navbar-active-txt-color: {$design->Navbar_Active_Txt_Color};\n";
        
        // Hero Section
        $css .= "    --hero-overlay-color: {$design->Hero_Overlay_Color};\n";
        $css .= "    --hero-overlay-opacity: {$design->Hero_Overlay_Opacity};\n";
        $css .= "    --hero-title-color: {$design->Hero_Title_Color};\n";
        $css .= "    --hero-subtitle-color: {$design->Hero_Subtitle_Color};\n";
        $css .= "    --hero-button-bg-color: {$design->Hero_Button_BG_Color};\n";
        $css .= "    --hero-button-txt-color: {$design->Hero_Button_Txt_Color};\n";
        $css .= "    --hero-button-hover-bg-color: {$design->Hero_Button_Hover_BG_Color};\n";
        $css .= "    --hero-button-hover-txt-color: {$design->Hero_Button_Hover_Txt_Color};\n";
        
        // Sections Styling
        $css .= "    --section-title-color: {$design->Section_Title_Color};\n";
        $css .= "    --section-subtitle-color: {$design->Section_Subtitle_Color};\n";
        $css .= "    --section-text-color: {$design->Section_Text_Color};\n";
        $css .= "    --section-bg-color: {$design->Section_BG_Color};\n";
        $css .= "    --section-alt-bg-color: {$design->Section_Alt_BG_Color};\n";
        
        // Cards Styling
        $css .= "    --card-bg-color: {$design->Card_BG_Color};\n";
        $css .= "    --card-border-color: {$design->Card_Border_Color};\n";
        $css .= "    --card-shadow-color: {$design->Card_Shadow_Color};\n";
        $css .= "    --card-title-color: {$design->Card_Title_Color};\n";
        $css .= "    --card-text-color: {$design->Card_Text_Color};\n";
        $css .= "    --card-hover-bg-color: {$design->Card_Hover_BG_Color};\n";
        $css .= "    --card-hover-shadow-color: {$design->Card_Hover_Shadow_Color};\n";
        
        // Buttons Styling
        $css .= "    --primary-button-bg-color: {$design->Primary_Button_BG_Color};\n";
        $css .= "    --primary-button-txt-color: {$design->Primary_Button_Txt_Color};\n";
        $css .= "    --primary-button-hover-bg-color: {$design->Primary_Button_Hover_BG_Color};\n";
        $css .= "    --primary-button-hover-txt-color: {$design->Primary_Button_Hover_Txt_Color};\n";
        $css .= "    --secondary-button-bg-color: {$design->Secondary_Button_BG_Color};\n";
        $css .= "    --secondary-button-txt-color: {$design->Secondary_Button_Txt_Color};\n";
        $css .= "    --secondary-button-hover-bg-color: {$design->Secondary_Button_Hover_BG_Color};\n";
        $css .= "    --secondary-button-hover-txt-color: {$design->Secondary_Button_Hover_Txt_Color};\n";
        
        // Footer Styling
        $css .= "    --footer-bg-color: {$design->Footer_BG_Color};\n";
        $css .= "    --footer-title-color: {$design->Footer_Title_Color};\n";
        $css .= "    --footer-txt-color: {$design->Footer_Txt_Color};\n";
        $css .= "    --footer-link-color: {$design->Footer_Link_Color};\n";
        $css .= "    --footer-link-hover-color: {$design->Footer_Link_Hover_Color};\n";
        $css .= "    --footer-social-bg-color: {$design->Footer_Social_BG_Color};\n";
        $css .= "    --footer-social-txt-color: {$design->Footer_Social_Txt_Color};\n";
        $css .= "    --footer-social-hover-bg-color: {$design->Footer_Social_Hover_BG_Color};\n";
        $css .= "    --footer-social-hover-txt-color: {$design->Footer_Social_Hover_Txt_Color};\n";
        
        // Form Styling
        $css .= "    --form-input-bg-color: {$design->Form_Input_BG_Color};\n";
        $css .= "    --form-input-border-color: {$design->Form_Input_Border_Color};\n";
        $css .= "    --form-input-txt-color: {$design->Form_Input_Txt_Color};\n";
        $css .= "    --form-input-focus-border-color: {$design->Form_Input_Focus_Border_Color};\n";
        $css .= "    --form-label-color: {$design->Form_Label_Color};\n";
        $css .= "    --form-button-bg-color: {$design->Form_Button_BG_Color};\n";
        $css .= "    --form-button-txt-color: {$design->Form_Button_Txt_Color};\n";
        
        // Product/Service Cards
        $css .= "    --product-card-bg-color: {$design->Product_Card_BG_Color};\n";
        $css .= "    --product-card-border-color: {$design->Product_Card_Border_Color};\n";
        $css .= "    --product-title-color: {$design->Product_Title_Color};\n";
        $css .= "    --product-price-color: {$design->Product_Price_Color};\n";
        $css .= "    --product-description-color: {$design->Product_Description_Color};\n";
        $css .= "    --product-badge-bg-color: {$design->Product_Badge_BG_Color};\n";
        $css .= "    --product-badge-txt-color: {$design->Product_Badge_Txt_Color};\n";
        
        // Layout Settings
        $css .= "    --container-max-width: {$design->Container_Max_Width};\n";
        $css .= "    --section-padding: {$design->Section_Padding};\n";
        $css .= "    --card-border-radius: {$design->Card_Border_Radius};\n";
        $css .= "    --button-border-radius: {$design->Button_Border_Radius};\n";
        
        // Typography
        $css .= "    --primary-font-family: {$design->Primary_Font_Family};\n";
        $css .= "    --secondary-font-family: {$design->Secondary_Font_Family};\n";
        $css .= "    --base-font-size: {$design->Base_Font_Size};\n";
        $css .= "    --line-height: {$design->Line_Height};\n";
        $css .= "    --heading-font-weight: {$design->Heading_Font_Weight};\n";
        $css .= "    --body-font-weight: {$design->Body_Font_Weight};\n";
        
        // Animation Settings
        $css .= "    --animation-duration: {$design->Animation_Duration};\n";
        $css .= "    --animation-easing: {$design->Animation_Easing};\n";
        
        $css .= "}\n";
        
        return $css;
    }
    
    /**
     * Get font configuration
     */
    public static function getFontConfiguration($design = null)
    {
        if (!$design) {
            $design = self::getCurrentDesign();
        }
        
        $fonts = [];
        
        switch ($design->Font_Type) {
            case 1:
                $fonts['name'] = 'Cairo';
                $fonts['url'] = 'https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;800;1000&display=swap';
                $fonts['family'] = "'Cairo', sans-serif";
                break;
            case 2:
                $fonts['name'] = 'Roboto';
                $fonts['url'] = 'https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap';
                $fonts['family'] = "'Roboto', sans-serif";
                break;
            case 3:
                $fonts['name'] = 'Open Sans';
                $fonts['url'] = 'https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap';
                $fonts['family'] = "'Open Sans', sans-serif";
                break;
            case 4:
                $fonts['name'] = 'Poppins';
                $fonts['url'] = 'https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap';
                $fonts['family'] = "'Poppins', sans-serif";
                break;
            default:
                $fonts['name'] = 'System Default';
                $fonts['url'] = '';
                $fonts['family'] = 'system-ui, -apple-system, sans-serif';
        }
        
        return $fonts;
    }
    
    /**
     * Check if feature is enabled
     */
    public static function isFeatureEnabled($feature, $design = null)
    {
        if (!$design) {
            $design = self::getCurrentDesign();
        }
        
        return $design->{$feature} ?? false;
    }
    
    /**
     * Get direction based on locale
     */
    public static function getDirection()
    {
        if (session()->has('lang')) {
            return session('lang') == 'ar' ? 'rtl' : 'ltr';
        }
        return 'rtl'; // Default to RTL for Arabic
    }
    
    /**
     * Check if RTL is enabled
     */
    public static function isRTL()
    {
        return self::getDirection() === 'rtl';
    }
}
