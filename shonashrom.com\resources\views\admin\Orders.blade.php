@extends('admin.index')
@section('content')
@php
use App\Models\City;
use App\Models\Place;
use App\Models\Governrate;
use App\Models\ProductOrder;
use App\Models\Products;
@endphp
  <title>{{trans('admin.Orders')}}</title>

 <main id="js-page-content" role="main" class="page-content">
                    <ol class="breadcrumb page-breadcrumb">
                        <li class="breadcrumb-item"><a href="javascript:void(0);">{{trans('admin.Home')}}</a></li>
                        <li class="breadcrumb-item active">{{trans('admin.Orders')}}</li>
                        <li class="position-absolute pos-top pos-right d-none d-sm-block"><span
                                class="js-get-date"></span></li>
                    </ol>
                    <div class="subheader">
                       
                    </div>
                    <div class="row">
                        <div class="col-xl-12">
                            <div id="panel-1" class="panel">
                                <div class="panel-hdr">
                                    <h2>
                                        <span class="fw-300"><i> {{trans('admin.Orders')}}</i></span>
                                    </h2>

                                    <div class="panel-toolbar">
 
                                        
                            <button class="btn btn-primary btn-sm mx-3" data-toggle="dropdown">Table Style</button>
                 <div class="dropdown-menu dropdown-menu-animated dropdown-menu-right position-absolute pos-top">
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-bordered" data-target="#dt-basic-example"> Bordered
                                                Table </button>
                                            <button class="dropdown-item" data-action="toggle" data-class="table-sm"
                                                data-target="#dt-basic-example"> Smaller Table </button>
                                            <button class="dropdown-item" data-action="toggle" data-class="table-dark"
                                                data-target="#dt-basic-example"> Table Dark </button>
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-hover" data-target="#dt-basic-example"> Table Hover
                                            </button>
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-stripe" data-target="#dt-basic-example"> Table
                                                Stripped </button>
                                            <div class="dropdown-divider m-0"></div>
                                            <div class="dropdown-multilevel dropdown-multilevel-left">
                                                <div class="dropdown-item">
                                                    tbody color
                                                </div>
                                                <div class="dropdown-menu no-transition-delay">
                                                    <div class="js-tbody-colors dropdown-multilevel dropdown-multilevel-left d-flex flex-wrap"
                                                        style="width: 15.9rem; padding: 0.5rem">
                                                        <a href="javascript:void(0);" data-bg="bg-primary-100"
                                                            class="btn d-inline-block bg-primary-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-200"
                                                            class="btn d-inline-block bg-primary-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-300"
                                                            class="btn d-inline-block bg-primary-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-400"
                                                            class="btn d-inline-block bg-primary-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-500"
                                                            class="btn d-inline-block bg-primary-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-600"
                                                            class="btn d-inline-block bg-primary-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-700"
                                                            class="btn d-inline-block bg-primary-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-800"
                                                            class="btn d-inline-block bg-primary-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-900"
                                                            class="btn d-inline-block bg-primary-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-100"
                                                            class="btn d-inline-block bg-success-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-200"
                                                            class="btn d-inline-block bg-success-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-300"
                                                            class="btn d-inline-block bg-success-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-400"
                                                            class="btn d-inline-block bg-success-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-500"
                                                            class="btn d-inline-block bg-success-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-600"
                                                            class="btn d-inline-block bg-success-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-700"
                                                            class="btn d-inline-block bg-success-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-800"
                                                            class="btn d-inline-block bg-success-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-900"
                                                            class="btn d-inline-block bg-success-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-100"
                                                            class="btn d-inline-block bg-info-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-200"
                                                            class="btn d-inline-block bg-info-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-300"
                                                            class="btn d-inline-block bg-info-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-400"
                                                            class="btn d-inline-block bg-info-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-500"
                                                            class="btn d-inline-block bg-info-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-600"
                                                            class="btn d-inline-block bg-info-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-700"
                                                            class="btn d-inline-block bg-info-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-800"
                                                            class="btn d-inline-block bg-info-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-900"
                                                            class="btn d-inline-block bg-info-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-100"
                                                            class="btn d-inline-block bg-danger-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-200"
                                                            class="btn d-inline-block bg-danger-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-300"
                                                            class="btn d-inline-block bg-danger-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-400"
                                                            class="btn d-inline-block bg-danger-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-500"
                                                            class="btn d-inline-block bg-danger-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-600"
                                                            class="btn d-inline-block bg-danger-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-700"
                                                            class="btn d-inline-block bg-danger-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-800"
                                                            class="btn d-inline-block bg-danger-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-900"
                                                            class="btn d-inline-block bg-danger-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-100"
                                                            class="btn d-inline-block bg-warning-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-200"
                                                            class="btn d-inline-block bg-warning-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-300"
                                                            class="btn d-inline-block bg-warning-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-400"
                                                            class="btn d-inline-block bg-warning-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-500"
                                                            class="btn d-inline-block bg-warning-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-600"
                                                            class="btn d-inline-block bg-warning-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-700"
                                                            class="btn d-inline-block bg-warning-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-800"
                                                            class="btn d-inline-block bg-warning-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-900"
                                                            class="btn d-inline-block bg-warning-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-100"
                                                            class="btn d-inline-block bg-fusion-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-200"
                                                            class="btn d-inline-block bg-fusion-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-300"
                                                            class="btn d-inline-block bg-fusion-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-400"
                                                            class="btn d-inline-block bg-fusion-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-500"
                                                            class="btn d-inline-block bg-fusion-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-600"
                                                            class="btn d-inline-block bg-fusion-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-700"
                                                            class="btn d-inline-block bg-fusion-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-800"
                                                            class="btn d-inline-block bg-fusion-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-900"
                                                            class="btn d-inline-block bg-fusion-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg=""
                                                            class="btn d-inline-block bg-white border width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="dropdown-multilevel dropdown-multilevel-left">
                                                <div class="dropdown-item">
                                                    thead color
                                                </div>
                                                <div class="dropdown-menu no-transition-delay">
                                                    <div class="js-thead-colors dropdown-multilevel dropdown-multilevel-left d-flex flex-wrap"
                                                        style="width: 15.9rem; padding: 0.5rem">
                                                        <a href="javascript:void(0);" data-bg="bg-primary-100"
                                                            class="btn d-inline-block bg-primary-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-200"
                                                            class="btn d-inline-block bg-primary-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-300"
                                                            class="btn d-inline-block bg-primary-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-400"
                                                            class="btn d-inline-block bg-primary-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-500"
                                                            class="btn d-inline-block bg-primary-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-600"
                                                            class="btn d-inline-block bg-primary-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-700"
                                                            class="btn d-inline-block bg-primary-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-800"
                                                            class="btn d-inline-block bg-primary-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-900"
                                                            class="btn d-inline-block bg-primary-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-100"
                                                            class="btn d-inline-block bg-success-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-200"
                                                            class="btn d-inline-block bg-success-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-300"
                                                            class="btn d-inline-block bg-success-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-400"
                                                            class="btn d-inline-block bg-success-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-500"
                                                            class="btn d-inline-block bg-success-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-600"
                                                            class="btn d-inline-block bg-success-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-700"
                                                            class="btn d-inline-block bg-success-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-800"
                                                            class="btn d-inline-block bg-success-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-900"
                                                            class="btn d-inline-block bg-success-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-100"
                                                            class="btn d-inline-block bg-info-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-200"
                                                            class="btn d-inline-block bg-info-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-300"
                                                            class="btn d-inline-block bg-info-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-400"
                                                            class="btn d-inline-block bg-info-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-500"
                                                            class="btn d-inline-block bg-info-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-600"
                                                            class="btn d-inline-block bg-info-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-700"
                                                            class="btn d-inline-block bg-info-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-800"
                                                            class="btn d-inline-block bg-info-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-900"
                                                            class="btn d-inline-block bg-info-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-100"
                                                            class="btn d-inline-block bg-danger-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-200"
                                                            class="btn d-inline-block bg-danger-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-300"
                                                            class="btn d-inline-block bg-danger-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-400"
                                                            class="btn d-inline-block bg-danger-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-500"
                                                            class="btn d-inline-block bg-danger-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-600"
                                                            class="btn d-inline-block bg-danger-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-700"
                                                            class="btn d-inline-block bg-danger-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-800"
                                                            class="btn d-inline-block bg-danger-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-900"
                                                            class="btn d-inline-block bg-danger-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-100"
                                                            class="btn d-inline-block bg-warning-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-200"
                                                            class="btn d-inline-block bg-warning-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-300"
                                                            class="btn d-inline-block bg-warning-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-400"
                                                            class="btn d-inline-block bg-warning-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-500"
                                                            class="btn d-inline-block bg-warning-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-600"
                                                            class="btn d-inline-block bg-warning-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-700"
                                                            class="btn d-inline-block bg-warning-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-800"
                                                            class="btn d-inline-block bg-warning-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-900"
                                                            class="btn d-inline-block bg-warning-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-100"
                                                            class="btn d-inline-block bg-fusion-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-200"
                                                            class="btn d-inline-block bg-fusion-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-300"
                                                            class="btn d-inline-block bg-fusion-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-400"
                                                            class="btn d-inline-block bg-fusion-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-500"
                                                            class="btn d-inline-block bg-fusion-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-600"
                                                            class="btn d-inline-block bg-fusion-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-700"
                                                            class="btn d-inline-block bg-fusion-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-800"
                                                            class="btn d-inline-block bg-fusion-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-900"
                                                            class="btn d-inline-block bg-fusion-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg=""
                                                            class="btn d-inline-block bg-white border width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="panel-container show">
                                      <span id="ex"> @include('admin.layouts.messages')</span>  
                                    <div class="panel-content">
                                        <!-- datatable start -->
                                        <div id="mobile-overflow">
                                        <table id="dt-basic-example"
                                            class="table table-bordered table-hover table-striped mobile-width">
                                            <thead class="bg-highlight">
                                                <tr>
                                                    <th>{{trans('admin.User')}}</th>
                                                    <th>{{trans('admin.Date')}}</th>
                                                    <th>{{trans('admin.Total_Price')}}</th>
                                                    <th>{{trans('admin.Address')}}</th>
                                                    <th>{{trans('admin.Products')}}</th>
                                           
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($items as $item)
                                                <tr>
                                       
                                              
                                                    <td>{{$item->Name}}</td>
                                                    <td>{{$item->Date}}</td>
                                                    <td>{{$item->Total_Price}}</td>
                                                    <td>
                                                    
                                                                          
             <button type="button" class="btn btn-default" data-toggle="modal" data-target="#Address{{$item->id}}">{{trans('admin.Address')}}</button>
                                                            
                                                    </td>              
                                                    <td>
                                                    
                                                                          
             <button type="button" class="btn btn-default" data-toggle="modal" data-target="#Products{{$item->id}}">{{trans('admin.Products')}}</button>
                                                            
                                                    </td>
               
                                                </tr>
                                                @endforeach
                                            </tbody>
                                            <tfoot>
                                                 <tr>
                                                       <th>{{trans('admin.User')}}</th>
                                                    <th>{{trans('admin.Date')}}</th>
                                                    <th>{{trans('admin.Total_Price')}}</th>
                                                    <th>{{trans('admin.Address')}}</th>
                                                    <th>{{trans('admin.Products')}}</th>
                                                </tr>
                                            </tfoot>
                                        </table>
                                        </div>
                                  {{$items->Links()}}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
     
     
      @foreach($items as $item) 

                    <!-- Modal Address -->
                    <div class="modal fade" id="Address{{$item->id}}" tabindex="-1" role="dialog" aria-hidden="true">
                        
                        <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4 class="modal-title">
                                    {{trans('admin.Address')}}
                                    </h4>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                    </button>
                                </div>
                            <div class="modal-body">
          

                   <table id="mytable" class="table align-middle mb-0 bg-white">
              <thead class="bg-light">
                <tr class="header-row" style="text-align: center;">
  
                                   <th>{{trans('admin.Name')}}</th>
                                   <th>{{trans('admin.Email')}}</th>
                                   <th>{{trans('admin.Phone')}}</th>
                                   <th>{{trans('admin.OtherPhone')}}</th>
                                   <th>{{trans('admin.Address_Name')}}</th>
                    <th>{{trans('admin.Special_Mark')}} </th>     
                                                                  
                                                                         <th>{{trans('admin.Street')}}</th>    
                                                                         <th>{{trans('admin.Buliding')}}</th>    
                                                                         <th>{{trans('admin.Floor')}}</th>    
                                                                         <th>{{trans('admin.Flat')}}</th>    
                                                                          <th>{{trans('admin.Governrate')}}</th>
                                                                        <th>{{trans('admin.City')}}</th>
                                                                        <th>{{trans('admin.Place')}}</th>
                                                                         <th>{{trans('admin.Location')}} </th>    
                                                                         <th>{{trans('admin.Address_Details')}} </th>    
                        
           
                </tr>
              </thead>
              <tbody>
            
                <tr style="text-align: center;">
              
                    <td>{{$item->Name}}</td>
                    <td>{{$item->Email}}</td>
                    <td>{{$item->Phone}}</td>
                    <td>{{$item->OtherPhone}}</td>
                    <td>{{$item->Address_Name}}</td>
                    <td>{{$item->Special_MarkAdd}}</td>
                    <td>{{$item->StreetAdd}}</td>
                    <td>{{$item->BulidingAdd}}</td>
                    <td>{{$item->FloorAdd}}</td>
                    <td>{{$item->FlatAdd}}</td>
                    
                    <td>
                    
                @php  $govo=Governrate::where('GOV_ID',$item->Governrate)->first();  @endphp
                        
                    @if(!empty($govo))    
                    {{app()->getLocale() == 'ar' ?$govo->Arabic_Name :$govo->English_Name}}    
                        
                    @endif    
                        
                    </td>            
                    <td>
                    
                @php  $govoC=City::where('CIT_ID',$item->City)->first();  @endphp
                        
                    @if(!empty($govoC))    
                    {{app()->getLocale() == 'ar' ?$govoC->Arabic_Name :$govoC->English_Name}}    
                        
                    @endif    
                        
                    </td>                 
                    <td>
                    
                @php  $govoP=Place::where('PLACE_ID',$item->Place)->first();  @endphp
                        
                    @if(!empty($govoP))    
                    {{app()->getLocale() == 'ar' ?$govoP->Arabic_Name :$govoP->English_Name}}    
                        
                    @endif    
                        
                    </td>
            


                    
                    <td>{{$item->LocationAdd}}</td>
                    <td>{{$item->Address_DetailsAdd}}</td>

            
                </tr>
           
               
              </tbody>
            </table>
   
        
      </div>
                                <div class="modal-footer">
                 <button type="button" class="btn btn-secondary" data-dismiss="modal"> {{trans('admin.Close')}}</button>
   
                                </div>
                            </div>
                        </div>
                    </div>
     
     
           <!-- Modal Products -->
                    <div class="modal fade" id="Products{{$item->id}}" tabindex="-1" role="dialog" aria-hidden="true">
                        
                        <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4 class="modal-title">
                                    {{trans('admin.Products')}}
                                    </h4>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                    </button>
                                </div>
                            <div class="modal-body">
          
        @php  $Prods=ProductOrder::where('Order',$item->id)->get();  @endphp
                   <table id="mytable" class="table align-middle mb-0 bg-white">
              <thead class="bg-light">
                <tr class="header-row" style="text-align: center;">
  
                  <th>{{trans('admin.Image')}}</th>
                  <th>{{trans('admin.Name')}}</th>
                  <th>{{trans('admin.Qty')}}</th>
                  <th>{{trans('admin.Price')}}</th>
                  <th>{{trans('admin.Total')}}</th>
           
                </tr>
              </thead>
              <tbody>
                  @foreach($Prods as $ppro)
  @php  $OO=Products::where('id',$ppro->Product)->first();  @endphp                  
                <tr style="text-align: center;">
                  <td>

                <img src="{{URL::to($OO->Image)}}" style="width: 30%">      
                  </td>
                    
                    <td>
    {{app()->getLocale() == 'ar' ?$OO->P_Ar_Name :$OO->P_En_Name}}                
                    </td>
                    
                    <td>{{$ppro->Qty}}</td>
                    <td>{{$ppro->Price}}</td>
                    <td>{{$ppro->Total}}</td>
            
                </tr>
           
                  @endforeach
              </tbody>
            </table>
   
        
      </div>
                                <div class="modal-footer">
                 <button type="button" class="btn btn-secondary" data-dismiss="modal"> {{trans('admin.Close')}}</button>
             
                                </div>
                            </div>
                        </div>
                    </div>
     

     @endforeach
     


                </main>
@endsection

@push('js')
    <link rel="stylesheet" media="screen, print" href="{{asset('Admin/css/datagrid/datatables/datatables.bundle.css')}}">
    <link rel="stylesheet" media="screen, print" href="{{asset('Admin/css/formplugins/select2/select2.bundle.css')}}">
    <script src="{{asset('Admin/js/formplugins/select2/select2.bundle.js')}}"></script>
    <script src="{{asset('Admin/js/datagrid/datatables/datatables.export.js')}}"></script>
    <script src="{{asset('Admin/js/datagrid/datatables/datatables.bundle.js')}}"></script>
<script src="{{asset('Admin/js/formplugins/summernote/summernote.js')}}"></script>
<script>
   var autoSave = $('#autoSave');
   var interval;
   var timer = function()
   {
       interval = setInterval(function()
       {
           //start slide...
           if (autoSave.prop('checked'))
               saveToLocal();
   
           clearInterval(interval);
       }, 3000);
   };
   
   //save
   var saveToLocal = function()
   {
       localStorage.setItem('summernoteData', $('#saveToLocal').summernote("code"));
       console.log("saved");
   }
   
   //delete 
   var removeFromLocal = function()
   {
       localStorage.removeItem("summernoteData");
       $('#saveToLocal').summernote('reset');
   }
   
   $(document).ready(function()
   {
       //init default
       $('.js-summernote').summernote(
       {
           height: 200,
           tabsize: 2,
           placeholder: "Type here...",
           dialogsFade: true,
           toolbar: [
               ['style', ['style']],
               ['font', ['strikethrough', 'superscript', 'subscript']],
               ['font', ['bold', 'italic', 'underline', 'clear']],
               ['fontsize', ['fontsize']],
               ['fontname', ['fontname']],
               ['color', ['color']],
               ['para', ['ul', 'ol', 'paragraph']],
               ['height', ['height']]
               ['table', ['table']],
               ['insert', ['link', 'picture', 'video']],
               ['view', ['fullscreen', 'codeview', 'help']]
           ],
           callbacks:
           {
               //restore from localStorage
               onInit: function(e)
               {
                   $('.js-summernote').summernote("code", localStorage.getItem("summernoteData"));
               },
               onChange: function(contents, $editable)
               {
                   clearInterval(interval);
                   timer();
               }
           }
       });
   
       //load emojis
       $.ajax(
       {
           url: 'https://api.github.com/emojis',
           async: false
       }).then(function(data)
       {
           window.emojis = Object.keys(data);
           window.emojiUrls = data;
       });
   
       //init emoji example
       $(".js-hint2emoji").summernote(
       {
           height: 100,
           toolbar: false,
           placeholder: 'type starting with : and any alphabet',
           hint:
           {
               match: /:([\-+\w]+)$/,
               search: function(keyword, callback)
               {
                   callback($.grep(emojis, function(item)
                   {
                       return item.indexOf(keyword) === 0;
                   }));
               },
               template: function(item)
               {
                   var content = emojiUrls[item];
                   return '<img src="' + content + '" width="20" /> :' + item + ':';
               },
               content: function(item)
               {
                   var url = emojiUrls[item];
                   if (url)
                   {
                       return $('<img />').attr('src', url).css('width', 20)[0];
                   }
                   return '';
               }
           }
       });
   
       //init mentions example
       $(".js-hint2mention").summernote(
       {
           height: 100,
           toolbar: false,
           placeholder: "type starting with @",
           hint:
           {
               mentions: ['jayden', 'sam', 'alvin', 'david'],
               match: /\B@(\w*)$/,
               search: function(keyword, callback)
               {
                   callback($.grep(this.mentions, function(item)
                   {
                       return item.indexOf(keyword) == 0;
                   }));
               },
               content: function(item)
               {
                   return '@' + item;
               }
           }
       });
   
   });
   
</script>
<!-- Search Selecet -->
<script>
    
    $(document).ready(function () {
        $(function () {
            $(".select2").select2();

            $(".select2-placeholder-multiple").select2({
                placeholder: "Select State",
            });
            $(".js-hide-search").select2({
                minimumResultsForSearch: 1 / 0,
            });
            $(".js-max-length").select2({
                maximumSelectionLength: 2,
                placeholder: "Select maximum 2 items",
            });
            $(".select2-placeholder").select2({
                placeholder: "Select a state",
                allowClear: true,
            });

            $(".js-select2-icons").select2({
                minimumResultsForSearch: 1 / 0,
                templateResult: icon,
                templateSelection: icon,
                escapeMarkup: function (elm) {
                    return elm;
                },
            });

            function icon(elm) {
                elm.element;
                return elm.id ? "<i class='" + $(elm.element).data("icon") + " mr-2'></i>" + elm.text : elm.text;
            }

            $(".Emp").select2({
                placeholder: "select...",
                ajax: {
                    type: "GET",
                    dataType: "json",
                    url: "AllEmps",
                    processResults: function (data) {
                        return {
                            results: $.map(data, function (obj, index) {
                                return { id: index, text: obj };
                            }),
                        };

                        console.log(data);
                    },
  data: function (params) {  
   
            
                   var query = {
                            search: params.term,
                        };
                                
            
              $.ajax({
                              url: 'AllEmpsJ/'+params.term,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                          $('.Emp').empty();  
                                  $.each(data, function(key, value){
   
                         $('.Emp').append('<option value="'+ key +'">' + value + '</option>');
                   
                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
            
            
            
        }
                },
            });

            $(".Emp").on("select2:select", function (e) {
                console.log("select done", e.params.data);
            });
            
                    $(".Ship").select2({
                placeholder: "select...",
                ajax: {
                    type: "GET",
                    dataType: "json",
                    url: "AllShips",
                    processResults: function (data) {
                        return {
                            results: $.map(data, function (obj, index) {
                                return { id: index, text: obj };
                            }),
                        };

                        console.log(data);
                    },
                    data: function (params) {  
   
            
                   var query = {
                            search: params.term,
                        };
                                
            
              $.ajax({
                              url: 'AllShipsJ/'+params.term,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                          $('.Ship').empty();  
                                  $.each(data, function(key, value){
   
                         $('.Ship').append('<option value="'+ key +'">' + value + '</option>');
                   
                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
            
            
            
        }
                },
            });

            $(".Ship").on("select2:select", function (e) {
                console.log("select done", e.params.data);
            });
            
                    $(".Vend").select2({
                placeholder: "select...",
                ajax: {
                    type: "GET",
                    dataType: "json",
                    url: "AllVend",
                    processResults: function (data) {
                        return {
                            results: $.map(data, function (obj, index) {
                                return { id: index, text: obj };
                            }),
                        };

                        console.log(data);
                    },
         data: function (params) {  
   
            
                   var query = {
                            search: params.term,
                        };
                                
            
              $.ajax({
                              url: 'AllVendJ/'+params.term,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                          $('.Vend').empty();  
                                  $.each(data, function(key, value){
   
                         $('.Vend').append('<option value="'+ key +'">' + value + '</option>');
                   
                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
            
            
            
        }
                },
            });

            $(".Vend").on("select2:select", function (e) {
                console.log("select done", e.params.data);
            });
            
            
                    $(".Cli").select2({
                placeholder: "select...",
                ajax: {
                    type: "GET",
                    dataType: "json",
                    url: "AllCli",
                    processResults: function (data) {
                        return {
                            results: $.map(data, function (obj, index) {
                                return { id: index, text: obj };
                            }),
                        };

                        console.log(data);
                    },
                   data: function (params) {  
   
            
                   var query = {
                            search: params.term,
                        };
                                
            
              $.ajax({
                              url: 'AllCliJ/'+params.term,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                          $('.Cli').empty();  
                                  $.each(data, function(key, value){
   
                         $('.Cli').append('<option value="'+ key +'">' + value + '</option>');
                   
                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
            
            
            
        }
                },
            });

            $(".Cli").on("select2:select", function (e) {
                console.log("select done", e.params.data);
            });
            
                       $(".Exe").select2({
                placeholder: "select...",
                ajax: {
                    type: "GET",
                    dataType: "json",
                    url: "AllEmps",
                    processResults: function (data) {
                        return {
                            results: $.map(data, function (obj, index) {
                                return { id: index, text: obj };
                            }),
                        };

                        console.log(data);
                    },
  data: function (params) {  
   
            
                   var query = {
                            search: params.term,
                        };
                                
            
              $.ajax({
                              url: 'AllEmpsJ/'+params.term,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                          $('.Exe').empty();  
                                  $.each(data, function(key, value){
   
                         $('.Exe').append('<option value="'+ key +'">' + value + '</option>');
                   
                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
            
            
            
        }
                },
            });

            $(".Exe").on("select2:select", function (e) {
                console.log("select done", e.params.data);
            });
             
            
            
            

        });
    });
</script>

<script>
    
        //_fnFeatureHtmlLength();
        $(document).ready(function () {
            // Setup - add a text input to each footer cell
            $('#dt-basic-example thead tr').clone(true).appendTo('#dt-basic-example thead');
            $('#dt-basic-example thead tr:eq(1) th').each(function (i) {
                var title = $(this).text();
                $(this).html('<input type="text" class="form-control form-control-sm" placeholder="Search ' + title + '" />');

                $('input', this).on('keyup change', function () {
                    if (table.column(i).search() !== this.value) {
                        table
                            .column(i)
                            .search(this.value)
                            .draw();
                    }
                });
            });
            var table = $('#dt-basic-example').DataTable(
                {
                    // responsive: true,
                    orderCellsTop: true,
                    fixedHeader: true,
                    lengthChange: true,

                    dom: "<'row mb-3'<'col-sm-12 col-md-3 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-9 d-flex align-items-center justify-content-end'B>>" +
                        "<'row'<'col-sm-12'tr>>" +
                        "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",

                    buttons: [
                        {
                            extend: 'pageLength',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'colvis',
                            text: 'Column Visibility',
                            titleAttr: 'Col visibility',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'pdfHtml5',
                            text: 'PDF',
                            titleAttr: 'Generate PDF',
                            className: 'btn-outline-danger btn-sm mr-1'
                        },
                        {
                            extend: 'excelHtml5',
                            text: 'Excel',
                            titleAttr: 'Generate Excel',
                            className: 'btn-outline-success btn-sm mr-1'
                        },
                        {
                            extend: 'csvHtml5',
                            text: 'CSV',
                            titleAttr: 'Generate CSV',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'copyHtml5',
                            text: 'Copy',
                            titleAttr: 'Copy to clipboard',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'print',
                            text: 'Print',
                            titleAttr: 'Print Table',
                            className: 'btn-outline-primary btn-sm'
                        }
                    ],
                });
            $('.js-thead-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example thead').removeClassPrefix('bg-').addClass(theadColor);
            });

            $('.js-tbody-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example').removeClassPrefix('bg-').addClass(theadColor);
            });

        });

    </script>
@endpush

    