<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Comments extends Model
{
    use HasFactory;
     protected $table = 'comments';
     protected $fillable = [
   
     
                'Comment',
                'Product',
                'Status',   
                'User',
              

    ];
           public function User()
    {
        return $this->belongsTo(User::class,'User');
    }
}
