@extends('admin.index')
@section('content')

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">{{trans('admin.Design_System')}}</h4>
            </div>
            <div class="card-body">
                <form action="{{url('UpdateDesignSystem')}}" method="post" enctype="multipart/form-data">
                    @csrf
                    
                    <!-- Font Configuration -->
                    <div class="row">
                        <div class="col-md-12">
                            <h5>{{trans('admin.Font_Configuration')}}</h5>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>{{trans('admin.Font_Type')}}</label>
                                <select name="Font_Type" class="form-control">
                                    <option value="1" {{$design->Font_Type == 1 ? 'selected' : ''}}>Cairo</option>
                                    <option value="2" {{$design->Font_Type == 2 ? 'selected' : ''}}>Roboto</option>
                                    <option value="3" {{$design->Font_Type == 3 ? 'selected' : ''}}>Open Sans</option>
                                    <option value="4" {{$design->Font_Type == 4 ? 'selected' : ''}}>Poppins</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Body Styling -->
                    <div class="row">
                        <div class="col-md-12">
                            <h5>{{trans('admin.Body_Styling')}}</h5>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>{{trans('admin.Background_Type')}}</label>
                                <select name="Body_BG_Type" class="form-control" id="Body_BG_Type">
                                    <option value="1" {{$design->Body_BG_Type == 1 ? 'selected' : ''}}>{{trans('admin.Color')}}</option>
                                    <option value="2" {{$design->Body_BG_Type == 2 ? 'selected' : ''}}>{{trans('admin.Image')}}</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6" id="BodyColor">
                            <div class="form-group">
                                <label>{{trans('admin.Background_Color')}}</label>
                                <input type="color" name="Body_BG_Color" class="form-control" value="{{$design->Body_BG_Color}}">
                            </div>
                        </div>
                        <div class="col-md-6" id="BodyImage" style="display: none;">
                            <div class="form-group">
                                <label>{{trans('admin.Background_Image')}}</label>
                                <input type="file" name="Body_BG_Image" class="form-control">
                                @if($design->Body_BG_Image)
                                    <img src="{{URL::to($design->Body_BG_Image)}}" style="width: 100px; height: 60px; margin-top: 10px;">
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Header Styling -->
                    <div class="row">
                        <div class="col-md-12">
                            <h5>{{trans('admin.Header_Styling')}}</h5>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>{{trans('admin.Header_Background_Color')}}</label>
                                <input type="color" name="Header_BG_Color" class="form-control" value="{{$design->Header_BG_Color}}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>{{trans('admin.Header_Text_Color')}}</label>
                                <input type="color" name="Header_Txt_Color" class="form-control" value="{{$design->Header_Txt_Color}}">
                            </div>
                        </div>
                    </div>

                    <!-- Navigation Styling -->
                    <div class="row">
                        <div class="col-md-12">
                            <h5>{{trans('admin.Navigation_Styling')}}</h5>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>{{trans('admin.Navbar_Background_Color')}}</label>
                                <input type="color" name="Navbar_BG_Color" class="form-control" value="{{$design->Navbar_BG_Color}}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>{{trans('admin.Navbar_Text_Color')}}</label>
                                <input type="color" name="Navbar_Txt_Color" class="form-control" value="{{$design->Navbar_Txt_Color}}">
                            </div>
                        </div>
                    </div>

                    <!-- Button Styling -->
                    <div class="row">
                        <div class="col-md-12">
                            <h5>{{trans('admin.Button_Styling')}}</h5>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>{{trans('admin.Primary_Button_Background')}}</label>
                                <input type="color" name="Primary_Button_BG_Color" class="form-control" value="{{$design->Primary_Button_BG_Color}}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>{{trans('admin.Primary_Button_Text')}}</label>
                                <input type="color" name="Primary_Button_Txt_Color" class="form-control" value="{{$design->Primary_Button_Txt_Color}}">
                            </div>
                        </div>
                    </div>

                    <!-- Footer Styling -->
                    <div class="row">
                        <div class="col-md-12">
                            <h5>{{trans('admin.Footer_Styling')}}</h5>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>{{trans('admin.Footer_Background_Color')}}</label>
                                <input type="color" name="Footer_BG_Color" class="form-control" value="{{$design->Footer_BG_Color}}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>{{trans('admin.Footer_Text_Color')}}</label>
                                <input type="color" name="Footer_Txt_Color" class="form-control" value="{{$design->Footer_Txt_Color}}">
                            </div>
                        </div>
                    </div>

                    <!-- Feature Toggles -->
                    <div class="row">
                        <div class="col-md-12">
                            <h5>{{trans('admin.Feature_Toggles')}}</h5>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" name="Show_Preloader" {{$design->Show_Preloader ? 'checked' : ''}}>
                                    {{trans('admin.Show_Preloader')}}
                                </label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" name="Show_Breadcrumbs" {{$design->Show_Breadcrumbs ? 'checked' : ''}}>
                                    {{trans('admin.Show_Breadcrumbs')}}
                                </label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" name="Show_Social_Links" {{$design->Show_Social_Links ? 'checked' : ''}}>
                                    {{trans('admin.Show_Social_Links')}}
                                </label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" name="Enable_Animations" {{$design->Enable_Animations ? 'checked' : ''}}>
                                    {{trans('admin.Enable_Animations')}}
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-primary">{{trans('admin.Update')}}</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    var x = $('#Body_BG_Type').val();
    
    if (parseFloat(x) == 1) {
        document.getElementById('BodyColor').style.display = "block";
        document.getElementById('BodyImage').style.display = "none";
    } else {
        document.getElementById('BodyColor').style.display = "none";
        document.getElementById('BodyImage').style.display = "block";
    }
    
    $('#Body_BG_Type').change(function() {
        var x = $(this).val();
        
        if (parseFloat(x) == 1) {
            document.getElementById('BodyColor').style.display = "block";
            document.getElementById('BodyImage').style.display = "none";
        } else {
            document.getElementById('BodyColor').style.display = "none";
            document.getElementById('BodyImage').style.display = "block";
        }
    });
});
</script>

@endsection
