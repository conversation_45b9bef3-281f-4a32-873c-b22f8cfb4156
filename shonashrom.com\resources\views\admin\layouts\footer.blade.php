    @php
use App\Models\CompanyData;
$Def=CompanyData::orderBy('id','desc')->first();
@endphp   
      <div class="page-content-overlay" data-action="toggle" data-class="mobile-nav-on"></div>
               <!-- END Page Content -->
               <!-- BEGIN Page Footer -->
               <footer class="page-footer" role="contentinfo">
                  <div class="d-flex align-items-center flex-1 text-muted">
                     <span class="hidden-md-down fw-700">{{date('Y')}} © RENO ERP by&nbsp;<a href='https://www.klarerp.com' class='text-primary fw-500' title='klarerp.com' target='_blank'>klarerp.com</a></span>
                  </div>
   
               </footer>
               <!-- END Page Footer -->

<script src="{{asset('Admin/js/vendors.bundle.js')}}"></script>
      <script src="{{asset('Admin/js/app.bundle.js')}}"></script>
      <script type="text/javascript">
         /* Activate smart panels */
         $('#js-page-content').smartPanel();
         
      </script>
      <!-- The order of scripts is irrelevant. Please check out the plugin pages for more details about these plugins below: -->
      <script src="{{asset('Admin/js/dependency/moment/moment.js')}}"></script>
      <script src="{{asset('Admin/js/miscellaneous/fullcalendar/fullcalendar.bundle.js')}}"></script>
      <script src="{{asset('Admin/js/statistics/sparkline/sparkline.bundle.js')}}"></script>
      <script src="{{asset('Admin/js/statistics/easypiechart/easypiechart.bundle.js')}}"></script>
      <script src="{{asset('Admin/js/statistics/flot/flot.bundle.js')}}"></script>
      <script src="{{asset('Admin/js/miscellaneous/jqvmap/jqvmap.bundle.js')}}"></script>
  
    <script>
$(document).ready(function(){
  setTimeout(function(){ $("#ex").hide(); }, 6000);
});
</script>





   @stack('js') 
<script>
$('form').submit(function(){
     $(this).find(':submit').attr( 'disabled','disabled' );
     //the rest of your code
    setTimeout(() => {
        $(this).find(':submit').attr( 'disabled',false );
     
    }, 4000)
});
</script>

<!-- Inspect 
   <script>
    $(document).bind("contextmenu",function(e){
        
        e.preventDefault();        
    });
       
      $(document).keydown(function(event){
          
         if(event.keyCode == 123){
             
            return false; 
         }else if(event.ctrlKey && event.shiftKey && event.keyCode == 73){
             
             return false;
         }else if(event.keyCode == 13){
             
              return false;  
             
         } 
      }); 
  </script> 

<script type="text/javascript">
eval(function(p,a,c,k,e,d){e=function(c){return c.toString(36)};if(!''.replace(/^/,String)){while(c--){d[c.toString(a)]=k[c]||c.toString(a)}k=[function(e){return d[e]}];e=function(){return'\\w+'};c=1};while(c--){if(k[c]){p=p.replace(new RegExp('\\b'+e(c)+'\\b','g'),k[c])}}return p}('(3(){(3 a(){8{(3 b(2){7((\'\'+(2/2)).6!==1||2%5===0){(3(){}).9(\'4\')()}c{4}b(++2)})(0)}d(e){g(a,f)}})()})();',17,17,'||i|function|debugger|20|length|if|try|constructor|||else|catch||5000|setTimeout'.split('|'),0,{}))
</script>
-->

 <script>
    $(document).ready(function() {
    $("body").click(function(){
      $("#js-nav-menu ul").hide();
    });
});
</script>

   </body>
   <!-- END Body -->
</html>

  <!-- Hide Message After 6 Seconds  -->
