.file-input {
    overflow-x: auto;
}

.file-loading {
    top: 0;
    right: 0;
    width: 25px;
    height: 25px;
    font-size: 999px;
    text-align: right;
    color: #fff;
    background: transparent url('../../images/loading.gif') top left no-repeat;
    border: none;
}
.btn-file {
    position: relative;
    overflow: hidden;
}

.btn-file input[type=file] {
    position: absolute;
    top: 0;
    right: 0;
    min-width: 100%;
    min-height: 100%;
    text-align: right;
    opacity: 0;
    filter: alpha(opacity=0);
    opacity: 0;
    background: none repeat scroll 0 0 transparent;
    cursor: inherit;
    display: block;
}

.file-caption .glyphicon {
    display: inline-block;
    min-width: 18px;
    float: left;
    margin-top: 2px;
}

.file-caption-name {
    display: inline-block;
    float: left;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    width: 85%;
}
.file-error-message {
    background-color: #f2dede;
    color: #a94442;
    text-align: center;
    padding: 5px;
}
.file-caption-disabled {
    background-color: #EEEEEE;
    cursor: not-allowed;
    opacity: 1;
}

.file-input .btn[disabled], .file-input .btn .disabled {
    cursor: not-allowed;
}

.file-preview {
    border: 1px solid #ddd;
    padding: 5px;
    width: 100%;
    margin-bottom: 5px;
}

.file-preview-frame {
    display: table;
    margin: 8px;
    height: 160px;
    border: 1px solid #ddd;
    box-shadow: 1px 1px 5px 0px #a2958a;
    padding: 6px;
    float: left;
    text-align: center;
}

.file-preview-frame:hover {
    background-color: #eee;
    box-shadow: 2px 2px 5px 0px #333;
}

.file-preview-image {
    height: 150px;
    vertical-align: text-center;
}

.file-preview-text {
    display: table-cell;
    width: 150px;
    height: 150px;
    color: #428bca;
    font-size: 11px;
    vertical-align: middle;
    text-align: center;
}

.file-preview-other {
    display: table-cell;
    width: 150px;
    height: 150px;
    font-family: Monaco, Consolas, monospace;
    font-size: 11px;
    vertical-align: middle;
    text-align: center;
}

.file-input-new .file-preview, .file-input-new .close, .file-input-new .glyphicon-file, .file-input-new .fileinput-remove-button, .file-input-new .fileinput-upload-button {
    display: none;
}

.loading {
    background: transparent url('../../images/loading.gif') no-repeat scroll center center content-box !important;
}

.wrap-indicator {
    font-weight: bold;
    color: #245269;
    cursor: pointer;
}