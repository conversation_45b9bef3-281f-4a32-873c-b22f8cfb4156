<?php $__env->startSection('content'); ?>

<div class="page-wrapper">
    <div class="page-content">
        <!--breadcrumb-->
        <div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
            <div class="breadcrumb-title pe-3"><?php echo e(trans('admin.Website')); ?></div>
            <div class="ps-3">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0 p-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(url('Admin')); ?>"><i class="bx bx-home-alt"></i></a></li>
                        <li class="breadcrumb-item active" aria-current="page"><?php echo e(trans('admin.Features')); ?></li>
                    </ol>
                </nav>
            </div>
        </div>
        <!--end breadcrumb-->

        <div class="row">
            <div class="col-xl-9 mx-auto">
                <div class="card border-top border-0 border-4 border-primary">
                    <div class="card-body p-5">
                        <div class="card-title d-flex align-items-center">
                            <div><i class="bx bxs-star me-1 font-22 text-primary"></i></div>
                            <h5 class="mb-0 text-primary"><?php echo e(trans('admin.Features')); ?></h5>
                        </div>
                        <hr>

                        <!-- Add New Feature Form -->
                        <form method="POST" action="<?php echo e(url('AddFeature')); ?>" enctype="multipart/form-data">
                            <?php echo csrf_field(); ?>
                            <div class="row mb-3">
                                <div class="col-sm-6">
                                    <label class="form-label"><?php echo e(trans('admin.Arabic_Title')); ?></label>
                                    <input type="text" class="form-control" name="Arabic_Title" required>
                                </div>
                                <div class="col-sm-6">
                                    <label class="form-label"><?php echo e(trans('admin.English_Title')); ?></label>
                                    <input type="text" class="form-control" name="English_Title" required>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-6">
                                    <label class="form-label"><?php echo e(trans('admin.Arabic_Desc')); ?></label>
                                    <textarea class="form-control" name="Arabic_Desc" rows="3"></textarea>
                                </div>
                                <div class="col-sm-6">
                                    <label class="form-label"><?php echo e(trans('admin.English_Desc')); ?></label>
                                    <textarea class="form-control" name="English_Desc" rows="3"></textarea>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <label class="form-label"><?php echo e(trans('admin.Icon')); ?></label>
                                    <input type="file" class="form-control" name="Icon" accept="image/*">
                                </div>
                                <div class="col-sm-4">
                                    <label class="form-label"><?php echo e(trans('admin.Status')); ?></label>
                                    <select class="form-control" name="Status" required>
                                        <option value="1"><?php echo e(trans('admin.Active')); ?></option>
                                        <option value="0"><?php echo e(trans('admin.Inactive')); ?></option>
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <label class="form-label"><?php echo e(trans('admin.Sort_Order')); ?></label>
                                    <input type="number" class="form-control" name="Sort_Order" value="0">
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-sm-12">
                                    <button type="submit" class="btn btn-primary px-5"><?php echo e(trans('admin.Add')); ?></button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features List -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered">
                                <thead>
                                    <tr>
                                        <th><?php echo e(trans('admin.Icon')); ?></th>
                                        <th><?php echo e(trans('admin.Arabic_Title')); ?></th>
                                        <th><?php echo e(trans('admin.English_Title')); ?></th>
                                        <th><?php echo e(trans('admin.Status')); ?></th>
                                        <th><?php echo e(trans('admin.Sort_Order')); ?></th>
                                        <th><?php echo e(trans('admin.Actions')); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <?php if($item->Icon): ?>
                                                <img src="<?php echo e(URL::to($item->Icon)); ?>" width="50" height="50" class="rounded">
                                            <?php else: ?>
                                                <span class="badge bg-secondary"><?php echo e(trans('admin.No_Image')); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e($item->Arabic_Title); ?></td>
                                        <td><?php echo e($item->English_Title); ?></td>
                                        <td>
                                            <?php if($item->Status == 1): ?>
                                                <span class="badge bg-success"><?php echo e(trans('admin.Active')); ?></span>
                                            <?php else: ?>
                                                <span class="badge bg-danger"><?php echo e(trans('admin.Inactive')); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e($item->Sort_Order); ?></td>
                                        <td>
                                            <a href="<?php echo e(url('EditFeature/'.$item->id)); ?>" class="btn btn-primary btn-sm"><?php echo e(trans('admin.Edit')); ?></a>
                                            <a href="<?php echo e(url('DeleteFeature/'.$item->id)); ?>" class="btn btn-danger btn-sm" onclick="return confirm('<?php echo e(trans('admin.Are_You_Sure')); ?>')"><?php echo e(trans('admin.Delete')); ?></a>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\ost_erp\resources\views/admin/Website/Features.blade.php ENDPATH**/ ?>