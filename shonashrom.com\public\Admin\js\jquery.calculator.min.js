﻿/* http://keith-wood.name/calculator.html
   Calculator field entry extension for jQuery v2.0.1.
   Written by <PERSON> (kbwood{at}iinet.com.au) October 2008.
   Licensed under the MIT (https://github.com/jquery/jquery/blob/master/MIT-LICENSE.txt) licence. 
   Please attribute the author if you use it. */
(function($){var m='calculator';var n=['  BSCECA','_1_2_3_+@X','_4_5_6_-@U','_7_8_9_*@E','_0_._=_/'];var o='d';var p='b';var q='u';var r='c';var s='s';$.JQPlugin.createPlugin({name:m,defaultOptions:{showOn:'focus',buttonImage:'',buttonImageOnly:false,isOperator:null,showAnim:'show',showOptions:{},duration:'normal',appendText:'',useThemeRoller:false,calculatorClass:'',showFormula:false,prompt:'',layout:n,value:0,base:10,precision:10,memoryAsCookie:false,cookieName:'calculatorMemory',cookieExpires:24*60*60,cookiePath:'',useDegrees:false,constrainInput:true,onOpen:null,onButton:null,onClose:null},regionalOptions:{'':{decimalChar:'.',buttonText:'...',buttonStatus:'Open the calculator',closeText:'Close',closeStatus:'Close the calculator',useText:'Use',useStatus:'Use the current value',eraseText:'Erase',eraseStatus:'Erase the value from the field',backspaceText:'BS',backspaceStatus:'Erase the last digit',clearErrorText:'CE',clearErrorStatus:'Erase the last number',clearText:'CA',clearStatus:'Reset the calculator',memClearText:'MC',memClearStatus:'Clear the memory',memRecallText:'MR',memRecallStatus:'Recall the value from memory',memStoreText:'MS',memStoreStatus:'Store the value in memory',memAddText:'M+',memAddStatus:'Add to memory',memSubtractText:'M-',memSubtractStatus:'Subtract from memory',base2Text:'Bin',base2Status:'Switch to binary',base8Text:'Oct',base8Status:'Switch to octal',base10Text:'Dec',base10Status:'Switch to decimal',base16Text:'Hex',base16Status:'Switch to hexadecimal',degreesText:'Deg',degreesStatus:'Switch to degrees',radiansText:'Rad',radiansStatus:'Switch to radians',isRTL:false}},_getters:['isDisabled'],_curInst:null,_disabledFields:[],_showingCalculator:false,_showingKeystrokes:false,_keyDefs:{},digit:o,binary:p,unary:q,control:r,space:s,_mainDivClass:m+'-popup',_inlineClass:m+'-inline',_appendClass:m+'-append',_triggerClass:m+'-trigger',_disableClass:m+'-disabled',_inlineEntryClass:m+'-keyentry',_promptClass:m+'-prompt',_formulaClass:m+'-formula',_resultClass:m+'-result',_focussedClass:m+'-focussed',_keystrokeClass:m+'-keystroke',_rtlClass:m+'-rtl',_rowClass:m+'-row',_ctrlClass:m+'-ctrl',_baseActiveClass:m+'-base-active',_angleActiveClass:m+'-angle-active',_digitClass:m+'-digit',_operatorClass:m+'-oper',_memEmptyClass:m+'-mem-empty',_keyNameClass:m+'-keyname',_keyDownClass:m+'-key-down',_keyStrokeClass:m+'-keystroke',standardLayout:n,scientificLayout:['@X@U@E  BSCECA','DGRD    _ MC_ _7_8_9_+','SNASSRLG_ MR_ _4_5_6_-','CSACSQLN_ MS_ _1_2_3_*','TNATXYEX_ M+_ _0_.+-_/','PIRN1X  _ M-_   _%_='],addKeyDef:function(a,b,c,d,e,f,g,h){this._keyDefs[a]=[b,(typeof c==='boolean'?(c?this.binary:this.unary):c),d,e,f,g,h];if(f){this[f]=a}if(g){if(typeof g==='number'){this._keyCodes[g]=a}else{this._keyChars[g]=a}}return this},_init:function(){this.mainDiv=$('<div class="'+this._mainDivClass+'" style="display: none;"></div>').on('click.'+m,this._focusEntry);this._keyCodes={};this._keyChars={};this._super()},_instSettings:function(a,b){var c=a[0].nodeName.toLowerCase()!=='input';var d=(!c?a:$('<input type="text" class="'+this._inlineEntryClass+'"/>'));return{_input:d,_inline:c,memory:0,_mainDiv:(c?$('<div class="'+this._inlineClass+'"></div>'):this.mainDiv)}},_postAttach:function(a,b){if(b.options.memoryAsCookie){var c=this._getMemoryCookie(b);if(c&&!isNaN(c)){b.memory=c}}if(!b._inline&&a.is(':disabled')){this.disable(a[0])}},_optionsChanged:function(d,e,f){$.extend(e.options,f);if(this._curInst===e){this.hide()}d.empty().off('.'+e.name).siblings('.'+this._appendClass).remove().end().siblings('.'+this._triggerClass).remove().end().prev('.'+this._inlineEntryClass).remove();if(e.options.appendText){d[e.options.isRTL?'before':'after']('<span class="'+this._appendClass+'">'+e.options.appendText+'</span>')}if(!e._inline){if(e.options.showOn==='focus'||e.options.showOn==='both'){d.on('focus.'+e.name,this.show)}if(e.options.showOn==='button'||e.options.showOn==='both'||e.options.showOn==='opbutton'){var g=$(e.options.buttonImageOnly?$('<img/>').attr({src:e.options.buttonImage,alt:e.options.buttonStatus,title:e.options.buttonStatus}):$('<button type="button" title="'+e.options.buttonStatus+'"></button>').html(e.options.buttonImage===''?e.options.buttonText:$('<img/>').attr({src:e.options.buttonImage})));d[e.options.isRTL?'before':'after'](g);g.addClass(this._triggerClass).on('click.'+e.name,function(){if(t._showingCalculator&&t._lastInput===d[0]){t.hide()}else{t.show(d[0])}return false})}}e._input.on('keydown.'+e.name,this._doKeyDown).on('keyup.'+e.name,this._doKeyUp).on('keypress.'+e.name,this._doKeyPress);if(e._inline){d.append(e._input).append(e._mainDiv).on('click.'+e.name,function(){e._input.focus()});this._reset(e,'0');this._setValue(e);this._updateCalculator(e);e._mainDiv.on('keydown.'+e.name,this._doKeyDown).on('keyup.'+e.name,this._doKeyUp).on('keypress.'+e.name,this._doKeyPress);e._input.on('focus.'+e.name,function(){if(!t.isDisabled(d[0])){e._focussed=true;$('.'+t._resultClass,e._mainDiv).addClass(t._focussedClass)}}).on('blur.'+e.name,function(){e._focussed=false;$('.'+t._resultClass,e._mainDiv).removeClass(t._focussedClass)})}d.addClass(this._getMarker()).on('setData.'+e.name,function(a,b,c){e.options[b]=c}).on('getData.'+e.name,function(a,b){return e.options[b]}).data(e.name,e);e._input.data(e.name,e);if(e._inline){this._setValue(e)}this._updateCalculator(e)},_preDestroy:function(a,b){b._input.off('.'+b.name).removeData(b.name);a.empty().off('.'+b.name).siblings('.'+this._appendClass).remove().end().siblings('.'+this._triggerClass).remove().end().prev('.'+this._inlineEntryClass).remove()},enable:function(b){b=$(b);if(!b.hasClass(this._getMarker())){return}var c=b[0].nodeName.toLowerCase();if(c==='input'){b.prop('disabled',false).siblings('button.'+this._triggerClass).prop('disabled',false).end().siblings('img.'+this._triggerClass).css({opacity:'1.0',cursor:''})}else if(c==='div'||c==='span'){b.find('.'+this._inlineEntryClass+',button').prop('disabled',false).end().children('.'+this._disableClass).remove()}this._disabledFields=$.map(this._disabledFields,function(a){return(a===b[0]?null:a)})},disable:function(b){b=$(b);if(!b.hasClass(this._getMarker())){return}var c=b[0].nodeName.toLowerCase();if(c==='input'){b.prop('disabled',true).siblings('button.'+this._triggerClass).prop('disabled',true).end().siblings('img.'+this._triggerClass).css({opacity:'0.5',cursor:'default'})}else if(c==='div'||c==='span'){var d=b.children('.'+this._inlineClass);var e=d.offset();var f={left:0,top:0};d.parents().each(function(){if($(this).css('position')==='relative'){f=$(this).offset();return false}});b.find('.'+this._inlineEntryClass+',button').prop('disabled',true);if(b.find('.'+this._disableClass).length===0){b.prepend('<div class="'+this._disableClass+'" style="width: '+d.outerWidth()+'px; height: '+d.outerHeight()+'px; left: '+(e.left-f.left)+'px; top: '+(e.top-f.top)+'px;"></div>')}}this._disabledFields=$.map(this._disabledFields,function(a){return(a===b[0]?null:a)});this._disabledFields[this._disabledFields.length]=b[0]},isDisabled:function(a){return(a&&$.inArray(a,this._disabledFields)>-1)},show:function(a){a=a.target||a;if(t.isDisabled(a)||t._lastInput===a){return}var b=t._getInst(a);t.hide(null,'');t._lastInput=a;t._pos=t._findPos(a);t._pos[1]+=a.offsetHeight;var c=false;$(a).parents().each(function(){c|=$(this).css('position')==='fixed';return!c});var d={left:t._pos[0],top:t._pos[1]};t._pos=null;b._mainDiv.css({position:'absolute',display:'block',top:'-1000px',width:'auto'});if($.isFunction(b.options.onOpen)){b.options.onOpen.apply((b._input?b._input[0]:null),[(b._inline?b.curValue:b._input.val()),b])}t._reset(b,b._input.val());t._updateCalculator(b);d=t._checkOffset(b,d,c);b._mainDiv.css({position:(c?'fixed':'absolute'),display:'none',left:d.left+'px',top:d.top+'px'});var e=b.options.duration;e=(e=='normal'&&$.ui&&parseInt($.ui.version.substring(2))>=8?'_default':e);var f=function(){t._showingCalculator=true};if($.effects&&($.effects[b.options.showAnim]||($.effects.effect&&$.effects.effect[b.options.showAnim]))){var g=b._mainDiv.data();for(var h in g){if(h.match(/^ec\.storage\./)){g[h]=b._mainDiv.css(h.replace(/ec\.storage\./,''))}}b._mainDiv.data(g).show(b.options.showAnim,b.options.showOptions,e,f)}else{b._mainDiv[b.options.showAnim||'show']((b.options.showAnim?e:null),f)}if(!b.options.showAnim){f()}if(b._input[0].type!=='hidden'){b._input[0].focus()}t._curInst=b},_reset:function(a,b){b=''+(b||0);b=(a.options.decimalChar!=='.'?b.replace(new RegExp(a.options.decimalChar),'.'):b);a.curValue=(a.options.base===10?parseFloat(b):parseInt(b,a.options.base))||0;a.dispValue=this._setDisplay(a);a.prevValue=a._savedValue=0;a._pendingOp=a._savedOp=this._noOp;a._formula='';a._newValue=true},_getMemoryCookie:function(a){var b=new RegExp('^.*'+a.options.cookieName+'=([^;]*).*$');return parseFloat(document.cookie.replace(b,'$1'))},_setMemoryCookie:function(a){if(!a.options.memoryAsCookie){return}var b=a.options.cookieExpires;if(typeof b==='number'){var c=new Date();c.setTime(c.getTime()+b*1000);b=c.toUTCString()}else if(b.constructor===Date){b=c.toUTCString()}else{b=''}document.cookie=a.options.cookieName+'='+a.memory+'; expires='+b+'; path='+a.options.cookiePath},_setValue:function(a){a.curValue=a.options.value||0;a.dispValue=this._setDisplay(a)},_updateCalculator:function(a){var b=this._getBorders(a._mainDiv);a._mainDiv.html(this._generateHTML(a)).removeClass().addClass(a.options.calculatorClass+(a.options.useThemeRoller?' ui-widget ui-widget-content':'')+(a.options.isRTL?' '+t._rtlClass:'')+' '+(a._inline?this._inlineClass:this._mainDivClass));if(this.isDisabled(a.elem[0])){this.disable(a.elem[0])}if(this._curInst===a){a._input.focus()}},_getBorders:function(b){var c=function(a){return{thin:1,medium:3,thick:5}[a]||a};return[parseFloat(c(b.css('border-left-width'))),parseFloat(c(b.css('border-top-width')))]},_checkOffset:function(a,b,c){var d=a._input?this._findPos(a._input[0]):null;var e=window.innerWidth||document.documentElement.clientWidth;var f=window.innerHeight||document.documentElement.clientHeight;var g=document.documentElement.scrollLeft||document.body.scrollLeft;var h=document.documentElement.scrollTop||document.body.scrollTop;if(a.options.isRTL||(b.left+a._mainDiv.outerWidth()-g)>e){b.left=Math.max((c?0:g),d[0]+(a._input?a._input.outerWidth():0)-(c?g:0)-a._mainDiv.outerWidth())}else{b.left=Math.max((c?0:g),b.left-(c?g:0))}if((b.top+a._mainDiv.outerHeight()-h)>f){b.top=Math.max((c?0:h),d[1]-(c?h:0)-a._mainDiv.outerHeight())}else{b.top=Math.max((c?0:h),b.top-(c?h:0))}return b},_findPos:function(a){while(a&&(a.type==='hidden'||a.nodeType!==1)){a=a.nextSibling}var b=$(a).offset();return[b.left,b.top]},hide:function(a,b){var c=this._curInst;if(!c||(a&&c!==t._getInst(a))){return}if(this._showingCalculator){b=(b!=null?b:c.options.duration);b=(b==='normal'&&$.ui&&parseInt($.ui.version.substring(2))>=8?'_default':b);if($.effects&&($.effects[c.options.showAnim]||($.effects.effect&&$.effects.effect[c.options.showAnim]))){c._mainDiv.hide(c.options.showAnim,c.options.showOptions,b)}else{c._mainDiv[(c.options.showAnim==='slideDown'?'slideUp':(c.options.showAnim==='fadeIn'?'fadeOut':'hide'))](c.options.showAnim?b:null)}}if($.isFunction(c.options.onClose)){c.options.onClose.apply((c._input?c._input[0]:null),[(c._inline?c.curValue:c._input.val()),c])}if(this._showingCalculator){this._showingCalculator=false;this._lastInput=null}this._curInst=null},_checkExternalClick:function(a){if(!t._curInst){return}var b=$(a.target);if(!b.parents().andSelf().hasClass(t._mainDivClass)&&!b.hasClass(t._getMarker())&&!b.parents().andSelf().hasClass(t._triggerClass)&&t._showingCalculator){t.hide()}},_focusEntry:function(){if(t._curInst&&t._curInst._input){t._curInst._input.focus()}},_doKeyDown:function(e){var a=false;var b=t._getInst(e.target);var c=(b&&b._inline?$(e.target).parent()[0]:null);if(e.keyCode===9){t.mainDiv.stop(true,true);t.hide();if(b&&b._inline){b._input.blur()}}else if(t._showingCalculator||(c&&!t.isDisabled(c))){if(e.keyCode===18){if(!t._showingKeystrokes){b._mainDiv.find('.'+t._keystrokeClass).show();t._showingKeystrokes=true}a=true}else{var d=t._keyCodes[e.keyCode];if(d){$('button[data-keystroke="'+d+'"]',b._mainDiv).not(':disabled').click();a=true}}}else if(e.keyCode===36&&e.ctrlKey&&b&&!b._inline){t.show(this)}if(a){e.preventDefault();e.stopPropagation()}return!a},_doKeyUp:function(e){if(t._showingKeystrokes){var a=t._getInst(e.target);a._mainDiv.find('.'+t._keystrokeClass).hide();t._showingKeystrokes=false}},_doKeyPress:function(e){var a=t._getInst(e.target);if(!a){return true}var b=(a&&a._inline?$(e.target).parent()[0]:null);var c=String.fromCharCode(e.charCode===undefined?e.keyCode:e.charCode);var d=a.options.isOperator||t.isOperator;if(!t._showingCalculator&&!b&&(a.options.showOn==='operator'||a.options.showOn==='opbutton')&&d.apply(a._input,[c,e,a._input.val(),a.options.base,a.options.decimalChar])){t.show(this);t._showingCalculator=true}if(t._showingCalculator||(b&&!t.isDisabled(b))){var f=t._keyChars[c===a.options.decimalChar?'.':c];if(f){$('button[data-keystroke="'+f+'"]',a._mainDiv).not(':disabled').click()}return false}if(c>=' '&&a.options.constrainInput){var g=new RegExp('^-?'+(a.options.base===10?'[0-9]*(\\'+a.options.decimalChar+'[0-9]*)?':'['+'0123456789abcdef'.substring(0,a.options.base)+']*')+'$');return(a._input.val()+c).toLowerCase().match(g)!=null}return true},isOperator:function(a,b,c,d,e){return a>' '&&!(a==='-'&&c==='')&&('0123456789abcdef'.substr(0,d)+'.'+e).indexOf(a.toLowerCase())===-1},_generateHTML:function(a){var b=(!a.options.prompt?'':'<div class="'+this._promptClass+(a.options.useThemeRoller?' ui-widget-header ui-corner-all':'')+'">'+a.options.prompt+'</div>')+'<div class="'+this._resultClass+(a.options.useThemeRoller?' ui-widget-header':'')+(a._focussed?' '+this._focussedClass:'')+'">'+(a.options.showFormula?'<span class="'+this._formulaClass+'">'+a._formula+'</span>':'')+'<span>'+a.dispValue+'</span></div>';for(var i=0;i<a.options.layout.length;i++){b+='<div class="'+this._rowClass+'">';for(var j=0;j<a.options.layout[i].length;j+=2){var c=a.options.layout[i].substr(j,2);var d=this._keyDefs[c]||this._keyDefs['??'];var e=(d[0].charAt(0)==='#'?a.options[d[0].substr(1)+'Text']:d[0]);var f=(d[0].charAt(0)==='#'?a.options[d[0].substr(1)+'Status']:'');var g=(d[3]?d[3].split(' '):[]);for(var k=0;k<g.length;k++){g[k]=a.name+'-'+g[k]}g=g.join(' ');var h=(a.options.useThemeRoller?' ui-state-active':'');var l=(a.options.useThemeRoller?' ui-state-highlight':'');b+=(d[1]===this.space?'<span class="'+a.name+'-'+d[3]+'"></span>':(a._inline&&(d[2]==='._close'||d[2]==='._erase')?'':'<button type="button" data-keystroke="'+c+'"'+(d[1]===this.control?' class="'+this._ctrlClass+(d[0].match(/^#base/)?(d[0].replace(/^#base/,'')===a.options.base?h||' '+this._baseActiveClass:l):(d[0]==='#degrees'?(a.options.useDegrees?h||' '+this._angleActiveClass:l):(d[0]==='#radians'?(!a.options.useDegrees?h||' '+this._angleActiveClass:l):l))):(d[1]===this.digit?(parseInt(d[0],16)>=a.options.base||(a.options.base!==10&&d[0]==='.')?' disabled="disabled"':'')+' class="'+this._digitClass:(d[1]===this.binary?' class="'+this._operatorClass:' class="'+this._operatorClass+(d[0].match(/^#mem(Clear|Recall)$/)&&!a.memory?' '+this._memEmptyClass:''))))+(a.options.useThemeRoller?' ui-state-default':'')+(g?' '+g:'')+'" '+(f?'title="'+f+'"':'')+'>'+(c==='_.'?a.options.decimalChar:e)+(d[5]&&d[5]!==d[0]?'<span class="'+this._keystrokeClass+(a.options.useThemeRoller?' ui-state-error':'')+(d[6]?' '+this._keyNameClass:'')+'">'+(d[6]||d[5])+'</span>':'')+'</button>'))}b+='</div>'}b+='<div style="clear: both;"></div>';b=$(b);b.find('button').on('mouseover.'+a.name,function(){t._saveClasses=this.className}).on('mousedown.'+a.name,function(){$(this).addClass(this._keyDownClass+(a.options.useThemeRoller?' ui-state-active':''))}).on('mouseup.'+a.name,function(){$(this).removeClass().addClass(t._saveClasses)}).on('mouseout.'+a.name,function(){$(this).removeClass().addClass(t._saveClasses)}).on('click.'+a.name,function(){t._handleButton(a,$(this))});return b},_setDisplay:function(a){var b=new Number(a.curValue).toFixed(a.options.precision).valueOf();var c=b.replace(/^.+(e.+)$/,'$1').replace(/^[^e].*$/,'');if(c){b=new Number(b.replace(/e.+$/,'')).toFixed(a.options.precision).valueOf()}return parseFloat(b.replace(/0+$/,'')+c).toString(a.options.base).toUpperCase().replace(/\./,a.options.decimalChar)},_sendButton:function(a,b){if($.isFunction(a.options.onButton)){a.options.onButton.apply((a._input?a._input[0]:null),[b,a.dispValue,a])}},_handleButton:function(a,b){var c=this._keyDefs[b.data('keystroke')];if(!c){return}var d=b.text().substr(0,b.text().length-b.children('.'+this._keyStrokeClass).text().length);switch(c[1]){case this.control:c[2].apply(this,[a,d]);break;case this.digit:this._digit(a,d);break;case this.binary:this._binaryOp(a,c[2],d);break;case this.unary:this._unaryOp(a,c[2],d);break}if(t._showingCalculator||a._inline){a._input.focus()}},_noOp:function(a){},_digit:function(a,b){a.dispValue=(a._newValue?'':a.dispValue);if(b===a.options.decimalChar&&a.dispValue.indexOf(b)>-1){return}a.dispValue=(a.dispValue+b).replace(/^0(\d)/,'$1').replace(new RegExp('^(-?)([\\.'+a.options.decimalChar+'])'),'$10$2');a._formula+=b;if(a.options.decimalChar!=='.'){a.dispValue=a.dispValue.replace(new RegExp('^'+a.options.decimalChar),'0.')}var c=(a.options.decimalChar!=='.'?a.dispValue.replace(new RegExp(a.options.decimalChar),'.'):a.dispValue);a.curValue=(a.options.base===10?parseFloat(c):parseInt(c,a.options.base));a._newValue=false;this._sendButton(a,b);this._updateCalculator(a)},_binaryOp:function(a,b,c){if(!a._newValue&&a._pendingOp){a._pendingOp(a);a.curValue=(a.options.base===10?a.curValue:Math.floor(a.curValue));a.dispValue=this._setDisplay(a)}a.prevValue=a.curValue;a._newValue=true;a._pendingOp=b;a._formula=a._formula.replace(/\D$/,'')+c;this._sendButton(a,c);this._updateCalculator(a)},_add:function(a){a.curValue=a.prevValue+a.curValue},_subtract:function(a){a.curValue=a.prevValue-a.curValue},_multiply:function(a){a.curValue=a.prevValue*a.curValue},_divide:function(a){a.curValue=a.prevValue/a.curValue},_power:function(a){a.curValue=Math.pow(a.prevValue,a.curValue)},_unaryOp:function(a,b,c){a._newValue=true;b.apply(this,[a]);a.curValue=(a.options.base===10?a.curValue:Math.floor(a.curValue));a.dispValue=this._setDisplay(a);a._formula+=(c==='='?'':' '+c+' ');this._sendButton(a,c);this._updateCalculator(a)},_plusMinus:function(a){a.curValue=-1*a.curValue;a.dispValue=this._setDisplay(a);a._newValue=false},_pi:function(a){a.curValue=Math.PI},_percent:function(a){if(a._pendingOp===this._add){a.curValue=a.prevValue*(1+a.curValue/100)}else if(a._pendingOp===this._subtract){a.curValue=a.prevValue*(1-a.curValue/100)}else if(a._pendingOp===this._multiply){a.curValue=a.prevValue*a.curValue/100}else if(a._pendingOp===this._divide){a.curValue=a.prevValue/a.curValue*100}a._savedOp=a._pendingOp;a._pendingOp=this._noOp},_equals:function(a){if(a._pendingOp===this._noOp){if(a._savedOp!==this._noOp){a.prevValue=a.curValue;a.curValue=a._savedValue;a._savedOp(a)}}else{a._savedOp=a._pendingOp;a._savedValue=a.curValue;a._pendingOp(a);a._pendingOp=this._noOp}a._formula=''},_memAdd:function(a){a.memory+=a.curValue;this._setMemoryCookie(a)},_memSubtract:function(a){a.memory-=a.curValue;this._setMemoryCookie(a)},_memStore:function(a){a.memory=a.curValue;this._setMemoryCookie(a)},_memRecall:function(a){a.curValue=a.memory},_memClear:function(a){a.memory=0;this._setMemoryCookie(a)},_sin:function(a){this._trig(a,Math.sin)},_cos:function(a){this._trig(a,Math.cos)},_tan:function(a){this._trig(a,Math.tan)},_trig:function(a,b,c){a.curValue=b(a.curValue*(a.options.useDegrees?Math.PI/180:1))},_asin:function(a){this._atrig(a,Math.asin)},_acos:function(a){this._atrig(a,Math.acos)},_atan:function(a){this._atrig(a,Math.atan)},_atrig:function(a,b,c){a.curValue=b(a.curValue);if(a.options.useDegrees){a.curValue=a.curValue/Math.PI*180}},_inverse:function(a){a.curValue=1/a.curValue},_log:function(a){a.curValue=Math.log(a.curValue)/Math.log(10)},_ln:function(a){a.curValue=Math.log(a.curValue)},_exp:function(a){a.curValue=Math.exp(a.curValue)},_sqr:function(a){a.curValue*=a.curValue},_sqrt:function(a){a.curValue=Math.sqrt(a.curValue)},_random:function(a){a.curValue=Math.random()},_base2:function(a,b){this._changeBase(a,b,2)},_base8:function(a,b){this._changeBase(a,b,8)},_base10:function(a,b){this._changeBase(a,b,10)},_base16:function(a,b){this._changeBase(a,b,16)},_changeBase:function(a,b,c){a.options.base=c;a.curValue=(c===10?a.curValue:Math.floor(a.curValue));a.dispValue=this._setDisplay(a);a._newValue=true;this._sendButton(a,b);this._updateCalculator(a)},_degrees:function(a,b){this._degreesRadians(a,b,true)},_radians:function(a,b){this._degreesRadians(a,b,false)},_degreesRadians:function(a,b,c){a.options.useDegrees=c;this._sendButton(a,b);this._updateCalculator(a)},_undo:function(a,b){a.dispValue=a.dispValue.substr(0,a.dispValue.length-1)||'0';a.curValue=(a.options.base===10?parseFloat(a.dispValue):parseInt(a.dispValue,a.options.base));a._formula=a._formula.replace(/[\.\d]$/,'');this._sendButton(a,b);this._updateCalculator(a)},_clearError:function(a,b){a.dispValue='0';a.curValue=0;a._formula=a._formula.replace(/[\.\d]+$/,'');a._newValue=true;this._sendButton(a,b);this._updateCalculator(a)},_clear:function(a,b){this._reset(a,0);this._sendButton(a,b);this._updateCalculator(a)},_close:function(a,b){this._finished(a,b,a._input.val())},_use:function(a,b){if(a._pendingOp!==this._noOp){this._unaryOp(a,this._equals,b)}this._finished(a,b,a.dispValue)},_erase:function(a,b){this._reset(a,0);this._updateCalculator(a);this._finished(a,b,'')},_finished:function(a,b,c){if(a._inline){this._curInst=a}else{a._input.val(c)}this._sendButton(a,b);this.hide(a._input[0])}});var t=$.calculator;var u=[['_0','0',t.digit,null,'','0','0'],['_1','1',t.digit,null,'','1','1'],['_2','2',t.digit,null,'','2','2'],['_3','3',t.digit,null,'','3','3'],['_4','4',t.digit,null,'','4','4'],['_5','5',t.digit,null,'','5','5'],['_6','6',t.digit,null,'','6','6'],['_7','7',t.digit,null,'','7','7'],['_8','8',t.digit,null,'','8','8'],['_9','9',t.digit,null,'','9','9'],['_A','A',t.digit,null,'hex-digit','A','a'],['_B','B',t.digit,null,'hex-digit','B','b'],['_C','C',t.digit,null,'hex-digit','C','c'],['_D','D',t.digit,null,'hex-digit','D','d'],['_E','E',t.digit,null,'hex-digit','E','e'],['_F','F',t.digit,null,'hex-digit','F','f'],['_.','.',t.digit,null,'decimal','DECIMAL','.'],['_+','+',t.binary,t._add,'arith add','ADD','+'],['_-','-',t.binary,t._subtract,'arith subtract','SUBTRACT','-'],['_*','*',t.binary,t._multiply,'arith multiply','MULTIPLY','*'],['_/','/',t.binary,t._divide,'arith divide','DIVIDE','/'],['_%','%',t.unary,t._percent,'arith percent','PERCENT','%'],['_=','=',t.unary,t._equals,'arith equals','EQUALS','='],['+-','±',t.unary,t._plusMinus,'arith plus-minus','PLUS_MINUS','#'],['PI','π',t.unary,t._pi,'pi','PI','p'],['1X','1/x',t.unary,t._inverse,'fn inverse','INV','i'],['LG','log',t.unary,t._log,'fn log','LOG','l'],['LN','ln',t.unary,t._ln,'fn ln','LN','n'],['EX','eⁿ',t.unary,t._exp,'fn exp','EXP','E'],['SQ','x²',t.unary,t._sqr,'fn sqr','SQR','@'],['SR','√',t.unary,t._sqrt,'fn sqrt','SQRT','!'],['XY','x^y',t.binary,t._power,'fn power','POWER','^'],['RN','rnd',t.unary,t._random,'random','RANDOM','?'],['SN','sin',t.unary,t._sin,'trig sin','SIN','s'],['CS','cos',t.unary,t._cos,'trig cos','COS','o'],['TN','tan',t.unary,t._tan,'trig tan','TAN','t'],['AS','asin',t.unary,t._asin,'trig asin','ASIN','S'],['AC','acos',t.unary,t._acos,'trig acos','ACOS','O'],['AT','atan',t.unary,t._atan,'trig atan','ATAN','T'],['MC','#memClear',t.unary,t._memClear,'memory mem-clear','MEM_CLEAR','x'],['MR','#memRecall',t.unary,t._memRecall,'memory mem-recall','MEM_RECALL','r'],['MS','#memStore',t.unary,t._memStore,'memory mem-store','MEM_STORE','m'],['M+','#memAdd',t.unary,t._memAdd,'memory mem-add','MEM_ADD','>'],['M-','#memSubtract',t.unary,t._memSubtract,'memory mem-subtract','MEM_SUBTRACT','<'],['BB','#base2',t.control,t._base2,'base base2','BASE_2','B'],['BO','#base8',t.control,t._base8,'base base8','BASE_8','C'],['BD','#base10',t.control,t._base10,'base base10','BASE_10','D'],['BH','#base16',t.control,t._base16,'base base16','BASE_16','H'],['DG','#degrees',t.control,t._degrees,'angle degrees','DEGREES','G'],['RD','#radians',t.control,t._radians,'angle radians','RADIANS','R'],['BS','#backspace',t.control,t._undo,'undo','UNDO',8,'BSp'],['CE','#clearError',t.control,t._clearError,'clear-error','CLEAR_ERROR',36,'Hom'],['CA','#clear',t.control,t._clear,'clear','CLEAR',35,'End'],['@X','#close',t.control,t._close,'close','CLOSE',27,'Esc'],['@U','#use',t.control,t._use,'use','USE',13,'Ent'],['@E','#erase',t.control,t._erase,'erase','ERASE',46,'Del'],['  ','',t.space,null,'space','SPACE'],['_ ','',t.space,null,'half-space','HALF_SPACE'],['??','??',t.unary,t._noOp]];$.each(u,function(i,a){t.addKeyDef.apply(t,a)});$(function(){$('body').append(t.mainDiv).on('mousedown.'+m,t._checkExternalClick)})})(jQuery);