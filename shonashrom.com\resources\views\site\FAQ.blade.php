@extends('site.index')
@section('content')

<title>{{trans('admin.FAQ')}}</title>
   


    <!--==============================
    Breadcumb
    ============================== -->
    <div class="breadcumb-wrapper" data-bg-src="{{asset('Front/assets/img/bg/breadcrumb-bg.png')}}">
        <!-- bg animated image/ -->
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="breadcumb-content">
                        <h1 class="breadcumb-title">{{trans('admin.FAQ')}}</h1>
                        <ul class="breadcumb-menu">
                            <li><a href="{{url('/')}}">{{trans('admin.Home')}}</a></li>
                            <li class="active">{{trans('admin.FAQ')}}</li>
                        </ul>
                    </div>
                </div>
            </div>

        </div>
    </div>


    <!--==============================
        FAQ Area
    ==============================-->
      <div class="feature-area-3 space-bottom overflow-hidden space">
        <div class="container">
            <div class="title-area">
                <span class="sub-title style2">{{trans('admin.FAQ')}}</span>

            </div>
        </div>
        <div class="container">
            <div class="row">
                <div class="col-xl-4">
                    <div class="feature-tab-button">
                        <div class="filter-menu-active">
                            
                                   @foreach($Faqs as $faq)
                            <button data-filter=".cat{{$faq->id}}" class=" @if($faq->id == 1) active @endif" type="button">
                                     {{app()->getLocale() == 'ar' ?$faq->Arabic_Question :$faq->English_Question}}
                                <i class="fas fa-circle-arrow-right"></i></button>
                           @endforeach
                        </div>
                    </div>
                </div>
                <div class="col-xl-8">
                    <div class="feature-tab-content filter-active-cat1 mt-xl-0 mt-40">
                        
                        
                               @foreach($Faqs as $faq)
                        <div class="filter-item cat{{$faq->id}}">
                        
                            <div class="tab-content mt-n1">
                                 {{app()->getLocale() == 'ar' ?$faq->Arabic_Answer :$faq->English_Answer}}      
                            </div>
                        </div>
                     @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>

    



@endsection
      