@extends('admin.index')
@section('content')

  <title>{{trans('admin.SocialMedia')}}</title>
     <main id="js-page-content" role="main" class="page-content">
                    <ol class="breadcrumb page-breadcrumb">
                        <li class="breadcrumb-item"><a href="javascript:void(0);">{{trans('admin.Settings')}}</a></li>
                      
                        <li class="breadcrumb-item active"> {{trans('admin.SocialMedia')}}   </li>
                        <li class="position-absolute pos-top pos-right d-none d-sm-block"><span
                                class="js-get-date"></span></li>
                    </ol>
                    
                    <!-- data entry -->
                    <div class="row">
                        <div class="col-lg-12">
                            <div id="panel-2" class="panel">
                                <div class="panel-hdr">

                                </div>
                                <div class="panel-container show">
                                  <span id="ex"> @include('admin.layouts.messages')</span>     
                                    <div class="panel-content">
                                        <ul class="nav nav-tabs" role="tablist">
                                            <li class="nav-item">
            <a class="nav-link active" data-toggle="tab" href="#tab_borders_icons-8" role="tab">   {{trans('admin.SocialMedia')}}  </a>
                                            </li>
                                        </ul>
                                        <div class="tab-content border border-top-0 p-3">
                           <div class="tab-pane fade show active" id="tab_borders_icons-8" role="tabpanel">
                                                                          
     <form action="{{url('SocialMediaUpdate/'.$item->id)}}" method="post" enctype="multipart/form-data" class="form-row">
                                  {!! csrf_field() !!}
             
                                                    <div class="col-md-12">
                                                        <div class="data-def">
                                                            <div class="form-row">
                                                      
                                  <div class="form-group col-md-6">
                                        <label for="exampleInputEmail1">{{trans('admin.Facebook')}}</label>
                                        <i class="fab fa-facebook-square icon"></i>
                                        <input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp" placeholder="{{trans('admin.Facebook')}}" name="Facebook" value="{{$item->Facebook}}">
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label for="exampleInputPassword1">{{trans('admin.Twitter')}} </label>
                                        <i class="fab fa-twitter-square icon"></i>
                                        <input type="text" class="form-control" id="exampleInputPassword1" placeholder="{{trans('admin.Twitter')}}" name="Twitter" value="{{$item->Twitter}}">
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label for="exampleInputPassword1">{{trans('admin.Instagram')}} </label>
                                        <i class="fab fa-instagram icon"></i>
                                        <input type="text" class="form-control" id="exampleInputPassword1" placeholder="{{trans('admin.Instagram')}}" name="Instagram" value="{{$item->Instagram}}">
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label for="exampleInputEmail1">{{trans('admin.Youtube')}}</label>
                                        <i class="fab fa-youtube-square icon"></i>
                                        <input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp" placeholder="{{trans('admin.Youtube')}}" name="Youtube" value="{{$item->Youtube}}">
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label for="exampleInputPassword1">{{trans('admin.Snapchat')}} </label>
                                        <i class="fab fa-snapchat-square icon"></i>
                                        <input type="text" class="form-control" id="exampleInputPassword1" placeholder="{{trans('admin.Snapchat')}}" name="Snapchat" value="{{$item->Snapchat}}">
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label for="exampleInputPassword1">{{trans('admin.Whatsapp')}} </label>
                                        <i class="fab fa-whatsapp-square icon"></i>
                                        <input type="text" class="form-control" id="exampleInputPassword1" placeholder="{{trans('admin.Whatsapp')}}" name="Whatsapp" value="{{$item->Whatsapp}}">
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label for="exampleInputPassword1">{{trans('admin.Google_Plus')}} </label>
                                        <i class="fab fa-google-plus-square icon"></i>
                                        <input type="text" class="form-control" id="exampleInputPassword1" placeholder="{{trans('admin.Google_Plus')}}" name="Google_Plus" value="{{$item->Google_Plus}}">
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label for="exampleInputPassword1 icon">{{trans('admin.LinkedIn')}} </label>
                                        <i class="fab fa-linkedin"></i>
                                        <input type="text" class="form-control" id="exampleInputPassword1" placeholder="{{trans('admin.LinkedIn')}}" name="LinkedIn" value="{{$item->LinkedIn}}">
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label for="exampleInputPassword1">{{trans('admin.Pinterest')}} </label>
                                        <i class="fab fa-pinterest-square icon"></i>
                                        <input type="text" class="form-control" id="exampleInputPassword1" placeholder="{{trans('admin.Pinterest')}}" name="Pinterest" value="{{$item->Pinterest}}">
                                    </div>
                                    
                                    <div class="form-group col-md-6">
                                        <label for="exampleInputPassword1">{{trans('admin.Telegram')}} </label>
                                        <i class="fab fa-telegram"></i>
                                        <input type="text" class="form-control" id="exampleInputPassword1" placeholder="{{trans('admin.Telegram')}}" name="Telegram" value="{{$item->Telegram}}">
                                    </div>
                                    
                                    
                                         <div class="form-group col-md-6">
                                        <label for="exampleInputPassword1">{{trans('admin.iOS')}}  </label>
                                        <i class="fab fa-telegram"></i>
                                        <input type="text" class="form-control" id="exampleInputPassword1" placeholder="{{trans('admin.iOS')}}" name="iOS" value="{{$item->iOS}}">
                                    </div>
                                    
                                         <div class="form-group col-md-6">
                                        <label for="exampleInputPassword1">{{trans('admin.Android')}}  </label>
                                        <i class="fab fa-telegram"></i>
                                        <input type="text" class="form-control" id="exampleInputPassword1" placeholder="{{trans('admin.Android')}}" name="Android" value="{{$item->Android}}">
                                    </div>
                                            
       
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-12">
                                <button type="submit" class="btn btn-primary mt-2">{{trans('admin.Save')}}</button>
                                                    </div>
                                                </form>
 
                                            </div>
                                        </div>
                                      
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                 
                </main>
 
@endsection

@push('js')
  <link rel="stylesheet" media="screen, print" href="{{asset('Admin/css/datagrid/datatables/datatables.bundle.css')}}">
    <link rel="stylesheet" media="screen, print" href="{{asset('Admin/css/formplugins/summernote/summernote.css')}}">
    <link rel="stylesheet" media="screen, print" href="{{asset('Admin/css/formplugins/select2/select2.bundle.css')}}">

 <script src="{{asset('Admin/js/datagrid/datatables/datatables.bundle.js')}}"></script>
    <script src="{{asset('Admin/js/datagrid/datatables/datatables.export.js')}}"></script>
    <script src="{{asset('Admin/js/formplugins/summernote/summernote.js')}}"></script>
    <script src="{{asset('Admin/js/formplugins/select2/select2.bundle.js')}}"></script>
    <script>
        //_fnFeatureHtmlLength();
        $(document).ready(function () {
            // Setup - add a text input to each footer cell
            $('#dt-basic-example thead tr').clone(true).appendTo('#dt-basic-example thead');
            $('#dt-basic-example thead tr:eq(1) th').each(function (i) {
                var title = $(this).text();
                $(this).html('<input type="text" class="form-control form-control-sm" placeholder="Search ' + title + '" />');

                $('input', this).on('keyup change', function () {
                    if (table.column(i).search() !== this.value) {
                        table
                            .column(i)
                            .search(this.value)
                            .draw();
                    }
                });
            });
            var table = $('#dt-basic-example').DataTable(
                {
                    responsive: true,
                    orderCellsTop: true,
                    fixedHeader: true,
                    lengthChange: true,

                    dom: "<'row mb-3'<'col-sm-12 col-md-3 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-9 d-flex align-items-center justify-content-end'B>>" +
                        "<'row'<'col-sm-12'tr>>" +
                        "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",

                    buttons: [
                        {
                            extend: 'pageLength',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'colvis',
                            text: 'Column Visibility',
                            titleAttr: 'Col visibility',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'pdfHtml5',
                            text: 'PDF',
                            titleAttr: 'Generate PDF',
                            className: 'btn-outline-danger btn-sm mr-1'
                        },
                        {
                            extend: 'excelHtml5',
                            text: 'Excel',
                            titleAttr: 'Generate Excel',
                            className: 'btn-outline-success btn-sm mr-1'
                        },
                        {
                            extend: 'csvHtml5',
                            text: 'CSV',
                            titleAttr: 'Generate CSV',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'copyHtml5',
                            text: 'Copy',
                            titleAttr: 'Copy to clipboard',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'print',
                            text: 'Print',
                            titleAttr: 'Print Table',
                            className: 'btn-outline-primary btn-sm'
                        }
                    ],
                });
            $('.js-thead-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example thead').removeClassPrefix('bg-').addClass(theadColor);
            });

            $('.js-tbody-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example').removeClassPrefix('bg-').addClass(theadColor);
            });

        });

    </script>


@endpush

