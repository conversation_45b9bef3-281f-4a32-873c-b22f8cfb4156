.redactor_box {
	position: relative;
	overflow: visible;
	border: 1px solid #ddd;
	background-color: #fff;
}

body .redactor_air {
	position: absolute;
	z-index: 2;
}

/*
	Fullscreen
*/
body .redactor_box_fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 2000;
    overflow: hidden;
    width: 100%;
}

.redactor_box iframe {
	display: block;
	margin: 0;
	padding: 0;
}

.redactor_box textarea, .redactor_box textarea:focus {
	position: relative;
	z-index: 1004;
	display: block;
	overflow: auto;
	margin: 0;
	padding: 0;
	width: 100%;
	outline: none;
	outline: none;
	border: none;
	background-color: #222;
	box-shadow: none;
	color: #ccc;
	font-size: 13px;
	font-family: Menlo, Monaco, monospace, sans-serif;
	resize: none;
}

.redactor_editor,
.redactor_editor:focus,
.redactor_editor div,
.redactor_editor p,
.redactor_editor ul,
.redactor_editor ol,
.redactor_editor table,
.redactor_editor dl,
.redactor_editor blockquote,
.redactor_editor pre,
.redactor_editor h1,
.redactor_editor h2,
.redactor_editor h3,
.redactor_editor h4,
.redactor_editor h5 {
	font-family: Arial, Helvetica, Verdana, Tahoma, sans-serif !important;
}

.redactor_editor code,
.redactor_editor pre {
	font-family: Menlo, Monaco, monospace, sans-serif !important;
}

.redactor_editor,
.redactor_editor:focus,
.redactor_editor div,
.redactor_editor p,
.redactor_editor ul,
.redactor_editor ol,
.redactor_editor table,
.redactor_editor dl,
.redactor_editor blockquote,
.redactor_editor pre {
	font-size: 15px !important;
	line-height: 1.5em;
}

.redactor_editor,
.redactor_editor:focus {
	position: relative;
	overflow: auto;
	margin: 0 !important;
	padding: 10px;
	padding-bottom: 5px;
	outline: none;
	background: none;
	background: #fff !important;
	box-shadow: none !important;
	white-space: normal;
}
.redactor_editor a {
	color: #15c !important;
	text-decoration: underline !important;
}

.redactor_editor .redactor_placeholder {
	color: #999 !important;
	display: block !important;
	margin-bottom: 10px !important;
}

.redactor_editor object,
.redactor_editor embed,
.redactor_editor video,
.redactor_editor img {
	max-width: 100%;
	width: auto;
}
.redactor_editor video,
.redactor_editor img {
	height: auto;
}

.redactor_editor div,
.redactor_editor p,
.redactor_editor ul,
.redactor_editor ol,
.redactor_editor table,
.redactor_editor dl,
.redactor_editor blockquote,
.redactor_editor pre {
	margin: 0;
	margin-bottom: 10px !important;
	border: none;
	background: none !important;
	box-shadow: none !important;
}
.redactor_editor iframe,
.redactor_editor object,
.redactor_editor hr {
	margin-bottom: 15px !important;
}
.redactor_editor blockquote {
	margin-left: 3em !important;
	color: #777;
	font-style: italic !important;
}
.redactor_editor ul,
.redactor_editor ol {
	padding-left: 2em !important;
}
.redactor_editor ul ul,
.redactor_editor ol ol,
.redactor_editor ul ol,
.redactor_editor ol ul {
	margin: 2px !important;
	padding: 0 !important;
	padding-left: 2em !important;
	border: none;
}
.redactor_editor dl dt { font-weight: bold; }
.redactor_editor dd { margin-left: 1em;}

.redactor_editor table {
	border-collapse: collapse;
	font-size: 1em !important;
}
.redactor_editor table td {
	padding: 5px !important;
	border: 1px solid #ddd;
	vertical-align: top;
}
.redactor_editor table thead td {
	border-bottom: 2px solid #000 !important;
	font-weight: bold !important;
}
.redactor_editor code {
	background-color: #d8d7d7 !important;
}
.redactor_editor pre {
	overflow: auto;
	padding: 1em !important;
	border: 1px solid #ddd !important;
	border-radius: 3px !important;
	background: #f8f8f8 !important;
	white-space: pre;
	font-size: 90% !important;
}
.redactor_editor hr {
  display: block;
  height: 1px;
  border: 0;
  border-top: 1px solid #ccc;
}

.redactor_editor h1,
.redactor_editor h2,
.redactor_editor h3,
.redactor_editor h4,
.redactor_editor h5 {
	margin-top: 0 !important;
	margin-right: 0 !important;
	margin-left: 0;
	padding: 0 !important;
	background: none;
	color: #000;
	font-weight: bold;
}

.redactor_editor h1 {
	margin-bottom: 10px;
	font-size: 36px !important;
	line-height: 40px !important;
}
.redactor_editor h2 {
	margin-bottom: 10px;
	font-size: 30px !important;
	line-height: 38px !important;
}
.redactor_editor h3 {
	margin-bottom: 10px;
	font-size: 24px !important;
	line-height: 30px;
}
.redactor_editor h4 {
	margin-bottom: 10px;
	font-size: 18px !important;
	line-height: 24px !important;
}
.redactor_editor h5 {
	margin-bottom: 10px;
	font-size: 1em !important;
}

.redactor_editor.redactor_editor_wym {
	padding: 10px 7px 0 7px !important;
	background: #f6f6f6 !important;
}
.redactor_editor_wym div,
.redactor_editor_wym p,
.redactor_editor_wym ul,
.redactor_editor_wym ol,
.redactor_editor_wym table,
.redactor_editor_wym dl,
.redactor_editor_wym pre,
.redactor_editor_wym h1,
.redactor_editor_wym h2,
.redactor_editor_wym h3,
.redactor_editor_wym h4,
.redactor_editor_wym h5,
.redactor_editor_wym h6,
.redactor_editor_wym blockquote {
	margin: 0 0 5px 0;
	padding: 10px !important;
	border: 1px solid #e4e4e4 !important;
	background-color: #fff !important;
}
.redactor_editor_wym div {
	border: 1px dotted #aaa !important;
}
.redactor_editor_wym pre {
	border: 2px dashed #e4e4e4 !important;
	background-color: #f8f8f8 !important;
}
.redactor_editor_wym ul,
.redactor_editor_wym ol {
	padding-left: 2em !important;
}
.redactor_editor_wym ul li ul,
.redactor_editor_wym ul li ol,
.redactor_editor_wym ol li ol,
.redactor_editor_wym ol li ul {
	border: none !important;
}

/*
	TOOLBAR
*/
.redactor_toolbar {
	position: relative;
	top: 0;
	left: 0;
	margin: 0 !important;
	padding: 0 !important;
	padding-left: 2px !important;
	border: 1px solid #ddd;
	border-bottom-color: #b8b8b8;
	background: #fafafa;
	background: -moz-linear-gradient(top,  #fafafa 0%, #e5e5e5 94%, #d3d3d3 94%, #d3d3d3 100%);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#fafafa), color-stop(94%,#e5e5e5), color-stop(94%,#d3d3d3), color-stop(100%,#d3d3d3));
	background: -webkit-linear-gradient(top,  #fafafa 0%,#e5e5e5 94%,#d3d3d3 94%,#d3d3d3 100%);
	background: -o-linear-gradient(top,  #fafafa 0%,#e5e5e5 94%,#d3d3d3 94%,#d3d3d3 100%);
	background: -ms-linear-gradient(top,  #fafafa 0%,#e5e5e5 94%,#d3d3d3 94%,#d3d3d3 100%);
	background: linear-gradient(to bottom,  #fafafa 0%,#e5e5e5 94%,#d3d3d3 94%,#d3d3d3 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#fafafa', endColorstr='#d3d3d3',GradientType=0 );

	list-style: none !important;
	font-size: 0;
	font-family: Helvetica, Arial, Verdana, Tahoma, sans-serif !important;
	line-height: 0 !important;

}
.redactor_toolbar:after {
	display: block;
	visibility: hidden;
	clear: both;
	height: 0;
	content: ".";
}
.redactor_box .redactor_toolbar {
	border: none;
	border-bottom: 1px solid #b8b8b8;
}
.redactor_toolbar.toolbar_fixed_box {
	border: 1px solid #ddd;
	border-bottom-color: #b8b8b8;
}
body .redactor_air .redactor_toolbar {
	padding-right: 2px !important;
}
.redactor_toolbar li {
	float: left !important;
	margin: 0 !important;
	padding: 1px 0 3px 1px;
	outline: none;
	list-style: none !important;
}
.redactor_toolbar li.redactor_separator {
	float: left;
	margin: 0 2px 0 3px !important;
	padding: 0;
	height: 29px;
	border-right: 1px solid #f4f4f4;
	border-left: 1px solid #d8d8d8;
}
.redactor_toolbar li a {
	display: block;
	width: 25px;
	height: 25px;
	outline: none;
	border: 1px solid transparent;
	text-decoration: none;
	font-size: 0;
	line-height: 0;
	cursor: pointer;
	zoom: 1;
	*border: 1px solid #eee;
}
.redactor_toolbar li.redactor_btn_right {
	float: none;
	float: right !important;
}
.redactor_toolbar li a {
	display: block;
	background-image: url(data:image/png;base64,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);
	background-position: 0;
	background-repeat: no-repeat;
	text-indent: -9999px;
}
@media
only screen and (-webkit-min-device-pixel-ratio: 2),
only screen and (   min--moz-device-pixel-ratio: 2),
only screen and (     -o-min-device-pixel-ratio: 2/1),
only screen and (        min-device-pixel-ratio: 2),
only screen and (                min-resolution: 192dpi),
only screen and (                min-resolution: 2dppx) {

	.redactor_toolbar li a {
		background-image: url(data:image/png;base64,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);
		background-size: auto 25px;
	}

}
.redactor_toolbar li a:hover {
	outline: none;
	border-color: #98a6ba;
	border-color: rgba(162, 185, 208, .8);
	background-color: #d4dce9;
	background-color: rgba(176, 199, 223, .5);
}
.redactor_toolbar li a:active,
.redactor_toolbar li a.redactor_act {
	outline: none;
	border-color: #b5b5b5;
	background-color: #ddd;
}
.redactor_button_disabled {
	opacity: .3 ;
}
.redactor_button_disabled:hover {
	outline: none;
	border-color: transparent !important;
	background-color: transparent !important;
	cursor: default;
}

/*
	BUTTONS
	step 25px
*/
body .redactor_toolbar li a.redactor_btn_html				{ background-position: 0px; }
body .redactor_toolbar li a.redactor_btn_formatting		    { background-position: -25px; }
body .redactor_toolbar li a.redactor_btn_bold				{ background-position: -50px; }
body .redactor_toolbar li a.redactor_btn_italic			    { background-position: -75px; }
body .redactor_toolbar li a.redactor_btn_deleted		 	{ background-position: -500px; }
body .redactor_toolbar li a.redactor_btn_unorderedlist 	    { background-position: -100px; }
body .redactor_toolbar li a.redactor_btn_orderedlist   	    { background-position: -125px; }
body .redactor_toolbar li a.redactor_btn_outdent	 		{ background-position: -150px; }
body .redactor_toolbar li a.redactor_btn_indent		 	  	{ background-position: -175px; }
body .redactor_toolbar li a.redactor_btn_image		 		{ background-position: -200px; }
body .redactor_toolbar li a.redactor_btn_video		 		{ background-position: -225px; }
body .redactor_toolbar li a.redactor_btn_file		 		{ background-position: -250px; }
body .redactor_toolbar li a.redactor_btn_table		 		{ background-position: -275px; }
body .redactor_toolbar li a.redactor_btn_link		 		{ background-position: -300px; }
body .redactor_toolbar li a.redactor_btn_fontcolor		 	{ background-position: -325px; }
body .redactor_toolbar li a.redactor_btn_backcolor		 	{ background-position: -350px; }
body .redactor_toolbar li a.redactor_btn_alignleft		  	{ background-position: -375px; }
body .redactor_toolbar li a.redactor_btn_aligncenter		{ background-position: -400px; }
body .redactor_toolbar li a.redactor_btn_alignright		  	{ background-position: -425px; }
body .redactor_toolbar li a.redactor_btn_justify		 	{ background-position: -450px; }
body .redactor_toolbar li a.redactor_btn_horizontalrule 	{ background-position: -475px; }
body .redactor_toolbar li a.redactor_btn_underline		 	{ background-position: -525px; }

body .redactor_toolbar li a.redactor_btn_fullscreen		 	{ background-position: -550px; }
body .redactor_toolbar li a.redactor_btn_normalscreen		{ background-position: -575px; }
body .redactor_toolbar li a.redactor_btn_clips		 		{ background-position: -600px; }

body .redactor_toolbar li a.redactor_btn_alignment	 		{ background-position: -625px; }

body .redactor_toolbar li a.redactor_btn_fontfamily	 		{ background-position: -650px; }
body .redactor_toolbar li a.redactor_btn_fontsize	 		{ background-position: -675px; }

body .redactor_toolbar li a.redactor_btn_direction	 		{ background-position: -700px; }
body .redactor_toolbar li a.redactor_btn_lists		 		{ background-position: -725px; }
body .redactor_toolbar li a.redactor_btn_font		 		{ background-position: -750px; }

body .redactor_toolbar li a.redactor_btn_h1			 		{ background-position: -775px; }
body .redactor_toolbar li a.redactor_btn_h2			 		{ background-position: -800px; }
body .redactor_toolbar li a.redactor_btn_h3			 		{ background-position: -825px; }
body .redactor_toolbar li a.redactor_btn_quote		 		{ background-position: -850px; }
body .redactor_toolbar li a.redactor_btn_pre		 		{ background-position: -875px; }

/*
	Toolbar classes
*/
.redactor_format_blockquote {
	padding-left: 10px;
	color: #666 !important;
	font-style: italic;
}
.redactor_format_pre {
	font-family: monospace, sans-serif;
}
.redactor_format_h1, .redactor_format_h2, .redactor_format_h3, .redactor_format_h4 {
	font-weight: bold;
}
.redactor_format_h1 {
	font-size: 30px;
	line-height: 36px;
}
.redactor_format_h2 {
	font-size: 24px;
	line-height: 36px;
}
.redactor_format_h3 {
	font-size: 20px;
	line-height: 30px;
}
.redactor_format_h4 {
	font-size: 16px;
	line-height: 26px;
}

/*
	DROPDOWN
*/
.redactor_dropdown {
	position: absolute;
	top: 28px;
	left: 0;
	z-index: 2004;
	padding: 10px;
	width: 200px;
	border: 1px solid #ccc;
	background-color: #fff;
	box-shadow: 0 2px 4px #ccc;
	font-size: 13px;
	font-family: Helvetica, Arial, Verdana, Tahoma, sans-serif;
	line-height: 21px;
}
.redactor_separator_drop {
	padding: 0 !important;
	border-top: 1px solid #ddd;
	font-size: 0;
	line-height: 0;
}
.redactor_dropdown a {
	display: block;
	padding: 3px 5px;
	color: #000;
	text-decoration: none;
}
.redactor_dropdown a:hover {
	background-color: #dde4ef;
	color: #444 !important;
	text-decoration: none;
}

/* ColorPicker */
.redactor_color_link {
	float: left !important;
	padding: 0 !important;
	width: 15px !important;
	height: 15px !important;
	border: 2px solid #fff !important;
	border-radius: 4px !important;
	box-shadow: 0 1px 2px rgba(0, 0, 0, .2) inset !important;
	font-size: 0;
}
.redactor_color_none {
	display: block;
	clear: both;
	padding: 4px 0 !important;
	font-size: 11px;
	line-height: 1;
}



/* MODAL */
#redactor_modal_overlay {
	position: fixed;
	top: 0;
	left: 0;
	z-index: 50000;
	margin: auto;
	width: 100%;
	height: 100%;

	background-color: #333 !important;
	opacity: 0.50;

	-ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
	filter:alpha(opacity=50);
}

#redactor_modal {
	position: fixed;
	top: 50%;
	left: 50%;
  	z-index: 50001;
	padding: 0;
  	border-radius: 3px;
	background: #f7f7f7;
	background: -moz-linear-gradient(top,  #f7f7f7 0%, #e2e2e2 100%);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#f7f7f7), color-stop(100%,#e2e2e2));
	background: -webkit-linear-gradient(top,  #f7f7f7 0%,#e2e2e2 100%);
	background: -o-linear-gradient(top,  #f7f7f7 0%,#e2e2e2 100%);
	background: -ms-linear-gradient(top,  #f7f7f7 0%,#e2e2e2 100%);
	background: linear-gradient(to bottom,  #f7f7f7 0%,#e2e2e2 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f7f7f7', endColorstr='#e2e2e2',GradientType=0 );

	box-shadow: 0px 5px 60px #000;
	color: #000;
	text-shadow: 0 1px 0 #fff;
	font-size: 12px !important;
	font-family: Helvetica, Arial, Verdana, Tahoma, sans-serif;

}
#redactor_modal header {
	padding: 11px 30px 0 15px;
	border-radius: 3px 3px 0 0;
	font-weight: bold;
	font-size: 12px;
}
#redactor_modal section {
	padding: 20px 30px;

}
#redactor_modal_close {
	position: absolute;
	top: 5px;
	right: 5px;
	width: 20px;
	height: 20px;
	color: #777;
	font-size: 20px;
	cursor: pointer;
}
#redactor_modal_close:hover {
	color: #000;
}
#redactor_modal label {
	display: block !important;
	float: none !important;
	margin: 10px 0 3px 0 !important;
	padding: 0 !important;
	font-size: 12px !important;
}
#redactor_modal textarea {
	display: block;
	margin-top: 4px;
}
.redactor_input  {
	width: 99%;
	font-size: 14px;
}



.redactor_modal_box {
	overflow: auto;
	margin-bottom: 10px;
	height: 350px;
}
#redactor_image_box {
	overflow: auto;
	margin-bottom: 10px;
	height: 270px;
}
#redactor_image_box_select {
	display: block;
	margin-bottom: 15px !important;
	width: 200px;
}
#redactor_image_box img {
	margin-right: 10px;
	margin-bottom: 10px;
	max-width: 100px;
	cursor: pointer;
}
#redactor_tabs {
	margin-bottom: 18px;
}
#redactor_tabs a {
	display: inline-block;
	margin-right: 5px;
	padding: 4px 14px;
	border: 1px solid #d2d2d2;
	border-radius: 10px;
	background-color: #fff;
	color: #000;
	text-decoration: none;
	font-size: 12px;
	line-height: 1;
}
#redactor_tabs a:hover, #redactor_tabs a.redactor_tabs_act {
	padding: 5px 15px;
	border: none;
	background-color: #ddd;
	box-shadow: 0 1px 2px rgba(0, 0, 0, .4) inset;
	color: #777 !important;
	text-decoration: none !important;
	text-shadow: 0 1px 0 #eee;
}
#redactor_modal footer {
	padding: 9px 30px 20px 30px;
	border-radius: 0 0 3px 3px;
	text-align: right;
}

#redactor_modal input[type="radio"],
#redactor_modal input[type="checkbox"] {
	position: relative;
	top: -1px;
}
#redactor_modal input[type="text"],
#redactor_modal input[type="password"],
#redactor_modal input[type="email"],
#redactor_modal textarea {
	position: relative;
	z-index: 2;
	margin: 0;
	padding: 1px 2px;
	height: 23px;
	border: 1px solid #ccc;
	border-radius: 1px;
	background-color: white;
	box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) inset;
	color: #333;
	font-size: 13px;
	font-family: Helvetica, Arial, Tahoma, sans-serif;
	line-height: 1;
	-webkit-transition: border 0.3s ease-in;
	-moz-transition: border 0.3s ease-in;
	-ms-transition: border 0.3s ease-in;
	-o-transition: border 0.3s ease-in;
	transition: border 0.3s ease-in;
}
#redactor_modal textarea {
	line-height: 1.4em;
}
#redactor_modal input:focus,
#redactor_modal textarea:focus {
	outline: none;
	border-color: #5ca9e4;
	box-shadow: 0 0 0 2px rgba(70, 161, 231, 0.3), 0 1px 2px rgba(0, 0, 0, 0.2) inset;
}
.redactor_modal_btn {
	position: relative;
	display: inline-block;
	margin-left: 8px;
	padding: 6px 16px 5px 16px;
	outline: none;
	border: 1px solid #ccc;
	border-bottom-color: #aaa;
	border-radius: 4px;
	background-color: #f3f3f3;
	background-image: -moz-linear-gradient(top, #ffffff, #e1e1e1);
	background-image: -ms-linear-gradient(top, #ffffff, #e1e1e1);
	background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#ffffff), to(#e1e1e1));
	background-image: -webkit-linear-gradient(top, #ffffff, #e1e1e1);
	background-image: -o-linear-gradient(top, #ffffff, #e1e1e1);
	background-image: linear-gradient(top, #ffffff, #e1e1e1);
	box-shadow: 0 1px 1px rgba(0, 0, 0, .1);
	color: #000;
	text-align: center;
	text-decoration: none;
	text-shadow: 0 1px 0px #ffffff;
	font-weight: normal;
	font-size: 12px;
	font-family: Helvetica, Arial, Verdana, Tahoma, sans-serif;
	line-height: 1;
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#e1e1e1', GradientType=0);
	cursor: pointer;
}
.redactor_modal_btn:hover {
	color: #555;
}
.redactor_modal_btn:hover {
	background: none;
	background: #f3f3f3;
	text-decoration: none;
	text-shadow: 0 1px 0px rgba(255, 255, 255, 0.8);
	filter: none;
}


/* Drag and Drop Area */
.redactor_droparea {
	position: relative;
    margin: auto;
    margin-bottom: 5px;
    width: 100%;
}
.redactor_droparea .redactor_dropareabox {
	position: relative;
	z-index: 1;
    padding: 60px 0;
    width: 99%;
    border: 2px dashed #bbb;
    background-color: #fff;
    text-align: center;
}
.redactor_droparea .redactor_dropareabox, .redactor_dropalternative {
    color: #555;
    font-size: 12px;
}
.redactor_dropalternative {
	margin: 4px 0 2px 0;
}
.redactor_dropareabox.hover {
    border-color: #aaa;
    background: #efe3b8;
}
.redactor_dropareabox.error {
    border-color: #dcc3c3;
    background: #f7e5e5;
}
.redactor_dropareabox.drop {
    border-color: #e0e5d6;
    background: #f4f4ee;
}

/* =Progress
-----------------------------------------------------------------------------*/
.redactor-progress {
	height: 12px;
	overflow: hidden;
	background-color: #f4f4f4;
	border-radius: 3px;
	box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2);
	margin-bottom: 1.5em;
}
.redactor-progress .redactor-progress-bar {
	top: 1px;
	left: 1px;
	position: relative;
	background-color: #55aaff;
	width: 0;
	height: 12px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	-ms-box-sizing: border-box;
	box-sizing: border-box;

}
.redactor-progress-striped .redactor-progress-bar {
	background-image: url('data:image/gif;base64,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');
}
.redactor-progress-striped .redactor-progress-bar:after {
	content: "";
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA6lpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYwIDYxLjEzNDc3NywgMjAxMC8wMi8xMi0xNzozMjowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczpkYz0iaHR0cDovL3B1cmwub3JnL2RjL2VsZW1lbnRzLzEuMS8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxuczpzdFJlZj0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlUmVmIyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M1IE1hY2ludG9zaCIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDpFRTE5QjlCQTlDMkQxMUUwOUFFQ0M4MEYwM0YzNUE2RCIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDowNkRFQUIzNjlDMkUxMUUwOUFFQ0M4MEYwM0YzNUE2RCI+IDxkYzp0aXRsZT4gPHJkZjpBbHQ+IDxyZGY6bGkgeG1sOmxhbmc9IngtZGVmYXVsdCI+Z3JhZGllbnQ8L3JkZjpsaT4gPC9yZGY6QWx0PiA8L2RjOnRpdGxlPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpFRTE5QjlCODlDMkQxMUUwOUFFQ0M4MEYwM0YzNUE2RCIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpFRTE5QjlCOTlDMkQxMUUwOUFFQ0M4MEYwM0YzNUE2RCIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/Pq477Q0AAAD2SURBVHjaxFIxDsIwDLRF/1AmRp7AM9iYWHkD76AP6h9Qi1SGfqAMqGJg6XA4jts0RUwZiKLEsZ3L+Rwmoi0lDC6Ky4rAMuGO5DY5iuWH93oDegMuK8QA7JIYCMDpvwDDMBzNHCGtONYq2enjHKYLMObCp7dtu/+FDppDgyJpTemsrm/9l7L2ku4aUy4BTEmKR1hmVXV9OjfsqlqC7irAhBKxDnmOQdPc+ynKMXdenEELAFmzrnu8RoK6jpRhHkGJmFgdXmsByNf5Wx+fJPbigEI3OKrB77Bfy2VZzppqC0IfAtlIAusC9CNtUn/iIRXgnALwEWAA/+5+ZNOapmcAAAAASUVORK5CYII=');
}

/*.redactor_toolbar li:first_child, .redactor_btn_html { position: absolute !important; right: 1px !important; }*/
