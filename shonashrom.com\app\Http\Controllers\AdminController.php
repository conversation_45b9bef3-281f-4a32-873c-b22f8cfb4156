<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use App\Models\Admin;
use App\Models\CompanyData;
use App\Models\SocialMedia;
use App\Models\Webslider;
use App\Models\Services;
use App\Models\Team;
use App\Models\Testiminoals;
use App\Models\Articles;
use App\Models\About;
use App\Models\HowWeWork;
use App\Models\Categories;
use App\Models\Properties;
use App\Models\Gallery;
use App\Models\Footer;
use App\Models\User;
use App\Models\HowWorkIcons;
use App\Models\Terms;
use App\Models\Privacy;
use App\Models\FAQ;
use App\Models\Features;
use App\Models\Groups;
use App\Models\Products;
use App\Models\ProductsImags;
use App\Models\ISPResult;
use App\Models\Comments;
use App\Models\Governrate;
use App\Models\City;
use App\Models\Place;
use App\Models\ShonaDesign;
use App\Helpers\DesignHelper;
use App\Models\Orders;
use Str;
use DB;

class AdminController extends Controller
{

        //Login
        public function LoginPage(){
        return view('admin.Login');

    }

         public function Login(){

        $data= $this->validate(request(),[
             'email'=>'required|email',
             'password'=>'required|min:6|max:50',
               ],[
         ]);

      $rememberme = request('rememberme') == 1?true:false;

       if(auth()->guard('admin')->attempt(['email'=>request('email'),'password'=>request('password')],$rememberme)){
          return redirect('KlarAdmin');

       }else{
          session()->flash('error',trans('admin.incorrect_information_login'));
       	 return redirect('AdminLogin');
       }



    }

         public function Logout(){
    auth()->guard('admin')->logout();
	return redirect('AdminLogin');

 }

         public function forgotpasswordPage(){
        return view('admin.forgotpassword');

    }

         public function forgotpassword(){

                if(SpamProtector::isSpamEmail(request('email')))
                {
  return back();
}


                  $data= $this->validate(request(),[
             'email'=>'required|email',
               ],[
         ]);

         $admin = Admin::where('email',request('email'))->first();
               if(!empty($admin)){

                   $token = app('auth.password.broker')->createToken($admin);
                   $data = DB::table('password_resets')->insert([
                   	  'email'  => $admin->email,
                   	  'token'  => $token,
                   	  'created_at'  => Carbon::now(),

                   ]);




                   Mail::to($admin->email)->send(new AdminResetPassword(['data'=>$admin,'token'=>$token]));
                   session()->flash('success',trans('admin.Reset_Password'));
                     return back();

               }else{

                session()->flash('success',trans('admin.WrongEmail'));


               }

               return back();

        }

         public function reset_password($token){


             $check_token = DB::table('password_resets')->where('token',$token)->where('created_at','>',Carbon::now()->subHours(2) ) ->first();

              if(!empty($check_token)){
             return view('admin.reset_password',['data'=>$check_token]);

             }else{

             	return redirect('AdminLogin');
             }
             }

         public function reset_password_final($token){

                  $this->validate(request(),[
                  'password' => 'required|confirmed',
                  'password_confirmation' => 'required',
                      ],[
                        'password.required' => trans('admin.passwordRequired'),
                        'password_confirmation.required' => trans('admin.password_confirmationRequired'),

                  ]);
                  $check_token = DB::table('password_resets')->where('token',$token)->where('created_at','>',Carbon::now()->subHours(2) ) ->first();
                   if(!empty($check_token)){
                   	$admin = Admin::where('email', $check_token->email)->update([
                   		'email' => $check_token->email,
                   		'password' =>bcrypt(request('password'))

                   	]);
                  DB::table('password_resets')->where('email',request('email'))->delete();
                auth()->guard('admin')->attempt(['email'=>$check_token->email,'password'=>request('password')],true);
                 return redirect('KlarAdmin');

             }else{

             	return redirect('AdminLogin');
             }

               }


    //Company_Data
    public function Company_Data(){

        $Companies=CompanyData::orderBy('id','desc')->first();

         return view('admin.Settings.Default_Data',[
             'Companies'=>$Companies,
         ]);
    }

     public function AddDefaultCompanyFirst(){


         $data['Name']=null;

         CompanyData::create($data);


             return back();

    }

      public function AddDefaultCompany(){

        $data= $this->validate(request(),[
             'Name'=>'required',
              'Logo'=>'image|mimes:jpeg,png,jpg|max:2048',
              'Icon'=>'image|mimes:jpeg,png,jpg|max:2048',
               ],[
            'Name.required' => trans('admin.nameRequired'),


         ]);

          $image=request()->file('Logo');
          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='LogoImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
                   }


             if(!empty($image_url)){

                 $data['Logo']=$image_url;

             }else{
                 $data['Logo']=request('Logos');
             }


                $imagee=request()->file('Icon');
          if($imagee){
            $image_namee=Str::random(20);
            $exte=strtolower($imagee->getClientOriginalExtension());
            $image_full_namee=$image_namee .'.' . $exte ;
            $upload_pathe='IconImages/';
            $image_urle=$upload_pathe.$image_full_namee;
            $successe=$imagee->move($upload_pathe,$image_full_namee);
                   }


             if(!empty($image_urle)){

                 $data['Icon']=$image_urle;

             }else{
               $data['Icon']=request('Icons');
             }




                       $imagexs=request()->file('Logo_Store');
          if($imagexs){
            $image_namexs=Str::random(20);
            $extxs=strtolower($imagexs->getClientOriginalExtension());
            $image_full_namexs=$image_namexs .'.' . $extxs ;
            $upload_pathxs='LogoImages/';
            $image_urlxs=$upload_pathxs.$image_full_namexs;
            $successxs=$imagexs->move($upload_pathxs,$image_full_namexs);
                   }


             if(!empty($image_urlxs)){

                 $data['Logo_Store']=$image_urlxs;

             }else{
                 $data['Logo_Store']=request('Logo_StoreS');
             }



                                $imagexse=request()->file('Icon_Store');
          if($imagexse){
            $image_namexse=Str::random(20);
            $extxse=strtolower($imagexse->getClientOriginalExtension());
            $image_full_namexse=$image_namexse .'.' . $extxse ;
            $upload_pathxse='LogoImages/';
            $image_urlxse=$upload_pathxse.$image_full_namexse;
            $successxse=$imagexse->move($upload_pathxse,$image_full_namexse);
                   }


             if(!empty($image_urlxse)){

                 $data['Icon_Store']=$image_urlxse;

             }else{
                 $data['Icon_Store']=request('Icon_StoreS');
             }


         $data['Name']=request('Name');
         $data['NameEn']=request('NameEn');

         CompanyData::orderBy('id','desc')->update($data);


             session()->flash('success',trans('admin.Updated'));
             return back();

    }

               // ==== Social Media ==
            public function SocialMediaPage(){

                 $item=SocialMedia::orderBy('id','desc')->first();
        return view('admin.Settings.SocialMedia',["item"=>$item]);

    }

            public function SocialMediaUpdate($id){

          $data['Facebook'] = request('Facebook');
          $data['Twitter'] = request('Twitter');
          $data['Instagram'] = request('Instagram');
          $data['Youtube'] = request('Youtube');
          $data['Snapchat'] = request('Snapchat');
          $data['Whatsapp'] = request('Whatsapp');
          $data['Google_Plus'] = request('Google_Plus');
          $data['LinkedIn'] = request('LinkedIn');
          $data['Pinterest'] = request('Pinterest');
          $data['Telegram'] = request('Telegram');
          $data['iOS'] = request('iOS');
          $data['Android'] = request('Android');


           SocialMedia::OrderBy('id','desc')->update($data);
                session()->flash('success',trans('admin.Updated'));
                return back();



      }


    //Admin
     public function AdminsPage(){
        $items=Admin::all();
         return view('admin.Admins',['items'=>$items]);
    }

     public function AddAdmin(){



        $data= $this->validate(request(),[
             'name'=>'required',
             'email'=>'required|email|unique:admins',
             'password'=>'required|min:6',
             'image'=>'image|mimes:jpeg,png,jpg|max:2048',
               ],[
            'name.required' => trans('admin.nameRequired'),
            'email.required' => trans('admin.emailRequired'),
            'email.email' =>trans('admin.emailEmail'),
            'email.unique' =>trans('admin.emailUnique'),
            'password.required' =>trans('admin.passwordRequired'),
            'password.min' => trans('admin.passwordmin_6'),

         ]);

          $image=request()->file('image');
          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='AdminsImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
                   }


             if(!empty($image_url)){

                 $data['image']=$image_url;

             }else{
                 $data['image']=null;
             }


         $data['name']=request('name');
         $data['email']=request('email');
         $data['password']=bcrypt(request('password'));
         Admin::create($data);

         $user=Admin::orderBy('id','desc')->first();

             session()->flash('success',trans('admin.NewAddAdmin'));
             return back();

    }

     public function EditAdmin($id){

            $data= $this->validate(request(),[
             'name'=>'required',
             'email'=>'required|email|unique:admins,email,'.$id,
             'password'=>'sometimes|nullable|min:6',
             'image'=>'sometimes|nullable|image|mimes:jpeg,png,jpg|max:2048',

                  ],[
            'name.required' => trans('admin.nameRequired'),
            'email.required' => trans('admin.emailRequired'),
            'email.email' =>trans('admin.emailEmail'),
            'email.unique' =>trans('admin.emailUnique'),
            'password.required' =>trans('admin.passwordRequired'),
            'password.min' => trans('admin.passwordmin_6'),


         ]);

           $image=request()->file('image');

          if($image){

            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='AdminsImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
        }

        if(empty($image_url)){$data['image']=request('images') ; }else{$data['image']=$image_url;}

             if(request()->has('password')){

              $data['password']  =bcrypt(request('password'));

            }

            if(empty (request('password'))){

             $data['password']  = request('passwords');

        }




           $data['name']=request('name');
           $data['email']=request('email');
           Admin::where('id',$id)->update($data);


             $user=Admin::find($id);
            session()->flash('success',trans('admin.Updated'));
            return back();


     }

     public function DeleteAdmin($id){

        $del=Admin::find($id);
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }

     public function Profile(){
            $item=Admin::find(auth()->guard('admin')->user()->id);
            return view('admin.Profile',['item'=> $item]);

           }

    public function UpdateAdminProfile($id){

            $data= $this->validate(request(),[
             'name'=>'required',
             'email'=>'required|email|unique:admins,email,'.$id,
             'password'=>'sometimes|nullable|min:6',
             'image'=>'sometimes|nullable|image|mimes:jpeg,png,jpg|max:2048',

                  ],[
            'name.required' => trans('admin.nameRequired'),
            'email.required' => trans('admin.emailRequired'),
            'email.email' =>trans('admin.emailEmail'),
            'email.unique' =>trans('admin.emailUnique'),
            'password.required' =>trans('admin.passwordRequired'),
            'password.min' => trans('admin.passwordmin_6'),


         ]);

           $image=request()->file('image');

          if($image){

            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='AdminsImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
        }

        if(empty($image_url)){$data['image']=request('images') ; }else{$data['image']=$image_url;}

             if(request()->has('password')){

              $data['password']  =bcrypt(request('password'));

            }

            if(empty (request('password'))){

             $data['password']  = request('passwords');

        }





            $data['name']=request('name');
            $data['email']=request('email');



           Admin::where('id',$id)->update($data);


            session()->flash('success',trans('admin.Updated'));
            return back();


     }


            // ====  Web SLider ====

 public function Webslider(){
        $items=Webslider::all();
         return view('admin.WebSlider',['items'=>$items]);
    }

 public function AddWebslider(){

         $data= $this->validate(request(),[
             'Image'=>'required',
             'Arabic_Title'=>'required',
             'English_Title'=>'required',
             'Arabic_Desc'=>'required',
             'English_Desc'=>'required',
             'Type'=>'required',
                ],[
            'Image.required' =>trans('admin.ImageRequired'),
            'Arabic_Title.required' =>trans('admin.Arabic_TitleRequired'),
            'English_Title.required' =>trans('admin.English_TitleRequired'),
            'Arabic_Desc.required' =>trans('admin.Arabic_DescRequired'),
            'English_Desc.required' =>trans('admin.English_DescRequired'),

         ]);

          $image=request()->file('Image');

          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='WebSliderImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
        }

                  $data['Image']=$image_url;
                  $data['Type']=request('Type');

                  Webslider::create($data);
                  session()->flash('success',trans('admin.Added_Successfully'));
                  return back();
           }

 public function EditWebslider($id){
            $data= $this->validate(request(),[
             'Image'=>'sometimes|nullable',
             'Arabic_Title'=>'required',
             'English_Title'=>'required',
             'Arabic_Desc'=>'required',
             'English_Desc'=>'required',
             'Type'=>'required',

                ],[
            'Image.required' =>trans('admin.ImageRequired'),
            'Arabic_Title.required' =>trans('admin.Arabic_TitleRequired'),
            'English_Title.required' =>trans('admin.English_TitleRequired'),
            'Arabic_Desc.required' =>trans('admin.Arabic_DescRequired'),
            'English_Desc.required' =>trans('admin.English_DescRequired'),

         ]);

        $image=request()->file('Image');

          if($image){

            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='WebSliderImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
        }

        if(empty($image_url)){$data['Image']=request('Images') ; }else{$data['Image']=$image_url;}

                  $data['Type']=request('Type');
            Webslider::where('id',$id)->update($data);
            session()->flash('success',trans('admin.Updated'));
            return back();

}

 public function DeleteWebslider($id){

        $del=Webslider::find($id);
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }

                // ====  Services ====

 public function Services(){
        $items=Services::all();
         return view('admin.Services',['items'=>$items]);
    }

 public function AddServices(){

         $data= $this->validate(request(),[
             'Icon'=>'required',
             'Arabic_Title'=>'required',
             'English_Title'=>'required',
             'Arabic_Desc'=>'required',
             'English_Desc'=>'required',


                ],[

            'Arabic_Title.required' =>trans('admin.Arabic_TitleRequired'),
            'English_Title.required' =>trans('admin.English_TitleRequired'),
            'Arabic_Desc.required' =>trans('admin.Arabic_DescRequired'),
            'English_Desc.required' =>trans('admin.English_DescRequired'),

         ]);



               $image=request()->file('Icon');

          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='WebSliderImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
        }

                  $data['Icon']=$image_url;




                  Services::create($data);
                  session()->flash('success',trans('admin.Added_Successfully'));
                  return back();
           }

 public function EditServices($id){
            $data= $this->validate(request(),[

             'Arabic_Title'=>'required',
             'English_Title'=>'required',
             'Arabic_Desc'=>'required',
             'English_Desc'=>'required',


                ],[
            'Image.required' =>trans('admin.ImageRequired'),
            'Arabic_Title.required' =>trans('admin.Arabic_TitleRequired'),
            'English_Title.required' =>trans('admin.English_TitleRequired'),
            'Arabic_Desc.required' =>trans('admin.Arabic_DescRequired'),
            'English_Desc.required' =>trans('admin.English_DescRequired'),

         ]);


             $image=request()->file('Icon');

          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='WebSliderImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
        }


   if(empty($image_url)){$data['Icon']=request('Images') ; }else{$data['Icon']=$image_url;}






            Services::where('id',$id)->update($data);
            session()->flash('success',trans('admin.Updated'));
            return back();

}

 public function DeleteServices($id){

        $del=Services::find($id);
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }






                // ====  Testiminoals ====

 public function Testiminoals(){
        $items=Testiminoals::all();
         return view('admin.Testiminoals',['items'=>$items]);
    }

 public function AddTestiminoals(){

         $data= $this->validate(request(),[
             'Image'=>'required',
             'Arabic_Name'=>'required',
             'English_Name'=>'required',
             'Arabic_Job'=>'required',
             'English_Job'=>'required',
                    'Arabic_Desc'=>'required',
             'English_Desc'=>'required',

                ],[


         ]);

 $image=request()->file('Image');

          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='TestiImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
        }

                  $data['Image']=$image_url;


                  Testiminoals::create($data);
                  session()->flash('success',trans('admin.Added_Successfully'));
                  return back();
           }

 public function EditTestiminoals($id){
              $data= $this->validate(request(),[
                   'Image'=>'sometimes|nullable',
             'Arabic_Name'=>'required',
             'English_Name'=>'required',
             'Arabic_Job'=>'required',
             'English_Job'=>'required',
                    'Arabic_Desc'=>'required',
             'English_Desc'=>'required',

                ],[


         ]);


        $image=request()->file('Image');

          if($image){

            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='TestiImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
        }

        if(empty($image_url)){$data['Image']=request('Images') ; }else{$data['Image']=$image_url;}

            Testiminoals::where('id',$id)->update($data);
            session()->flash('success',trans('admin.Updated'));
            return back();

}

 public function DeleteTestiminoals($id){

        $del=Testiminoals::find($id);
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }

                  //Articles
     public function ArticlesPage(){
        $items=Articles::all();
         return view('admin.Articles',['items'=>$items]);
    }

     public function AddArticles(){



        $data= $this->validate(request(),[
             'Arabic_Title'=>'required',
             'English_Title'=>'required',
             'Arabic_Desc'=>'required',
             'English_Desc'=>'required',
             'Date'=>'required',
             'Category'=>'required',
             'Author'=>'required',
             'Image'=>'image|mimes:jpeg,png,jpg|max:2048',
             'Sub_Image'=>'image|mimes:jpeg,png,jpg|max:2048',
               ],[


         ]);

          $image=request()->file('Image');
          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='ArticlesImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
                   }


             if(!empty($image_url)){

                 $data['Image']=$image_url;

             }else{
                 $data['Image']=null;
             }


              $imagee=request()->file('Sub_Image');
          if($imagee){
            $image_namee=Str::random(20);
            $exte=strtolower($imagee->getClientOriginalExtension());
            $image_full_namee=$image_namee .'.' . $exte ;
            $upload_pathe='ArticlesImages/';
            $image_urle=$upload_pathe.$image_full_namee;
            $successe=$imagee->move($upload_pathe,$image_full_namee);
                   }


             if(!empty($image_urle)){

                 $data['Sub_Image']=$image_urle;

             }else{
                 $data['Sub_Image']=null;
             }

         $data['Arabic_Title']=request('Arabic_Title');
         $data['English_Title']=request('English_Title');
         $data['Arabic_Desc']=request('Arabic_Desc');
         $data['English_Desc']=request('English_Desc');
         $data['Date']=request('Date');
         $data['Category']=request('Category');
         $data['Author']=request('Author');

         Articles::create($data);


             session()->flash('success',trans('admin.Added_Successfully'));
             return back();

    }

     public function EditArticles($id){

         $data= $this->validate(request(),[
             'Arabic_Title'=>'required',
             'English_Title'=>'required',
             'Arabic_Desc'=>'required',
             'English_Desc'=>'required',
             'Date'=>'required',
             'Category'=>'required',
             'Author'=>'required',
             'Image'=>'image|mimes:jpeg,png,jpg|max:2048',
             'Sub_Image'=>'image|mimes:jpeg,png,jpg|max:2048',
               ],[


         ]);

           $image=request()->file('Image');

          if($image){

            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='ArticlesImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
        }

        if(empty($image_url)){$data['Image']=request('Images') ; }else{$data['Image']=$image_url;}

          $imagee=request()->file('Sub_Image');
          if($imagee){
            $image_namee=Str::random(20);
            $exte=strtolower($imagee->getClientOriginalExtension());
            $image_full_namee=$image_namee .'.' . $exte ;
            $upload_pathe='ArticlesImages/';
            $image_urle=$upload_pathe.$image_full_namee;
            $successe=$imagee->move($upload_pathe,$image_full_namee);
                   }


             if(empty($image_urle)){$data['Sub_Image']=request('Sub_Images') ; }else{$data['Sub_Image']=$image_urle;}

         $data['Arabic_Title']=request('Arabic_Title');
         $data['English_Title']=request('English_Title');
         $data['Arabic_Desc']=request('Arabic_Desc');
         $data['English_Desc']=request('English_Desc');
         $data['Date']=request('Date');
         $data['Category']=request('Category');
         $data['Author']=request('Author');


           Articles::where('id',$id)->update($data);



            session()->flash('success',trans('admin.Updated'));
            return back();


     }

     public function DeleteArticles($id){

        $del=Articles::find($id);
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }

        //About
    public function About(){

        $Companies=About::orderBy('id','desc')->first();

         return view('admin.About',[
             'Companies'=>$Companies,


         ]);
    }

      public function AddAbout(){

        $data= $this->validate(request(),[

             'Arabic_Desc'=>'required',
             'English_Desc'=>'required',



               ],[


         ]);


  $image=request()->file('Image');
          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='AboutImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
                   }


             if(!empty($image_url)){

                 $data['Image']=$image_url;

             }else{
                 $data['Image']=request('Images');
             }




         $data['Arabic_Desc']=request('Arabic_Desc');
         $data['English_Desc']=request('English_Desc');
         $data['Arabic_Title']=request('Arabic_Title');
         $data['English_Title']=request('English_Title');


         About::orderBy('id','desc')->update($data);


             session()->flash('success',trans('admin.Updated'));
             return back();

    }


    //ISPResult

        public function ISPAdmin(){

        $Companies=ISPResult::orderBy('id','desc')->first();

         return view('admin.ISP',[
             'Companies'=>$Companies,


         ]);
    }

      public function AddISP(){

        $data= $this->validate(request(),[

             'Arabic_Desc'=>'required',
             'English_Desc'=>'required',



               ],[


         ]);


  $image=request()->file('Image');
          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='AboutImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
                   }


             if(!empty($image_url)){

                 $data['Image']=$image_url;

             }else{
                 $data['Image']=request('Images');
             }




         $data['Arabic_Desc']=request('Arabic_Desc');
         $data['English_Desc']=request('English_Desc');
         $data['Arabic_Title']=request('Arabic_Title');
         $data['English_Title']=request('English_Title');


         ISPResult::orderBy('id','desc')->update($data);


             session()->flash('success',trans('admin.Updated'));
             return back();

    }




            //HowWeWork
    public function HowWeWork(){

        $Companies=HowWeWork::orderBy('id','desc')->first();
        $items=HowWorkIcons::orderBy('id','asc')->get();

         return view('admin.HowWeWork',[
             'Companies'=>$Companies,
             'items'=>$items,
         ]);
    }

      public function AddHowWeWork(){

        $data= $this->validate(request(),[
             'Arabic_Title'=>'required',
             'English_Title'=>'required',
             'Arabic_Desc'=>'required',
             'English_Desc'=>'required',


               ],[


         ]);



           $image=request()->file('Image');
          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='AboutImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
                   }


             if(!empty($image_url)){

                 $data['Image']=$image_url;

             }else{
                 $data['Image']=request('Images');
             }

         $data['Arabic_Title']=request('Arabic_Title');
         $data['English_Title']=request('English_Title');
         $data['Arabic_Desc']=request('Arabic_Desc');
         $data['English_Desc']=request('English_Desc');


         HowWeWork::orderBy('id','desc')->update($data);


             session()->flash('success',trans('admin.Updated'));
             return back();

    }

      public function EditHowWorkIcons($id){

        $data= $this->validate(request(),[

             'Arabic_Title'=>'required',
             'English_Title'=>'required',
             'Arabic_Desc'=>'required',
             'English_Desc'=>'required',


               ],[


         ]);


                 $image=request()->file('Icon');

          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='WebSliderImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
        }


   if(empty($image_url)){$data['Icon']=request('Images') ; }else{$data['Icon']=$image_url;}



         $data['Arabic_Title']=request('Arabic_Title');
         $data['English_Title']=request('English_Title');
         $data['Arabic_Desc']=request('Arabic_Desc');
         $data['English_Desc']=request('English_Desc');


         HowWorkIcons::where('id',$id)->update($data);


             session()->flash('success',trans('admin.Updated'));
             return back();

    }




    //Categories
 public function Categories(){
        $items=Categories::all();
         return view('admin.Categories',['items'=>$items]);
    }

 public function AddCategories(){

         $data= $this->validate(request(),[

             'Arabic_Name'=>'required',
             'English_Name'=>'required',


                ],[


         ]);



                  Categories::create($data);
                  session()->flash('success',trans('admin.Added_Successfully'));
                  return back();
           }

 public function EditCategories($id){
              $data= $this->validate(request(),[

             'Arabic_Name'=>'required',
             'English_Name'=>'required',


                ],[


         ]);



            Categories::where('id',$id)->update($data);
            session()->flash('success',trans('admin.Updated'));
            return back();

}

 public function DeleteCategories($id){

        $del=Categories::find($id);
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }


        //FAQ
 public function FAQ(){
        $items=FAQ::all();
         return view('admin.FAQ',['items'=>$items]);
    }

 public function AddFAQ(){

         $data= $this->validate(request(),[

             'Arabic_Question'=>'required',
             'English_Question'=>'required',
             'Arabic_Answer'=>'required',
             'English_Answer'=>'required',


                ],[


         ]);


     $data['Arabic_Question']=request('Arabic_Question');
     $data['English_Question']=request('English_Question');
     $data['Arabic_Answer']=request('Arabic_Answer');
     $data['English_Answer']=request('English_Answer');

                  FAQ::create($data);
                  session()->flash('success',trans('admin.Added_Successfully'));
                  return back();
           }

 public function EditFAQ($id){
              $data= $this->validate(request(),[

             'Arabic_Question'=>'required',
             'English_Question'=>'required',
             'Arabic_Answer'=>'required',
             'English_Answer'=>'required',


                ],[


         ]);


     $data['Arabic_Question']=request('Arabic_Question');
     $data['English_Question']=request('English_Question');
     $data['Arabic_Answer']=request('Arabic_Answer');
     $data['English_Answer']=request('English_Answer');


            FAQ::where('id',$id)->update($data);
            session()->flash('success',trans('admin.Updated'));
            return back();

}

 public function DeleteFAQ($id){

        $del=FAQ::find($id);
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }



        //Gallery
     public function Gallery(){
        $items=Gallery::get();
           $Categories=Categories::all();
         return view('admin.Gallery',['items'=>$items,'Categories'=>$Categories]);
    }

     public function AddGallery(){

         $data= $this->validate(request(),[

             'Image'=>'required',

                ],[
            'Image.required' =>trans('admin.ImageRequired'),


         ]);


              $image=request()->file('Image');
          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='ServicesImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
                   }


             if(!empty($image_url)){

                 $data['Image']=$image_url;

             }else{
                 $data['Image']=null;
             }

      $data['Category']=request('Category');
      $data['Arabic_Name']=request('Arabic_Name');
      $data['English_Name']=request('English_Name');
      $data['Links']=request('Links');
                  Gallery::create($data);
                  session()->flash('success',trans('admin.Added_Successfully'));
                  return back();
           }

 public function EditGallery($id){


            $image=request()->file('Image');
          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='ServicesImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
                   }


             if(!empty($image_url)){

                 $data['Image']=$image_url;

             }else{
                 $data['Image']=request('Images');
             }

                $data['Category']=request('Category');
                $data['Arabic_Name']=request('Arabic_Name');
                $data['English_Name']=request('English_Name');
                $data['Links']=request('Links');
            Gallery::where('id',$id)->update($data);
            session()->flash('success',trans('admin.Updated'));
            return back();

}

 public function DeleteGallery($id){

        $del=Gallery::find($id);
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }




                //Footer
    public function Footer(){

        $Companies=Footer::orderBy('id','desc')->first();

         return view('admin.Footer',[
             'Companies'=>$Companies,
         ]);
    }


      public function FooterUpdate(){

        $data= $this->validate(request(),[
             'Arabic_Desc'=>'required',
             'English_Desc'=>'required',
             'Arabic_Address'=>'required',
             'English_Address'=>'required',
             'Phone'=>'required',
             'Phone2'=>'required',
             'Email'=>'required',

               ],[


         ]);




         $data['Arabic_Desc']=request('Arabic_Desc');
         $data['English_Desc']=request('English_Desc');
         $data['Arabic_Address']=request('Arabic_Address');
         $data['English_Address']=request('English_Address');
         $data['Phone']=request('Phone');
         $data['Phone2']=request('Phone2');
         $data['Email']=request('Email');
         $data['Video']=request('Video');
         $data['Map']=request('Map');


         Footer::orderBy('id','desc')->update($data);


             session()->flash('success',trans('admin.Updated'));
             return back();

    }


//Client
    public function Client(){
        $items=User::paginate(20);
         return view('admin.Client',['items'=>$items]);
    }

     public function AddClient(){



        $data= $this->validate(request(),[
             'name'=>'required',
             'phone'=>'required',
             'email'=>'required|email|unique:users',
             'password'=>'required|min:6',
             'link'=>'required',
             'product'=>'required',

               ],[
            'name.required' => trans('admin.nameRequired'),
            'email.required' => trans('admin.emailRequired'),
            'email.email' =>trans('admin.emailEmail'),
            'email.unique' =>trans('admin.emailUnique'),
            'password.required' =>trans('admin.passwordRequired'),
            'password.min' => trans('admin.passwordmin_6'),

         ]);


         $data['name']=request('name');
         $data['email']=request('email');
         $data['password']=bcrypt(request('password'));
           $data['phone']=request('phone');
           $data['link']=request('link');
           $data['price']=request('price');
           $data['subscribe_type']=request('subscribe_type');
           $data['expire_date']=request('expire_date');
           $data['product']=request('product');
         User::create($data);


             session()->flash('success',trans('admin.Added_Successfully'));
             return back();

    }

     public function EditClient($id){

            $data= $this->validate(request(),[
             'name'=>'required',
             'email'=>'required|email|unique:users,email,'.$id,
             'password'=>'sometimes|nullable|min:6',
                'link'=>'required',
             'product'=>'required',

                  ],[
            'name.required' => trans('admin.nameRequired'),
            'email.required' => trans('admin.emailRequired'),
            'email.email' =>trans('admin.emailEmail'),
            'email.unique' =>trans('admin.emailUnique'),
            'password.required' =>trans('admin.passwordRequired'),
            'password.min' => trans('admin.passwordmin_6'),


         ]);


             if(request()->has('password')){

              $data['password']  =bcrypt(request('password'));

            }

            if(empty (request('password'))){

             $data['password']  = request('passwords');

        }




           $data['name']=request('name');
           $data['email']=request('email');
            $data['phone']=request('phone');
           $data['link']=request('link');
           $data['price']=request('price');
           $data['subscribe_type']=request('subscribe_type');
           $data['expire_date']=request('expire_date');
           $data['product']=request('product');
           User::where('id',$id)->update($data);


            session()->flash('success',trans('admin.Updated'));
            return back();


     }

     public function DeleteClient($id){

        $del=User::find($id);
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }







//Terms

    public function Terms(){

        $Companies=Terms::orderBy('id','desc')->first();

         return view('admin.Terms',[
             'Companies'=>$Companies,


         ]);
    }

      public function AddTerms(){

        $data= $this->validate(request(),[

             'Arabic_Desc'=>'required',
             'English_Desc'=>'required',



               ],[


         ]);






         $data['Arabic_Desc']=request('Arabic_Desc');
         $data['English_Desc']=request('English_Desc');

         Terms::orderBy('id','desc')->update($data);


             session()->flash('success',trans('admin.Updated'));
             return back();

    }




//Privacy

    public function Privacy(){

        $Companies=Privacy::orderBy('id','desc')->first();

         return view('admin.Privacy',[
             'Companies'=>$Companies,


         ]);
    }

      public function AddPrivacy(){

        $data= $this->validate(request(),[

             'Arabic_Desc'=>'required',
             'English_Desc'=>'required',



               ],[


         ]);






         $data['Arabic_Desc']=request('Arabic_Desc');
         $data['English_Desc']=request('English_Desc');

         Privacy::orderBy('id','desc')->update($data);


             session()->flash('success',trans('admin.Updated'));
             return back();

    }



//UpdateProfile


             public function UpdateProfile(){

         $id=auth()->user()->id;

            $data= $this->validate(request(),[
             'name'=>'required',
             'email'=>'required|email|unique:users,email,'.$id,


                  ],[
            'name.required' => trans('admin.nameRequired'),
            'email.required' => trans('admin.emailRequired'),
            'email.email' =>trans('admin.emailEmail'),
            'email.unique' =>trans('admin.emailUnique'),
            'password.required' =>trans('admin.passwordRequired'),
            'password.min' => trans('admin.passwordmin_6'),


         ]);


         $user=User::find(auth()->user()->id);
             if(!empty(request('password'))){



                    if (Hash::check(request('password'),$user->password)) {


                        if(!empty(request('new_password'))){
                       $dataX['password']=bcrypt(request('new_password'));

                User::where('id',auth()->user()->id)->update($dataX);
                        }

             }else{

                         session()->flash('error',trans('admin.CurrentPassWrong'));
             return back();
             }



            }



           $data['name']=request('name');
           $data['email']=request('email');
         $data['phone']=request('phone');
           User::where('id',$id)->update($data);

            session()->flash('success',trans('admin.Updated'));
            return back();


     }



        //Features
     public function Features(){
        $items=Features::all();
         return view('admin.Features',['items'=>$items]);
    }

 public function EditFeatures($id){
           $data= $this->validate(request(),[

             'Arabic_Title'=>'required',
             'English_Title'=>'required',



                ],[


         ]);





     $data['Arabic_Title']=request('Arabic_Title');
     $data['English_Title']=request('English_Title');
             $image=request()->file('Icon');

          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='WebSliderImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
        }


   if(empty($image_url)){$data['Icon']=request('Images') ; }else{$data['Icon']=$image_url;}






            Features::where('id',$id)->update($data);
            session()->flash('success',trans('admin.Updated'));
            return back();

}



    //Groups
        public function Groups(){
            $items=Groups::all();
         return view('admin.Groups',['items'=>$items]);
    }

         public function AddGroups(){



        $data= $this->validate(request(),[
             'Name'=>'required',
             'NameEn'=>'required',

               ],[


         ]);

          $image=request()->file('Image');
          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='ArticlesImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
                   }


             if(!empty($image_url)){

                 $data['Image']=$image_url;

             }else{
                 $data['Image']=null;
             }



         $data['Name']=request('Name');
         $data['NameEn']=request('NameEn');


         Groups::create($data);


             session()->flash('success',trans('admin.Added_Successfully'));
             return back();

    }

     public function EditGroups($id){

           $data= $this->validate(request(),[
             'Name'=>'required',
             'NameEn'=>'required',

               ],[


         ]);

          $image=request()->file('Image');
          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='ArticlesImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
                   }


             if(!empty($image_url)){

                 $data['Image']=$image_url;

             }else{
                 $data['Image']=request('Images');
             }



         $data['Name']=request('Name');
         $data['NameEn']=request('NameEn');


           Groups::where('id',$id)->update($data);



            session()->flash('success',trans('admin.Updated'));
            return back();


     }

     public function DeleteGroups($id){

        $del=Groups::find($id);
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }






    //SyncGroups
       public function SyncGroups(){


           Groups::truncate();
          $this->ApproveSyncGroups();
         return redirect('Groups');


    }

    private function ApproveSyncGroups(){

               $curl = curl_init();

curl_setopt_array($curl, array(
  CURLOPT_URL => 'https://shonashrom.com/public/api/GroupsOstegy',
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'GET',
));

$response = curl_exec($curl);

curl_close($curl);
           $xs=json_decode($response,true);


           for($i=0; $i < count($xs['data']) ; $i++){

            $data['Group_ID']=$xs['data'][$i]['id'];
            $data['Name']=$xs['data'][$i]['name'];
            $data['NameEn']=$xs['data'][$i]['nameEn'];
            $data['Image']=$xs['data'][$i]['image'];


            Groups::create($data);


           }

         return  $x=session()->flash('success','Syncnoraztion Successfully') ;

    }




    //Govenrate
           public function Govenrate(){
            $items=Governrate::all();
         return view('admin.Govenrate',['items'=>$items]);
    }

         public function AddGovenrate(){



        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
             'English_Name'=>'required',

               ],[


         ]);



         $data['Arabic_Name']=request('Arabic_Name');
         $data['English_Name']=request('English_Name');


         Governrate::create($data);


             session()->flash('success',trans('admin.Added_Successfully'));
             return back();

    }

     public function EditGovenrate($id){

         $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
             'English_Name'=>'required',

               ],[


         ]);



         $data['Arabic_Name']=request('Arabic_Name');
         $data['English_Name']=request('English_Name');


           Governrate::where('id',$id)->update($data);



            session()->flash('success',trans('admin.Updated'));
            return back();


     }

     public function DeleteGovenrate($id){

        $del=Governrate::find($id);
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }







//SyncGovenrate
           public function SyncGovenrate(){


           Governrate::truncate();
          $this->ApproveSyncGovenrate();
         return redirect('Govenrate');


    }

    private function ApproveSyncGovenrate(){

               $curl = curl_init();

curl_setopt_array($curl, array(
  CURLOPT_URL => 'https://shonashrom.com/public/api/GovenrateOstegy',
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'GET',
));

$response = curl_exec($curl);

curl_close($curl);
           $xs=json_decode($response,true);


           for($i=0; $i < count($xs['data']) ; $i++){

            $data['GOV_ID']=$xs['data'][$i]['id'];
            $data['Arabic_Name']=$xs['data'][$i]['arabic_name'];
            $data['English_Name']=$xs['data'][$i]['english_name'];


            Governrate::create($data);


           }

         return  $x=session()->flash('success','Syncnoraztion Successfully') ;

    }

   // City
             public function City($id){
            $items=City::where('GOV_ID',$id)->get();
            $GOV_ID=$id;
         return view('admin.City',['items'=>$items,'GOV_ID'=>$GOV_ID]);
    }

             public function AddCity(){



        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
             'English_Name'=>'required',

               ],[


         ]);



         $data['Arabic_Name']=request('Arabic_Name');
         $data['English_Name']=request('English_Name');
         $data['Ship_Price']=request('Ship_Price');
         $data['GOV_ID']=request('GOV_ID');


         City::create($data);


             session()->flash('success',trans('admin.Added_Successfully'));
             return back();

    }

     public function EditGCity($id){

         $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
             'English_Name'=>'required',

               ],[


         ]);



         $data['Arabic_Name']=request('Arabic_Name');
         $data['English_Name']=request('English_Name');
         $data['Ship_Price']=request('Ship_Price');
         $data['GOV_ID']=request('GOV_ID');


           City::where('id',$id)->update($data);



            session()->flash('success',trans('admin.Updated'));
            return back();


     }

     public function DeleteGCity($id){

        $del=City::find($id);
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }







//SyncCity

               public function SyncCity(){


           City::truncate();
          $this->ApproveSyncCity();
         return back();


    }

    private function ApproveSyncCity(){

               $curl = curl_init();

curl_setopt_array($curl, array(
  CURLOPT_URL => 'https://shonashrom.com/public/api/CityOstegy',
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'GET',
));

$response = curl_exec($curl);

curl_close($curl);
           $xs=json_decode($response,true);


           for($i=0; $i < count($xs['data']) ; $i++){

            $data['GOV_ID']=$xs['data'][$i]['gov'];
            $data['CIT_ID']=$xs['data'][$i]['id'];
                $data['Ship_Price']=$xs['data'][$i]['ship'];
            $data['Arabic_Name']=$xs['data'][$i]['arabic_name'];
            $data['English_Name']=$xs['data'][$i]['english_name'];


            City::create($data);


           }

         return  $x=session()->flash('success','Syncnoraztion Successfully') ;

    }




    //Place
          public function Place($id){
            $items=Place::where('CIT_ID',$id)->get();
              $CIT_ID=$id;
         return view('admin.Place',['items'=>$items,'CIT_ID'=>$CIT_ID]);
    }

             public function AddPlace(){



        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
             'English_Name'=>'required',

               ],[


         ]);



         $data['Arabic_Name']=request('Arabic_Name');
         $data['English_Name']=request('English_Name');
         $data['Ship_Price']=request('Ship_Price');
         $data['CIT_ID']=request('CIT_ID');


         Place::create($data);


             session()->flash('success',trans('admin.Added_Successfully'));
             return back();

    }

     public function EditPlace($id){

         $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
             'English_Name'=>'required',

               ],[


         ]);



         $data['Arabic_Name']=request('Arabic_Name');
         $data['English_Name']=request('English_Name');
         $data['Ship_Price']=request('Ship_Price');
         $data['CIT_ID']=request('CIT_ID');


           Place::where('id',$id)->update($data);



            session()->flash('success',trans('admin.Updated'));
            return back();


     }

     public function DeletePlace($id){

        $del=Place::find($id);
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }








//SyncPlace
                 public function SyncPlace(){


           Place::truncate();
          $this->ApproveSyncPlace();
         return back();


    }

    private function ApproveSyncPlace(){

               $curl = curl_init();

curl_setopt_array($curl, array(
  CURLOPT_URL => 'https://shonashrom.com/public/api/PlaceOstegy',
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'GET',
));

$response = curl_exec($curl);

curl_close($curl);
           $xs=json_decode($response,true);


           for($i=0; $i < count($xs['data']) ; $i++){

            $data['PLACE_ID']=$xs['data'][$i]['id'];
            $data['CIT_ID']=$xs['data'][$i]['cit'];
            $data['Ship_Price']=$xs['data'][$i]['ship'];
            $data['Arabic_Name']=$xs['data'][$i]['arabic_name'];
            $data['English_Name']=$xs['data'][$i]['english_name'];


            Place::create($data);


           }

         return  $x=session()->flash('success','Syncnoraztion Successfully') ;

    }





    //Products
     public function Products(){
            $items=Products::all();
            $Groups=Groups::all();
         return view('admin.Products',['items'=>$items,'Groups'=>$Groups]);
    }

         public function AddProducts(Request $request){



        $data= $this->validate(request(),[
             'P_Ar_Name'=>'required',
             'Price'=>'required',
             'Ar_Desc'=>'required',
             'Ar_Spec'=>'required',
             'Type'=>'required',
             'Ar_Sup_Desc'=>'required',

               ],[

         ]);


                           $image=request()->file('Image');
          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='ServicesImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
                   }


             if(!empty($image_url)){

                 $data['Image']=$image_url;

             }else{
                 $data['Image']=null;
             }

         $data['P_Ar_Name']=request('P_Ar_Name');
         $data['P_En_Name']=request('P_En_Name');
         $data['Symbol']=request('Symbol');
         $data['Offer_Price']=request('Offer_Price');
         $data['Price']=request('Price');
         $data['Pro_ID']=request('Pro_ID');
         $data['Group_ID']=request('Group_ID');
         $data['Ar_Desc']=request('Ar_Desc');
         $data['En_Desc']=request('En_Desc');
         $data['Ar_Spec']=request('Ar_Spec');
         $data['En_Spec']=request('En_Spec');
         $data['Type']=request('Type');
         $data['Ar_Sup_Desc']=request('Ar_Sup_Desc');
         $data['En_Sup_Desc']=request('En_Sup_Desc');

         Products::create($data);
         $last=Products::orderBy('id','desc')->first();

                            if(request()->hasFile('Sub_Image')){
foreach ($request->Sub_Image as $photo) {
$filename = $photo->store('ProductImagesCatlouge');
ProductsImags::create([
'Pro_ID' => $last->id,
'Image' => $filename
]);
}
     }

             session()->flash('success',trans('admin.Added_Successfully'));
             return back();

    }

     public function EditProducts(Request $request,$id){

            $data= $this->validate(request(),[
             'P_Ar_Name'=>'required',
             'Price'=>'required',
             'Ar_Desc'=>'required',
             'Ar_Spec'=>'required',
             'Type'=>'required',
             'Ar_Sup_Desc'=>'required',

               ],[

         ]);


                    $image=request()->file('Image');
          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='ServicesImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
                   }


             if(!empty($image_url)){

                 $data['Image']=$image_url;

             }else{
                 $data['Image']=request('Images');
             }
         $data['P_Ar_Name']=request('P_Ar_Name');
         $data['P_En_Name']=request('P_En_Name');
         $data['Symbol']=request('Symbol');
         $data['Offer_Price']=request('Offer_Price');
         $data['Price']=request('Price');
         $data['Pro_ID']=request('Pro_ID');
         $data['Group_ID']=request('Group_ID');
         $data['Ar_Desc']=request('Ar_Desc');
         $data['En_Desc']=request('En_Desc');
         $data['Ar_Spec']=request('Ar_Spec');
         $data['En_Spec']=request('En_Spec');
         $data['Type']=request('Type');
         $data['Ar_Sup_Desc']=request('Ar_Sup_Desc');
         $data['En_Sup_Desc']=request('En_Sup_Desc');


           Products::where('id',$id)->update($data);


              if(request()->hasFile('Sub_Image')){
foreach ($request->Sub_Image as $photo) {
$filename = $photo->store('ProductImagesCatlouge');
ProductsImags::create([
'Pro_ID' => $id,
'Image' => $filename
]);
}
     }


            session()->flash('success',trans('admin.Updated'));
            return back();


     }

     public function DeleteProducts($id){

        $del=Products::find($id);
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }


    public function DelSupImg($id){

        $del=ProductsImags::find($id);
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }




    //SyncProducts
       public function SyncProducts(){


           Products::truncate();
           ProductsImags::truncate();
          $this->ApproveSyncProducts();
         return redirect('Products');


    }

        private function ApproveSyncProducts(){

               $curl = curl_init();

curl_setopt_array($curl, array(
  CURLOPT_URL => 'https://shonashrom.com/public/api/ProductsOstegy',
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'GET',
));

$response = curl_exec($curl);

curl_close($curl);
           $xs=json_decode($response,true);


           for($i=0; $i < count($xs['data']) ; $i++){

            $data['Image']=$xs['data'][$i]['image'];
            $data['P_Ar_Name']=$xs['data'][$i]['name'];
            $data['P_En_Name']=$xs['data'][$i]['nameEn'];
            $data['Symbol']=$xs['data'][$i]['symbol'];
            $data['Offer_Price']=$xs['data'][$i]['offer_price'];
            $data['Price']=$xs['data'][$i]['price'];
            $data['Pro_ID']=$xs['data'][$i]['id'];
            $data['Group_ID']=$xs['data'][$i]['group'];
            $data['Ar_Desc']=$xs['data'][$i]['ar_desc'];
            $data['En_Desc']=$xs['data'][$i]['en_desc'];
            $data['Ar_Spec']=$xs['data'][$i]['ar_spec'];
            $data['En_Spec']=$xs['data'][$i]['en_spec'];
            $data['Type']=$xs['data'][$i]['store_type'];
            $data['Ar_Sup_Desc']=$xs['data'][$i]['arabic_brief_desc'];
            $data['En_Sup_Desc']=$xs['data'][$i]['english_brief_desc'];
            Products::create($data);

               if(!empty($xs['data'][$i]['sup_images'])){


               for($y=0; $y < count($xs['data'][$i]['sup_images']) ; $y++){

                         $dataX['Pro_ID']=$xs['data'][$i]['sup_images'][$y]['pro_id'];
                         $dataX['Image']='http://localhost:8888/laravel/ERP/public/storage/'.$xs['data'][$i]['sup_images'][$y]['sup_img '];
               ProductsImags::create($dataX);
               }

               }





           }



         return  $x=session()->flash('success','Syncnoraztion Successfully') ;

    }



    //Comments
         public function CommentsAdmin(){
            $items=Comments::orderBy('id','desc')->paginate(20);
         return view('admin.Comments',['items'=>$items]);
    }

         public function DeleteComment($id){

        $del=Comments::find($id);
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }

                 public function ApproveCommentsAdmin($id){

        Comments::where('id',$id)->update(['Status'=>1]);

        session()->flash('success',trans('admin.Updated'));
        return back();

           }

                     public function UnApproveCommentsAdmin($id){

        Comments::where('id',$id)->update(['Status'=>0]);

        session()->flash('success',trans('admin.Updated'));
        return back();

           }


    //OrdersAdmin
     public function OrdersAdmin(){
            $items=Orders::orderBy('id','desc')->paginate(20);
         return view('admin.Orders',['items'=>$items]);
    }

    //Design System
    public function DesignSystem(){
        $design = DesignHelper::getCurrentDesign();
        return view('admin.DesignSystem', ['design' => $design]);
    }

    public function UpdateDesignSystem(){
        $data = $this->validate(request(), [
            'Font_Type' => 'required|integer',
            'Body_BG_Type' => 'required|integer',
            'Body_BG_Color' => 'required|string',
            'Header_BG_Color' => 'required|string',
            'Header_Txt_Color' => 'required|string',
            'Navbar_BG_Color' => 'required|string',
            'Navbar_Txt_Color' => 'required|string',
            'Primary_Button_BG_Color' => 'required|string',
            'Primary_Button_Txt_Color' => 'required|string',
            'Footer_BG_Color' => 'required|string',
            'Footer_Txt_Color' => 'required|string',
            'Show_Preloader' => 'boolean',
            'Show_Breadcrumbs' => 'boolean',
            'Show_Social_Links' => 'boolean',
            'Show_Contact_Info' => 'boolean',
            'Enable_Animations' => 'boolean',
            'Enable_RTL_Support' => 'boolean',
        ]);

        // Handle background image upload
        $image = request()->file('Body_BG_Image');
        if ($image) {
            $image_name = Str::random(20);
            $ext = strtolower($image->getClientOriginalExtension());
            $image_full_name = $image_name . '.' . $ext;
            $upload_path = 'LogoImages/';
            $image_url = $upload_path . $image_full_name;
            $success = $image->move($upload_path, $image_full_name);

            if ($success) {
                $data['Body_BG_Image'] = $image_url;
            }
        }

        // Convert boolean fields
        $data['Show_Preloader'] = request()->has('Show_Preloader');
        $data['Show_Breadcrumbs'] = request()->has('Show_Breadcrumbs');
        $data['Show_Social_Links'] = request()->has('Show_Social_Links');
        $data['Show_Contact_Info'] = request()->has('Show_Contact_Info');
        $data['Enable_Animations'] = request()->has('Enable_Animations');
        $data['Enable_RTL_Support'] = request()->has('Enable_RTL_Support');

        // Update or create design configuration
        $design = ShonaDesign::orderBy('id', 'desc')->first();
        if ($design) {
            $design->update($data);
        } else {
            ShonaDesign::create($data);
        }

        session()->flash('success', trans('admin.Updated'));
        return back();
    }

}
