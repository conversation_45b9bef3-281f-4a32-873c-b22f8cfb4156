@php
use App\Helpers\DesignHelper;

$design = $design ?? DesignHelper::getCurrentDesign();
$fonts = $fonts ?? DesignHelper::getFontConfiguration($design);
$isRTL = $isRTL ?? DesignHelper::isRTL();
@endphp

<!-- Dynamic Design System Styles -->
<style>
    {!! DesignHelper::generateCSSVariables($design) !!}
    
    /* Apply dynamic font configuration */
    @if($fonts['url'])
    @import url('{{ $fonts['url'] }}');
    @endif
    
    body, h1, h2, h3, h4, h5, h6, div, span, strong, p, a, th, button {
        font-family: {{ $fonts['family'] }} !important;
        font-weight: var(--body-font-weight);
        line-height: var(--line-height);
    }
    
    h1, h2, h3, h4, h5, h6 {
        font-weight: var(--heading-font-weight);
        color: var(--section-title-color);
    }
    
    /* Body styling */
    body {
        @if($design->Body_BG_Type == 1)
            background-color: var(--body-bg-color) !important;
        @else
            background-image: url('{{ URL::to($design->Body_BG_Image) }}') !important;
            background-size: cover;
            background-repeat: no-repeat;
            background-attachment: fixed;
        @endif
        color: var(--section-text-color);
        font-size: var(--base-font-size);
    }
    
    /* Container styling */
    .container {
        max-width: var(--container-max-width);
    }
    
    /* Section styling */
    .space, .space-bottom, .space-top {
        padding: var(--section-padding) 0;
    }
    
    /* Card styling */
    .feature-card, .service-card, .product-card, .blog-card, .testi-box {
        background-color: var(--card-bg-color);
        border: 1px solid var(--card-border-color);
        border-radius: var(--card-border-radius);
        box-shadow: 0 2px 10px var(--card-shadow-color);
        transition: all var(--animation-duration) var(--animation-easing);
    }
    
    .feature-card:hover, .service-card:hover, .product-card:hover, .blog-card:hover {
        background-color: var(--card-hover-bg-color);
        box-shadow: 0 5px 20px var(--card-hover-shadow-color);
        transform: translateY(-5px);
    }
    
    /* Button styling */
    .btn, .btn.style2, .slick-arrow {
        background-color: var(--primary-button-bg-color) !important;
        color: var(--primary-button-txt-color) !important;
        border: none;
        border-radius: var(--button-border-radius);
        transition: all var(--animation-duration) var(--animation-easing);
    }
    
    .btn:hover, .btn.style2:hover, .slick-arrow:hover {
        background-color: var(--primary-button-hover-bg-color) !important;
        color: var(--primary-button-hover-txt-color) !important;
        transform: translateY(-2px);
    }
    
    /* Navigation styling */
    .navbar-area {
        background-color: var(--navbar-bg-color) !important;
    }
    
    .navbar-nav .nav-link {
        color: var(--navbar-txt-color) !important;
        transition: all var(--animation-duration) var(--animation-easing);
    }
    
    .navbar-nav .nav-link:hover {
        color: var(--navbar-hover-txt-color) !important;
        background-color: var(--navbar-hover-bg-color);
        border-radius: var(--button-border-radius);
    }
    
    /* Hero section styling */
    .hero-slider::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: var(--hero-overlay-color);
        opacity: var(--hero-overlay-opacity);
        z-index: 1;
    }
    
    .hero-title {
        color: var(--hero-title-color) !important;
        position: relative;
        z-index: 2;
    }
    
    .hero-subtitle {
        color: var(--hero-subtitle-color) !important;
        position: relative;
        z-index: 2;
    }
    
    /* Section titles */
    .sec-title, .section-title {
        color: var(--section-title-color) !important;
    }
    
    .sub-title, .section-subtitle {
        color: var(--section-subtitle-color) !important;
    }
    
    .sec-text, .section-text {
        color: var(--section-text-color) !important;
    }
    
    /* Product styling */
    .product-title a {
        color: var(--product-title-color) !important;
    }
    
    .price {
        color: var(--product-price-color) !important;
        font-weight: bold;
    }
    
    .product-tag {
        background-color: var(--product-badge-bg-color) !important;
        color: var(--product-badge-txt-color) !important;
    }
    
    /* Footer styling */
    .footer-area {
        background-color: var(--footer-bg-color) !important;
    }
    
    .footer-area h2, .footer-area h3, .footer-area h4, .footer-area h5 {
        color: var(--footer-title-color) !important;
    }
    
    .footer-area p, .footer-area span, .footer-area div {
        color: var(--footer-txt-color) !important;
    }
    
    .footer-area a {
        color: var(--footer-link-color) !important;
        transition: color var(--animation-duration) var(--animation-easing);
    }
    
    .footer-area a:hover {
        color: var(--footer-link-hover-color) !important;
    }
    
    /* Social media styling */
    .social-links a {
        background-color: var(--footer-social-bg-color) !important;
        color: var(--footer-social-txt-color) !important;
        transition: all var(--animation-duration) var(--animation-easing);
    }
    
    .social-links a:hover {
        background-color: var(--footer-social-hover-bg-color) !important;
        color: var(--footer-social-hover-txt-color) !important;
    }
    
    /* RTL Support */
    @if($isRTL)
    body {
        direction: rtl;
        text-align: right;
    }
    
    .navbar-nav {
        margin-left: 0;
        margin-right: auto;
    }
    @endif
    
    /* Animation controls */
    @if(!$design->Enable_Animations)
    * {
        animation: none !important;
        transition: none !important;
    }
    @endif
    
    /* Preloader styling */
    @if($design->Show_Preloader)
    .preloader {
        background-color: var(--preloader-bg-color);
    }
    
    .preloader .loader {
        border-color: var(--preloader-spinner-color);
    }
    @else
    .preloader {
        display: none !important;
    }
    @endif
    
    /* Testimonials styling */
    .testi-box {
        background-color: var(--testimonial-bg-color) !important;
        border: 1px solid var(--testimonial-border-color) !important;
    }
    
    .testi-box_text {
        color: var(--testimonial-text-color) !important;
    }
    
    .testi-box_name {
        color: var(--testimonial-author-color) !important;
    }
    
    /* Gallery styling */
    .gallery-card .gallery-content {
        background: linear-gradient(to top, var(--gallery-overlay-color), transparent);
    }
    
    .gallery-content_title {
        color: var(--gallery-title-color) !important;
    }
    
    .gallery-content_subtitle {
        color: var(--gallery-category-color) !important;
    }
    
    /* Blog styling */
    .blog-card {
        background-color: var(--blog-card-bg-color) !important;
        border: 1px solid var(--blog-card-border-color) !important;
    }
    
    .blog-title {
        color: var(--blog-title-color) !important;
    }
    
    .blog-meta a {
        color: var(--blog-meta-color) !important;
    }
    
    /* Form styling */
    .form-control {
        background-color: var(--form-input-bg-color) !important;
        border-color: var(--form-input-border-color) !important;
        color: var(--form-input-txt-color) !important;
    }
    
    .form-control:focus {
        border-color: var(--form-input-focus-border-color) !important;
        box-shadow: 0 0 0 0.2rem rgba(var(--form-input-focus-border-color), 0.25) !important;
    }
    
    label {
        color: var(--form-label-color) !important;
    }
</style>
