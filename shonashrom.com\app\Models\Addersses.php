<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Addersses extends Model
{
    use HasFactory;
         protected $table = 'addersses';
     protected $fillable = [
   
     
                'Address_Name',
                'User',
                'Gov',    
                'City',
                'Place',
                'Street',    
                'Bulliding',
                'Floor',
                'Flat',    
                'Special_Mark',
                'Location',
                'Address_Details',   
                'Address_ID',   

              

    ];
               public function User()
    {
        return $this->belongsTo(User::class,'User');
    }               public function Gov()
    {
        return $this->belongsTo(Governrate::class,'Gov');
    }               public function City()
    {
        return $this->belongsTo(City::class,'City');
    }               public function Place()
    {
        return $this->belongsTo(Place::class,'Place');
    }
}
