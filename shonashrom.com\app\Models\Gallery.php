<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Gallery extends Model
{
    use HasFactory;
       protected $table = 'galleries';
     protected $fillable = [
   
     
                'Image',
                'Arabic_Name',
                'English_Name',
                'Category',
                'Links',

              

    ];
    
                      public function Category()
    {
        return $this->belongsTo(Categories::class,'Category');
    }
    
}
