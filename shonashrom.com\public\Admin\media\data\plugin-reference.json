[{"plugin": "Pace JS", "url": "https://github.hubspot.com/pace/", "license": "MIT", "description": "This plugin is not required but highly recommended. If you decide to remove PaceJS from core, Internet Explorer may have some CSS issues during page load. Some animations will execute before a page is fully loaded, resulting 'jumpy / jerky' animations."}, {"plugin": "j<PERSON><PERSON><PERSON>", "url": "https://jquery.com/", "license": "MIT", "description": "jQuery is a fast, small, and feature-rich JavaScript library. It makes things like HTML document traversal and manipulation, event handling, animation, and Ajax much simpler with an easy-to-use API that works across a multitude of browsers. With a combination of versatility and extensibility, jQuery has changed the way that millions of people write JavaScript."}, {"plugin": "jqueryUI", "url": "https://jqueryui.com/", "license": "MIT", "description": "jQuery UI is a curated set of user interface interactions, effects, widgets, and themes built on top of the jQuery JavaScript Library. Whether you're building highly interactive web applications or you just need to add a date picker to a form control, jQuery UI is the perfect choice."}, {"plugin": "bootstrap", "url": "https://getbootstrap.com", "license": "MIT", "description": "Bootstrap is an open source toolkit for developing with HTML, CSS, and JS. Quickly prototype your ideas or build your entire app with our Sass variables and mixins, responsive grid system, extensive prebuilt components, and powerful plugins built on jQuery."}, {"plugin": "BootboxJS", "url": "http://bootboxjs.com/", "license": "MIT", "description": "Bootbox.js is a small JavaScript library which allows you to create programmatic dialog boxes using Bootstrap modals, without having to worry about creating, managing, or removing any of the required DOM elements or JavaScript event handlers."}, {"plugin": "JquerySnippets", "url": "https://www.gotbootstrap.com", "license": "Part of SmartAdmin WebApp", "description": "Contains a bunch of jQuery snippets that are re-usable in many types of applications"}, {"plugin": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://benalman.com/projects/jquery-throttle-debounce-plugin/", "license": "MIT", "description": "Query throttle / debounce allows you to rate-limit your functions in multiple useful ways. Passing a delay and callback to $.throttle returns a new function that will execute no more than once every delay milliseconds. Passing a delay and callback to $.debounce returns a new function that will execute only once, coalescing multiple sequential calls into a single execution at either the very beginning or end."}, {"plugin": "Jquery Slimscroll", "url": "https://github.com/rochal/jQuery-slimScroll", "license": "MIT", "description": "slimScroll is a small (4.6KB) jQuery plugin that transforms any div into a scrollable area with a nice scrollbar - similar to the one Facebook and Google started using in their products recently. slimScroll doesn't occupy any visual space as it only appears on a user initiated mouse-over. User can drag the scrollbar or use mouse-wheel to change the scroll value."}, {"plugin": "waves", "url": "http://fian.my.id/Waves/#start", "license": "MIT", "description": "Waves is an external library that we've included in SmartAdmin to allow us to create the ink effect you notice on Google's Material Design concept. To put the waves effect on an element, you just add the class .js-waves-on. Waves are automatically attached to all .btn classes and nav elements, if you like to turn this feature off for any reason you can add .js-waves-off"}, {"plugin": "SmartPanels", "url": "https://www.gotbootstrap.com", "license": "Part of SmartAdmin WebApp", "description": "Turn your ordinary panels to SmartPanel. Smartpanels lets you add buttons dynamically to panel header using data attributes. All panels are Sortable across all bootstrap col-spans and uses localStorage to store position, color, collapse state and various other panel settings. Use built in hooks to detect state change of panels and execute your script."}, {"plugin": "i18next", "url": "http://i18next.github.io/i18next/", "license": "MIT", "description": "I18next is an internationalization-framework written in and for JavaScript. But it's much more than that. i18next goes beyond just providing the standard i18n features such as (plurals, context, interpolation, format). It provides you with a complete solution to localize your product from web to mobile and desktop."}, {"plugin": "Dygraphs", "url": "http://dygraphs.com/", "license": "MIT", "description": "Dygraphs is a fast, flexible open source JavaScript charting library."}, {"plugin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://www.flotcharts.org/flot/examples/", "license": "MIT", "description": "Flot is a pure JavaScript plotting library for jQuery, with a focus on simple usage, attractive looks and interactive features. Additional examples are bundled with Flot. Flot is easy to use, just a few lines of code, you can make a simple line chart, it also provides a comprehensive API documentation where you can find examples, usage and methods. The most important part, Flot continues to release new versions, each new version comes with new features."}, {"plugin": "Chart.js", "url": "https://www.chartjs.org/docs/latest/", "license": "MIT", "description": "Chart.js is a JavaScript library that allows you to draw different types of charts by using the HTML5 canvas element. The library doesn’t have dependencies and its weight is very low as it’s ~11kb in size when minified, concatenated, and served gzipped. However, you’re able to reduce the size even further, if you don’t use all six of the core chart types, by including only the modules you need."}, {"plugin": "Chartist.js", "url": "https://gionkunz.github.io/chartist-js/getting-started.html", "license": "MIT", "description": "Chartist's goal is to provide a simple, lightweight and unintrusive library to responsively craft charts on your website. Chartist leverages the power of browsers today and say good bye to the idea of solving all problems ourselves. Chartist works with inline-SVG and therefore leverages the power of the DOM to provide parts of its functionality. This also means that Chartist does not provide it's own event handling, labels, behaviors or anything else that can just be done with plain HTML, JavaScript and CSS. The single and only responsibility of <PERSON><PERSON> is to help you drawing 'Simple responsive Charts' using inline-SVG in the DOM, CSS to style and JavaScript to provide an API for configuring your charts."}, {"plugin": "D3 Library", "url": "https://d3js.org/", "license": "BSD 3-<PERSON><PERSON>", "description": "D3.js is a JavaScript library for manipulating documents based on data. D3 helps you bring data to life using HTML, SVG, and CSS. This plugin is currently being used as a dependency for C3 Charts"}, {"plugin": "C3 Charts", "url": "https://c3js.org/examples.html", "license": "MIT", "description": "C3 makes it easy to generate D3-based charts by wrapping the code required to construct the entire chart. We don't need to write D3 code any more. The plugin adds classes to each element when generating, so you can define a custom style by the class and it's possible to extend the structure directly by D3. Provides a variety of APIs and callbacks to access the state of the chart."}, {"plugin": "<PERSON><PERSON><PERSON>", "url": "http://benpickles.github.io/peity/", "license": "MIT", "description": "Peity (sounds like deity) is a jQuery plugin that converts an element's content into a svg mini pie donut line or bar chart and is compatible with any browser that supports svg. Peity chart is only 4kb, which makes it great to create fast lightweight charts with minimum effort."}, {"plugin": "Sparkline", "url": "https://omnipotent.net/jquery.sparkline/#s-about", "license": "MIT", "description": "This jQuery plugin generates sparklines (small inline charts) directly in the browser using data supplied either inline in the HTML, or via javascript. Use sparklines to show trends in a series of values, such as seasonal increases or decreases, economic cycles, or to highlight maximum and minimum values."}, {"plugin": "Easy Pie Chart", "url": "https://github.com/rendro/easy-pie-chart", "license": "MIT", "description": "Easy Pie Charts are highly customizable, very easy to implement, and resolution independent (retina optimized). It uses requestAnimationFrame for smooth animations on modern devices and works in all modern browsers. Since it is a SVG file output, it can be resized to any width and height."}, {"plugin": "Datatables", "url": "https://www.datatables.net/manual/", "license": "MIT", "description": "DataTables is a plug-in for the jQuery Javascript library. It is a highly flexible tool, built upon the foundations of progressive enhancement, that adds all of these advanced features to any HTML table. The stated goal of DataTables is 'To enhance the accessibility of data in HTML tables'"}, {"plugin": "Toastr", "url": "https://github.com/CodeSeven/toastr", "license": "MIT", "description": "Toastr is a Javascript library for Gnome / Growl type non-blocking notifications. jQuery is required. The goal is to create a simple core library that can be customized and extended."}, {"plugin": "Sweetalert2", "url": "https://sweetalert2.github.io/#usage", "license": "MIT", "description": "SweetAlert2 is a JavaScript library that helps us create alerts in our web applications. SweetAlert2 is a replacement for default JavaScript pop up boxes. It needs zero dependencies, is customizable, well structured, accessible (wai-aria) and responsive. It needs promise.js for IE11 support. It is currently not supported in IE10"}, {"plugin": "Bootstrap Colorpicker", "url": "https://farbelous.io/bootstrap-colorpicker/", "license": "MIT", "description": "A nifty plugin that lets you select colors on the fly. The color pallet is auto generated or can be configured manually. The selection tool is loaded inside bootstrap's popover component, allowing you to load it from any direction, even on bootstrap modals"}, {"plugin": "Bootstrap Datepicker", "url": "https://bootstrap-datepicker.readthedocs.io/en/stable/", "license": "MIT", "description": "Bootstrap datepicker allows the user to enter a date by merely clicking on a date in the pop-up calendar as opposed to having to take their hand off the mouse to type in a date. The UI makes easy to select the date and keep the proper formatting"}, {"plugin": "Date Range Picker", "url": "http://www.daterangepicker.com/", "license": "MIT", "description": "This date range picker component creates a dropdown menu from which a user can select a range of dates. I created it while building the UI for Improvely, which needed a way to select date ranges for reports. Features include limiting the selectable date range, localizable strings and date formats, a single date picker mode, a time picker, and predefined date ranges."}, {"plugin": "Dropzone", "url": "https://www.dropzonejs.com/", "license": "MIT", "description": "It’s lightweight, doesn’t depend on any other library (like jQuery) and is highly customizable."}, {"plugin": "Ion.RangeSlider", "url": "http://ionden.com/a/plugins/ion.rangeSlider/index.html", "license": "MIT", "description": "Easy to use, flexible and responsive range slider comes with skin support (6 skins included). Features include two types of sliders (1 or double), support for negative and fractional values. Ability to setup custom step and snap to grid option. Customizable grid values and the ability to add port & prefixes. Slider writes its value right into input value field, making it easy to use in any HTML form. You can have many sliders in one page without any conflict."}, {"plugin": "Inputmusk", "url": "https://github.com/RobinHerbots/Inputmask", "license": "MIT", "description": "Inputmask is a javascript library which creates an input mask. Inputmask can run against vanilla javascript, jQuery and jqlite. An inputmask helps the user with the input by ensuring a predefined format. This can be useful for dates, numerics, phone numbers. It also easy to use and understand, possibility to define aliases which hide complexity, non-greedy masks, regex and dynamic masks."}, {"plugin": "C<PERSON>per", "url": "https://github.com/fengyuanchen/cropperjs/blob/master/README.md", "license": "MIT", "description": "Cropperjs is a slick and lightweight responsive cropping tool for images. It supports 39 options, 27 methods and 6 events. You can select an specific area of an image, and then upload the coordinates data to server-side to crop the image, or crop the image on browser-side directly. The plugin is regularly updated and has tons of potential! Making it a great fit for SmartAdmin WebApp."}, {"plugin": "Select2", "url": "https://github.com/select2/select2", "license": "MIT", "description": "Enhancing native selects with search by using native selects with a better multi-select interface. Easily load items via AJAX and have them searchable. Native selects only support one level of nesting. Select2 does not have this restriction. You can also work with large, remote datasets: ability to partially load a dataset based on the search term."}, {"plugin": "Summernote", "url": "https://summernote.org/deep-dive/", "license": "MIT", "description": "Summernote is a JavaScript library that helps you create WYSIWYG editors online. It lets you paste images from clipboard, save images directly in the content of the field using base64 encoding, so you don't need to implement image handling at all. And it is a very lightweight plugin, making it ideal for your project!"}, {"plugin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://refreshless.com/nouislider/", "license": "MIT", "description": "noUiSlider is a lightweight range slider with multi-touch support and a ton of features. It supports non-linear ranges, requires no external dependencies, has keyboard support, and it works great in responsive designs"}, {"plugin": "Fullcalendar", "url": "https://fullcalendar.io/docs#toc", "license": "MIT", "description": "FullCalendar is a fully responsive event display callendar that can display events directly from your database or from your google calendar. The events can be dragged, changed, edited (which requires implimentation). This is the latest version of FullCalendar which lacks IE10 support."}, {"plugin": "Lightgallery", "url": "https://sachinchoolur.github.io/lightGallery/", "license": "A part of SmartAdmin Template", "description": "LightGallery is a premium plugin, market value of $100, included as part of SmartAdmin WebApp, meaning you do not need to purchase a separate license for LightGallery when using it with SmartAdmin. This professional plugin is extreamly popular among the photographic community and comes packed with tons of features and goodies."}, {"plugin": "AltEditor", "url": "https://github.com/KasperOlesen/DataTable-AltEditor", "license": "A part of SmartAdmin Template", "description": "DataTables AltEditor is a MIT licensed free editor. The plugin adds capabilities to add, edit and delete rows in your datatables through the use of modals. We have modified the editor extensively to be used with SmartAdmin WebApp and make your job a little easier. This current version of AltEditor is exclusive to SmartAdmin and we intend to keep it up to date to be compatible with DataTables."}, {"plugin": "Jqvmap", "url": "https://www.npmjs.com/package/jqvmap", "license": "MIT", "description": "Jqvmap is a vector-based, cross-browser and cross-platform component for interactive geography-related data visualization on the web. It provides numerious features like smooth zooming and panning, fully-customizable styling, markers, labels and tooltips."}, {"plugin": "Moment.js", "url": "https://momentjs.com/", "license": "MIT", "description": "Parse, validate, manipulate, and display dates and times in JavaScript."}]