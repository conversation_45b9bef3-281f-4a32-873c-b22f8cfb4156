@import "helpers/bootstrap.min.css";
@import "helpers/jquery-ui.css";
@import "helpers/font-awesome.min.css";
@import "helpers/datatables.css";
@import "helpers/icheck/square/blue.css";
@import "helpers/redactor.css";
@import "helpers/bootstrap-fileupload.css";
@import "helpers/ekko-lightbox.css";
@import "helpers/perfect-scrollbar.css";
@import "helpers/select2.css";
@import "helpers/bootstrap-datatimepicker.css";
@import "helpers/print.css";

@font-face {
  font-family: 'FontAwesome';
  src: url('../fonts/fontawesome-webfont.eot?v=4.1.0');
  src: url('../fonts/fontawesome-webfont.eot?#iefix&v=4.1.0') format('embedded-opentype'), url('../fonts/fontawesome-webfont.woff?v=4.1.0') format('woff'), url('../fonts/fontawesome-webfont.ttf?v=4.1.0') format('truetype'), url('../fonts/fontawesome-webfont.svg?v=4.1.0#fontawesomeregular') format('svg');
  font-weight: normal;
  font-style: normal;
}

/*@font-face {
    font-family: 'Lato';
    font-style: normal;
    font-weight: 400;
    src: local('Lato Regular'), local('Lato-Regular'), url(../fonts/9k-RPmcnxYEPm8CNFsH2gg.woff) format('woff');
}
@font-face {
  font-family: 'Lato';
  font-style: normal;
  font-weight: 700;
  src: local('Lato Bold'), local('Lato-Bold'), url(../fonts/wkfQbvfT_02e2IWO3yYueQ.woff) format('woff');
}
body, h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 { font-family: 'Lato', 'Helvetica Neue', Helvetica, Arial, sans-serif; }*/

@font-face {
  font-family: 'Ubuntu';
  font-style: normal;
  font-weight: 400;
  src: local('Ubuntu'), url(../fonts/mZSs29ggGoaqrCNB3kDfZQ.woff) format('woff');
}
@font-face {
  font-family: 'Ubuntu';
  font-style: normal;
  font-weight: 700;
  src: local('Ubuntu Bold'), local('Ubuntu-Bold'), url(../fonts/trnbTfqisuuhRVI3i45C5w.woff) format('woff');
}
body, h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 { font-family: 'Ubuntu', sans-serif; }

.no-border { border-width: 0 !important; }
.border-bottom { border-bottom: 1px solid #ddd !important; }


/* Customize the code below
============================================= */
.order_barcodes {
  /* uncomment line below to hide the barcodes */
  /* display: none; */
}
{"mode":"full","isActive":false}