@extends('site.index')
@section('content')
     <style>
           @media (max-width: 575px){
.product-meta {
    width: unset;
    display:unset;
}}
          @media (max-width: 575px){
.CategorySec {
    width:unset;
}}
.product-content {
    display:unset;
}
       </style>
<title>{{trans('admin.Prescription')}}</title>

    <!--==============================
    Breadcumb
    ============================== -->
    <div class="breadcumb-wrapper" data-bg-src="{{asset('Front/assets/img/bg/breadcrumb-bg.png')}}">
        <!-- bg animated image/ -->
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="breadcumb-content">
                        <h1 class="breadcumb-title">{{trans('admin.Prescription')}}</h1>
                        <ul class="breadcumb-menu">
                            <li><a href="{{url('/')}}">{{trans('admin.Home')}}</a></li>
                            <li class="active">{{trans('admin.Prescription')}}</li>
                        </ul>
                    </div>
                </div>
            </div>

        </div>
    </div>

     
 <!--==============================
    Blog Area  
    ==============================-->
    <section class="blog-area space-top space-extra-bottom">
        <div class="container">
            <div class="row">
                <div class="col-xxl-12 col-lg-12">
                    <div class="blog-single">
                        <div class="blog-thumb">
                            <img src="{{URL::to($Pro->Image)}}" alt="img">
                        </div>
                        <div class="blog-content">
                          
                       {!! app()->getLocale() == 'ar' ?$Pro->Ar_Desc :$Pro->En_Desc !!}
                        </div>
                
                    </div>
             
                </div>

            </div>
        </div>
    </section>   




@endsection