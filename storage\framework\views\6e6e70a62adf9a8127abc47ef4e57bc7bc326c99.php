<?php $__env->startSection('content'); ?>
<?php
use App\Models\ProductUnits;
use App\Models\Coins;
use App\Models\Countris;
use App\Models\Wishlist;
use App\Models\Compare;
use App\Models\Products;

    if(empty(session()->get('ChangeCountryy'))) {
    
         $Coin=Coins::where('Draw',1)->first();
    $Country=Countris::where('Coin',$Coin->id)->first();
    session()->put('ChangeCountryy',$Country->id);  
      }

$Ses=Countris::find(session()->get('ChangeCountryy'));


use App\Models\HomeEComDesign;
use App\Models\HomeProductEComDesign;
use App\Models\SupPagesEComDesign;
$sub=SupPagesEComDesign::orderBy('id','desc')->first();
$HomeV=HomeEComDesign::orderBy('id','desc')->first();
$HomeVV=HomeProductEComDesign::orderBy('id','desc')->first();

?>


     <title><?php echo e($Comp->Name); ?></title>
        <!-- Start Main Slider Area -->
        <div class="home-slides owl-carousel owl-theme">
                    <?php $__currentLoopData = $Sliders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $slide): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php if($slide->Type != 1): ?>
            
            <?php if($HomeV->Slider_BG_Type == 1): ?>
            <div class="main-slider-item" style="background: <?php echo e($HomeV->Slider_BG_Color); ?>">
                <?php else: ?>
                 <div class="main-slider-item" style="background-image: url(<?php echo e(URL::to($HomeV->Slider_BG_Image)); ?>)">   
            <?php endif; ?>    
                
                <div class="d-table">
                    <div class="d-table-cell">
                        <div class="container">
                            <div class="row align-items-center">
                                <div class="col-lg-6">
                                    <div class="main-slider-content">
                                        <h1 style="color: <?php echo e($HomeV->Slider_Title_Txt_Color); ?>">      <?php echo e(app()->getLocale() == 'ar' ?$slide->Arabic_Title :$slide->English_Title); ?></h1>
                                        <p style="color: <?php echo e($HomeV->Slider_Desc_Txt_Color); ?>">      <?php echo e(app()->getLocale() == 'ar' ?$slide->Arabic_Desc :$slide->English_Desc); ?></p>
                                        
                                        <div class="slider-btn">
                                            <a href="<?php echo e(url('ShopSite')); ?>" class="default-btn">
                                                <i  class="flaticon-shopping-cart"></i>
                          
                                                <?php echo e(trans('admin.Shop_Now')); ?>

                                                <span></span>
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-lg-6">
                                    <div class="main-slider-image">
                                        <img src="<?php echo e(URL::to($slide->Image)); ?>" alt="image">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        <!-- End Main Slider Area -->

        <!-- Start Overview Area -->
        <section class="overview-area pt-100 pb-20">
            <div class="container">
                <div class="row">
                    <div class="col-lg-6 col-md-6">
                        <div class="single-overview">
                            <div class="row align-items-center">
                                
                      
                                <div class="col-lg-6">
                                    <div class="overview-image">
                                        <img src="<?php echo e(URL::to($OneAds->Image)); ?>" alt="image">
                                    </div>
                                </div>

                                <div class="col-lg-6">
                                    <div class="overview-content">
                                    
                                        <div class="overview-btn">
                                            <a href="<?php echo e(url('ShopSite')); ?>" class="default-btn">
                                                <i class="flaticon-shopping-cart"></i>
                                             <?php echo e(trans('admin.Shop_Now')); ?>

                                                <span></span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                          
                            </div>
                        </div>
                    </div>

               <div class="col-lg-6 col-md-6">
                        <div class="single-overview w">
                            <div class="row align-items-center">
                                <div class="col-lg-6">
                                    <div class="overview-image">
                                        <img src="<?php echo e(URL::to($TwoAds->Image)); ?>" alt="image">
                                    </div>
                                </div>

                                <div class="col-lg-6">
                                    <div class="overview-content">
                                    
                                        <div class="overview-btn">
                                            <a href="<?php echo e(url('ShopSite')); ?>" class="default-btn">
                                                <i class="flaticon-shopping-cart"></i>
                                                 <?php echo e(trans('admin.Shop_Now')); ?>

                                                <span></span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
 
        <!-- End Overview Area -->

        <!-- Start Support Area -->
        <section class="support-area">
            <div class="container">
                <div class="support-inner-box" style="background: <?php echo e($HomeV->Support_Icons_BG_Color); ?>">
                    <div class="row">
                        
                <?php $__currentLoopData = $Befores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $bef): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>        
                        <div class="col-lg-3 col-md-6">
                            <div class="single-support">
                                <div class="icon">
                             <?php echo $bef->Image; ?>

                                </div>

                                <div class="support-content">
                                    <h3 style="color: <?php echo e($HomeV->Support_Icons_Txt_Color); ?>" ><?php echo e(app()->getLocale() == 'ar' ?$bef->Arabic_Title :$bef->English_Title); ?></h3>
                                </div>
                            </div>
                        </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>        

                        


          
                    </div>
                </div>
            </div>
        </section>
        <!-- End Support Area -->

        <!-- Start Arrivals Products Area -->
        <section class="arrivals-products-area pt-100 pb-70">
            <div class="container">
                <div class="section-title" style=" border-bottom:1px solid <?php echo e($HomeVV->New_Arrivals_Title_Txt_Color); ?>">
                    <h2 style=" background: :<?php echo e($HomeVV->New_Arrivals_Title_BG_Color); ?>; color: <?php echo e($HomeVV->New_Arrivals_Title_Txt_Color); ?>"><?php echo e(trans('admin.New_Arrivals')); ?></h2>
                </div>

                <div class="row first owl-carousel owl-theme">

                    <?php $__currentLoopData = $ProductsNew; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pro): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-lg-12 col-sm-12 item" style=" background: :<?php echo e($HomeVV->New_Arrivals_Product_BG_Color); ?>;">
                        <div class="single-arrivals-products">
                            <div class="arrivals-products-image">
                                <a href="<?php echo e(url('ProductDetails/'.$pro->id)); ?>"><img src="<?php echo e(URL::to($pro->Image)); ?>" alt="image"></a>
                                <div class="tag">
                                
                        <?php echo e(app()->getLocale() == 'ar' ?$pro->Group()->first()->Name :$pro->Group()->first()->NameEn); ?>        
                                </div>
                                <ul class="arrivals-action">
                                    <li>
                                        <a href="<?php echo e(url('ProductDetails/'.$pro->id)); ?>">
                                            <i class="flaticon-shopping-cart"></i>
                                        </a>
                                    </li>
                                    
                                   <?php if(!empty(auth()->guard('client')->user()->id)): ?>            
                                    <?php            
                                       $Wish=Wishlist::where('Product',$pro->id)
                                            ->where('User',auth()->guard('client')->user()->id)->first(); 
                                       
                                                       $Comp=Compare::where('Product',$pro->id)
                                            ->where('User',auth()->guard('client')->user()->id)->first(); 
                                                
                                    ?>
          
                                    <li>
                                        
                                        <?php if(empty($Wish)): ?>     
                                        <a href="<?php echo e(url('AddWish/'.$pro->id)); ?>"><i class="flaticon-heart"></i></a>
                                             <?php else: ?>   
                                       
                                        <i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Icon_Txt_Color); ?>" class="bx bxs-heart"></i>
                                            <?php endif; ?> 
                                    </li>
                                    <li>
                                       <?php if(empty($Comp)): ?>      
                                        <a href="<?php echo e(url('AddCompare/'.$pro->id)); ?>"><i class="bx bx-git-compare"></i></a>
                                        <?php else: ?>
             <i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Icon_Txt_Color); ?>" class="bx bx-git-compare">  <i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Icon_Txt_Color); ?>" class="bx bx-badge-check"></i></i>
                                      
                                          <?php endif; ?>  
                                    </li>
                          <?php endif; ?>              
                                </ul>
                            </div>

                            <div class="arrivals-products-content">
                                <h3>
                                    
    <a href="<?php echo e(url('ProductDetails/'.$pro->id)); ?>" style="color: <?php echo e($HomeVV->New_Arrivals_Product_Txt_Color); ?>">   <?php echo e(app()->getLocale() == 'ar' ?$pro->P_Ar_Name :$pro->P_En_Name); ?>     </a>
                                </h3>
                                <ul class="rating">
                                                      <?php if(empty($pro->rate)): ?>
                                                          <li><i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                    <li><i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                    <li><i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                    <li><i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                    <li><i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                            <?php elseif($pro->rate == 1): ?> 
                                                          <li><i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                    <li><i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                    <li><i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                    <li><i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                            <?php elseif($pro->rate == 2): ?>  
                                                 <li><i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                    <li><i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                    <li><i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                            <?php elseif($pro->rate == 3): ?> 
                                                           <li><i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                    <li><i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                            <?php elseif($pro->rate == 4): ?>  
                                                               <li><i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                            <?php elseif($pro->rate == 5): ?>
                                                             <li><i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i style="color: <?php echo e($HomeVV->New_Arrivals_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                            <?php endif; ?>  
               
                                </ul>
                                <span style="color: <?php echo e($HomeVV->New_Arrivals_Product_Price_Color); ?>">
                                
     <?php  $Price=ProductUnits::where('Product',$pro->id)->where('Def',1)->first();   ?>  
     <?php if($pro->Offer == 1): ?>
<del style="color: <?php echo e($HomeVV->New_Arrivals_Product_Price_Color); ?>" ><?php echo e($Ses->Coin()->first()->Symbol); ?><?php echo e(round($Price->Price / $Ses->Coin()->first()->Draw)); ?></del>
<?php echo e($Ses->Coin()->first()->Symbol); ?><?php echo e(round($pro->OfferPrice / $Ses->Coin()->first()->Draw)); ?>

        <?php else: ?>
<?php echo e($Ses->Coin()->first()->Symbol); ?><?php echo e(round($Price->Price / $Ses->Coin()->first()->Draw)); ?>

        <?php endif; ?>     
                      
                                    
                                    
                                
                                </span>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                   
                </div>
            </div>
        </section>
        <!-- End Arrivals Products Area -->

        <!-- Start Offer Products Area -->
        <section class="offer-products-area pb-70">
            <div class="container">
                <div class="section-title" style=" border-bottom:1px solid <?php echo e($HomeVV->Special_Offer_Title_Txt_Color); ?>">
                    <h2 style=" background: :<?php echo e($HomeVV->Special_Offer_Title_BG_Color); ?>; color: <?php echo e($HomeVV->Special_Offer_Title_Txt_Color); ?>"><?php echo e(trans('admin.Special_Offer')); ?></h2>
                </div>

             
                <div class="row  first owl-carousel owl-theme">
                    
               <?php $__currentLoopData = $ProductsOffers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pro): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>        
              <div class="col-lg-12 col-md-12 item">
                        <div class="single-offer-products">
                            <div class="offer-products-image">
                                <a href="<?php echo e(url('ProductDetails/'.$pro->id)); ?>"><img src="<?php echo e(URL::to($pro->Image)); ?>" alt="image"></a>
                            </div>
        
                            <div class="offer-products-content">
              <span style="color: <?php echo e($HomeVV->Special_Offer_Product_Txt_Color); ?>"> <?php echo e(app()->getLocale() == 'ar' ?$pro->Group()->first()->Name :$pro->Group()->first()->NameEn); ?>    </span>
                                <h3>
           <a href="<?php echo e(url('ProductDetails/'.$pro->id)); ?>" style="color: <?php echo e($HomeVV->Special_Offer_Product_Txt_Color); ?>"><?php echo e(app()->getLocale() == 'ar' ?$pro->P_Ar_Name :$pro->P_En_Name); ?></a>
                                </h3>
                                <div class="price">
                                    
                                    
                                         <?php  $Price=ProductUnits::where('Product',$pro->id)->where('Def',1)->first();   ?>  
     <?php if($pro->Offer == 1): ?>
      
 <span style="color: <?php echo e($HomeVV->Special_Offer_Product_Price_Color); ?>" class="new-price"><?php echo e($Ses->Coin()->first()->Symbol); ?><?php echo e(round($pro->OfferPrice / $Ses->Coin()->first()->Draw)); ?></span>
    <span style="color: <?php echo e($HomeVV->Special_Offer_Product_Price_Color); ?>" class="old-price"><?php echo e($Ses->Coin()->first()->Symbol); ?><?php echo e(round($Price->Price / $Ses->Coin()->first()->Draw)); ?></span>            
                                    
        <?php else: ?>

         <span style="color: <?php echo e($HomeVV->Special_Offer_Product_Price_Color); ?>" class="new-price"><?php echo e($Ses->Coin()->first()->Symbol); ?><?php echo e(round($Price->Price / $Ses->Coin()->first()->Draw)); ?></span>                            
        <?php endif; ?>   
         
                                </div>
                                <ul class="rating">
                                    <li>
                                                  <?php if(empty($pro->rate)): ?>
                                            <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bx-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bx-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bx-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bx-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bx-star'></i>
                                            <?php elseif($pro->rate == 1): ?> 
                                     <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bxs-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bx-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bx-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bx-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bx-star'></i>
                                            <?php elseif($pro->rate == 2): ?>  
                                 <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bxs-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bxs-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bx-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bx-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bx-star'></i>
                                            <?php elseif($pro->rate == 3): ?> 
                                  <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bxs-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bxs-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bxs-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bx-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bx-star'></i>
                                            <?php elseif($pro->rate == 4): ?>  
                                  <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bxs-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bxs-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bxs-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bxs-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bx-star'></i>
                                            <?php elseif($pro->rate == 5): ?>
                                     <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bxs-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bxs-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bxs-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bxs-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bxs-star'></i>
                                            <?php endif; ?>  
                                    
                                           </li> 
                                </ul>
                            </div>
                        </div>
                    </div>
                   <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </section>
        <!-- End Offer Products Area -->

        <!-- Start Collection Area -->
        <section class="collection-area">
            <div class="container">
                <div class="collection-inner-box">
                    <div class="row align-items-center">
                        <div class="col-lg-4 col-md-6">
                            <div class="collection-image">
                                <img src="<?php echo e(URL::to($ThreeAds->Image)); ?>" alt="image">
                            </div>
                        </div>

                        <div class="col-lg-4 col-md-6">
                            <div class="collection-content">

                                <div class="collection-btn">
                                    <a href="<?php echo e(url('ShopSite')); ?>" class="default-btn">
                                        <i class="flaticon-shopping-cart"></i>
                                             <?php echo e(trans('admin.Shop_Now')); ?>

                                        <span></span>
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4 col-md-6 offset-lg-0 offset-md-3">
                            <div class="collection-image">
                                <img src="<?php echo e(URL::to($FourAds->Image)); ?>" alt="image">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- End Collection Area -->

   
        <!-- Start Bestsellers Area -->
        <section class="bestsellers-area pt-100 pb-70">
            <div class="container">
                <div class="section-title" style=" border-bottom:1px solid <?php echo e($HomeVV->Best_Sellers_Title_Txt_Color); ?>" >
                    <h2 style=" background: :<?php echo e($HomeVV->Best_Sellers_Title_BG_Color); ?>; color: <?php echo e($HomeVV->Best_Sellers_Title_Txt_Color); ?>" >Bestsellers</h2>
                </div>
        
                <div class="tab bestsellers-list-tab">
                    <ul class="tabs">
                        
                    <?php $__currentLoopData = $Groups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $grop): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                        <li>
                            <a href="#"><?php echo e(app()->getLocale() == 'ar' ?$grop->Name :$grop->NameEn); ?></a>
                        </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>    
               
                    </ul>

                    <div class="tab_content">
                               <?php $__currentLoopData = $Groups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $grop): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
                        <?php  
                        
                        $ProductsMost=Products::whereIn('Store_Show',[1,3])
      ->whereIn('P_Type',['Completed','Single_Variable','Duble_Variable'])
            ->where('Store_Type',2)
            ->where('Group',$grop->id)
    ->orderBy('id','desc')->take(50)->get();    
                        
                        ?>
                        <div class="tabs_item">
                            <div class="row">
                                
                                         <?php $__currentLoopData = $ProductsMost; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pro): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>  
                                <div class="col-lg-3 col-sm-6">
                                    <div class="single-bestsellers-products">
                                        <div class="bestsellers-products-image">
                                            <a href="<?php echo e(url('ProductDetails/'.$pro->id)); ?>"><img src="<?php echo e(URL::to($pro->Image)); ?>" alt="image"></a>
                                            <div class="tag">  <?php echo e(app()->getLocale() == 'ar' ?$pro->Group()->first()->Name :$pro->Group()->first()->NameEn); ?></div>
                                            <ul class="bestsellers-action">
                                                 <li>
                                        <a href="<?php echo e(url('ProductDetails/'.$pro->id)); ?>">
                                            <i class="flaticon-shopping-cart"></i>
                                        </a>
                                    </li>
                                    
                                   <?php if(!empty(auth()->guard('client')->user()->id)): ?>            
                                    <?php            
                                       $Wish=Wishlist::where('Product',$pro->id)
                                            ->where('User',auth()->guard('client')->user()->id)->first(); 
                                       
                                                       $Comp=Compare::where('Product',$pro->id)
                                            ->where('User',auth()->guard('client')->user()->id)->first(); 
                                                
                                    ?>
          
                                    <li>
                                        
                                        <?php if(empty($Wish)): ?>     
                                        <a href="<?php echo e(url('AddWish/'.$pro->id)); ?>"><i class="flaticon-heart"></i></a>
                                             <?php else: ?>   
                                       
                                        <i style="color:<?php echo e($HomeVV->Best_Sellers_Product_Icon_Txt_Color); ?>" class="bx bxs-heart"></i>
                                            <?php endif; ?> 
                                    </li>
                                    <li>
                                       <?php if(empty($Comp)): ?>      
                                        <a href="<?php echo e(url('AddCompare/'.$pro->id)); ?>"><i class="bx bx-git-compare"></i></a>
                                        <?php else: ?>
                                        
                                        <i style="color:<?php echo e($HomeVV->Best_Sellers_Product_Icon_Txt_Color); ?>" class="bx bx-git-compare">  <i style="color:<?php echo e($HomeVV->Best_Sellers_Product_Icon_Txt_Color); ?>" class="bx bx-badge-check"></i></i>
                                      
                                          <?php endif; ?>  
                                    </li>
                          <?php endif; ?>    
                                            </ul>
                                        </div>
            
                                        <div class="bestsellers-products-content">
                                            <h3>
      <a href="<?php echo e(url('ProductDetails/'.$pro->id)); ?>"  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Txt_Color); ?>">   <?php echo e(app()->getLocale() == 'ar' ?$pro->P_Ar_Name :$pro->P_En_Name); ?>     </a>
                                            </h3>
                                            <ul class="rating">
                                             <?php if(empty($pro->rate)): ?>
                                     <li><i style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                            <?php elseif($pro->rate == 1): ?> 
                                     <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                            <?php elseif($pro->rate == 2): ?>  
                                     <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                            <?php elseif($pro->rate == 3): ?> 
                                     <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                            <?php elseif($pro->rate == 4): ?>  
                                     <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                            <?php elseif($pro->rate == 5): ?>
                                     <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                            <?php endif; ?>  
                                            </ul>
                                                                     <span style="color:<?php echo e($HomeVV->Best_Sellers_Product_Price_Color); ?>">
                                
     <?php  $Price=ProductUnits::where('Product',$pro->id)->where('Def',1)->first();   ?>  
     <?php if($pro->Offer == 1): ?>
<del style="color:<?php echo e($HomeVV->Best_Sellers_Product_Price_Color); ?>"><?php echo e($Ses->Coin()->first()->Symbol); ?><?php echo e(round($Price->Price / $Ses->Coin()->first()->Draw)); ?></del>
<?php echo e($Ses->Coin()->first()->Symbol); ?><?php echo e(round($pro->OfferPrice / $Ses->Coin()->first()->Draw)); ?>

        <?php else: ?>
<?php echo e($Ses->Coin()->first()->Symbol); ?><?php echo e(round($Price->Price / $Ses->Coin()->first()->Draw)); ?>

        <?php endif; ?>     
                                
                                
                                </span>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                
                              
                                
                              

                            </div>
                        </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                     
                    </div>
                </div>
            </div>
        </section>
        <!-- End Bestsellers Area -->


        <!-- Start Blog Area -->
        <section class="blog-area pb-70">
            <div class="container">
                <div class="section-title">
                    <h2><?php echo e(trans('admin.Latest_Blog')); ?> </h2>
                </div>

                <div class="row owl-carousel new-carousel">
                    
                         <?php $__currentLoopData = $Articles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $art): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-lg-12 item col-md-6 ">
                        <div class="single-blog">
                            <div class="blog-image">
                                <a href="<?php echo e(url('BlogDetails/'.$art->id)); ?>">
                                    <img src="<?php echo e(URL::to($art->Sub_Image)); ?>" alt="image">
                                </a>
                            </div>



                            <div class="blog-content">
                            
                                <h3>
                <a style="color: <?php echo e($sub->Blogs_Title_Color); ?>" href="<?php echo e(url('BlogDetails/'.$art->id)); ?>">       <?php echo e(app()->getLocale() == 'ar' ?$art->Arabic_Title :$art->English_Title); ?></a>
                                </h3>
                                <div class="post-meta">
                                    <a style="color: <?php echo e($sub->Blogs_Txt_Color); ?>" href="<?php echo e(url('BlogDetails/'.$art->id)); ?>"><?php echo e($art->Date); ?></a> 
                                </div>
                                <p style="display: none">       <?php echo app()->getLocale() == 'ar' ?$art->Arabic_Desc :$art->English_Desc; ?></p>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                </div>
            </div>
        </section>
        <!-- End Blog Area -->

        <!-- Start Partner Area -->
        <div class="partner-area ptb-100" style="background: <?php echo e($HomeV->Partners_BG_Color); ?>">
            <div class="container">
                <div class="partner-slider owl-carousel owl-theme">
                    
                   <?php $__currentLoopData = $Brands; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $brand): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
   
                    <div class="partner-item">
                        <a href="<?php echo e(url('ShopFilterBrand/'.$brand->id)); ?>">
                            <img src="<?php echo e(URL::to($brand->Image)); ?> " alt="image">
                        </a>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                   
                </div>
            </div>
        </div>
        <!-- End Partner Area -->

       
            <!-- Slider -->
            <style>
    .slider-btn .default-btn{
        
        background: <?php echo e($HomeV->Slider_Button_BG_Color); ?>  !important;
        color: <?php echo e($HomeV->Slider_Button_Txt_Color); ?>  !important;
    }
    
    
      .slider-btn .default-btn:hover{
        
        background: <?php echo e($HomeV->Slider_Button_Hover_BG_Color); ?>  !important;
        color: <?php echo e($HomeV->Slider_Button_Hover_Txt_Color); ?>  !important;
    }
    
    .slider-btn .default-btn:hover i {

        color: <?php echo e($HomeV->Slider_Button_Hover_Txt_Color); ?>  !important;
}
    
    
    .slider-btn .default-btn:hover span {
        
             background: <?php echo e($HomeV->Slider_Button_Hover_BG_Color); ?>  !important;
        color: <?php echo e($HomeV->Slider_Button_Hover_Txt_Color); ?>  !important;
        
    }
</style>  
            
            <!-- Ads Top -->
            <style>
            .single-overview {
    background-color: <?php echo e($HomeV->Ads_Top_Img_First_BG_Color); ?> !important;
                }
                
                .single-overview::before {
                    
              background-color: <?php echo e($HomeV->Ads_Top_Img_First_Before_BG_Color); ?> !important;         
                }
                
                
                       .w {
    background-color: <?php echo e($HomeV->Ads_Top_Img_Second_BG_Color); ?> !important;
                }
                
                .w::before {
                    
              background-color: <?php echo e($HomeV->Ads_Top_Img_Second_Before_BG_Color); ?> !important;         
                }
                
                
                
                  .single-overview .default-btn{
        
        background: <?php echo e($HomeV->Ads_Top_Img_Button_BG_Color); ?>  !important;
        color: <?php echo e($HomeV->Ads_Top_Img_Button_Txt_Color); ?>  !important;
    }
    
    
      .single-overview .default-btn:hover{
        
        background: <?php echo e($HomeV->Ads_Top_Img_Button_Hover_BG_Color); ?>  !important;
        color: <?php echo e($HomeV->Ads_Top_Img_Button_Hover_Txt_Color); ?>  !important;
    }
    
    .single-overview .default-btn:hover i {

        color: <?php echo e($HomeV->Ads_Top_Img_Button_Hover_Txt_Color); ?>  !important;
}
    
    
    .single-overview .default-btn:hover span {
        
             background: <?php echo e($HomeV->Ads_Top_Img_Button_Hover_BG_Color); ?>  !important;
        color: <?php echo e($HomeV->Ads_Top_Img_Button_Hover_Txt_Color); ?>  !important;
        
    }
                
            </style>
            
              <!-- Support Icons -->
            <style>
                .single-support .icon i {
                    
                    color: <?php echo e($HomeV->Support_Icons_Color); ?> !important;
                    
                }
            </style>
            
                 <!-- Ads Bottom -->
            <style>
            
                .collection-inner-box{
                   
                     background-color: <?php echo e($HomeV->Ads_Bootom_Imgs_BG_Color); ?> !important;
                    
                }
                
                .collection-inner-box::before {
                    
              background-color: <?php echo e($HomeV->Ads_Bootom_Imgs_Middle_BG_Color); ?> !important;         
                    
                }
                
                
                   .collection-inner-box .default-btn{
        
        background: <?php echo e($HomeV->Ads_Bootom_Imgs_Button_BG_Color); ?>  !important;
        color: <?php echo e($HomeV->Ads_Bootom_Imgs_Button_Txt_Color); ?>  !important;
    }
    
    
      .collection-inner-box .default-btn:hover{
        
        background: <?php echo e($HomeV->Ads_Bootom_Imgs_Button_Hover_BG_Color); ?>  !important;
        color: <?php echo e($HomeV->Ads_Bootom_Imgs_Button_Hover_Txt_Color); ?>  !important;
    }
    
    .collection-inner-box .default-btn:hover i {

        color: <?php echo e($HomeV->Ads_Bootom_Imgs_Button_Hover_Txt_Color); ?>  !important;
}
    
    
    .collection-inner-box .default-btn:hover span {
        
             background: <?php echo e($HomeV->Ads_Bootom_Imgs_Button_Hover_BG_Color); ?>  !important;
        color: <?php echo e($HomeV->Ads_Bootom_Imgs_Button_Hover_Txt_Color); ?>  !important;
        
    }
            </style> 

               <!-- Blogs -->
            <style>
                .single-blog .blog-content h3 a:hover {
                    
                    color: <?php echo e($sub->Blogs_Hover_Txt_Color); ?> !important;   
                }
                
                   .single-blog .blog-content .post-meta a:hover {
                    
                    color: <?php echo e($sub->Blogs_Hover_Txt_Color); ?> !important;   
                }
            </style>
            
            <!-- Special Offer -->
            <style>
                    single-offer-products {
                        border: 1px solid <?php echo e($HomeVV->Special_Offer_Product_Border_Color); ?> !important;
                        background-color: <?php echo e($HomeVV->Special_Offer_Product_BG_Color); ?> !important;
                    }
                     
                     .single-offer-products .offer-products-content h3 a:hover {
                         
                         color: <?php echo e($HomeVV->Special_Offer_Product_Txt_Hover_Color); ?> !important;
                     }
                </style>  
            
            <!-- Best Sellers -->
              <style>
                .bestsellers-list-tab .tabs li a{
                    
                    color: <?php echo e($HomeVV->Best_Sellers_Category_Txt_Color); ?> !important;
                }  
                
                .bestsellers-list-tab .tabs li.current a {
      color: <?php echo e($HomeVV->Best_Sellers_Category_Active_Txt_Color); ?> !important;
    border-bottom: 1px solid <?php echo e($HomeVV->Best_Sellers_Category_Active_Txt_Color); ?> !important;
}
                
                
        .single-bestsellers-products .bestsellers-products-image .tag {

    background: <?php echo e($HomeVV->Best_Sellers_Product_Group_BG_Color); ?> !important;
    color: <?php echo e($HomeVV->Best_Sellers_Product_Group_Txt_Color); ?> !important;
            
                }
                
                      .single-bestsellers-products .bestsellers-products-image .tag:hover {

    background: <?php echo e($HomeVV->Best_Sellers_Product_Group_Hover_BG_Color); ?> !important;
    color: <?php echo e($HomeVV->Best_Sellers_Product_Group_Hover_Txt_Color); ?> !important;
            
                }
                
                .single-bestsellers-products {
                    
             background: <?php echo e($HomeVV->Best_Sellers_Product_BG_Color); ?> !important;         
                }
                
                
                .single-bestsellers-products .bestsellers-products-image .bestsellers-action li a i {

                    background-color:<?php echo e($HomeVV->Best_Sellers_Product_Icon_BG_Color); ?> !important;
                    color: <?php echo e($HomeVV->Best_Sellers_Product_Icon_Txt_Color); ?> !important;
                
                }
                
                            .single-bestsellers-products .bestsellers-products-image .bestsellers-action li a i:hover {

                                background-color:<?php echo e($HomeVV->Best_Sellers_Product_Icon_Hover_BG_Color); ?> !important;
                                color: <?php echo e($HomeVV->Best_Sellers_Product_Icon_Hover_Txt_Color); ?> !important;
                
                }
            </style>
            
                <!-- New Arrivals -->
            <style>
                .single-arrivals-products .arrivals-products-image .tag{
                
                        background: <?php echo e($HomeVV->New_Arrivals_Product_Group_BG_Color); ?> !important;
    color: <?php echo e($HomeVV->New_Arrivals_Product_Group_Txt_Color); ?> !important;
                    
                }
                
                .single-arrivals-products .arrivals-products-image .tag:hover{
                  
                                background: <?php echo e($HomeVV->New_Arrivals_Product_Group_Hover_BG_Color); ?> !important;
    color: <?php echo e($HomeVV->New_Arrivals_Product_Group_Hover_Txt_Color); ?> !important;
                    
                }
                
                
                .single-arrivals-products .arrivals-products-image .arrivals-action li a i{
                    
                         background-color:<?php echo e($HomeVV->New_Arrivals_Product_Icon_BG_Color); ?> !important;
                    color: <?php echo e($HomeVV->New_Arrivals_Product_Icon_Txt_Color); ?> !important;
                    
                }        
                
                
                .single-arrivals-products .arrivals-products-image .arrivals-action li a i:hover{
                    
                         background-color:<?php echo e($HomeVV->New_Arrivals_Product_Icon_Hover_BG_Color); ?> !important;
                    color: <?php echo e($HomeVV->New_Arrivals_Product_Icon_Hover_Txt_Color); ?> !important;
                    
                }              
                
                .single-arrivals-products .arrivals-products-content span:hover {

                   color: <?php echo e($HomeVV->New_Arrivals_Product_Hover_Price_Color); ?> !important;   
                }



            
            </style>

<?php $__env->stopSection(); ?>    
<?php echo $__env->make('site.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\ost_erp\resources\views/site/home.blade.php ENDPATH**/ ?>