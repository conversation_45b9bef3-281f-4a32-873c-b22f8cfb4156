<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ShonaDesign extends Model
{
    use HasFactory;
    
    protected $table = 'shona_designs';
    
    protected $fillable = [
        // Font Configuration
        'Font_Type',
        
        // Body Styling
        'Body_BG_Type',
        'Body_BG_Image',
        'Body_BG_Color',
        'Body_Title_Color',
        
        // Header Styling
        'Header_BG_Color',
        'Header_Txt_Color',
        'Header_Logo_Size',
        'Header_Height',
        
        // Navigation Styling
        'Navbar_BG_Color',
        'Navbar_Txt_Color',
        'Navbar_Hover_BG_Color',
        'Navbar_Hover_Txt_Color',
        'Navbar_Active_BG_Color',
        'Navbar_Active_Txt_Color',
        
        // Hero Section
        'Hero_Overlay_Color',
        'Hero_Overlay_Opacity',
        'Hero_Title_Color',
        'Hero_Subtitle_Color',
        'Hero_Button_BG_Color',
        'Hero_Button_Txt_Color',
        'Hero_Button_Hover_BG_Color',
        'Hero_Button_Hover_Txt_Color',
        
        // Sections Styling
        'Section_Title_Color',
        'Section_Subtitle_Color',
        'Section_Text_Color',
        'Section_BG_Color',
        'Section_Alt_BG_Color',
        
        // Cards Styling
        'Card_BG_Color',
        'Card_Border_Color',
        'Card_Shadow_Color',
        'Card_Title_Color',
        'Card_Text_Color',
        'Card_Hover_BG_Color',
        'Card_Hover_Shadow_Color',
        
        // Buttons Styling
        'Primary_Button_BG_Color',
        'Primary_Button_Txt_Color',
        'Primary_Button_Hover_BG_Color',
        'Primary_Button_Hover_Txt_Color',
        'Secondary_Button_BG_Color',
        'Secondary_Button_Txt_Color',
        'Secondary_Button_Hover_BG_Color',
        'Secondary_Button_Hover_Txt_Color',
        
        // Footer Styling
        'Footer_BG_Color',
        'Footer_Title_Color',
        'Footer_Txt_Color',
        'Footer_Link_Color',
        'Footer_Link_Hover_Color',
        'Footer_Social_BG_Color',
        'Footer_Social_Txt_Color',
        'Footer_Social_Hover_BG_Color',
        'Footer_Social_Hover_Txt_Color',
        
        // Form Styling
        'Form_Input_BG_Color',
        'Form_Input_Border_Color',
        'Form_Input_Txt_Color',
        'Form_Input_Focus_Border_Color',
        'Form_Label_Color',
        'Form_Button_BG_Color',
        'Form_Button_Txt_Color',
        
        // Product/Service Cards
        'Product_Card_BG_Color',
        'Product_Card_Border_Color',
        'Product_Title_Color',
        'Product_Price_Color',
        'Product_Description_Color',
        'Product_Badge_BG_Color',
        'Product_Badge_Txt_Color',
        
        // Testimonials
        'Testimonial_BG_Color',
        'Testimonial_Border_Color',
        'Testimonial_Text_Color',
        'Testimonial_Author_Color',
        'Testimonial_Quote_Color',
        
        // Gallery
        'Gallery_Overlay_Color',
        'Gallery_Overlay_Opacity',
        'Gallery_Icon_Color',
        'Gallery_Title_Color',
        'Gallery_Category_Color',
        
        // Blog/Articles
        'Blog_Card_BG_Color',
        'Blog_Card_Border_Color',
        'Blog_Title_Color',
        'Blog_Meta_Color',
        'Blog_Text_Color',
        'Blog_Read_More_Color',
        
        // Preloader
        'Preloader_BG_Color',
        'Preloader_Spinner_Color',
        'Preloader_Text_Color',
        
        // Modal/Popup
        'Modal_BG_Color',
        'Modal_Overlay_Color',
        'Modal_Title_Color',
        'Modal_Text_Color',
        'Modal_Button_BG_Color',
        'Modal_Button_Txt_Color',
        
        // Pagination
        'Pagination_BG_Color',
        'Pagination_Txt_Color',
        'Pagination_Active_BG_Color',
        'Pagination_Active_Txt_Color',
        'Pagination_Hover_BG_Color',
        'Pagination_Hover_Txt_Color',
        
        // Breadcrumb
        'Breadcrumb_BG_Color',
        'Breadcrumb_Txt_Color',
        'Breadcrumb_Link_Color',
        'Breadcrumb_Active_Color',
        
        // Feature Toggles
        'Show_Preloader',
        'Show_Breadcrumbs',
        'Show_Social_Links',
        'Show_Contact_Info',
        'Enable_Dark_Mode',
        'Enable_RTL_Support',
        
        // Animation Settings
        'Enable_Animations',
        'Animation_Duration',
        'Animation_Easing',
        
        // Layout Settings
        'Container_Max_Width',
        'Section_Padding',
        'Card_Border_Radius',
        'Button_Border_Radius',
        
        // Typography
        'Primary_Font_Family',
        'Secondary_Font_Family',
        'Base_Font_Size',
        'Line_Height',
        'Heading_Font_Weight',
        'Body_Font_Weight',
    ];
    
    protected $casts = [
        'Show_Preloader' => 'boolean',
        'Show_Breadcrumbs' => 'boolean',
        'Show_Social_Links' => 'boolean',
        'Show_Contact_Info' => 'boolean',
        'Enable_Dark_Mode' => 'boolean',
        'Enable_RTL_Support' => 'boolean',
        'Enable_Animations' => 'boolean',
        'Hero_Overlay_Opacity' => 'float',
        'Gallery_Overlay_Opacity' => 'float',
    ];
    
    /**
     * Get default design configuration
     */
    public static function getDefaults()
    {
        return [
            'Font_Type' => 1,
            'Body_BG_Type' => 1,
            'Body_BG_Color' => '#ffffff',
            'Header_BG_Color' => '#ffffff',
            'Header_Txt_Color' => '#333333',
            'Navbar_BG_Color' => '#2c3e50',
            'Navbar_Txt_Color' => '#ffffff',
            'Primary_Button_BG_Color' => '#3498db',
            'Primary_Button_Txt_Color' => '#ffffff',
            'Footer_BG_Color' => '#2c3e50',
            'Footer_Txt_Color' => '#ffffff',
            'Show_Preloader' => true,
            'Show_Breadcrumbs' => true,
            'Show_Social_Links' => true,
            'Show_Contact_Info' => true,
            'Enable_Animations' => true,
            'Animation_Duration' => '0.3s',
            'Container_Max_Width' => '1200px',
            'Section_Padding' => '80px',
            'Card_Border_Radius' => '8px',
            'Button_Border_Radius' => '4px',
        ];
    }
    
    /**
     * Get current design configuration or create default
     */
    public static function getCurrentDesign()
    {
        $design = self::orderBy('id', 'desc')->first();
        
        if (!$design) {
            $design = self::create(self::getDefaults());
        }
        
        return $design;
    }
}
