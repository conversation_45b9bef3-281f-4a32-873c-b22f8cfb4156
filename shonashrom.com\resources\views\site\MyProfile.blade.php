@extends('site.index')
@section('content')
@php
use App\Models\City;
use App\Models\Place;
use App\Models\Governrate;
use App\Models\ProductOrder;
use App\Models\Products;
@endphp
<title>{{trans('admin.Profile')}}</title>
<style>
    select, .single-select, .form-control, .form-select, textarea, input{
        color:white !important;
    }
</style>

    <!--==============================
    Breadcumb
    ============================== -->
    <div class="breadcumb-wrapper" data-bg-src="{{asset('Front/assets/img/bg/breadcrumb-bg.png')}}">
        <!-- bg animated image/ -->
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="breadcumb-content">
                        <h1 class="breadcumb-title">{{trans('admin.Profile')}}</h1>
                        <ul class="breadcumb-menu">
                            <li><a href="{{url('/')}}">{{trans('admin.Home')}}</a></li>
                            <li class="active">{{trans('admin.Profile')}}</li>
                        </ul>
                    </div>
                </div>
            </div>

        </div>
    </div>



      <div class="service-bg2-area mt-5 mb-5">
        <!--==============================
        BMI Area  
        ==============================-->
        <div class="bmi-area-1 mt-5 mb-5">
            <div class="container">
                <div class="row justify-content-between">
                    <div class="col-lg-12 align-self-end">
                        <div class="bmi-calculator-form">
                            <h4 class="form-title">{{trans('admin.Profile')}}</h4>
                                <form method="post" action="{{url('UpdateAccount')}}" style="    display: flex;flex-wrap: wrap;">
                           @csrf
                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <input  type="text" class="form-control style-border"  name="name" placeholder="{{trans('admin.Name')}}" value="{{$item->name}}" required>
                                        </div>
                                    </div>
                                 
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <input  type="tel" class="form-control style-border" name="phone" value="{{$item->phone}}" placeholder="{{trans('admin.Phone')}}" required>
                                        </div>
                                    </div>
                              <div class="col-lg-12">
                                        <div class="form-group">
                                            <input type="email" class="form-control style-border"  name="email" placeholder="{{trans('admin.Email')}}" value="{{$item->email}}" required>
                                        </div>
                                    </div>
                                    
                                </div>
                      




                             
                                <div class="btn style2 mt-5">
                                     <button type="submit" class="btn style2">   {{trans('admin.Update')}}  </button>                            
                                </div>
                              
                            </form>
                        </div>                  
                    </div>
                </div>
            </div>
        </div>


    </div>
    
       <div class="bmi-area-1 ">
            <div class="container">
                <div class="row justify-content-between">
                    <div class="col-lg-12 align-self-end">
                        <div class="bmi-calculator-form">
                            <h4 class="form-title">Update Your Password</h4>
                        <form method="post" action="{{url('UpdatePassword')}}">
                                 @csrf
                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <input type="password" class="form-control style-border" name="CurrentPassword" placeholder="{{trans('admin.CurrentPassword')}}" required>
                                        </div>
                                    </div>
                                 
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <input type="password" class="form-control style-border" name="password"  placeholder="{{trans('admin.New_Password')}}" required>
                                        </div>
                                    </div>
                              <div class="col-lg-12">
                                        <div class="form-group">
                                            <input type="password" class="form-control style-border"  name="Confirm_Password" placeholder="{{trans('admin.Confirm_Password')}}"  required>
                                        </div>
                                    </div>
                                    
                                </div>
                             
                             
                                <div class="btn style3 mt-5">
                                   <button type="submit" class="btn style2">   {{trans('admin.Update')}}  </button>                           
                                </div>

                            </form>
                        </div>                  
                    </div>
                </div>
            </div>
        </div>

<div class="container mt-5 mb-5 tabless" style="overflow-x:scroll; ">
   
              <table id="mytable" class="table align-middle mb-0 bg-white table-responsive">
                   <div class="mb-2 myprofile">
           <h2 class="">{{trans('admin.Addresses')}}</h2>
   
            <button class="ProfileButton btn btn-link btn-sm btn-rounded collapsed" data-bs-toggle="collapse" data-bs-target="#collapseExample" aria-expanded="false" aria-controls="collapseExample" style="color:white;">
                     {{trans('admin.Add_New_Address')}}
            </button> 
         
         </div>
              <thead class="bg-light">
                <tr class="header-row">
                                       <th>{{trans('admin.Name')}}</th>
                                                                        <th>{{trans('admin.Governrate')}}</th>
                                                                        <th>{{trans('admin.City')}}</th>
                                                                        <th>{{trans('admin.Place')}}</th>
                                                                         <th>{{trans('admin.Street')}}</th>    
                                                                         <th>{{trans('admin.Buliding')}}</th>    
                                                                         <th>{{trans('admin.Floor')}}</th>    
                                                                         <th>{{trans('admin.Flat')}}</th>    
                                                                         <th>{{trans('admin.Special_Mark')}} </th>    
                                                                         <th>{{trans('admin.Location')}} </th>    
                                                                         <th>{{trans('admin.Address_Details')}} </th>    
                                                                         <th>{{trans('admin.Actions')}} </th>   
                </tr>
              </thead>
              <tbody>
       
            
               @foreach($Addersses as $address)
             <tr style="text-align: center;">
                 
               <td>
                 <div class="d-flex align-items-center">
                   <div class="">
                     <p class="fw-bold mb-1">{{$address->Address_Name}}</p>
                   </div>
                 </div>
               </td>             
               <td>
                 <div class="d-flex align-items-center">
                   <div class="">
                     <p class="fw-bold mb-1">                 {{app()->getLocale() == 'ar' ?$address->Gov()->first()->Arabic_Name :$address->Gov()->first()->English_Name}} </p>
                   </div>
                 </div>
               </td>             
               <td>
                 <div class="d-flex align-items-center">
                   <div class="">
                     <p class="fw-bold mb-1">    {{app()->getLocale() == 'ar' ?$address->City()->first()->Arabic_Name :$address->City()->first()->English_Name}}    </p>
                   </div>
                 </div>
               </td>             
               <td>
                 <div class="d-flex align-items-center">
                   <div class="">
                     <p class="fw-bold mb-1">         {{app()->getLocale() == 'ar' ?$address->Place()->first()->Arabic_Name :$address->Place()->first()->English_Name}}  </p>
                   </div>
                 </div>
               </td>             
               <td>
                 <div class="d-flex align-items-center">
                   <div class="">
                     <p class="fw-bold mb-1">{{$address->Street}}</p>
                   </div>
                 </div>
               </td>             
               <td>
                 <div class="d-flex align-items-center">
                   <div class="">
                     <p class="fw-bold mb-1">{{$address->Bulliding}}</p>
                   </div>
                 </div>
               </td>             
               <td>
                 <div class="d-flex align-items-center">
                   <div class="">
                     <p class="fw-bold mb-1">{{$address->Floor}}</p>
                   </div>
                 </div>
               </td>             
               <td>
                 <div class="d-flex align-items-center">
                   <div class="">
                     <p class="fw-bold mb-1">{{$address->Flat}}</p>
                   </div>
                 </div>
               </td>             
               <td>
                 <div class="d-flex align-items-center">
                   <div class="">
                     <p class="fw-bold mb-1">{{$address->Special_Mark}}</p>
                   </div>
                 </div>
               </td>             
               <td>
                 <div class="d-flex align-items-center">
                   <div class="">
                     <p class="fw-bold mb-1">{{$address->Location}}</p>
                   </div>
                 </div>
               </td>             
               <td>
                 <div class="d-flex align-items-center">
                   <div class="">
                     <p class="fw-bold mb-1">{{$address->Address_Details}}</p>
                   </div>
                 </div>
               </td>             

               <td>
                  <button
                  type="button"
               class="btn btn-link btn-sm btn-rounded text-primary"
                  type="button" data-bs-toggle="modal" data-bs-target="#delete{{$address->id}}" onclick="Show({{$address->id}})" >
                 
                <i class="far fa-trash"></i>
                </button>

 <button
 type="button"
   class="btn btn-link btn-sm btn-rounded text-primary" onclick="ShowW({{$address->id}})"
 type="button" data-bs-toggle="modal" data-bs-target="#edit{{$address->id}}"  >

<i class="far fa-wrench"></i>
</button>
                   
                   

               </td>
             </tr>
        @endforeach
              
              </tbody>
            </table>
   

    </div>

<div class="collapse" id="collapseExample">
  <div class="card card-body">
   <div class="container">
             <div class="bmi-area-1 mt-5 mb-5">
            <div class="container">
                <div class="row justify-content-between">
                    <div class="col-lg-12 align-self-end">
                        <div class="bmi-calculator-form">
                            <h4 class="form-title">{{trans('admin.Add_New_Address')}}
 
                                   </h4>
                               <form method="post" action="{{url('UpdateAddress')}}">
                                    @csrf
     <div class="row justify-content-center g-4 mb-3">
              <div class=" col-lg-4 col-12">
                <div class="account__form-inputgroup">
                  <div class="tp-support-form-field mb-20">
              <input type="text" name="Address_Name" class="form-control" placeholder="{{trans('admin.Address_Name')}}" required>
                      
                  </div>
                </div>
              </div>
              <div class="col-lg-4  col-12">
                <div class="account__form-inputgroup">
                  <div class="tp-support-form-field mb-20">
                     <input type="text" name="StreetAdd" class="form-control" placeholder="{{trans('admin.Street')}}" required>
                      
                  </div>
                </div>
              </div>
              <div class=" col-lg-4 col-12">
                <div class="account__form-inputgroup">
                  <div class="tp-support-form-field mb-20">
                 <input type="text" name="BulidingAdd" class="form-control" placeholder="{{trans('admin.Buliding')}}" required>
                      
                  </div>
                </div>
              </div>
              <div class="col-lg-4 col-12">
                <div class="account__form-inputgroup">
                  <div class="tp-support-form-field mb-20">
                      <input type="text" name="FloorAdd" class="form-control" placeholder="{{trans('admin.Floor')}}" required>
                      
                  </div>
                </div>
              </div>
              <div class="col-lg-4 col-12">
               <div class="account__form-inputgroup">
                 <div class="tp-support-form-field mb-20">
                   <input type="text" name="FlatAdd" class="form-control" placeholder="{{trans('admin.Flat')}}" required>
                     
                 </div>
               </div>
             </div>
             <div class="col-lg-4 col-12">
               <div class="account__form-inputgroup">
                 <div class="tp-support-form-field mb-20">
                   <input type="text" name="Special_MarkAdd" class="form-control" placeholder="{{trans('admin.Special_Mark')}}">
                     
                 </div>
               </div>
             </div>
             <div class="col-lg-6 col-12">
               <div class="account__form-inputgroup">
                 <div class="tp-support-form-field mb-20">
                 <input type="text" name="LocationAdd" class="form-control" placeholder="{{trans('admin.Location')}}">
                     
                 </div>
               </div>
             </div>
             <div class="col-lg-6 col-12">
               <div class="account__form-inputgroup">
                 <div class="tp-support-form-field mb-20">
                      <input type="text" name="Address_DetailsAdd" class="form-control" placeholder="{{trans('admin.Address_Details')}}" required>
                     
                 </div>
               </div>
             </div>
                  
      
                
                
                        <div class="col-lg-4 col-12">
               <div class="account__form-inputgroup">
                 <div class="tp-support-form-field mb-20">
                                     <label for="CustomerPassword">{{trans('admin.Governrate')}}</label>
                              <select class="form-control X Governrate" name="Governrate"  id=""  required>
                                  <option value="">{{trans('admin.Governrate')}}</option>
                                  @foreach($Governrate as $gov)
                               <option value="{{$gov->id}}">
                     {{app()->getLocale() == 'ar' ?$gov->Arabic_Name :$gov->English_Name}}
                               </option>      
                                  @endforeach
                              </select> 
                 </div>
               </div>
             </div>
                
                                 
                         
              <div class=" col-lg-4 col-12">
                <div class="account__form-inputgroup">
                  <div class="tp-support-form-field mb-20">
                    <label for="CustomerPassword">{{trans('admin.City')}}</label>
                              <select class="form-control X City" name="City"  id="" style="display: block !important"  required>
                  
                              </select>  
                    </div>  
                    </div>  
                    </div>  
                
                                                 
                                                 
                 
              <div class=" col-lg-4 col-12">
                <div class="account__form-inputgroup">
                  <div class="tp-support-form-field mb-20">
                             <label for="CustomerPassword">{{trans('admin.Place')}}</label>
                              <select class="form-control X Place" style="display: block !important" name="Place"  id=""  required>
                   
                              </select>
                    </div>  
                    </div>  
                    </div>  
             
                         
                
                                            
                 </div>
           
        
                                <div class="btn style3 mt-5">
                                     <button type="submit" class="tp-btn-round custom-btn--fluid" ><span>{{trans('admin.Submit')}}</span></button>                           
                                </div>
                              
                            </form>
                        </div>                  
                    </div>
                </div>
            </div>
        </div>

      </div>
  </div>
</div>
    
   <div class="container mt-5 mb-5" style="overflow-x:scroll; ">
           <div class="mb-3 myprofile">
           <h2 class="">{{trans('admin.MyOrders')}}</h2>
   
         
         </div>
              <table id="mytable" class="table align-middle mb-0 bg-white table-responsive">
              <thead class="bg-light">
                <tr class="header-row">
                   <th>{{trans('admin.Date')}}</th>
                  <th>{{trans('admin.Total')}}</th>
                  <th>{{trans('admin.Products')}}</th>
                  <th>{{trans('admin.Address')}}</th>
                </tr>
              </thead>
              <tbody>
               @foreach($Orders as $order)
                <tr style="text-align: center;">
                  <td>
                  
                    
                        <p class="fw-bold mb-1">{{$order->Date}}</p>
                  
                    
                  </td>
                  <td>
                    <p class="fw-bold fw-normal mb-1">{{$order->Total_Price}}</p>
                  </td>
                  <td>
                  <button
                  type="button"
               class="btn btn-link btn-sm btn-rounded text-primary"
                  type="button" data-bs-toggle="modal" data-bs-target="#Pro{{$order->id}}" onclick="ShowO({{$order->id}})" >
                 
             {{trans('admin.Products')}}
                </button>
   
  
           
                  </td>
   <td>


 <button
 type="button"
   class="btn btn-link btn-sm btn-rounded text-primary" onclick="ShowWO({{$order->id}})"
 type="button" data-bs-toggle="modal" data-bs-target="#Add{{$order->id}}"  >

{{trans('admin.Address')}}
</button>
       
       

                  </td>
                </tr>
           
                  @endforeach
            
              </tbody>
            </table>

    </div>


   




   @foreach($Addersses as $address)    

<!-- Modal Delete-->
<div class="modal fade" id="delete{{$address->id}}" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel"> {{trans('admin.Delete')}}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
          {{trans('admin.RUSWDT')}} <strong>{{$address->Address_Name}}</strong>
      </div>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" >{{trans('admin.No')}}</button>
                              <a href="{{url('DeleteMyAddress/'.$address->id.'/'.$address->Address_ID)}}"  class="btn btn-primary"> {{trans('admin.Yes')}}</a>
      </div>
    </div>
  </div>
</div>



<!-- Modal Edit-->
<div class="modal fade" id="edit{{$address->id}}" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel"> {{trans('admin.Edit')}}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
           <form method="post" action="{{url('EditAddress')}}">
                                    @csrf
                    <input type="hidden" name="ADDRESS_ID" value="{{$address->Address_ID}}">                
                    <input type="hidden" name="ID" value="{{$address->id}}">   
               

            <!-- Sign up form content -->
            <div class="row justify-content-center g-4 mb-3">
              <div class=" col-lg-4 col-12">
                <div class="account__form-inputgroup">
                  <div class="tp-support-form-field mb-20">
                <label>{{trans('admin.Address_Name')}}</label>      
              <input type="text" name="Address_Name" value="{{$address->Address_Name}}" class="form-control" placeholder="{{trans('admin.Address_Name')}}" required>
                      
                  </div>
                </div>
              </div>
              <div class="col-lg-4  col-12">
                <div class="account__form-inputgroup">
                  <div class="tp-support-form-field mb-20">
                           <label>{{trans('admin.Street')}}</label> 
                     <input type="text" name="StreetAdd" value="{{$address->Street}}"  class="form-control" placeholder="{{trans('admin.Street')}}" required>
                      
                  </div>
                </div>
              </div>
              <div class=" col-lg-4 col-12">
                <div class="account__form-inputgroup">
                  <div class="tp-support-form-field mb-20">
                           <label>{{trans('admin.Buliding')}}</label> 
                 <input type="text" name="BulidingAdd" value="{{$address->Bulliding}}"  class="form-control" placeholder="{{trans('admin.Buliding')}}" required>
                      
                  </div>
                </div>
              </div>
              <div class="col-lg-4 col-12">
                <div class="account__form-inputgroup">
                  <div class="tp-support-form-field mb-20">
                           <label>{{trans('admin.Floor')}}</label> 
                      <input type="text" name="FloorAdd" value="{{$address->Floor}}"  class="form-control" placeholder="{{trans('admin.Floor')}}" required>
                      
                  </div>
                </div>
              </div>
              <div class="col-lg-4 col-12">
               <div class="account__form-inputgroup">
                 <div class="tp-support-form-field mb-20">
                          <label>{{trans('admin.Flat')}}</label> 
                   <input type="text" name="FlatAdd" value="{{$address->Flat}}"  class="form-control" placeholder="{{trans('admin.Flat')}}" required>
                     
                 </div>
               </div>
             </div>
             <div class="col-lg-4 col-12">
               <div class="account__form-inputgroup">
                 <div class="tp-support-form-field mb-20">
                          <label>{{trans('admin.Special_Mark')}}</label> 
                   <input type="text" name="Special_MarkAdd" value="{{$address->Special_Mark}}"  class="form-control" placeholder="{{trans('admin.Special_Mark')}}">
                     
                 </div>
               </div>
             </div>
             <div class="col-lg-6 col-12">
               <div class="account__form-inputgroup">
                 <div class="tp-support-form-field mb-20">
                          <label>{{trans('admin.Location')}}</label> 
                 <input type="text" name="LocationAdd" value="{{$address->Location}}"  class="form-control" placeholder="{{trans('admin.Location')}}">
                     
                 </div>
               </div>
             </div>
             <div class="col-lg-6 col-12">
               <div class="account__form-inputgroup">
                 <div class="tp-support-form-field mb-20">
                          <label>{{trans('admin.Address_Details')}}</label> 
                      <input type="text" name="Address_DetailsAdd"  value="{{$address->Address_Details}}" class="form-control" placeholder="{{trans('admin.Address_Details')}}" required>
                     
                 </div>
               </div>
             </div>
                  
      
                
                
                        <div class="col-lg-4 col-12">
               <div class="account__form-inputgroup">
                 <div class="tp-support-form-field mb-20">
                                     <label for="CustomerPassword">{{trans('admin.Governrate')}}</label>
                              <select class="form-control X Governrate" name="Governrate"  id=""  required>
                                  <option value="">{{trans('admin.Governrate')}}</option>
                                  @foreach($Governrate as $gov)
                               <option value="{{$gov->id}}" @if($gov->id == $address->Gov) selected @endif>
                     {{app()->getLocale() == 'ar' ?$gov->Arabic_Name :$gov->English_Name}}
                               </option>      
                                  @endforeach
                              </select> 
                 </div>
               </div>
             </div>
                
                                 
                         
              <div class=" col-lg-4 col-12">
                <div class="account__form-inputgroup">
                  <div class="tp-support-form-field mb-20">
                    <label for="CustomerPassword">{{trans('admin.City')}}</label>
                      @php   $CITY=City::where('id',$address->City)->first();  @endphp
                              <select class="form-control X City" name="City"  id="" style="display: block !important"  required>
                  
                                  @if(!empty($CITY))
                            <option value="{{$CITY->id}}">
                        {{app()->getLocale() == 'ar' ?$CITY->Arabic_Name :$CITY->English_Name}}          
                                  </option>      
                                  @endif
                              </select>  
                    </div>  
                    </div>  
                    </div>  
                
                                                 
                                                 
                 
              <div class=" col-lg-4 col-12">
                <div class="account__form-inputgroup">
                  <div class="tp-support-form-field mb-20">
                    @php   $PLACE=Place::where('id',$address->Place)->first();  @endphp      
                             <label for="CustomerPassword">{{trans('admin.Place')}}</label>
                              <select class="form-control X Place" style="display: block !important" name="Place"  id=""  required>
                   
                                                @if(!empty($PLACE))
                            <option value="{{$PLACE->id}}">
                        {{app()->getLocale() == 'ar' ?$PLACE->Arabic_Name :$PLACE->English_Name}}          
                                  </option>      
                                  @endif
                              </select>
                    </div>  
                    </div>  
                    </div>  
             
                         
                
                                            
                 </div>
           
            <div class="account__form-btn mt-4">
              <button type="submit" class="tp-btn-round custom-btn--fluid" ><span>{{trans('admin.Submit')}}</span></button>
            </div>
          
          
          </form>
        </div>  
              <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" >{{trans('admin.Close')}}</button>
        </div>
    </div>
  </div>
</div>

@endforeach



   @foreach($Orders as $order)    

<!-- Modal Products-->
<div class="modal fade" id="Pro{{$order->id}}" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel"> {{trans('admin.Products')}}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
          
        @php  $Prods=ProductOrder::where('Order',$order->id)->get();  @endphp
                   <table id="mytable" class="table align-middle mb-0 bg-white">
              <thead class="bg-light">
                <tr class="header-row" style="text-align: center;">
  
                  <th>{{trans('admin.Image')}}</th>
                  <th>{{trans('admin.Name')}}</th>
                  <th>{{trans('admin.Qty')}}</th>
                  <th>{{trans('admin.Price')}}</th>
                  <th>{{trans('admin.Total')}}</th>
           
                </tr>
              </thead>
              <tbody>
                  @foreach($Prods as $ppro)
  @php  $OO=Products::where('id',$ppro->Product)->first();  @endphp                  
                <tr style="text-align: center;">
                  <td>

                <img src="{{URL::to($OO->Image)}}" style="width: 30%">      
                  </td>
                    
                    <td>
    {{app()->getLocale() == 'ar' ?$OO->P_Ar_Name :$OO->P_En_Name}}                
                    </td>
                    
                    <td>{{$ppro->Qty}}</td>
                    <td>{{$ppro->Price}}</td>
                    <td>{{$ppro->Total}}</td>
            
                </tr>
           
                  @endforeach
              </tbody>
            </table>
   
        
      </div>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" >{{trans('admin.Close')}}</button>

      </div>
    </div>
  </div>
</div>


<!-- Modal Address-->
<div class="modal fade" id="Add{{$order->id}}" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel"> {{trans('admin.Products')}}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
          

                   <table id="mytable" class="table align-middle mb-0 bg-white">
              <thead class="bg-light">
                <tr class="header-row" style="text-align: center;">
  
                                   <th>{{trans('admin.Name')}}</th>
                                   <th>{{trans('admin.Email')}}</th>
                                   <th>{{trans('admin.Phone')}}</th>
                                   <th>{{trans('admin.OtherPhone')}}</th>
                                   <th>{{trans('admin.Address_Name')}}</th>
                    <th>{{trans('admin.Special_Mark')}} </th>     
                                                                  
                                                                         <th>{{trans('admin.Street')}}</th>    
                                                                         <th>{{trans('admin.Buliding')}}</th>    
                                                                         <th>{{trans('admin.Floor')}}</th>    
                                                                         <th>{{trans('admin.Flat')}}</th>    
                                                                          <th>{{trans('admin.Governrate')}}</th>
                                                                        <th>{{trans('admin.City')}}</th>
                                                                        <th>{{trans('admin.Place')}}</th>
                                                                         <th>{{trans('admin.Location')}} </th>    
                                                                         <th>{{trans('admin.Address_Details')}} </th>    
                        
           
                </tr>
              </thead>
              <tbody>
            
                <tr style="text-align: center;">
              
                    <td>{{$order->Name}}</td>
                    <td>{{$order->Email}}</td>
                    <td>{{$order->Phone}}</td>
                    <td>{{$order->OtherPhone}}</td>
                    <td>{{$order->Address_Name}}</td>
                    <td>{{$order->Special_MarkAdd}}</td>
                    <td>{{$order->StreetAdd}}</td>
                    <td>{{$order->BulidingAdd}}</td>
                    <td>{{$order->FloorAdd}}</td>
                    <td>{{$order->FlatAdd}}</td>
                    
                    <td>
                    
                @php  $govo=Governrate::where('id',$order->Governrate)->first();  @endphp
                        
                    @if(!empty($govo))    
                    {{app()->getLocale() == 'ar' ?$govo->Arabic_Name :$govo->English_Name}}    
                        
                    @endif    
                        
                    </td>            
                    <td>
                    
                @php  $govoC=City::where('id',$order->City)->first();  @endphp
                        
                    @if(!empty($govoC))    
                    {{app()->getLocale() == 'ar' ?$govoC->Arabic_Name :$govoC->English_Name}}    
                        
                    @endif    
                        
                    </td>                 
                    <td>
                    
                @php  $govoP=Place::where('id',$order->Place)->first();  @endphp
                        
                    @if(!empty($govoP))    
                    {{app()->getLocale() == 'ar' ?$govoP->Arabic_Name :$govoP->English_Name}}    
                        
                    @endif    
                        
                    </td>
            


                    
                    <td>{{$order->LocationAdd}}</td>
                    <td>{{$order->Address_DetailsAdd}}</td>

            
                </tr>
           
               
              </tbody>
            </table>
   
        
      </div>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" >{{trans('admin.Close')}}</button>

      </div>
    </div>
  </div>
</div>


@endforeach




<script>

function myFunction() {
  var x = document.getElementById("myDIV");
  if (x.style.display === "block") {
    x.style.display = "none";
  } else {
    x.style.display = "block";
  }
}</script>


<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

<!-- Filter Governrate and City !-->
<script>
   $(document).ready(function() {
   
       $('#Governrate').on('change', function(){
           var countryId = $(this).val();
           if(countryId) {
               $.ajax({
                   url: 'GovernrateFilterr/'+countryId,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('#City').empty();
   
                       $.each(data, function(key, value){
   
             $('#City').append('<option value="'+ key +'">' + value + '</option>');
  
                           
                       });
                       
                      var CIITY = $('#City').val();  
                         $.ajax({
                   url: 'CityFilterr/'+CIITY,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('#Place').empty();
   
                       $.each(data, function(key, value){
   
             $('#Place').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                       
       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
                       
                       
                       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
           } else {
   
               $('select[name="states"]').empty();
           }
   
       });
   
   });
    
   $(document).ready(function() {
   
   
           var countryId = $('#Governrate').val();
           if(countryId) {
               $.ajax({
                   url: 'GovernrateFilterr/'+countryId,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('#City').empty();
   
                       $.each(data, function(key, value){
   
             $('#City').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                       
                     var CIITY = $('#City').val();  
                         $.ajax({
                   url: 'CityFilterr/'+CIITY,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('#Place').empty();
   
                       $.each(data, function(key, value){
   
             $('#Place').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                       
       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
                              
                       
                       
                       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
           } else {
   
               $('select[name="states"]').empty();
           }
   
   
   });  
    
    
 $('#City').on('change', function(){
      var CIITY = $('#City').val();  
                         $.ajax({
                   url: 'CityFilterr/'+CIITY,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('#Place').empty();
   
                       $.each(data, function(key, value){
   
             $('#Place').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                       
       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
                       
     
 });
    
</script>


<script>
   $(document).ready(function() {
   
       $('.Governrate').on('change', function(){
           var countryId = $(this).val();
           if(countryId) {
               $.ajax({
                   url: 'GovernrateFilterr/'+countryId,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('.City').empty();
   
                       $.each(data, function(key, value){
   
             $('.City').append('<option value="'+ key +'">' + value + '</option>');
  
                           
                       });
                       
                      var CIITY = $('.City').val();  
                         $.ajax({
                   url: 'CityFilterr/'+CIITY,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('.Place').empty();
   
                       $.each(data, function(key, value){
   
             $('.Place').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                       
       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
                       
                       
                       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
           } else {
   
               $('select[name="states"]').empty();
           }
   
       });
   
   });
    
   $(document).ready(function() {
   
   
           var countryId = $('.Governrate').val();
           if(countryId) {
               $.ajax({
                   url: 'GovernrateFilterr/'+countryId,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('.City').empty();
   
                       $.each(data, function(key, value){
   
             $('.City').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                       
                     var CIITY = $('.City').val();  
                         $.ajax({
                   url: 'CityFilterr/'+CIITY,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('.Place').empty();
   
                       $.each(data, function(key, value){
   
             $('.Place').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                       
       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
                              
                       
                       
                       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
           } else {
   
               $('select[name="states"]').empty();
           }
   
   
   });  
    
    
 $('.City').on('change', function(){
      var CIITY = $('.City').val();  
                         $.ajax({
                   url: 'CityFilterr/'+CIITY,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('.Place').empty();
   
                       $.each(data, function(key, value){
   
             $('.Place').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                       
       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
                       
     
 });
    
</script>

<style>

    .X{
        display: block !important;
    }
    
    .nice-select{
display: none !important;
    }
    
    
    .modal{
        display: none ;
    }
</style>

<script>
 function Show(r){
     
     document.getElementById('#delete'+r).style.display='block';
     

 } 
    
    function ShowW(r){
     
     document.getElementById('#edit'+r).style.display='block';
     

 }    
    function ShowO(r){
     
     document.getElementById('#Pro'+r).style.display='block';
     




 }    
    function ShowWO(r){
     
     document.getElementById('#Add'+r).style.display='block';
     

 }
</script>


@endsection