
 <h1 style="text-align: center; color:#dc6255; font-style: italic "> {{trans('admin.Klar')}} </h1>

            <div class="row" style="text-align: center">
    <div class="col-md-6" style="text-align: center">{{trans('admin.Welcome')}}</div>
    <div class="col-md-6" style="text-align: center"> {{$data['data']->name}}</div>
                 </div>



         <div class="row" style="text-align: center">
    <div class="col-md-6" style="text-align: center">{{trans('admin.Reset_Password')}}</div>
    <div class="col-md-6" style="text-align: center"> 
        
       @component('mail::button', ['url' => url('reset/password/'.$data['token'])])

{{trans('admin.Click_Here_to_create_new_password')}}
@endcomponent
             </div>
                 </div>


   <div class="row" style="text-align: center">
    <div class="col-md-6" style="text-align: center">{{trans('admin.OR')}} </div>
    <div class="col-md-6" style="text-align: center">{{trans('admin.Copy_this_link')}}</div>
    <div class="col-md-6" style="text-align: center">
    <a href="{{url('reset/password/'.$data['token'])}}">{{url('reset/password/'.$data['token'])}}</a>
           </div>
                 </div>



    <div class="row" style="text-align: center; color: darkred">
            <div class="col-md-12">{{trans('admin.Thanks')}}</div>    
         <div class="col-md-12">  &copy; {{ date('Y') }} {{trans('admin.Klar')}}. {{trans('admin.All_Right_Reserved')}}.</div>
            <div class="col-md-12">
            <a href="https://klarapps.com" class="btn">{{trans('admin.Klar')}}/ https://klarapps.com </a>    
            </div>
                </div>



<style>
    .btn{
    display: inline-block;
    font-weight: 400;
    color: #373a3c;
    text-align: center;
    vertical-align: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: 0.625rem 1.1875rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 2px;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    color: #fff;
    background-color: #dc6255;
    border-color: #dc6255;
    text-decoration: unset;    
    }
    

</style>