@extends('site.index')
@section('content')
<title>{{trans('admin.Cart')}}</title>


    <!--==============================
    Breadcumb
    ============================== -->
    <div class="breadcumb-wrapper" data-bg-src="{{asset('Front/assets/img/bg/breadcrumb-bg.png')}}">
        <!-- bg animated image/ -->
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="breadcumb-content">
                        <h1 class="breadcumb-title">{{trans('admin.Cart')}}</h1>
                        <ul class="breadcumb-menu">
                            <li><a href="{{url('/')}}">{{trans('admin.Home')}}</a></li>
                            <li class="active">{{trans('admin.Cart')}}</li>
                        </ul>
                    </div>
                </div>
            </div>

        </div>
    </div>



<style>
    td{
        text-align: center;
    }
    </style>
    <!--==============================
    Cart Area  
    ==============================-->
   <div class="container">
       <form action="{{url('UpdateCart')}}" method="post">
                            @csrf
  <div class="content space" style="overflow-x: scroll;">
    <table border=" table-responsive">
      <tr>
             <th>{{trans('admin.Image')}}</th>
        <th>{{trans('admin.Name')}}</th>
        <th>{{trans('admin.Price')}}</th>
           <th>{{trans('admin.Qty')}}</th>
           <th>{{trans('admin.Total')}}</th>
        <th>{{trans('admin.Delete')}}</th>
          
      </tr>
        
        
               @foreach($Carts as $cart)  
         <input type="hidden" name="RowID[]" value="{{$cart->rowId}}">     
  
      <tr>
            <td><img src="{{URL::to($cart->options->image)}}" style="width: 70px; height:70px;"> </td>
        <td>{{app()->getLocale() == 'ar' ?$cart->name :$cart->options->other_name}} </td>
              <td>{{$cart->price}}</td>
      <td>        
                            <div class="quantity" style="background: black;">
                                <button class="quantity-minus qty-btn"><i class="fa-solid fa-minus"></i></button>
                                <input type="number" class="qty-input" step="1" min="1" max="100"  name="qty[]" value="{{$cart->qty}}" title="Qty">
                                <button class="quantity-plus qty-btn"><i class="fa-solid fa-plus"></i></button>                                
                            </div>
                  
                      </td>
    <td>    {{$cart->total}} </td>
        <td> <a href="{{url('DeleteCart/'.$cart->rowId)}}" class="remove"><i class="fal fa-times"></i></a></td>

      </tr>
          @endforeach
    </table>
           <div class="coupon2">
         <button class="btn d-xl-block " name="update_cart" type="submit">{{trans('admin.Update_Cart')}}</button>
                    </div>
  </div>
           </form>
</div>

     @if(Cart::content()->count() != 0) 
    <div class="row justify-content-end mb-10 mt-20 ">
                              <div class="col-md-5">
                                    <div class="cart-page-total">
                                       <h2>{{trans('admin.Total')}}</h2>
                                       <ul class="mb-20">
                                          <li class="cartch">{{trans('admin.Sub_Total')}} <span>{{Cart::subtotal()}}</span></li>
                                          <li class="cartch">{{trans('admin.Grand_Total')}} <span> {{str_replace(',', '', Cart::total())}}</span></li>
                                       </ul>
                                       <div class="checkout_btn f-right">
                                          <a class="btn d-xl-block " href="{{url('Checkout')}}">  {{trans('admin.Checkout')}}</a>
                                       </div>
                                    </div>
                              </div>
                           </div>
     @endif    

@endsection