/*! jQuery.Flipster, v1.1.5 (built 2020-10-17) */
!function(t,e,n){"use strict";function i(t,e){var n=null;return function(){var i=this,a=arguments;null===n&&(n=setTimeout((function(){t.apply(i,a),n=null}),e))}}var a,r=(a={},function(t){if(a[t]!==n)return a[t];var e=document.createElement("div").style,i=t.charAt(0).toUpperCase()+t.slice(1),r=(t+" "+["webkit","moz","ms","o"].join(i+" ")+i).split(" ");for(var s in r)if(r[s]in e)return a[t]=r[s];return a[t]=!1}),s="http://www.w3.org/2000/svg",o=t(e),l=r("transform"),c={itemContainer:"ul",itemSelector:"li",start:"center",fadeIn:400,loop:!1,autoplay:!1,pauseOnHover:!0,style:"coverflow",spacing:-.6,click:!0,keyboard:!0,scrollwheel:!0,touch:!0,nav:!1,buttons:!1,buttonPrev:"Previous",buttonNext:"Next",onItemSwitch:!1},u={main:"flipster",active:"flipster--active",container:"flipster__container",nav:"flipster__nav",navChild:"flipster__nav__child",navItem:"flipster__nav__item",navLink:"flipster__nav__link",navCurrent:"flipster__nav__item--current",navCategory:"flipster__nav__item--category",navCategoryLink:"flipster__nav__link--category",button:"flipster__button",buttonPrev:"flipster__button--prev",buttonNext:"flipster__button--next",item:"flipster__item",itemCurrent:"flipster__item--current",itemPast:"flipster__item--past",itemFuture:"flipster__item--future",itemContent:"flipster__item__content"},f=new RegExp("\\b("+u.itemCurrent+"|"+u.itemPast+"|"+u.itemFuture+")(.*?)(\\s|$)","g"),p=new RegExp("\\s\\s+","g");t.fn.flipster=function(e){if("string"==typeof e){var a=Array.prototype.slice.call(arguments,1);return this.each((function(){var n=t(this).data("methods");return n[e]?n[e].apply(this,a):this}))}var r=t.extend({},c,e);return this.each((function(){var e,a,c,v,h,d,m,g,_,y=t(this),b=[],x=0,w=!1;function C(e){return e=e||"next",t('<button class="'+u.button+" "+("next"===e?u.buttonNext:u.buttonPrev)+'" role="button" />').html((i="next"===(n=e)?r.buttonNext:r.buttonPrev,"custom"===r.buttons?i:'<svg viewBox="0 0 13 20" xmlns="'+s+'" aria-labelledby="title"><title>'+i+'</title><polyline points="10,3 3,10 10,17"'+("next"===n?' transform="rotate(180 6.5,10)"':"")+"/></svg>")).on("click",(function(t){D(e),t.preventDefault()}));var n,i}function k(){y.css("transition",""),a.css("transition",""),h.css("transition","")}function I(e){var n,i;e&&(y.css("transition","none"),a.css("transition","none"),h.css("transition","none")),c=a.width(),a.height((i=0,h.each((function(){n=t(this).height(),i<n&&(i=n)})),i)),c?(v&&(clearInterval(v),v=!1),h.each((function(n){var i,a,s=t(this);s.attr("class",(function(t,e){return e&&e.replace(f,"").replace(p," ")})),i=s.outerWidth(),0!==r.spacing&&s.css("margin-right",i*r.spacing+"px"),a=s.position().left,b[n]=-1*(a+i/2-c/2),n===h.length-1&&(P(),e&&setTimeout(k,1))}))):v=v||setInterval((function(){I(e)}),500)}function P(){var e,i,s,o=h.length;h.each((function(n){e=t(this),i=" ",s=n===x?(i+=u.itemCurrent,o+1):n<x?(i+=u.itemPast+" "+u.itemPast+"-"+(x-n),o-(x-n)):(i+=u.itemFuture+" "+u.itemFuture+"-"+(n-x),o-(n-x)),e.css("z-index",s).attr("class",(function(t,e){return e&&e.replace(f,"").replace(p," ")+i}))})),0<=x&&(c&&b[x]!==n||I(!0),l?a.css("transform","translateX("+b[x]+"px)"):a.css({left:b[x]+"px"})),function(){if(r.nav){var e=d.data("flip-category");g.removeClass(u.navCurrent),_.filter((function(){return t(this).data("index")===x||e&&t(this).data("category")===e})).parent().addClass(u.navCurrent)}}()}function D(t){var e=x;if(!(h.length<=1))return"prev"===t?0<x?x--:r.loop&&(x=h.length-1):"next"===t?x<h.length-1?x++:r.loop&&(x=0):"number"==typeof t?x=t:t!==n&&(x=h.index(t),r.loop&&e!=x&&(e==h.length-1&&x!=h.length-2&&(x=0),0==e&&1!=x&&(x=h.length-1))),d=h.eq(x),x!==e&&r.onItemSwitch&&r.onItemSwitch.call(y,h[x],h[e]),P(),y}function L(t){return r.autoplay=t||r.autoplay,clearInterval(w),w=setInterval((function(){var t=x;D("next"),t!==x||r.loop||clearInterval(w)}),r.autoplay),y}function E(){return clearInterval(w),w=0,y}function M(t){return E(),r.autoplay&&t&&(w=-1),y}function T(){I(!0),y.hide().css("visibility","").addClass(u.active).fadeIn(r.fadeIn)}function X(){var e;if(a=y.find(r.itemContainer).addClass(u.container),!((h=a.find(r.itemSelector)).length<=1))return h.addClass(u.item).each((function(){var e=t(this);e.children("."+u.itemContent).length||e.wrapInner('<div class="'+u.itemContent+'" />')})),r.click&&h.on("click.flipster touchend.flipster",(function(e){t(this).hasClass(u.itemCurrent)||e.preventDefault(),D(this)})),r.buttons&&1<h.length&&(y.find("."+u.button).remove(),y.append(C("prev"),C("next"))),e={},!r.nav||h.length<=1||(m&&m.remove(),m=t('<ul class="'+u.nav+'" role="navigation" />'),_=t(""),h.each((function(n){var i=t(this),a=i.data("flip-category"),r=i.data("flip-title")||i.attr("title")||n,s=t('<a href="#" class="'+u.navLink+'">'+r+"</a>").data("index",n);if(_=_.add(s),a){if(!e[a]){var o=t('<li class="'+u.navItem+" "+u.navCategory+'">'),l=t('<a href="#" class="'+u.navLink+" "+u.navCategoryLink+'" data-flip-category="'+a+'">'+a+"</a>").data("category",a).data("index",n);e[a]=t('<ul class="'+u.navChild+'" />'),_=_.add(l),o.append(l,e[a]).appendTo(m)}e[a].append(s)}else m.append(s);s.wrap('<li class="'+u.navItem+'">')})),m.on("click","a",(function(e){var n=t(this).data("index");0<=n&&(D(n),e.preventDefault())})),"after"===r.nav?y.append(m):y.prepend(m),g=m.find("."+u.navItem)),0<=x&&D(x),y}e={jump:D,next:function(){return D("next")},prev:function(){return D("prev")},play:L,stop:E,pause:M,index:X},y.data("methods",e),y.hasClass(u.active)||function(){var t;if(y.css("visibility","hidden"),X(),h.length<=1)y.css("visibility","");else{t=!!r.style&&"flipster--"+r.style.split(" ").join(" flipster--"),y.addClass([u.main,l?"flipster--transform":" flipster--no-transform",t,r.click?"flipster--click":""].join(" ")),r.start&&(x="center"===r.start?Math.floor(h.length/2):r.start),D(x);var e,n,s,c,f,p,v,d,m=y.find("img");if(m.length){var g=0;m.on("load",(function(){++g>=m.length&&T()})),setTimeout(T,750)}else T();o.on("resize.flipster",i(I,400)),r.autoplay&&L(),r.pauseOnHover&&a.on("mouseenter.flipster",(function(){w?M(!0):E()})).on("mouseleave.flipster",(function(){-1===w&&L()})),e=y,r.keyboard&&(e[0].tabIndex=0,e.on("keydown.flipster",i((function(t){var e=t.which;37!==e&&39!==e||(D(37===e?"prev":"next"),t.preventDefault())}),250))),function(t){if(r.scrollwheel){var e,n,a=!1,s=0,o=0,l=0,c=/mozilla/.test(navigator.userAgent.toLowerCase())&&!/webkit/.test(navigator.userAgent.toLowerCase());t.on("mousewheel.flipster wheel.flipster",(function(){a=!0})).on("mousewheel.flipster wheel.flipster",i((function(t){clearTimeout(o),o=setTimeout((function(){l=s=0}),300),t=t.originalEvent,l+=t.wheelDelta||-1*(t.deltaY+t.deltaX),Math.abs(l)<25&&!c||(s++,n!==(e=0<l?"prev":"next")&&(s=0),n=e,(s<6||s%3==0)&&D(e),l=0)}),50)),t.on("mousewheel.flipster wheel.flipster",(function(t){a&&(t.preventDefault(),a=!1)}))}}(a),n=a,r.touch&&n.on({"touchstart.flipster":function(t){t=t.originalEvent,s=t.touches?t.touches[0].clientX:t.clientX,c=t.touches?t.touches[0].clientY:t.clientY},"touchmove.flipster":function(t){t=t.originalEvent,f=t.touches?t.touches[0].clientX:t.clientX,p=t.touches?t.touches[0].clientY:t.clientY,d=f-s,v=p-c,30<Math.abs(d)&&Math.abs(v)<100&&t.preventDefault()},"touchend.flipster touchcancel.flipster ":function(){d=f-s,v=p-c,30<Math.abs(d)&&Math.abs(v)<100&&D(0<d?"prev":"next")}})}}()}))}}(jQuery,window);