<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;

class AdminResetPasswordApi extends Mailable
{
    use Queueable, SerializesModels;
     protected $data = [];
     protected $code = [];
  
    public function __construct($data=[],$code=[])
    {
        $this->data = $data; 
        $this->code = $code; 
    }

    public function build()
    {
        return $this->markdown('admin.emails.ResetPasswordApi')
                     ->subject('Reset Password')
                     ->with('data',$this->data,$this->code);
    }
}
