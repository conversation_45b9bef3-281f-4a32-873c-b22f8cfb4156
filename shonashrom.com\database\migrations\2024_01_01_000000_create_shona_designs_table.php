<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateShonaDesignsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shona_designs', function (Blueprint $table) {
            $table->id();
            
            // Font Configuration
            $table->integer('Font_Type')->default(1);
            
            // Body Styling
            $table->integer('Body_BG_Type')->default(1);
            $table->text('Body_BG_Image')->nullable();
            $table->string('Body_BG_Color')->default('#ffffff');
            $table->string('Body_Title_Color')->default('#333333');
            
            // Header Styling
            $table->string('Header_BG_Color')->default('#ffffff');
            $table->string('Header_Txt_Color')->default('#333333');
            $table->string('Header_Logo_Size')->default('150px');
            $table->string('Header_Height')->default('80px');
            
            // Navigation Styling
            $table->string('Navbar_BG_Color')->default('#2c3e50');
            $table->string('Navbar_Txt_Color')->default('#ffffff');
            $table->string('Navbar_Hover_BG_Color')->default('#34495e');
            $table->string('Navbar_Hover_Txt_Color')->default('#ffffff');
            $table->string('Navbar_Active_BG_Color')->default('#3498db');
            $table->string('Navbar_Active_Txt_Color')->default('#ffffff');
            
            // Hero Section
            $table->string('Hero_Overlay_Color')->default('#000000');
            $table->decimal('Hero_Overlay_Opacity', 3, 2)->default(0.5);
            $table->string('Hero_Title_Color')->default('#ffffff');
            $table->string('Hero_Subtitle_Color')->default('#ffffff');
            $table->string('Hero_Button_BG_Color')->default('#3498db');
            $table->string('Hero_Button_Txt_Color')->default('#ffffff');
            $table->string('Hero_Button_Hover_BG_Color')->default('#2980b9');
            $table->string('Hero_Button_Hover_Txt_Color')->default('#ffffff');
            
            // Sections Styling
            $table->string('Section_Title_Color')->default('#2c3e50');
            $table->string('Section_Subtitle_Color')->default('#7f8c8d');
            $table->string('Section_Text_Color')->default('#555555');
            $table->string('Section_BG_Color')->default('#ffffff');
            $table->string('Section_Alt_BG_Color')->default('#f8f9fa');
            
            // Cards Styling
            $table->string('Card_BG_Color')->default('#ffffff');
            $table->string('Card_Border_Color')->default('#e9ecef');
            $table->string('Card_Shadow_Color')->default('rgba(0,0,0,0.1)');
            $table->string('Card_Title_Color')->default('#2c3e50');
            $table->string('Card_Text_Color')->default('#555555');
            $table->string('Card_Hover_BG_Color')->default('#f8f9fa');
            $table->string('Card_Hover_Shadow_Color')->default('rgba(0,0,0,0.15)');
            
            // Buttons Styling
            $table->string('Primary_Button_BG_Color')->default('#3498db');
            $table->string('Primary_Button_Txt_Color')->default('#ffffff');
            $table->string('Primary_Button_Hover_BG_Color')->default('#2980b9');
            $table->string('Primary_Button_Hover_Txt_Color')->default('#ffffff');
            $table->string('Secondary_Button_BG_Color')->default('#95a5a6');
            $table->string('Secondary_Button_Txt_Color')->default('#ffffff');
            $table->string('Secondary_Button_Hover_BG_Color')->default('#7f8c8d');
            $table->string('Secondary_Button_Hover_Txt_Color')->default('#ffffff');
            
            // Footer Styling
            $table->string('Footer_BG_Color')->default('#2c3e50');
            $table->string('Footer_Title_Color')->default('#ffffff');
            $table->string('Footer_Txt_Color')->default('#bdc3c7');
            $table->string('Footer_Link_Color')->default('#3498db');
            $table->string('Footer_Link_Hover_Color')->default('#2980b9');
            $table->string('Footer_Social_BG_Color')->default('#34495e');
            $table->string('Footer_Social_Txt_Color')->default('#ffffff');
            $table->string('Footer_Social_Hover_BG_Color')->default('#3498db');
            $table->string('Footer_Social_Hover_Txt_Color')->default('#ffffff');
            
            // Form Styling
            $table->string('Form_Input_BG_Color')->default('#ffffff');
            $table->string('Form_Input_Border_Color')->default('#ced4da');
            $table->string('Form_Input_Txt_Color')->default('#495057');
            $table->string('Form_Input_Focus_Border_Color')->default('#3498db');
            $table->string('Form_Label_Color')->default('#495057');
            $table->string('Form_Button_BG_Color')->default('#3498db');
            $table->string('Form_Button_Txt_Color')->default('#ffffff');
            
            // Product/Service Cards
            $table->string('Product_Card_BG_Color')->default('#ffffff');
            $table->string('Product_Card_Border_Color')->default('#e9ecef');
            $table->string('Product_Title_Color')->default('#2c3e50');
            $table->string('Product_Price_Color')->default('#e74c3c');
            $table->string('Product_Description_Color')->default('#555555');
            $table->string('Product_Badge_BG_Color')->default('#e74c3c');
            $table->string('Product_Badge_Txt_Color')->default('#ffffff');
            
            // Testimonials
            $table->string('Testimonial_BG_Color')->default('#ffffff');
            $table->string('Testimonial_Border_Color')->default('#e9ecef');
            $table->string('Testimonial_Text_Color')->default('#555555');
            $table->string('Testimonial_Author_Color')->default('#2c3e50');
            $table->string('Testimonial_Quote_Color')->default('#7f8c8d');
            
            // Gallery
            $table->string('Gallery_Overlay_Color')->default('#000000');
            $table->decimal('Gallery_Overlay_Opacity', 3, 2)->default(0.7);
            $table->string('Gallery_Icon_Color')->default('#ffffff');
            $table->string('Gallery_Title_Color')->default('#ffffff');
            $table->string('Gallery_Category_Color')->default('#3498db');
            
            // Blog/Articles
            $table->string('Blog_Card_BG_Color')->default('#ffffff');
            $table->string('Blog_Card_Border_Color')->default('#e9ecef');
            $table->string('Blog_Title_Color')->default('#2c3e50');
            $table->string('Blog_Meta_Color')->default('#7f8c8d');
            $table->string('Blog_Text_Color')->default('#555555');
            $table->string('Blog_Read_More_Color')->default('#3498db');
            
            // Preloader
            $table->string('Preloader_BG_Color')->default('#ffffff');
            $table->string('Preloader_Spinner_Color')->default('#3498db');
            $table->string('Preloader_Text_Color')->default('#2c3e50');
            
            // Modal/Popup
            $table->string('Modal_BG_Color')->default('#ffffff');
            $table->string('Modal_Overlay_Color')->default('rgba(0,0,0,0.5)');
            $table->string('Modal_Title_Color')->default('#2c3e50');
            $table->string('Modal_Text_Color')->default('#555555');
            $table->string('Modal_Button_BG_Color')->default('#3498db');
            $table->string('Modal_Button_Txt_Color')->default('#ffffff');
            
            // Pagination
            $table->string('Pagination_BG_Color')->default('#ffffff');
            $table->string('Pagination_Txt_Color')->default('#495057');
            $table->string('Pagination_Active_BG_Color')->default('#3498db');
            $table->string('Pagination_Active_Txt_Color')->default('#ffffff');
            $table->string('Pagination_Hover_BG_Color')->default('#e9ecef');
            $table->string('Pagination_Hover_Txt_Color')->default('#495057');
            
            // Breadcrumb
            $table->string('Breadcrumb_BG_Color')->default('#f8f9fa');
            $table->string('Breadcrumb_Txt_Color')->default('#6c757d');
            $table->string('Breadcrumb_Link_Color')->default('#3498db');
            $table->string('Breadcrumb_Active_Color')->default('#495057');
            
            // Feature Toggles
            $table->boolean('Show_Preloader')->default(true);
            $table->boolean('Show_Breadcrumbs')->default(true);
            $table->boolean('Show_Social_Links')->default(true);
            $table->boolean('Show_Contact_Info')->default(true);
            $table->boolean('Enable_Dark_Mode')->default(false);
            $table->boolean('Enable_RTL_Support')->default(false);
            
            // Animation Settings
            $table->boolean('Enable_Animations')->default(true);
            $table->string('Animation_Duration')->default('0.3s');
            $table->string('Animation_Easing')->default('ease-in-out');
            
            // Layout Settings
            $table->string('Container_Max_Width')->default('1200px');
            $table->string('Section_Padding')->default('80px');
            $table->string('Card_Border_Radius')->default('8px');
            $table->string('Button_Border_Radius')->default('4px');
            
            // Typography
            $table->string('Primary_Font_Family')->default('Roboto, sans-serif');
            $table->string('Secondary_Font_Family')->default('Open Sans, sans-serif');
            $table->string('Base_Font_Size')->default('16px');
            $table->string('Line_Height')->default('1.6');
            $table->string('Heading_Font_Weight')->default('600');
            $table->string('Body_Font_Weight')->default('400');
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shona_designs');
    }
}
