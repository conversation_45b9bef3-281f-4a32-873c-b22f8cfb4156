<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->longText('Date')->nullable();
            $table->longText('Payment_Method')->nullable();
            $table->longText('Product_Numbers')->nullable();
            $table->longText('Total_Qty')->nullable();
            $table->longText('Total_Price')->nullable();
            $table->longText('Shipping')->nullable();
            $table->longText('Name')->nullable();
            $table->longText('Email')->nullable();
            $table->longText('Phone')->nullable();
            $table->longText('OtherPhone')->nullable();
            $table->longText('Address_Name')->nullable();
            $table->longText('Special_MarkAdd')->nullable();
            $table->longText('StreetAdd')->nullable();
            $table->longText('BulidingAdd')->nullable();
            $table->longText('FloorAdd')->nullable();
            $table->longText('FlatAdd')->nullable();
            $table->longText('Governrate')->nullable();
            $table->longText('City')->nullable();
            $table->longText('Place')->nullable();
            $table->longText('LocationAdd')->nullable();
            $table->longText('Address_DetailsAdd')->nullable();
            $table->longText('USER_ID')->nullable();
            $table->longText('User')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('orders');
    }
}
