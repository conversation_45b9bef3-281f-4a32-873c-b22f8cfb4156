<?php

/**
 * Shonashrom ERP Integration Setup Script
 * 
 * This script sets up the integrated design system for the Shonashrom site
 * Run this after copying the files to ensure proper integration
 */

echo "=== Shonashrom ERP Integration Setup ===\n\n";

// Check if we're in the right directory
if (!file_exists('artisan')) {
    echo "Error: Please run this script from the Shonashrom root directory\n";
    exit(1);
}

echo "1. Running database migrations...\n";
$output = shell_exec('php artisan migrate --force 2>&1');
echo $output . "\n";

echo "2. Running database seeders...\n";
$output = shell_exec('php artisan db:seed --class=ShonaDesignSeeder --force 2>&1');
echo $output . "\n";

echo "3. Clearing application cache...\n";
$output = shell_exec('php artisan cache:clear 2>&1');
echo $output . "\n";

echo "4. Clearing config cache...\n";
$output = shell_exec('php artisan config:clear 2>&1');
echo $output . "\n";

echo "5. Clearing view cache...\n";
$output = shell_exec('php artisan view:clear 2>&1');
echo $output . "\n";

echo "6. Optimizing application...\n";
$output = shell_exec('php artisan optimize 2>&1');
echo $output . "\n";

// Check if npm is available for asset compilation
if (shell_exec('which npm') || shell_exec('where npm')) {
    echo "7. Installing npm dependencies...\n";
    $output = shell_exec('npm install 2>&1');
    echo $output . "\n";
    
    echo "8. Compiling assets...\n";
    $output = shell_exec('npm run dev 2>&1');
    echo $output . "\n";
} else {
    echo "7. NPM not found, skipping asset compilation\n";
    echo "   Please install Node.js and run 'npm install && npm run dev' manually\n";
}

echo "=== Integration Setup Complete ===\n\n";

echo "Next steps:\n";
echo "1. Visit /DesignSystem in your admin panel to configure the design\n";
echo "2. Test the site to ensure all styling is working correctly\n";
echo "3. Customize colors, fonts, and layout settings as needed\n\n";

echo "Features enabled:\n";
echo "- Dynamic font configuration\n";
echo "- Customizable color schemes\n";
echo "- Responsive design system\n";
echo "- RTL language support\n";
echo "- Shared components between ERP and site\n";
echo "- Consistent styling across all pages\n\n";

echo "For support, check the documentation or contact the development team.\n";
?>
