@extends('admin.index')
@section('content')
<title>{{trans('admin.Profile')}}</title>

  <main id="js-page-content" role="main" class="page-content">
                        <ol class="breadcrumb page-breadcrumb">
                            <li class="breadcrumb-item"><a href="javascript:void(0);">{{trans('admin.Profile')}}</a></li>
                            <li class="breadcrumb-item">{{trans('admin.Profile')}}</li>
                            <li class="breadcrumb-item active">{{trans('admin.Profile')}}</li>
                            <li class="position-absolute pos-top pos-right d-none d-sm-block"><span class="js-get-date"></span></li>
                        </ol>
                        <div class="subheader">
                            <h1 class="subheader-title"> {{trans('admin.Profile')}} </h1>
                        </div>
                        <div class="row">
                            <div class="col-lg-6 col-xl-3 order-lg-1 order-xl-1">
                            <span id="ex"> @include('admin.layouts.messages')</span>     
                                <!-- profile summary -->
                                <div class="card mb-g rounded-top">
                                    <div class="row no-gutters row-grid">
                                        <div class="col-12">
                                            <div class="d-flex flex-column align-items-center justify-content-center p-4">
          <img src="{{URL::to(auth()->guard('admin')->user()->image)}}" class="rounded-circle shadow-2 img-thumbnail" alt="">
                                                <h5 class="mb-0 fw-700 text-center mt-3">
                                                
                                               {{auth()->guard('admin')->user()->name}}
                                                </h5>
                             
                                            </div>
                                        </div>
                                      
                                        <div class="col-12">
                                            <div class="p-3 text-center">
                                                <button type="button" class="btn btn-default" data-toggle="modal" data-target="#default-example-modal-lg-edit"><i class="fal fa-edit"></i></button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- photos -->
                            
                                  
                            </div>
                            
                          
                        </div>
      
      
                         <!-- Modal Edit-->
                    <div class="modal fade" id="default-example-modal-lg-edit" tabindex="-1" role="dialog" aria-hidden="true">
                        <div class="modal-dialog modal-lg" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">{{trans('admin.Profile')}}  </h5>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                    </button>
                                </div>
                                   <div class="modal-body">
       <form action="{{url('UpdateAdminProfile/'.auth()->guard('admin')->user()->id)}}" method="post" enctype="multipart/form-data">
                                        {!! csrf_field() !!}
                                    @honeypot
                            <input type="hidden" name="passwords" value="{{auth()->guard('admin')->user()->password}}">       
                            <input type="hidden" name="hidden" value="{{auth()->guard('admin')->user()->hidden}}">       
                            <input type="hidden" name="emp" value="{{auth()->guard('admin')->user()->emp}}">       
                            <input type="hidden" name="ship" value="{{auth()->guard('admin')->user()->ship}}">       
                            <input type="hidden" name="vend" value="{{auth()->guard('admin')->user()->vend}}">       
                            <input type="hidden" name="cli" value="{{auth()->guard('admin')->user()->cli}}">       
                            <input type="hidden" name="account" value="{{auth()->guard('admin')->user()->account}}">       
                            <input type="hidden" name="status" value="{{auth()->guard('admin')->user()->status}}">       
                            <input type="hidden" name="images" value="{{auth()->guard('admin')->user()->image}}">  
                            <input type="hidden" name="safe" value="{{auth()->guard('admin')->user()->safe}}">  
                            <input type="hidden" name="store" value="{{auth()->guard('admin')->user()->store}}">  
                            <input type="hidden" name="type" value="{{auth()->guard('admin')->user()->type}}">  
                            <input type="hidden" name="roles_name" value="{{auth()->guard('admin')->user()->roles_name}}">  

                                        <div class="form-row">
                                            <div class="form-group col-lg-6">
                                                <label class="form-label" for="simpleinput"> {{trans('admin.Name')}}</label>
                        <input type="text" name="name" value="{{auth()->guard('admin')->user()->name}}"  class="form-control" required>
                                            </div>
                                            <div class="form-group col-lg-6">
                                                <label class="form-label" for="simpleinput"> {{trans('admin.Email')}}</label>
                     <input type="email" name="email" value="{{auth()->guard('admin')->user()->email}}"  class="form-control" required>
                                            </div>
                                         <div class="form-group col-lg-6">
                                <label class="form-label" for="simpleinput"> {{trans('admin.Password')}}</label>
                     <input type="password" name="password" value="{{old('password')}}"  class="form-control" >
                                            </div>
                                              <div class="form-group col-lg-6">
                                <label class="form-label" for="simpleinput"> {{trans('admin.Phone')}}</label>
                     <input type="number" name="phone" value="{{auth()->guard('admin')->user()->phone}}"  class="form-control">
                                            </div>                
                                            
                                            <div class="form-group col-lg-12">
                                                <label class="form-label">{{trans('admin.Image')}}</label>
                                                <div class="custom-file">
                                      <input type="file" class="custom-file-input" id="customControlValidation7" name="image">
                                  <label class="custom-file-label" for="customControlValidation7">Choose file...</label>
                        
                                                </div>
                                            </div>
                                            <div class="form-group col-lg-12">
                                        <img src="{{URL::to(auth()->guard('admin')->user()->image)}}" style="height: 200px; max-width: 100%">    
                                            </div>
                                            
                                            
                                        </div>
                                         <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">{{trans('admin.Close')}}</button>
                        <button type="submit" class="btn btn-primary"> {{trans('admin.Add')}}</button>
                                </div>                
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
      
      
                    </main>

@endsection

@push('js')
     <!-- Optional: page related CSS-->
        <link rel="stylesheet" media="screen, print" href="{{asset('Admin/css/fa-brands.css')}}">
        <link rel="stylesheet" media="screen, print" href="{{asset('Admin/css/fa-solid.css')}}">
@endpush