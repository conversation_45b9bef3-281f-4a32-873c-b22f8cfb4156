<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MainEComDesign extends Model
{
    use HasFactory;
    protected $table = 'main_e_com_designs';
    protected $fillable = [
        'Font_Type',
          
        'Pagination_BG_Color',
        'Pagination_Txt_Color',
        'Pagination_Active_BG_Color',
        'Pagination_Active_Txt_Color',
          
        'Body_BG_Type',
        'Body_BG_Image',
        'Body_BG_Color',
          
        'Sub_Page_BG_Color',
        'Breadcumb_BG_Color',  
        'Breadcumb_Txt_Color',
          
        'Modal_BG_Color',
        'Modal_Txt_Color',
        'Modal_Button_BG_Color',
        'Modal_Button_Txt_Color',
          
        'Table_Header_BG_Color',
        'Table_Header_Txt_Color',
        'Table_Body_BG_Color',
        'Table_Body_Txt_Color',      
        'Table_Button_BG_Color',
        'Table_Button_Txt_Color',
          
        'CopyRights_Txt_Color',
        'CopyRights_Klar_Txt_Color',
        'CopyRights_Klar_Hover_Txt_Color',
          
        'Preloader_BG_Color',
        'Preloader_Small_Circle_Color',
        'Preloader_Large_Circle_Color',
          
        'Footer_Title_Color',
        'Footer_Txt_Color',  
        'Footer_Txt_Hover_Color',  
        'Footer_Social_Color',
        'Footer_Social_BG_Color',
        'Footer_Social_Hover_BG_Color',
        'Footer_Social_Hover_Txt_Color',
          
        'Header_Top_BG_Color',
        'Header_Top_Txt_Color',
        'Header_Top_Txt_Hover_Color',
        'Header_Middle_BG_Color',
        'Header_Middle_Icon_Color',
        'Header_Middle_Icon_Hover_Color',
        'Header_SearchBar_BG_Color',        
        'Header_SearchBar_Txt_Color',
        'Header_SearchBar_Icon_BG_Color',
        'Header_SearchBar_Icon_Txt_Color',
        'Header_SearchBar_Icon_Hover_BG_Color',
        'Header_SearchBar_Icon_Hover_Txt_Color',
        
        'Navbar_BG_Color',
        'Navbar_Txt_Color',
        'Navbar_Txt_Hover_Color',
        'Navbar_Txt_Active_Color',
        'Navbar_Dropdown_BG_Color',
        'Navbar_Dropdown_Txt_Color',
        'Navbar_Dropdown_Txt_Hover_Color',
        'Navbar_Dropdown_Border_Color',
        
        'Slider_BG_Type',
        'Slider_BG_Image',
        'Slider_BG_Color',
        'Slider_Title_Color',
        'Slider_Txt_Color',
        'Slider_Button_BG_Color',
        'Slider_Button_Txt_Color',
        'Slider_Button_Hover_BG_Color',
        'Slider_Button_Hover_Txt_Color',
        
        'Section_Title_Color',
        'Section_Txt_Color',
        'Section_BG_Color',
        'Section_Alt_BG_Color',
        
        'Card_BG_Color',
        'Card_Border_Color',
        'Card_Title_Color',
        'Card_Txt_Color',
        'Card_Price_Color',
        'Card_Button_BG_Color',
        'Card_Button_Txt_Color',
        'Card_Button_Hover_BG_Color',
        'Card_Button_Hover_Txt_Color',
        
        'Form_BG_Color',
        'Form_Border_Color',
        'Form_Input_BG_Color',
        'Form_Input_Border_Color',
        'Form_Input_Txt_Color',
        'Form_Label_Color',
        'Form_Button_BG_Color',
        'Form_Button_Txt_Color',
        'Form_Button_Hover_BG_Color',
        'Form_Button_Hover_Txt_Color',
    ];
}
