{"version": 3, "sources": ["dropzone.css", "../../../src/scss/_modules/variables.scss"], "names": [], "mappings": "AAAA;;;EAGE;AACF;EACE;IACE,UAAU;IACV,mCAAmC;IAInC,2BAA2B,EAAA;EAC7B;IACE,UAAU;IACV,kCAAkC;IAIlC,0BAA0B,EAAA;EAC5B;IACE,UAAU;IACV,oCAAoC;IAIpC,4BAA4B,EAAA,EAAA;;AAuBhC;EACE;IACE,UAAU;IACV,mCAAmC;IAInC,2BAA2B,EAAA;EAC7B;IACE,UAAU;IACV,kCAAkC;IAIlC,0BAA0B,EAAA;EAC5B;IACE,UAAU;IACV,oCAAoC;IAIpC,4BAA4B,EAAA,EAAA;;AAChC;EACE;IACE,UAAU;IACV,mCAAmC;IAInC,2BAA2B,EAAA;EAC7B;IACE,UAAU;IACV,kCAAkC;IAIlC,0BAA0B,EAAA,EAAA;;AAgB9B;EACE;IACE,UAAU;IACV,mCAAmC;IAInC,2BAA2B,EAAA;EAC7B;IACE,UAAU;IACV,kCAAkC;IAIlC,0BAA0B,EAAA,EAAA;;AAC9B;EACE;IACE,2BAA2B;IAI3B,mBAAmB,EAAA;EACrB;IACE,6BAA6B;IAI7B,qBAAqB,EAAA;EACvB;IACE,2BAA2B;IAI3B,mBAAmB,EAAA,EAAA;;AAoBvB;EACE;IACE,2BAA2B;IAI3B,mBAAmB,EAAA;EACrB;IACE,6BAA6B;IAI7B,qBAAqB,EAAA;EACvB;IACE,2BAA2B;IAI3B,mBAAmB,EAAA,EAAA;;AACvB;EACE,8BAAsB;UAAtB,sBAAsB,EAAA;;AAExB;EACE,iBAAiB;EACjB,oCAAoC;EACpC,iBAAiB;EACjB,kBAAkB,EAAA;;AAClB;EACE,eAAe,EAAA;;AACf;EACE,eAAe,EAAA;;AACjB;EACE,eAAe,EAAA;;AACnB;EACE,aAAa,EAAA;;AACf;EACE,mBAAmB,EAAA;;AACnB;EACE,YAAY,EAAA;;AAChB;EACE,kBAAkB;EAClB,aAAa,EAAA;;AACb;EACE,gBAAgB;EAChB,cAAc;EACd,YAAY;EACZ,UAAU;EACV,aAAa;EACb,eAAe;EACf,gBAAgB,EAAA;;AACpB;EACE,kBAAkB;EAClB,qBAAqB;EACrB,mBAAmB;EACnB,YAAY;EACZ,iBAAiB,EAAA;;AACjB;EACE,aAAa,EAAA;;AACb;EACE,UAAU,EAAA;;AACd;EACE,mBAAmB;EACnB,gBAAgB;EAChB,iFAAkD;EAAlD,kDAAkD,EAAA;;AACpD;EACE,UAAU,EAAA;;AACZ;EACE,iBAAiB,EAAA;;AACjB;EACE,uCAAuC;EAIvC,+BAA+B,EAAA;;AACnC;EACE,eAAe;EACf,kBAAkB;EAClB,cAAc;EACd,eAAe;EACf,YAAY,EAAA;;AACZ;EACE,0BAA0B,EAAA;;AAC9B;EACE,UAAU,EAAA;;AACZ;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,UAAU;EACV,eAAe;EACf,eAAe;EACf,eAAe;EACf,gBAAgB;EAChB,kBAAkB;EAClB,yBAAyB;EACzB,iBAAiB,EAAA;;AACjB;EACE,kBAAkB;EAClB,eAAe,EAAA;;AACjB;EACE,mBAAmB,EAAA;;AACnB;EACE,0CAA0C;EAC1C,0CAA0C,EAAA;;AAC5C;EACE,gBAAgB;EAChB,uBAAuB,EAAA;;AACvB;EACE,6BAA6B,EAAA;;AACnC;EACE,0CAA0C;EAC1C,gBAAgB;EAChB,kBAAkB,EAAA;;AACtB;EACE,oCAAoC;EAIpC,4BAA4B;EAC5B,yBAAyB;EACzB,iBAAiB,EAAA;;AACnB;EACE,mBAAmB;EACnB,gBAAgB;EAChB,YAAY;EACZ,aAAa;EACb,kBAAkB;EAClB,cAAc;EACd,WAAW,EAAA;;AACX;EACE,cAAc,EAAA;;AAClB;EACE,qEAAqE;EAIrE,6DAA6D,EAAA;;AAC/D;EACE,UAAU;EACV,8DAA8D;EAI9D,sDAAsD,EAAA;;AACxD;EACE,oBAAoB;EACpB,UAAU;EACV,YAAY;EACZ,kBAAkB;EAClB,cAAc;EACd,QAAQ;EACR,SAAS;EACT,kBAAkB;EAClB,iBAAiB,EAAA;;AACjB;EACE,cAAc;EACd,WAAW;EACX,YAAY,EAAA;;AAChB;EACE,UAAU;EACV,mCAAmC;EAInC,2BAA2B,EAAA;;AAC7B;EACE,UAAU;EACV,wCAAwC;EAIxC,gCAAgC,EAAA;;AAClC;EACE,yCAAyC;EAIzC,iCAAiC,EAAA;;AACnC;EACE,UAAU;EACV,aAAa;EACb,oBAAoB;EACpB,kBAAkB;EAClB,YAAY;EACZ,SAAS;EACT,QAAQ;EACR,gBAAgB;EAChB,WAAW;EACX,kBAAkB;EAClB,oCAAoC;EACpC,2BAA2B;EAC3B,kBAAkB;EAClB,gBAAgB,EAAA;;AAChB;EACE,gBAAgB;EAChB,iFAAkD;EAAlD,kDAAkD;EAClD,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,SAAS;EACT,QAAQ;EACR,2CAA2C;EAI3C,mCAAmC,EAAA;;AACvC;EACE,cAAc,EAAA;;AAChB;EACE,UAAU;EACV,oBAAoB,EAAA;;AACtB;EACE,oBAAoB;EACpB,aAAa;EACb,kBAAkB;EAClB,cAAc;EACd,aAAa;EACb,UAAU;EACV,qCAAqC;EAIrC,6BAA6B;EAC7B,kBAAkB;EAClB,eAAe;EACf,UAAU;EACV,WAAW;EACX,YAAY;EACZ,mBAAmB;EACnB,uFAAwD;EAAxD,wDAAwD;EACxD,oBAAoB;EACpB,YAAY,EAAA;;AACZ;EACE,WAAW;EACX,kBAAkB;EAClB,SAAS;EACT,UAAU;EACV,QAAQ;EACR,SAAS;EACT,kCAAkC;EAClC,mCAAmC;EACnC,gCAAgC,EAAA;;AC3YxC;4EDgc4E;AC9b5E,+CAAA;AAQA,+FAAA;AAQA;;;;;;kFDubkF;AC/alF;4EDib4E;AC3a5E;4ED6a4E;AC3a5E,cAAA;AAYA,kBAAA;AAYA,iBAAA;AAYA,kBAAA;AAYA,cAAA;AAYA,eAAA;AAYA,kBAAA;AAmFA;4EDyR4E;ACrR5E;4EDuR4E;ACxQR,kGAAA;AACG,2EAAA;AAcvE,+BAAA;AAgBA,6BAAA;AACA,wFAAA;AAQA;4EDuO4E;AC9M5E,oCAAA;AAYA,UAAA;AACA,wIAAA;AASA,UAAA;AAIA,aAAA;AAMA,qDAAA;AAGA,mCAAA;AAGA,oBAAA;AAiBA,iBAAA;AAQA,gBAAA;AAGA,UAAA;AAIA,UAAA;AAOA,gBAAA;AAMA,UAAA;AAKA,UAAA;AAKA,eAAA;AAIA,iBAAA;AAUA,aAAA;AAIA,qBAAA;AAKA,WAAA;AASA,cAAA;AASA,oBAAA;AAOA,aAAA;AAcA,aAAA;AAYA,UAAA;AAYA;;;;;;;;;;;;;;;;;;;;;;;;;;;CDgFC;ACnDD,UAAA;AAuBA,aAAA;AAIA;4ED4B4E;ACpB5E,6EAAA;AAEiC,WAAA;AACD,WAAA;AACA,WAAA;AACA,WAAA;AACA,WAAA;AACA,WAAA;AACC,WAAA;AAEjC;4EDoB4E;AClBlE,mFAAA;AAOV;4EDc4E;ACZG,mEAAA;AAE/E;4EDa4E;ACP5E,oEAAA;AAUA;4EDA4E;ACI5E;4EDF4E;ACI5B,0BAAA;AACH,iBAAA;AAG7C;4EDJ4E;ACS5E;4EDP4E;ACa5E;4EDX4E;ACe5E;4EDb4E;ACgB5E,WAAA;AAOA,WAAA;AAMA,SAAA;AAEoD,6DAAA;AACA,8DAAA;AACH,qDAAA;AAEjD,gCAAA;AAGA,qBAAA;AAC8D,uBAAA;AAO9D,QAAA;AAYA,uBAAA;AASA,UAAA;AAKA,sBAAA;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4EDhC4E;AC+D5E,oBAAA;AACA,eAAA;AAMA,eAAA;AAGA,uBAAA;AAQA,qBAAA;AAIA,mBAAA;AAKA,mBAAA;AAOA,kBAAA;AAIA,cAAA;AAIA,cAAA;AAKA,eAAA;AAIA,gCAAA;AAGA,qBAAA;AACA,mCAAA;AAGA,mBAAA;AAQA,2CAAA;AAK6C,kBAAA;AAE7C,gCAAA;AAKyE,+CAAA;AAEzE;4ED1H4E;AC4H5E,eAAA;AAIA;4ED7H4E;ACoI5E;4EDlI4E;ACsI5E;4EDpI4E;ACqJ5E;4EDnJ4E;AC0J5E;4EDxJ4E;ACgK5E;4ED9J4E;ACsK5E;4EDpK4E;ACyK5E,oBAAA;AAGA,0DAAA;AAQA,kBAAA;AD3aA;EACE,0BAA0B;EAC1B,kBAAkB;EAClB,mBAAmB;EACnB,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,mBAAe;MAAf,eAAe;EACf,eAAe,EAAA;;AAIjB;EACE,eAAe,EAAA;;AAGjB;EACE,0BAAsB;EACtB,gBAAgB;EAChB,kBAAkB;EAClB,yBAAyB;EACzB,WAAW,EAAA;;AAGb;EACC,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,yBAAmB;MAAnB,sBAAmB;UAAnB,mBAAmB;EACnB,wBAAuB;MAAvB,qBAAuB;UAAvB,uBAAuB;EACvB,gBAAgB,EAAA;EAJjB;IAOE,kBC7EuB,EAAA;;ADiFzB;EACC,uBAAuB,EAAA;;AAGxB;EACC,mBAAmB;EACnB,oBAAoB;EACpB,cAAc,EAAA;;AAGf;EACC,oBAAoB;EACpB,qBCzb2B,EAAA;;AD4b5B;EACC,kBCjGwB,EAAA", "file": "dropzone.css", "sourcesContent": ["/*\n * The MIT License\n * Copyright (c) 2012 <PERSON><PERSON>o <<EMAIL>>\n */\n@-webkit-keyframes passing-through {\n  0% {\n    opacity: 0;\n    -webkit-transform: translateY(40px);\n    -moz-transform: translateY(40px);\n    -ms-transform: translateY(40px);\n    -o-transform: translateY(40px);\n    transform: translateY(40px); }\n  30%, 70% {\n    opacity: 1;\n    -webkit-transform: translateY(0px);\n    -moz-transform: translateY(0px);\n    -ms-transform: translateY(0px);\n    -o-transform: translateY(0px);\n    transform: translateY(0px); }\n  100% {\n    opacity: 0;\n    -webkit-transform: translateY(-40px);\n    -moz-transform: translateY(-40px);\n    -ms-transform: translateY(-40px);\n    -o-transform: translateY(-40px);\n    transform: translateY(-40px); } }\n@-moz-keyframes passing-through {\n  0% {\n    opacity: 0;\n    -webkit-transform: translateY(40px);\n    -moz-transform: translateY(40px);\n    -ms-transform: translateY(40px);\n    -o-transform: translateY(40px);\n    transform: translateY(40px); }\n  30%, 70% {\n    opacity: 1;\n    -webkit-transform: translateY(0px);\n    -moz-transform: translateY(0px);\n    -ms-transform: translateY(0px);\n    -o-transform: translateY(0px);\n    transform: translateY(0px); }\n  100% {\n    opacity: 0;\n    -webkit-transform: translateY(-40px);\n    -moz-transform: translateY(-40px);\n    -ms-transform: translateY(-40px);\n    -o-transform: translateY(-40px);\n    transform: translateY(-40px); } }\n@keyframes passing-through {\n  0% {\n    opacity: 0;\n    -webkit-transform: translateY(40px);\n    -moz-transform: translateY(40px);\n    -ms-transform: translateY(40px);\n    -o-transform: translateY(40px);\n    transform: translateY(40px); }\n  30%, 70% {\n    opacity: 1;\n    -webkit-transform: translateY(0px);\n    -moz-transform: translateY(0px);\n    -ms-transform: translateY(0px);\n    -o-transform: translateY(0px);\n    transform: translateY(0px); }\n  100% {\n    opacity: 0;\n    -webkit-transform: translateY(-40px);\n    -moz-transform: translateY(-40px);\n    -ms-transform: translateY(-40px);\n    -o-transform: translateY(-40px);\n    transform: translateY(-40px); } }\n@-webkit-keyframes slide-in {\n  0% {\n    opacity: 0;\n    -webkit-transform: translateY(40px);\n    -moz-transform: translateY(40px);\n    -ms-transform: translateY(40px);\n    -o-transform: translateY(40px);\n    transform: translateY(40px); }\n  30% {\n    opacity: 1;\n    -webkit-transform: translateY(0px);\n    -moz-transform: translateY(0px);\n    -ms-transform: translateY(0px);\n    -o-transform: translateY(0px);\n    transform: translateY(0px); } }\n@-moz-keyframes slide-in {\n  0% {\n    opacity: 0;\n    -webkit-transform: translateY(40px);\n    -moz-transform: translateY(40px);\n    -ms-transform: translateY(40px);\n    -o-transform: translateY(40px);\n    transform: translateY(40px); }\n  30% {\n    opacity: 1;\n    -webkit-transform: translateY(0px);\n    -moz-transform: translateY(0px);\n    -ms-transform: translateY(0px);\n    -o-transform: translateY(0px);\n    transform: translateY(0px); } }\n@keyframes slide-in {\n  0% {\n    opacity: 0;\n    -webkit-transform: translateY(40px);\n    -moz-transform: translateY(40px);\n    -ms-transform: translateY(40px);\n    -o-transform: translateY(40px);\n    transform: translateY(40px); }\n  30% {\n    opacity: 1;\n    -webkit-transform: translateY(0px);\n    -moz-transform: translateY(0px);\n    -ms-transform: translateY(0px);\n    -o-transform: translateY(0px);\n    transform: translateY(0px); } }\n@-webkit-keyframes pulse {\n  0% {\n    -webkit-transform: scale(1);\n    -moz-transform: scale(1);\n    -ms-transform: scale(1);\n    -o-transform: scale(1);\n    transform: scale(1); }\n  10% {\n    -webkit-transform: scale(1.1);\n    -moz-transform: scale(1.1);\n    -ms-transform: scale(1.1);\n    -o-transform: scale(1.1);\n    transform: scale(1.1); }\n  20% {\n    -webkit-transform: scale(1);\n    -moz-transform: scale(1);\n    -ms-transform: scale(1);\n    -o-transform: scale(1);\n    transform: scale(1); } }\n@-moz-keyframes pulse {\n  0% {\n    -webkit-transform: scale(1);\n    -moz-transform: scale(1);\n    -ms-transform: scale(1);\n    -o-transform: scale(1);\n    transform: scale(1); }\n  10% {\n    -webkit-transform: scale(1.1);\n    -moz-transform: scale(1.1);\n    -ms-transform: scale(1.1);\n    -o-transform: scale(1.1);\n    transform: scale(1.1); }\n  20% {\n    -webkit-transform: scale(1);\n    -moz-transform: scale(1);\n    -ms-transform: scale(1);\n    -o-transform: scale(1);\n    transform: scale(1); } }\n@keyframes pulse {\n  0% {\n    -webkit-transform: scale(1);\n    -moz-transform: scale(1);\n    -ms-transform: scale(1);\n    -o-transform: scale(1);\n    transform: scale(1); }\n  10% {\n    -webkit-transform: scale(1.1);\n    -moz-transform: scale(1.1);\n    -ms-transform: scale(1.1);\n    -o-transform: scale(1.1);\n    transform: scale(1.1); }\n  20% {\n    -webkit-transform: scale(1);\n    -moz-transform: scale(1);\n    -ms-transform: scale(1);\n    -o-transform: scale(1);\n    transform: scale(1); } }\n.dropzone, .dropzone * {\n  box-sizing: border-box; }\n\n.dropzone {\n  min-height: 150px;\n  border: 2px solid rgba(0, 0, 0, 0.3);\n  background: white;\n  padding: 20px 20px; }\n  .dropzone.dz-clickable {\n    cursor: pointer; }\n    .dropzone.dz-clickable * {\n      cursor: default; }\n    .dropzone.dz-clickable .dz-message, .dropzone.dz-clickable .dz-message * {\n      cursor: pointer; }\n  .dropzone.dz-started .dz-message {\n    display: none; }\n  .dropzone.dz-drag-hover {\n    border-style: solid; }\n    .dropzone.dz-drag-hover .dz-message {\n      opacity: 0.5; }\n  .dropzone .dz-message {\n    text-align: center;\n    margin: 2em 0; }\n    .dropzone .dz-message .dz-button {\n      background: none;\n      color: inherit;\n      border: none;\n      padding: 0;\n      font: inherit;\n      cursor: pointer;\n      outline: inherit; }\n  .dropzone .dz-preview {\n    position: relative;\n    display: inline-block;\n    vertical-align: top;\n    margin: 16px;\n    min-height: 100px; }\n    .dropzone .dz-preview:hover {\n      z-index: 1000; }\n      .dropzone .dz-preview:hover .dz-details {\n        opacity: 1; }\n    .dropzone .dz-preview.dz-file-preview .dz-image {\n      border-radius: 20px;\n      background: #999;\n      background: linear-gradient(to bottom, #eee, #ddd); }\n    .dropzone .dz-preview.dz-file-preview .dz-details {\n      opacity: 1; }\n    .dropzone .dz-preview.dz-image-preview {\n      background: white; }\n      .dropzone .dz-preview.dz-image-preview .dz-details {\n        -webkit-transition: opacity 0.2s linear;\n        -moz-transition: opacity 0.2s linear;\n        -ms-transition: opacity 0.2s linear;\n        -o-transition: opacity 0.2s linear;\n        transition: opacity 0.2s linear; }\n    .dropzone .dz-preview .dz-remove {\n      font-size: 14px;\n      text-align: center;\n      display: block;\n      cursor: pointer;\n      border: none; }\n      .dropzone .dz-preview .dz-remove:hover {\n        text-decoration: underline; }\n    .dropzone .dz-preview:hover .dz-details {\n      opacity: 1; }\n    .dropzone .dz-preview .dz-details {\n      z-index: 20;\n      position: absolute;\n      top: 0;\n      left: 0;\n      opacity: 0;\n      font-size: 13px;\n      min-width: 100%;\n      max-width: 100%;\n      padding: 2em 1em;\n      text-align: center;\n      color: rgba(0, 0, 0, 0.9);\n      line-height: 150%; }\n      .dropzone .dz-preview .dz-details .dz-size {\n        margin-bottom: 1em;\n        font-size: 16px; }\n      .dropzone .dz-preview .dz-details .dz-filename {\n        white-space: nowrap; }\n        .dropzone .dz-preview .dz-details .dz-filename:hover span {\n          border: 1px solid rgba(200, 200, 200, 0.8);\n          background-color: rgba(255, 255, 255, 0.8); }\n        .dropzone .dz-preview .dz-details .dz-filename:not(:hover) {\n          overflow: hidden;\n          text-overflow: ellipsis; }\n          .dropzone .dz-preview .dz-details .dz-filename:not(:hover) span {\n            border: 1px solid transparent; }\n      .dropzone .dz-preview .dz-details .dz-filename span, .dropzone .dz-preview .dz-details .dz-size span {\n        background-color: rgba(255, 255, 255, 0.4);\n        padding: 0 0.4em;\n        border-radius: 3px; }\n    .dropzone .dz-preview:hover .dz-image img {\n      -webkit-transform: scale(1.05, 1.05);\n      -moz-transform: scale(1.05, 1.05);\n      -ms-transform: scale(1.05, 1.05);\n      -o-transform: scale(1.05, 1.05);\n      transform: scale(1.05, 1.05);\n      -webkit-filter: blur(8px);\n      filter: blur(8px); }\n    .dropzone .dz-preview .dz-image {\n      border-radius: 20px;\n      overflow: hidden;\n      width: 120px;\n      height: 120px;\n      position: relative;\n      display: block;\n      z-index: 10; }\n      .dropzone .dz-preview .dz-image img {\n        display: block; }\n    .dropzone .dz-preview.dz-success .dz-success-mark {\n      -webkit-animation: passing-through 3s cubic-bezier(0.77, 0, 0.175, 1);\n      -moz-animation: passing-through 3s cubic-bezier(0.77, 0, 0.175, 1);\n      -ms-animation: passing-through 3s cubic-bezier(0.77, 0, 0.175, 1);\n      -o-animation: passing-through 3s cubic-bezier(0.77, 0, 0.175, 1);\n      animation: passing-through 3s cubic-bezier(0.77, 0, 0.175, 1); }\n    .dropzone .dz-preview.dz-error .dz-error-mark {\n      opacity: 1;\n      -webkit-animation: slide-in 3s cubic-bezier(0.77, 0, 0.175, 1);\n      -moz-animation: slide-in 3s cubic-bezier(0.77, 0, 0.175, 1);\n      -ms-animation: slide-in 3s cubic-bezier(0.77, 0, 0.175, 1);\n      -o-animation: slide-in 3s cubic-bezier(0.77, 0, 0.175, 1);\n      animation: slide-in 3s cubic-bezier(0.77, 0, 0.175, 1); }\n    .dropzone .dz-preview .dz-success-mark, .dropzone .dz-preview .dz-error-mark {\n      pointer-events: none;\n      opacity: 0;\n      z-index: 500;\n      position: absolute;\n      display: block;\n      top: 50%;\n      left: 50%;\n      margin-left: -27px;\n      margin-top: -27px; }\n      .dropzone .dz-preview .dz-success-mark svg, .dropzone .dz-preview .dz-error-mark svg {\n        display: block;\n        width: 54px;\n        height: 54px; }\n    .dropzone .dz-preview.dz-processing .dz-progress {\n      opacity: 1;\n      -webkit-transition: all 0.2s linear;\n      -moz-transition: all 0.2s linear;\n      -ms-transition: all 0.2s linear;\n      -o-transition: all 0.2s linear;\n      transition: all 0.2s linear; }\n    .dropzone .dz-preview.dz-complete .dz-progress {\n      opacity: 0;\n      -webkit-transition: opacity 0.4s ease-in;\n      -moz-transition: opacity 0.4s ease-in;\n      -ms-transition: opacity 0.4s ease-in;\n      -o-transition: opacity 0.4s ease-in;\n      transition: opacity 0.4s ease-in; }\n    .dropzone .dz-preview:not(.dz-processing) .dz-progress {\n      -webkit-animation: pulse 6s ease infinite;\n      -moz-animation: pulse 6s ease infinite;\n      -ms-animation: pulse 6s ease infinite;\n      -o-animation: pulse 6s ease infinite;\n      animation: pulse 6s ease infinite; }\n    .dropzone .dz-preview .dz-progress {\n      opacity: 1;\n      z-index: 1000;\n      pointer-events: none;\n      position: absolute;\n      height: 16px;\n      left: 50%;\n      top: 50%;\n      margin-top: -8px;\n      width: 80px;\n      margin-left: -40px;\n      background: rgba(255, 255, 255, 0.9);\n      -webkit-transform: scale(1);\n      border-radius: 8px;\n      overflow: hidden; }\n      .dropzone .dz-preview .dz-progress .dz-upload {\n        background: #333;\n        background: linear-gradient(to bottom, #666, #444);\n        position: absolute;\n        top: 0;\n        left: 0;\n        bottom: 0;\n        width: 0;\n        -webkit-transition: width 300ms ease-in-out;\n        -moz-transition: width 300ms ease-in-out;\n        -ms-transition: width 300ms ease-in-out;\n        -o-transition: width 300ms ease-in-out;\n        transition: width 300ms ease-in-out; }\n    .dropzone .dz-preview.dz-error .dz-error-message {\n      display: block; }\n    .dropzone .dz-preview.dz-error:hover .dz-error-message {\n      opacity: 1;\n      pointer-events: auto; }\n    .dropzone .dz-preview .dz-error-message {\n      pointer-events: none;\n      z-index: 1000;\n      position: absolute;\n      display: block;\n      display: none;\n      opacity: 0;\n      -webkit-transition: opacity 0.3s ease;\n      -moz-transition: opacity 0.3s ease;\n      -ms-transition: opacity 0.3s ease;\n      -o-transition: opacity 0.3s ease;\n      transition: opacity 0.3s ease;\n      border-radius: 8px;\n      font-size: 13px;\n      top: 130px;\n      left: -10px;\n      width: 140px;\n      background: #be2626;\n      background: linear-gradient(to bottom, #be2626, #a92222);\n      padding: 0.5em 1.2em;\n      color: white; }\n      .dropzone .dz-preview .dz-error-message:after {\n        content: '';\n        position: absolute;\n        top: -6px;\n        left: 64px;\n        width: 0;\n        height: 0;\n        border-left: 6px solid transparent;\n        border-right: 6px solid transparent;\n        border-bottom: 6px solid #be2626; }\n\n@import \"./src/scss/_modules/variables\";\r\n\r\n.dropzone {\r\n  border: 2px dashed #dedede;\r\n  border-radius: 5px;\r\n  background: #f5f5f5;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  padding: 0.5rem;\r\n  //justify-content: center;\r\n}\r\n\r\n.dropzone i{\r\n  font-size: 3rem;\r\n}\r\n\r\n.dropzone .dz-message {\r\n  color: rgba(0,0,0,.54);\r\n  font-weight: 500;\r\n  font-size: initial;\r\n  text-transform: uppercase;\r\n  width: 100%;\r\n}\r\n\r\n.dropzone .dz-preview .dz-image {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tborder-radius: 0;\r\n\r\n\timg {\r\n\t\tborder-radius: $border-radius\r\n\t}\r\n}\r\n\r\n.dropzone .dz-preview.dz-image-preview {\r\n\tbackground: transparent;\r\n}\r\n\r\n.dropzone .dz-message {\r\n\tfont-weight: normal;\r\n\ttext-transform: none;\r\n\tcolor: inherit;\r\n}\r\n\r\n.dropzone.dz-drag-hover {\r\n\tborder-style: dashed;\r\n\tborder-color: $primary-500;\r\n}\r\n\r\n.dropzone .dz-preview.dz-file-preview .dz-image {\r\n\tborder-radius: $border-radius;\r\n\r\n}", "/*  THEME COLORs\r\n========================================================================== */\r\n/* Looks good on chrome default color profile */\r\n$color-primary:\t\t\t\t\t\t#886ab5;\r\n$color-success:\t\t\t\t\t\t#1dc9b7;\r\n$color-info:\t\t\t\t\t\t#2196F3;\r\n$color-warning:\t\t\t\t\t\t#ffc241;\r\n$color-danger:\t\t\t\t\t\t#fd3995;\r\n$color-fusion:\t\t\t\t\t\tdarken(desaturate(adjust-hue($color-primary, 5), 80%), 25%); \r\n\r\n/* We will manually convert these primary colors to rgb for the dark mode option of the theme */\r\n$rgb-primary:\t\t\t\t\t\thexToRGBString($color-primary) !default;\r\n$rgb-success:\t\t\t\t\t\thexToRGBString($color-success) !default;\r\n$rgb-info:\t\t\t\t\t\t\thexToRGBString($color-info) !default;\r\n$rgb-warning:\t\t\t\t\t\thexToRGBString($color-warning) !default;\r\n$rgb-danger:\t\t\t\t\t\thexToRGBString($color-danger) !default;\r\n$rgb-fusion:\t\t\t\t\t\thexToRGBString($color-fusion) !default; \r\n\r\n/* looks good in sRGB but washed up on chrome default \r\n$color-primary:\t\t\t\t\t\t#826bb0;\r\n$color-success:\t\t\t\t\t\t#31cb55;\r\n$color-info:\t\t\t\t\t\t#5e93ec;\r\n$color-warning:\t\t\t\t\t\t#eec559;\r\n$color-danger:\t\t\t\t\t\t#dc4b92;\r\n$color-fusion:\t\t\t\t\t\tdarken(desaturate(adjust-hue($color-primary, 5), 80%), 25%); */\r\n\r\n/*  Color Polarity\r\n========================================================================== */\r\n$white:\t\t\t\t\t\t\t\t#fff !default;\r\n$black:\t\t\t\t\t\t\t\t#000 !default;\r\n$disabled:\t\t\t\t\t\t\tdarken($white, 20%) !default;\r\n\r\n/*  PAINTBUCKET MIXER\r\n========================================================================== */\r\n/* the grays */ \r\n$gray-50:\t\t\t\t\t\t\t#f9f9f9;\r\n$gray-100:\t\t\t\t\t\t\t#f8f9fa;\r\n$gray-200:\t\t\t\t\t\t\t#f3f3f3;\r\n$gray-300:\t\t\t\t\t\t\t#dee2e6;\r\n$gray-400:\t\t\t\t\t\t\t#ced4da;\r\n$gray-500:\t\t\t\t\t\t\t#adb5bd;\r\n$gray-600:\t\t\t\t\t\t\t#868e96;\r\n$gray-700:\t\t\t\t\t\t\t#495057;\r\n$gray-800:\t\t\t\t\t\t\t#343a40;\r\n$gray-900:\t\t\t\t\t\t\t#212529;\r\n\r\n/* the sapphires */\r\n$primary-50:\t\t\t\t\t\tlighten($color-primary, 25%) !default;\t\r\n$primary-100:\t\t\t\t\t\tlighten($color-primary, 20%) !default;\t\r\n$primary-200:\t\t\t\t\t\tlighten($color-primary, 15%) !default;\t\r\n$primary-300:\t\t\t\t\t\tlighten($color-primary, 10%) !default;\t\r\n$primary-400:\t\t\t\t\t\tlighten($color-primary, 5%) !default;\r\n$primary-500:\t\t\t\t\t\t$color-primary !default;\r\n$primary-600:\t\t\t\t\t\tdarken($color-primary, 5%) !default;\r\n$primary-700:\t\t\t\t\t\tdarken($color-primary, 10%) !default;\r\n$primary-800:\t\t\t\t\t\tdarken($color-primary, 15%) !default;\r\n$primary-900:\t\t\t\t\t\tdarken($color-primary, 20%) !default;\r\n\r\n/* the emeralds */\r\n$success-50:\t\t\t\t\t\tlighten($color-success, 25%) !default;\t\r\n$success-100:\t\t\t\t\t\tlighten($color-success, 20%) !default;\t\r\n$success-200:\t\t\t\t\t\tlighten($color-success, 15%) !default;\t\r\n$success-300:\t\t\t\t\t\tlighten($color-success, 10%) !default;\t\r\n$success-400:\t\t\t\t\t\tlighten($color-success, 5%) !default;\r\n$success-500:\t\t\t\t\t\t$color-success !default;\r\n$success-600:\t\t\t\t\t\tdarken($color-success, 5%) !default;\r\n$success-700:\t\t\t\t\t\tdarken($color-success, 10%) !default;\r\n$success-800:\t\t\t\t\t\tdarken($color-success, 15%) !default;\r\n$success-900:\t\t\t\t\t\tdarken($color-success, 20%) !default;\r\n\r\n/* the amethyths */\r\n$info-50:\t\t\t\t\t\t\tlighten($color-info, 25%) !default;\t\r\n$info-100:\t\t\t\t\t\t\tlighten($color-info, 20%) !default;\t\r\n$info-200:\t\t\t\t\t\t\tlighten($color-info, 15%) !default;\t\r\n$info-300:\t\t\t\t\t\t\tlighten($color-info, 10%) !default;\t\r\n$info-400:\t\t\t\t\t\t\tlighten($color-info, 5%) !default;\r\n$info-500:\t\t\t\t\t\t\t$color-info !default;\r\n$info-600:\t\t\t\t\t\t\tdarken($color-info, 5%) !default;\r\n$info-700:\t\t\t\t\t\t\tdarken($color-info, 10%) !default;\r\n$info-800:\t\t\t\t\t\t\tdarken($color-info, 15%) !default;\r\n$info-900:\t\t\t\t\t\t\tdarken($color-info, 20%) !default;\r\n\r\n/* the topaz */\r\n$warning-50:\t\t\t\t\t\tlighten($color-warning, 25%) !default;\t\r\n$warning-100:\t\t\t\t\t\tlighten($color-warning, 20%) !default;\t\r\n$warning-200:\t\t\t\t\t\tlighten($color-warning, 15%) !default;\t\r\n$warning-300:\t\t\t\t\t\tlighten($color-warning, 10%) !default;\t\r\n$warning-400:\t\t\t\t\t\tlighten($color-warning, 5%) !default;\r\n$warning-500:\t\t\t\t\t\t$color-warning !default;\r\n$warning-600:\t\t\t\t\t\tdarken($color-warning, 5%) !default;\r\n$warning-700:\t\t\t\t\t\tdarken($color-warning, 10%) !default;\r\n$warning-800:\t\t\t\t\t\tdarken($color-warning, 15%) !default;\r\n$warning-900:\t\t\t\t\t\tdarken($color-warning, 20%) !default;\r\n\r\n/* the rubies */\r\n$danger-50:\t\t\t\t\t\t\tlighten($color-danger, 25%) !default;\t\r\n$danger-100:\t\t\t\t\t\tlighten($color-danger, 20%) !default;\t\r\n$danger-200:\t\t\t\t\t\tlighten($color-danger, 15%) !default;\t\r\n$danger-300:\t\t\t\t\t\tlighten($color-danger, 10%) !default;\t\r\n$danger-400:\t\t\t\t\t\tlighten($color-danger, 5%) !default;\r\n$danger-500:\t\t\t\t\t\t$color-danger !default;\r\n$danger-600:\t\t\t\t\t\tdarken($color-danger, 5%) !default;\r\n$danger-700:\t\t\t\t\t\tdarken($color-danger, 10%) !default;\r\n$danger-800:\t\t\t\t\t\tdarken($color-danger, 15%) !default;\r\n$danger-900:\t\t\t\t\t\tdarken($color-danger, 20%) !default;\r\n\r\n/* the graphites */\r\n$fusion-50:\t\t\t\t\t\t\tlighten($color-fusion, 25%) !default;\t\r\n$fusion-100:\t\t\t\t\t\tlighten($color-fusion, 20%) !default;\t\r\n$fusion-200:\t\t\t\t\t\tlighten($color-fusion, 15%) !default;\t\r\n$fusion-300:\t\t\t\t\t\tlighten($color-fusion, 10%) !default;\t\r\n$fusion-400:\t\t\t\t\t\tlighten($color-fusion, 5%) !default;\r\n$fusion-500:\t\t\t\t\t\t$color-fusion !default;\r\n$fusion-600:\t\t\t\t\t\tdarken($color-fusion, 5%) !default;\r\n$fusion-700:\t\t\t\t\t\tdarken($color-fusion, 10%) !default;\r\n$fusion-800:\t\t\t\t\t\tdarken($color-fusion, 15%) !default;\r\n$fusion-900:\t\t\t\t\t\tdarken($color-fusion, 20%) !default;\r\n\r\n$theme-colors-extended: () !default;\r\n$theme-colors-extended: map-merge((\r\n\t\"rgb-primary\":\t\t\t\t\t$rgb-primary,\r\n\t\"rgb-success\":\t\t\t\t\t$rgb-success,\r\n\t\"rgb-info\":\t\t\t\t\t\t$rgb-info,\r\n\t\"rgb-warning\":\t\t\t\t\t$rgb-warning,\r\n\t\"rgb-danger\":\t\t\t\t\t$rgb-danger,\r\n\t\"rgb-fusion\":\t\t\t\t\t$rgb-fusion,\r\n\t\"primary-50\":\t\t\t\t\t$primary-50,\r\n\t\"primary-100\":\t\t\t\t\t$primary-100,\r\n\t\"primary-200\":\t\t\t\t\t$primary-200,\r\n\t\"primary-300\":\t\t\t\t\t$primary-300,\r\n\t\"primary-400\":\t\t\t\t\t$primary-400,\r\n\t\"primary-500\":\t\t\t\t\t$primary-500,\r\n\t\"primary-600\":\t\t\t\t\t$primary-600,\r\n\t\"primary-700\":\t\t\t\t\t$primary-700,\r\n\t\"primary-800\":\t\t\t\t\t$primary-800,\r\n\t\"primary-900\":\t\t\t\t\t$primary-900,\r\n\t\"success-50\":\t\t\t\t\t$success-50,\r\n\t\"success-100\":\t\t\t\t\t$success-100,\r\n\t\"success-200\":\t\t\t\t\t$success-200,\r\n\t\"success-300\":\t\t\t\t\t$success-300,\r\n\t\"success-400\":\t\t\t\t\t$success-400,\r\n\t\"success-500\":\t\t\t\t\t$success-500,\r\n\t\"success-600\":\t\t\t\t\t$success-600,\r\n\t\"success-700\":\t\t\t\t\t$success-700,\r\n\t\"success-800\":\t\t\t\t\t$success-800,\r\n\t\"success-900\":\t\t\t\t\t$success-900,\r\n\t\"info-50\":\t\t\t\t\t\t$info-50,\r\n\t\"info-100\":\t\t\t\t\t\t$info-100,\r\n\t\"info-200\":\t\t\t\t\t\t$info-200,\r\n\t\"info-300\":\t\t\t\t\t\t$info-300,\r\n\t\"info-400\":\t\t\t\t\t\t$info-400,\r\n\t\"info-500\":\t\t\t\t\t\t$info-500,\r\n\t\"info-600\":\t\t\t\t\t\t$info-600,\r\n\t\"info-700\":\t\t\t\t\t\t$info-700,\r\n\t\"info-800\":\t\t\t\t\t\t$info-800,\r\n\t\"info-900\":\t\t\t\t\t\t$info-900,\r\n\t\"warning-50\":\t\t\t\t\t$warning-50,\r\n\t\"warning-100\":\t\t\t\t\t$warning-100,\r\n\t\"warning-200\":\t\t\t\t\t$warning-200,\r\n\t\"warning-300\":\t\t\t\t\t$warning-300,\r\n\t\"warning-400\":\t\t\t\t\t$warning-400,\r\n\t\"warning-500\":\t\t\t\t\t$warning-500,\r\n\t\"warning-600\":\t\t\t\t\t$warning-600,\r\n\t\"warning-700\":\t\t\t\t\t$warning-700,\r\n\t\"warning-800\":\t\t\t\t\t$warning-800,\r\n\t\"warning-900\":\t\t\t\t\t$warning-900,  \r\n\t\"danger-50\":\t\t\t\t\t$danger-50,\r\n\t\"danger-100\":\t\t\t\t\t$danger-100,\r\n\t\"danger-200\":\t\t\t\t\t$danger-200,\r\n\t\"danger-300\":\t\t\t\t\t$danger-300,\r\n\t\"danger-400\":\t\t\t\t\t$danger-400,\r\n\t\"danger-500\":\t\t\t\t\t$danger-500,\r\n\t\"danger-600\":\t\t\t\t\t$danger-600,\r\n\t\"danger-700\":\t\t\t\t\t$danger-700,\r\n\t\"danger-800\":\t\t\t\t\t$danger-800,\r\n\t\"danger-900\":\t\t\t\t\t$danger-900,\r\n\t\"fusion-50\":\t\t\t\t\t$fusion-50,\r\n\t\"fusion-100\":\t\t\t\t\t$fusion-100,\r\n\t\"fusion-200\":\t\t\t\t\t$fusion-200,\r\n\t\"fusion-300\":\t\t\t\t\t$fusion-300,\r\n\t\"fusion-400\":\t\t\t\t\t$fusion-400,\r\n\t\"fusion-500\":\t\t\t\t\t$fusion-500,\r\n\t\"fusion-600\":\t\t\t\t\t$fusion-600,\r\n\t\"fusion-700\":\t\t\t\t\t$fusion-700,\r\n\t\"fusion-800\":\t\t\t\t\t$fusion-800,\r\n\t\"fusion-900\":\t\t\t\t\t$fusion-900\r\n\r\n), $theme-colors-extended);\r\n\r\n/*  Define universal border difition (div outlines, etc)\r\n========================================================================== */\r\n$theme-border-utility-size:\t\t\t\t0px;\r\n\r\n/*  MOBILE BREAKPOINT & GUTTERS (contains some bootstrap responsive overrides)\r\n========================================================================== */\r\n$grid-breakpoints: (\r\n\t// Extra small screen / phone\r\n\txs: 0,\r\n\t// Small screen / phone\r\n\tsm: 576px,\r\n\t// Medium screen / tablet\r\n\tmd: 768px,\r\n\t// Large screen / desktop\r\n\tlg: 992px, // also change 'mobileResolutionTrigger' in app.config.js\r\n\t// Decently size screen / wide laptop\r\n\txl: 1399px \r\n);\r\n\r\n$mobile-breakpoint:\t\t\t\t\t\tlg !default;                               /* define when mobile menu activates, here we are declearing (lg) so it targets the one after it */\r\n$mobile-breakpoint-size:\t\t\t\tmap-get($grid-breakpoints, lg) !default;   /* bootstrap reference xs: 0,  sm: 544px, md: 768px, lg: 992px, xl: 1200px*/\r\n//$mobile-font-size:\t\t\t\t\t\t15px; \t                                   /* bigger fontsize for mobile screens */\r\n$grid-gutter-width-base:\t\t\t\t3rem;\r\n$grid-gutter-width:\t\t\t\t\t\t1.5rem;\r\n\r\n$grid-gutter-widths: (\r\n\txs: $grid-gutter-width-base / 2,         \r\n\tsm: $grid-gutter-width-base / 2,          \r\n\tmd: $grid-gutter-width-base / 2,        \r\n\tlg: $grid-gutter-width-base / 2,        \r\n\txl: $grid-gutter-width-base / 2        \r\n);\r\n\r\n\r\n/* global var used for spacing*/\r\n$spacer:                  1rem;\r\n$spacers: () ;\r\n$spacers: map-merge(\r\n\t(\r\n\t\t0: 0,\r\n\t\t1: ($spacer * .25),\r\n\t\t2: ($spacer * .5),\r\n\t\t3: $spacer,\r\n\t\t4: ($spacer * 1.5),\r\n\t\t5: ($spacer * 2),\r\n\t\t6: ($spacer * 2.5)\r\n\t),\r\n\t$spacers\r\n);\r\n\r\n/* Uniform Padding variable */\r\n/* Heads up! This is a global scoped variable - changing may impact the whole template */\r\n$p-1:\t\t\t\t\t\t\t\t\t0.25rem;\r\n$p-2:\t\t\t\t\t\t\t\t\t0.5rem;\r\n$p-3:\t\t\t\t\t\t\t\t\t1rem;\r\n$p-4:\t\t\t\t\t\t\t\t\t1.5rem;\r\n$p-5:\t\t\t\t\t\t\t\t\t2rem;\r\n\r\n\r\n/*   BOOTSTRAP OVERRIDES (bootstrap variables)\r\n========================================================================== */ \r\n$grays: (\r\n\t\"100\": $gray-100,\r\n\t\"200\": $gray-200,\r\n\t\"300\": $gray-300,\r\n\t\"400\": $gray-400,\r\n\t\"500\": $gray-500,\r\n\t\"600\": $gray-600,\r\n\t\"700\": $gray-700,\r\n\t\"800\": $gray-800,\r\n\t\"900\": $gray-900\r\n);\r\n\r\n$colors: (\r\n\t\"blue\": $color-primary,\r\n\t\"red\": $color-danger,\r\n\t\"orange\": $color-warning,\r\n\t\"yellow\": $color-warning,\r\n\t\"green\": $color-success,\r\n\t\"white\": $white,\r\n\t\"gray\": $gray-600,\r\n\t\"gray-dark\": $gray-700\r\n);\r\n\r\n/* usage: theme-colors(\"primary\"); */\r\n$theme-colors: (\r\n\t\"primary\": $color-primary,\r\n\t\"secondary\": $gray-600,\r\n\t\"success\": $color-success,\r\n\t\"info\": $color-info,\r\n\t\"warning\": $color-warning,\r\n\t\"danger\": $color-danger,\r\n\t\"light\": $white,\r\n\t\"dark\": $fusion-500\r\n);\r\n\r\n/* forms */\r\n/*$input-height:\t\t\t\t\t\t\tcalc(2.25rem + 1px); //I had to add this because the input gruops was having improper height for some reason... */\r\n$input-border-color:\t\t\t\t\t#E5E5E5;\r\n$input-focus-border-color:\t\t\t\t$color-primary;\r\n$input-btn-focus-color:\t\t\t\t\ttransparent;\r\n$input-padding-y:\t\t\t\t\t\t.5rem;  \r\n$input-padding-x:\t\t\t\t\t\t.875rem;\r\n$label-margin-bottom:\t\t\t\t\t.3rem;\r\n$form-group-margin-bottom:\t\t\t\t1.5rem;\r\n\r\n/* links */\r\n$link-color:\t\t\t\t\t\t\t$primary-500;\r\n$link-hover-color:\t\t\t\t\t\t$primary-400;\r\n\r\n/* checkbox */ \r\n$custom-control-indicator-size:\t\t\t\t\t1.125rem;\r\n$custom-checkbox-indicator-border-radius:\t\t2px;\r\n$custom-control-indicator-border-width: \t\t2px;\r\n$custom-control-indicator-bg-size:\t\t\t\t0.5rem;\r\n\r\n/*$custom-file-height-inner:\t\t\t\tcalc(2.25rem - 1px);*/\r\n//$custom-file-padding-y:\t\t\t\t\t$input-padding-y;\r\n\r\n/* not part of bootstrap variable */\r\n$custom-control-indicator-bg-size-checkbox:  50% 50% !default;\r\n\r\n/* custom checkbox */\r\n// the checkbox needs to be a little darker for input groups\r\n$custom-control-indicator-checked-bg:\t\t\t\t$primary-600;\r\n$custom-control-indicator-checked-border-color: \t$primary-700;\r\n$custom-control-indicator-checked-disabled-bg:\t\t$primary-100;\r\n\r\n$custom-control-indicator-active-bg:\t\t\t\t$primary-100; \r\n$custom-control-indicator-active-border-color:\t\t$primary-100;\r\n$custom-control-indicator-active-color:\t\t\t\t$primary-100;\r\n\r\n$custom-control-indicator-focus-border-color:\t\t$primary-400;\r\n$custom-select-focus-border-color:\t\t\t\t\t$primary-500;\r\n\r\n$custom-checkbox-indicator-indeterminate-border-color: $primary-500;\r\n$custom-checkbox-indicator-indeterminate-bg: $primary-500;\r\n\r\n\r\n/* custom range */\r\n$custom-range-thumb-width:\t\t\t\t1rem;\r\n$custom-range-thumb-border-radius:\t\t50%;\r\n$custom-range-track-height:\t\t\t\t0.325rem;\r\n$custom-range-thumb-bg:\t\t\t\t\t$primary-500;\r\n$custom-range-thumb-active-bg:\t\t\t$primary-300;\r\n$custom-range-thumb-focus-box-shadow:\t0 0 0 1px $white, 0 0 0 0.2rem rgba($primary-500, 0.25);\r\n\r\n/* custom file */\r\n$custom-file-focus-border-color:\t\t$primary-500;\r\n\r\n/* badge */\r\n$badge-font-size:\t\t\t\t\t\t85%;\r\n$badge-font-weight:\t\t\t\t\t\t500;\r\n\r\n/* cards */\r\n$card-spacer-y:\t\t\t\t\t\t\t1rem;\r\n$card-spacer-x:\t\t\t\t\t\t\t1rem;\r\n$card-cap-bg:\t\t\t\t\t\t\tinherit;\r\n$card-border-color:\t\t\t\t\t\trgba(0, 0, 0, 0.08);\r\n$list-group-border-color:\t\t\t\t$card-border-color;\r\n\r\n/*border radius*/\r\n$border-radius:\t\t\t\t\t\t\t4px;\r\n$border-radius-lg:\t\t\t\t\t\t$border-radius;\r\n$border-radius-sm:\t\t\t\t\t\t$border-radius;\r\n$border-radius-plus:\t\t\t\t\t10px;\r\n\r\n/* alert */\r\n$alert-padding-y:\t\t\t\t\t\t1rem;\r\n$alert-padding-x:\t\t\t\t\t\t1.25rem;\r\n$alert-margin-bottom:\t\t\t\t\t$grid-gutter-width + 0.5rem;\r\n\r\n/* toast */\r\n$toast-padding-y:\t\t\t\t\t\t0.5rem;\r\n$toast-padding-x:\t\t\t\t\t\t0.75rem;\r\n$toast-header-color:\t\t\t\t\t$fusion-500;\r\n\r\n/* breadcrumb */\r\n$breadcrumb-bg:\t\t\t\t\t\t\tlighten($fusion-50, 40%);\r\n$breadcrumb-divider-color:\t\t\t\tinherit;\r\n\r\n/* input button */\r\n$input-btn-padding-y-sm:\t\t\t\t.375rem;\r\n$input-btn-padding-x-sm:\t\t\t\t.844rem;\r\n\r\n$input-btn-padding-y:\t\t\t\t\t.5rem;\r\n$input-btn-padding-x:\t\t\t\t\t1.125rem;\r\n\r\n$input-btn-padding-y-lg:\t\t\t\t.75rem;\r\n$input-btn-padding-x-lg:\t\t\t\t1.5rem;\r\n\r\n/* nav link */\r\n$nav-link-padding-y:\t\t\t\t\t$input-btn-padding-y;\r\n$nav-link-padding-x:\t\t\t\t\t$input-btn-padding-x;\r\n\r\n/* nav, tabs, pills */\r\n$nav-tabs-border-color:\t\t\t\t\trgba($black, 0.1);\r\n$nav-tabs-link-active-border-color:\t\trgba($black, 0.1) rgba($black, 0.1) $white;\r\n$nav-tabs-link-hover-border-color:\t\trgba($black, 0.07) rgba($black, 0.07) transparent;\r\n\r\n/* tables */\r\n$table-border-color:\t\t\t\t\tlighten(desaturate($primary-500, 60%), 35%); //rgba($black, 0.09);\r\n$table-hover-bg:\t\t\t\t\t\tlighten(desaturate($primary-900, 70%), 63%);\r\n$table-accent-bg:\t\t\t\t\t\trgba($fusion-500,.02);\r\n$table-dark-bg:\t\t\t\t\t\t\t$fusion-300;\r\n$table-dark-border-color:\t\t\t\t$fusion-400;\r\n$table-dark-accent-bg:\t\t\t\t\trgba($white, .05);\r\n$table-dark-hover-bg:\t\t\t\t\t$color-primary;\r\n\r\n/* dropdowns */\r\n$dropdown-border-width:\t\t\t\t\t$theme-border-utility-size; \r\n$dropdown-padding-y:\t\t\t\t\t.3125rem;\r\n$dropdown-item-padding-y:\t\t\t\t.75rem;\r\n$dropdown-item-padding-x:\t\t\t\t1.5rem; \r\n$dropdown-link-active-bg:\t\t\t\tlighten($primary-50, 13%);  \r\n$dropdown-link-active-color:\t\t\t$primary-900;\r\n$dropdown-link-hover-color:\t\t\t\t$primary-700;\r\n\r\n/* dropdowns sizes */\r\n$dropdown-xl-width:\t\t\t\t\t\t21.875rem !default;\r\n$dropdown-lg-width:\t\t\t\t\t\t17.5rem !default;\r\n$dropdown-md-width:\t\t\t\t\t\t14rem !default;\r\n$dropdown-sm-width:\t\t\t\t\t\t8rem !default;\r\n$dropdown-shadow:\t\t\t\t\t\t0 0 15px 1px rgba(desaturate($primary-900, 20%), (20/100));   \r\n\r\n/* popovers */\r\n$popover-border-color:\t\t\t\t\trgba(0, 0, 0, 0.2);\r\n$popover-header-padding-y:\t\t\t\t1rem;\r\n$popover-header-padding-x:\t\t\t\t1rem;\r\n$popover-header-bg:\t\t\t\t\t\ttransparent;\r\n$popover-border-width:\t\t\t\t\t3px;\r\n$popover-arrow-width:\t\t\t\t\t15px;\r\n$popover-arrow-height:\t\t\t\t\t7px;\r\n$popover-arrow-outer-color:\t\t\t\tinherit;\r\n$popover-arrow-color:\t\t\t\t\ttransparent;\r\n$popover-font-size:\t\t\t\t\t\t14px;\r\n$popover-box-shadow:\t\t\t\t\t1px 0 13px rgba(90, 80, 105, 0.2);\r\n$popover-border-radius:\t\t\t\t\t0.5rem;\r\n\r\n/* tooltips */\r\n$tooltip-max-width:\t\t\t\t\t\t200px;\r\n$tooltip-color:\t\t\t\t\t\t\t$white;\r\n$tooltip-bg:\t\t\t\t\t\t\trgba($fusion-700, 0.9);\r\n$tooltip-border-radius:\t\t\t\t\t5px;\r\n$tooltip-opacity:\t\t\t\t\t\t1;\r\n$tooltip-padding-y:\t\t\t\t\t\t.3rem;\r\n$tooltip-padding-x:\t\t\t\t\t\t.6rem;\r\n$tooltip-margin:\t\t\t\t\t\t2px;\r\n$tooltip-arrow-width:\t\t\t\t\t8px;\r\n$tooltip-arrow-height:\t\t\t\t\t5px;\r\n\r\n/* modal */\r\n$modal-header-padding-y:\t\t\t\t1.25rem;\r\n$modal-header-padding-x:\t\t\t\t1.25rem;\r\n$modal-header-padding:\t\t\t\t\t$modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\r\n$modal-inner-padding:\t\t\t\t\t1.25rem;\r\n$modal-backdrop-opacity:\t\t\t\t0.2;\r\n$modal-content-border-color:\t\t\ttransparent;\r\n$modal-header-border-width:\t\t\t\t0px;\r\n$modal-footer-border-width:\t\t\t\t0px;\r\n\r\n//$modal-lg:\t\t\t\t\t\t\t\t650px;\r\n\r\n/* reference guide\r\nhttp://www.standardista.com/px-to-rem-conversion-if-root-font-size-is-16px/\r\n8px = 0.5rem\r\n9px = 0.5625rem\r\n10px = 0.625rem\r\n11px = 0.6875rem\r\n12px = 0.75rem\r\n13px = 0.8125rem\r\n14px = 0.875rem\r\n15px = 0.9375rem\r\n16px = 1rem (base)\r\n17px = 1.0625rem\r\n18px = 1.125rem\r\n19px = 1.1875rem\r\n20px = 1.25rem\r\n21px = 1.3125rem\r\n22px = 1.375rem\r\n24px = 1.5rem\r\n25px = 1.5625rem\r\n26px = 1.625rem\r\n28px = 1.75rem\r\n30px = 1.875rem\r\n32px = 2rem\r\n34px = 2.125rem\r\n36px = 2.25rem\r\n38px = 2.375rem\r\n40px = 2.5rem\r\n*/\r\n\r\n/* Fonts */\r\n$font-size-base:\t\t\t\t\t\t0.8125rem;\r\n$font-size-lg:\t\t\t\t\t\t\t1rem;\r\n$font-size-sm:\t\t\t\t\t\t\t0.75rem;\r\n$line-height-base:\t\t\t\t\t\t1.47;\r\n$headings-line-height:\t\t\t\t\t1.57;\r\n\r\n$h1-font-size:\t\t\t\t\t\t\t1.5rem;\r\n$h2-font-size:\t\t\t\t\t\t\t1.375rem;\r\n$h3-font-size:\t\t\t\t\t\t\t1.1875rem;\r\n$h4-font-size:\t\t\t\t\t\t\t1.0625rem;\r\n$h5-font-size:\t\t\t\t\t\t\t0.9375rem;\r\n$h6-font-size:\t\t\t\t\t\t\t0.875rem;\r\n\r\n$display1-size:\t\t\t\t\t\t\t5rem;\r\n$display2-size:\t\t\t\t\t\t\t4.5rem;\r\n$display3-size:\t\t\t\t\t\t\t3.5rem;\r\n$display4-size:\t\t\t\t\t\t\t2.5rem;\r\n\r\n$navbar-toggler-font-size:\t\t\t\t21px;\r\n$navbar-toggler-padding-y:\t\t\t\t7.5px; \r\n$navbar-toggler-padding-x:\t\t\t\t18px;\r\n\r\n/* carousel */\r\n$carousel-indicator-height:\t\t\t\t13px;\r\n$carousel-indicator-width:\t\t\t\t13px;\r\n\r\n/*  BASE VARS\r\n========================================================================== */\r\n// usage: background-image: url(\"#{$baseURL}img/bg.png\"); \r\n\r\n$baseURL:\t\t\t\t\t\t\t\t\"../\" !default;\r\n$webfontsURL:\t\t\t\t\t\t\t\"../webfonts\" !default;\r\n$base-text-color:\t\t\t\t\t\tdarken($white,60%) !default;\r\n\r\n/* font vars below will auto change to rem values using function rem($value)*/\r\n$fs-base:\t\t\t\t\t\t\t\t13px !default;\r\n$fs-nano:\t\t\t\t\t\t\t\t$fs-base - 2;   /* 11px   */\r\n$fs-xs: \t\t\t\t\t\t\t\t$fs-base - 1;   /* 12px   */\r\n$fs-sm: \t\t\t\t\t\t\t\t$fs-base - 0.5; /* 12.5px */\r\n$fs-md: \t\t\t\t\t\t\t\t$fs-base + 1;   /* 14px   */\r\n$fs-lg: \t\t\t\t\t\t\t\t$fs-base + 2;   /* 15px   */\r\n$fs-xl: \t\t\t\t\t\t\t\t$fs-base + 3;   /* 16px   */\r\n$fs-xxl: \t\t\t\t\t\t\t\t$fs-base + 15;  /* 28px   */\r\n\r\n/*  Font Family\r\n========================================================================== */\r\n\t\t\t\t\t\t\t\t\t\t/*hint: you can also try the font called 'Poppins' by replacing the font 'Roboto' */\r\n$font-import:\t\t\t\t\t\t\t\"https://fonts.googleapis.com/css?family=Roboto:300,400,500,700,900\" !default;\r\n$page-font:\t\t\t\t\t\t\t\t\"Roboto\", 'Helvetica Neue', Helvetica, Arial !default;\r\n$nav-font:\t\t\t\t\t\t\t\t$page-font !default;\r\n$heading-font-family:\t\t\t\t\t$page-font !default; \r\n$mobile-page-font:\t\t\t\t\t\t'HelveticaNeue-Light','Helvetica Neue Light','Helvetica Neue',Helvetica,Arial,sans-serif;\r\n\r\n/*  ANIMATIONS\r\n========================================================================== */\r\n$nav-hide-animate: \t\t\t\t\t\tall 470ms cubic-bezier(0.34, 1.25, 0.3, 1) !default;\t\t/* this addresses all animation related to nav hide to nav minify */\r\n\r\n/*  Z-INDEX declearation\r\n========================================================================== */\r\n$space:\t\t\t\t\t\t\t\t\t1000 !default;\r\n$cloud:\t\t\t\t\t\t\t\t\t950 !default;\r\n$ground:\t\t\t\t\t\t\t\t0 !default;\r\n$water:\t\t\t\t\t\t\t\t\t-99 !default;\r\n/* we adjust bootstrap z-index to be higher than our higest z-index*/\r\n$zindex-dropdown:\t\t\t\t\t\t$space + 1000;\r\n$zindex-sticky:\t\t\t\t\t\t\t$space + 1020;\r\n$zindex-fixed:\t\t\t\t\t\t\t$space + 1030;\r\n$zindex-modal-backdrop:\t\t\t\t\t$space + 1040;\r\n$zindex-modal:\t\t\t\t\t\t\t$space + 1050;\r\n$zindex-panel-fullscreen:\t\t\t\t$space + 1055;\r\n$zindex-popover:\t\t\t\t\t\t$space + 1060;\r\n$zindex-tooltip:\t\t\t\t\t\t$space + 1070;\r\n\r\n/*  CUSTOM ICON PREFIX \r\n========================================================================== */\r\n$cust-icon-prefix:\t\t\t\t\t\tni;\r\n\r\n/*  PRINT CSS (landscape or portrait)\r\n========================================================================== */\r\n$print-page-type: \t\t\t\t\t\tportrait; \t\t\t\t\t\t\t\t\t\t\t\t  /* landscape or portrait */\r\n$print-page-size:\t\t\t\t\t\tletter;\t\t\t\t\t\t\t\t\t\t\t\t\t  /* auto, letter */\r\n$print-page-margin:\t\t\t\t\t\t1.0cm;\r\n\r\n/*  Common Element Variables\r\n========================================================================== */\r\n$body-background-color:\t\t\t\t\t$white !default;\r\n$page-bg:\t\t\t\t\t\t\t\tdesaturate(lighten($primary-500, 41.7%), 5%)  !default; //#f9f9fc\r\n\r\n/* Z-index decleartion \"birds eye view\"\r\n========================================================================== */\r\n$depth:\t\t\t\t\t\t\t\t\t999 !default;\r\n$depth-header:\t\t\t\t\t\t\t$depth + 1 !default;\r\n$depth-nav:\t\t\t\t\t\t\t\t$depth-header + 2 !default;\r\n\r\n/*  Components\r\n========================================================================== */\r\n$frame-border-color:\t\t\t\t\t#f7f9fa !default;\r\n\r\n/*  PAGE HEADER STUFF\r\n========================================================================== */\r\n\r\n/* colors */\r\n$header-bg:\t\t\t\t\t\t\t\t$white !default;\r\n$header-border-color:\t\t\t\t\t#ccc !default;\r\n$header-border-bottom-color:\t\t\trgba(darken($primary-700, 10%), (13/100)) !default;\t\t\r\n$header-link-color:\t\t\t\t\t\t$primary-500 !default;\r\n$header-link-hover-color:\t\t\t\tdarken($header-bg, 75%) !default;\r\n\r\n/* height */\r\n$header-height:\t\t\t\t\t\t\t4.125rem !default;\r\n$header-height-nav-top:\t\t\t\t\t4.125rem !default;\r\n$header-inner-padding-x:\t\t\t\t2rem !default;\r\n$header-inner-padding-y:\t\t\t\t0 !default;\r\n\r\n/* logo */\r\n$header-logo-border-bottom:\t\t\t\trgba(darken($primary-700, 10%), (30/100)) !default;\r\n$header-logo-width:\t\t\t\t\t\t28px !default; \t\t\t\t\t\t\t\t\t\t  /* try not to go beywond the width of $main_nav_width value */\r\n$header-logo-height:\t\t\t\t\t28px !default; \t\t\t\t\t\t\t\t\t\t  /* you may need to change this depending on your logo design */\r\n$header-logo-text-align:\t\t\t\tcenter; \t\t\t\t\t\t\t\t\t\t\t  /* adjust this as you see fit : left, right, center */\r\n\r\n/* icon font size (not button) */\r\n$header-icon-size:\t\t\t\t\t\t21px;\r\n\r\n/* search input box */\r\n$header-search-border-color:\t\t\ttransparent !default;\t\t\t\t\t\t\t\t  /* suggestion: #ccced0*/\r\n$header-search-bg:\t\t\t\t\t\ttransparent !default;\r\n$header-search-width:\t\t\t\t\t25rem !default;\r\n$header-search-height:\t\t\t\t\t$header-height - 1.5rem !default; \r\n$header-search-font-size:\t\t\t\t$fs-base + 2;\r\n$header-search-padding:\t\t\t\t\t$spacer * 0.38;\r\n\r\n/* btn */\r\n$header-btn-active-bg:\t\t\t\t\t$fusion-500 !default;\r\n$header-btn-color:\t\t\t\t\t\tdarken($header-bg, 35%) !default;\r\n$header-btn-hover-color:\t\t\t\t$header-link-hover-color !default;\r\n$header-btn-active-color:\t\t\t\t$white !default;\r\n$header-btn-height: \t\t\t\t\t$header-height/2 + 0.1875rem !default;\r\n$header-btn-width: \t\t\t\t\t\t3.25rem !default;\r\n$header-btn-font-size:\t\t\t\t\t21px !default; //works only for font icons\r\n$header-btn-border-radius:\t\t\t\t$border-radius !default;\r\n$header-non-btn-width:\t\t\t\t\t3.125rem !default;\r\n$header-dropdown-arrow-color:\t\t\t$primary-700 !default;\r\n\r\n/* dropdown: app list */\r\n$header-applist-link-block-height:\t\t5.9375rem;\r\n$header-applist-link-block-width:\t\t6.25rem;\r\n$header-applist-rows-width:\t\t\t\t21.875rem;\r\n$header-applist-rows-height:\t\t\t22.5rem; \r\n$header-applist-box-padding-x:\t\t\t$p-2;\r\n$header-applist-box-padding-y:\t\t\t$p-3;\r\n$header-applist-icon-size:\t\t\t\t3.125rem;\r\n\r\n/* badge */\r\n$header-badge-min-width:\t\t\t\t1.25rem !default;\r\n$header-badge-left:\t\t\t\t\t\t1.5625rem !default;\r\n$header-badge-top:\t\t\t\t\t\t($header-height / 2 - $header-badge-min-width) + 0.28125rem !default; \r\n\r\n/* COMPONENTS & MODS */\r\n$nav-tabs-clean-link-height:\t\t\t45px !default;\r\n\r\n/*  NAVIGATION STUFF\r\n\r\nGuide:\r\n\r\naside.page-sidebar ($nav-width, $nav-background)\r\n\t.page-logo\r\n\t.primary-nav\r\n\t\t.info-card\r\n\t\tul.nav-menu\r\n\t\t\tli\r\n\t\t\t\ta (parent level-0..., $nav-link-color, $nav-link-hover-color, $nav-link-hover-bg-color, $nav-link-hover-left-border-color)\r\n\t\t\t\t\ticon \r\n\t\t\t\t\tspan\r\n\t\t\t\t\tcollapse-sign \r\n\t\t\t\t\t\r\n\t\t\t\tul.nav-menu-sub-one  \r\n\t\t\t\t\tli\r\n\t\t\t\t\t\ta ($nav-level-1... $nav-sub-link-height)\r\n\t\t\t\t\t\t\tspan\r\n\t\t\t\t\t\t\tcollapse-sign\r\n\r\n\t\t\t\t\t\tul.nav-menu-sub-two\r\n\t\t\t\t\t\t\tli\r\n\t\t\t\t\t\t\t\ta ($nav-level-2... $nav-sub-link-height)\r\n\t\t\t\t\t\t\t\t\tspan\r\n\r\n\t\tp.nav-title ($nav-title-*...)\r\n\r\n\r\n========================================================================== */\r\n\r\n/* main navigation */\r\n/* left panel */\r\n$nav-background:\t\t\t\t\t\tdesaturate($primary-900, 7%) !default;\r\n$nav-background-shade:\t\t\t\t\trgba(desaturate($info-500, 15%), 0.18) !default;                  \r\n$nav-base-color:\t\t\t\t\t\tlighten($nav-background, 7%) !default;\r\n$nav-width:\t\t\t\t\t\t\t\t16.875rem !default; \r\n\r\n/* nav footer */\r\n$nav-footer-link-color:\t\t\t\t\tlighten($nav-background, 25%) !default;\r\n\r\n/* nav parent level-0 */\r\n$nav-link-color: \t\t\t\t\t\tlighten($nav-base-color, 32%) !default;\r\n$nav-font-link-size: \t\t\t\t\t$fs-base + 1 !default;\r\n$nav-collapse-sign-font-size:\t\t\tinherit !default;\t\r\n$nav-padding-x:\t\t\t\t\t\t\t2rem !default; \r\n$nav-padding-y:\t\t\t\t\t\t\t0.8125rem !default;\r\n\r\n\r\n/* nav link level-1 */\r\n$nav-link-color-child: \t\t\t\t\tdarken($nav-link-color, 5%);\r\n$nav-link-color-child-hover:\t\t\t$white;\r\n\r\n/* nav level-1 bg */\r\n$nav-ul-ul-bg:\t\t\t\t\t\t\trgba($black,0.1);\r\n$nav-ul-padding-top:\t\t\t\t\t10px;\r\n$nav-ul-padding-bottom:\t\t\t\t\t10px;\r\n\r\n/* nav icon sizes */\r\n$nav-font-icon-size:\t\t\t\t\t1.125rem !default; //23px for Fontawesome & 20px for NextGen icons\r\n$nav-font-icon-size-sub:\t\t\t\t1.125rem !default;\r\n\r\n$nav-icon-width:\t\t\t\t\t\t1.75rem !default;\r\n$nav-icon-margin-right:\t\t\t\t\t0.25rem !default;\r\n\r\n/* badge default */\r\n$nav-badge-color: \t\t\t\t\t\t$white !default;\r\n$nav-badge-bg-color: \t\t\t\t\t$danger-500 !default;\r\n\r\n/* all child */\r\n$nav-icon-color:\t\t\t\t\t\tlighten(darken($nav-base-color, 15%),27%) !default;\r\n$nav-icon-hover-color:\t\t\t\t\tlighten(desaturate($color-primary, 30%), 10%) !default;\r\n\r\n/* nav title */\r\n$nav-title-color: \t\t\t\t\t\tlighten($nav-base-color, 10%) !default;\r\n$nav-title-border-bottom-color: \t\tlighten($nav-base-color, 3%) !default;\r\n$nav-title-font-size: \t\t\t\t\t$fs-base - 1.8px;\r\n\r\n/* nav Minify */\r\n$nav-minify-hover-bg:\t\t\t\t\tdarken($nav-base-color, 3%) !default;\r\n$nav-minify-hover-text:\t\t\t\t\t$white !default;\r\n$nav-minify-width:\t\t\t\t\t\t4.6875rem !default;\r\n/* when the menu pops on hover */\r\n$nav-minify-sub-width:\t\t\t\t\t$nav-width - ($nav-minify-width - 1.5625rem) !default; \t\t\t\t\r\n\r\n/* navigation Width */\r\n/* partial visibility of the menu */\r\n$nav-hidden-visiblity:\t\t\t\t\t0.625rem !default; \t\t\t\t\t\t\t\t\t\t\t\r\n\r\n/* top navigation */\r\n$nav-top-height:\t\t\t\t\t\t3.5rem !default;\r\n$nav-top-drowndown-width:\t\t\t\t13rem !default;\r\n$nav-top-drowndown-background:\t\t\t$nav-base-color;\r\n$nav-top-drowndown-hover:\t\t\t\trgba($black, 0.1);;\r\n$nav-top-drowndown-color:\t\t\t\t$nav-link-color;\r\n$nav-top-drowndown-hover-color:\t\t\t$white;\r\n\r\n/* nav Info Card (appears below the logo) */\r\n$nav-infocard-height:\t\t\t\t\t9.530rem !default;\r\n$profile-image-width:\t\t\t\t\t3.125rem !default; \r\n$profile-image-width-md:\t\t\t\t2rem !default;\r\n$profile-image-width-sm:\t\t\t\t1.5625rem !default;\r\n$image-share-height:\t\t\t\t\t2.8125rem !default; /* width is auto */\r\n\r\n/* nav DL labels for all child */\r\n$nav-dl-font-size:\t\t\t\t\t\t0.625rem !default;\r\n$nav-dl-width:\t\t\t\t\t\t\t1.25rem !default;\r\n$nav-dl-height:\t\t\t\t\t\t\t1rem !default;\r\n$nav-dl-margin-right:\t\t\t\t\t0.9375rem !default;\r\n$nav-dl-margin-left:\t\t\t\t\t$nav-dl-width + $nav-dl-margin-right !default; \t/* will be pulled to left as a negative value */\r\n\r\n/*   MISC Settings\r\n========================================================================== */\r\n/* List Table */\r\n$list-table-padding-x:\t\t\t\t\t11px !default;\r\n$list-table-padding-y:\t\t\t\t\t0 !default;\r\n\r\n/*   PAGE SETTINGS\r\n========================================================================== */\r\n$settings-incompat-title:\t\t\t\tvar(--theme-warning-900) !default;\r\n$settings-incompat-desc:\t\t\t\tvar(--theme-warning-900) !default;\r\n$settings-incompat-bg:\t\t\t\t\tvar(--theme-warning-50) !default;\r\n$settings-incompat-border:\t\t\t\tvar(--theme-warning-700) !default;\r\n\r\n/*   PAGE BREADCRUMB \r\n========================================================================== */\r\n$page-breadcrumb-maxwidth:\t\t\t\t200px;\r\n\r\n/*   PAGE COMPONENT PANELS \r\n========================================================================== */\r\n$panel-spacer-y:\t\t\t\t\t\t1rem;\r\n$panel-spacer-x:\t\t\t\t\t\t1rem;\r\n$panel-hdr-font-size:\t\t\t\t\t14px;\r\n$panel-hdr-height:\t\t\t\t\t\t3rem;\r\n$panel-btn-size:\t\t\t\t\t\t1rem;\r\n$panel-btn-spacing:\t\t\t\t\t\t0.3rem;\r\n$panel-toolbar-icon:\t\t\t\t\t1.5625rem;\r\n$panel-hdr-background:\t\t\t\t\t$white; //#fafafa;\r\n$panel-edge-radius:\t\t\t\t\t\t$border-radius;\r\n$panel-placeholder-color:\t\t\t\tlighten(desaturate($primary-50, 20%), 10%);\r\n\r\n$panel-btn-icon-width:\t\t\t\t\t2rem;\r\n$panel-btn-icon-height:\t\t\t\t\t2rem;\r\n$panel-btn-icon-font-size:\t\t\t\t1rem;\r\n\r\n/*   PAGE COMPONENT PROGRESSBARS \r\n========================================================================== */\r\n$progress-height:\t\t\t\t\t\t.75rem;\r\n$progress-font-size:\t\t\t\t\t.625rem;\r\n$progress-bg:\t\t\t\t\t\t\tlighten($fusion-50, 40%);\r\n$progress-border-radius:\t\t\t\t10rem;\r\n\r\n/*   PAGE COMPONENT MESSENGER \r\n========================================================================== */\r\n$msgr-list-width:\t\t\t\t\t\t14.563rem;\r\n$msgr-list-width-collapsed:\t\t\t\t3.125rem;\r\n$msgr-get-background:\t\t\t\t\t#f1f0f0;\r\n$msgr-sent-background:\t\t\t\t\t$success-500;\r\n$msgr-animation-delay:\t\t\t\t\t100ms;\r\n\r\n/*   FOOTER\r\n========================================================================== */\r\n$footer-bg:\t\t\t\t\t\t\t\t$white !default;\r\n$footer-text-color:\t\t\t\t\t\tdarken($base-text-color, 10%);\r\n$footer-height:\t\t\t\t\t\t\t2.8125rem !default;\r\n$footer-font-size:\t\t\t\t\t\t$fs-base !default;\r\n$footer-zindex:\t\t\t\t\t\t\t$cloud - 20 !default;\r\n\r\n/*   GLOBALS\r\n========================================================================== */\r\n$mod-main-boxed-width:\t\t\t\t\tmap-get($grid-breakpoints, xl);\r\n$slider-width:\t\t\t\t\t\t\t15rem;\r\n\r\n/* ACCESSIBILITIES */\r\n$enable-prefers-reduced-motion-media-query:   false;\r\n\r\n/* SHORTCUT BUTTON (appears on bottom right of the page) */\r\n$app-shortcut-btn-size: 49px;\r\n$menu-item-size: 45px;\r\n$menu-items:5;\r\n$menu-grid-icon: 5px;\r\n$menu-item-direction: 'top'; //top or left\r\n\r\n\r\n/* GULP WARNINGS */\r\n$ignore-warning: true;"]}