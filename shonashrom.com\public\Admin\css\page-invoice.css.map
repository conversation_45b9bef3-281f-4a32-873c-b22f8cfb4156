{"version": 3, "sources": ["page-invoice.css"], "names": [], "mappings": "AAAA;EACC;;IAEC,YAAY;IACZ,aAAa,EAAA;EAEd;IACC,iCAAiC,EAAA;EAElC;;;;;;;;;;;;IAYC,WAAW;IACX,UAAU,EAAA;EAEX;IACC,WAAW,EAAA;EAEZ;IACC,mBAAmB,EAAA;EAEpB;IACC,mBAAmB,EAAA;EAEpB;IACC,UAAU,EAAA;EAEX;IACC,mBAAmB,EAAA;EAEpB;IACC,mBAAmB,EAAA;EAEpB;IACC,UAAU,EAAA;EAEX;IACC,mBAAmB,EAAA;EAEpB;IACC,mBAAmB,EAAA;EAEpB;IACC,UAAU,EAAA;EAEX;IACC,mBAAmB,EAAA;EAEpB;IACC,kBAAkB,EAAA;EAEnB;IACC,SAAS;IACT,qBAAa;YAAb,aAAa;IACb,2BAA2B,EAAA;EAE5B;;IAEC,aAAa,EAAA;EAEd;IACC,oDAAoD;IACpD,0BAA0B,EAAA;EAE3B;IACC,0BAA0B,EAAA,EAC1B;;AAGF;EACC,UAAU;EACV,SAAS,EAAA;;AAGV;EACC,iBAAiB;EACjB,cAAc;EACd,cAAc;EACd,oBAAoB;EACpB,gDAAwC;UAAxC,wCAAwC;EACxC,iEAAiE;EACjE,sBAAsB;EACtB,aAAa;EACb,kBAAkB,EAAA;;AAGnB;EACC;;IAEC,UAAU;IACV,wBAAgB;YAAhB,gBAAgB,EAAA,EAChB", "file": "page-invoice.css", "sourcesContent": ["@media print {\r\n\thtml,\r\n\tbody {\r\n\t\twidth: 210mm;\r\n\t\theight: 297mm;\r\n\t}\r\n\t.invoice-page {\r\n\t\t-webkit-print-color-adjust: exact;\r\n\t}\r\n\t.col-sm-1,\r\n\t.col-sm-2,\r\n\t.col-sm-3,\r\n\t.col-sm-4,\r\n\t.col-sm-5,\r\n\t.col-sm-6,\r\n\t.col-sm-7,\r\n\t.col-sm-8,\r\n\t.col-sm-9,\r\n\t.col-sm-10,\r\n\t.col-sm-11,\r\n\t.col-sm-12 {\r\n\t\tfloat: left;\r\n\t\tpadding: 0;\r\n\t}\r\n\t.col-sm-12 {\r\n\t\twidth: 100%;\r\n\t}\r\n\t.col-sm-11 {\r\n\t\twidth: 91.66666667%;\r\n\t}\r\n\t.col-sm-10 {\r\n\t\twidth: 83.33333333%;\r\n\t}\r\n\t.col-sm-9 {\r\n\t\twidth: 75%;\r\n\t}\r\n\t.col-sm-8 {\r\n\t\twidth: 66.66666667%;\r\n\t}\r\n\t.col-sm-7 {\r\n\t\twidth: 58.33333333%;\r\n\t}\r\n\t.col-sm-6 {\r\n\t\twidth: 50%;\r\n\t}\r\n\t.col-sm-5 {\r\n\t\twidth: 41.66666667%;\r\n\t}\r\n\t.col-sm-4 {\r\n\t\twidth: 33.33333333%;\r\n\t}\r\n\t.col-sm-3 {\r\n\t\twidth: 25%;\r\n\t}\r\n\t.col-sm-2 {\r\n\t\twidth: 16.66666667%;\r\n\t}\r\n\t.col-sm-1 {\r\n\t\twidth: 8.33333333%;\r\n\t}\r\n\tdiv[data-size=\"A4\"] {\r\n\t\tmargin: 0;\r\n\t\tbox-shadow: 0;\r\n\t\tpadding: 3em 5em !important;\r\n\t}\r\n\t.breadcrumb,\r\n\t.subheader {\r\n\t\tdisplay: none;\r\n\t}\r\n\t*:not(.keep-print-font) {\r\n\t\tfont-family: Arial, Helvetica, sans-serif !important;\r\n\t\tfont-size: 11pt !important;\r\n\t}\r\n\ttable {\r\n\t\tfont-size: 100% !important;\r\n\t}\r\n}\r\n\r\n@page {\r\n\tsize: auto;\r\n\tmargin: 0;\r\n}\r\n\r\ndiv[data-size=\"A4\"] {\r\n\tbackground: white;\r\n\tdisplay: block;\r\n\tmargin: 0 auto;\r\n\tmargin-bottom: 0.5cm;\r\n\tbox-shadow: 0 0 0.5cm rgba(0, 0, 0, 0.5);\r\n\tbackground: url(../img/svg/pattern-1.svg) no-repeat center bottom;\r\n\tbackground-size: cover;\r\n\tpadding: 4rem;\r\n\tposition: relative;\r\n}\r\n\r\n@media only screen and ( max-width: 992px ){\r\n\tdiv[data-size=\"A4\"],\r\n\t.container {\r\n\t\tpadding: 0;\r\n\t\tbox-shadow: none;\r\n\t}\r\n}"]}