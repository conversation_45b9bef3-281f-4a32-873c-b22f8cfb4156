@php
use App\Helpers\DesignHelper;

$design = $design ?? DesignHelper::getCurrentDesign();
$title = $title ?? 'Page Title';
$breadcrumbs = $breadcrumbs ?? [['name' => 'Home', 'url' => url('/')]];
@endphp

@if($design->Show_Breadcrumbs)
<!--==============================
    Breadcrumb
==============================-->
<div class="breadcumb-wrapper" style="background-color: var(--breadcrumb-bg-color);">
    <div class="container">
        <div class="breadcumb-content">
            <h1 class="breadcumb-title" style="color: var(--breadcrumb-active-color);">{{ $title }}</h1>
            <ul class="breadcumb-menu">
                @foreach($breadcrumbs as $breadcrumb)
                    <li>
                        @if(isset($breadcrumb['url']))
                            <a href="{{ $breadcrumb['url'] }}" style="color: var(--breadcrumb-link-color);">{{ $breadcrumb['name'] }}</a>
                        @else
                            <span style="color: var(--breadcrumb-active-color);">{{ $breadcrumb['name'] }}</span>
                        @endif
                    </li>
                @endforeach
            </ul>
        </div>
    </div>
</div>
@endif
