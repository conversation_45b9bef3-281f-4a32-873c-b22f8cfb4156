@if(count($errors->all()) > 0)
<div class="alert">
  <span class="closebtn">&times;</span>  
 <ul>
		@foreach($errors->all() as $error)
            <li>{{$error}}</li>
        @endforeach
	</ul>
</div>

@endif

@if(session()->has('error'))
<div class="alert text-center">
  <span class="closebtn">&times;</span>  
  <h2 style="color: white">{{session('error')}}</h2>
</div>
@endif

@if(session()->has('success'))
<div class="alert success text-center">
  <span class="closebtn">&times;</span>  
  <h2 style="color: white">{{session('success')}}</h2>
</div>
@endif


<style>
.alert {
  padding: 20px;
  background-color: #f44336;
  color: white;
  opacity: 1;
  transition: opacity 0.6s;
  margin-bottom: 15px;
}

.alert.success {background-color: #4CAF50;}
.alert.info {background-color: #2196F3;}
.alert.warning {background-color: #ff9800;}

.closebtn {
  margin-left: 15px;
  color: white;
  font-weight: bold;
  float: right;
  font-size: 22px;
  line-height: 20px;
  cursor: pointer;
  transition: 0.3s;
}

.closebtn:hover {
  color: black;
}
</style>
<script>
var close = document.getElementsByClassName("closebtn");
var i;

for (i = 0; i < close.length; i++) {
  close[i].onclick = function(){
    var div = this.parentElement;
    div.style.opacity = "0";
    setTimeout(function(){ div.style.display = "none"; }, 600);
  }
}
</script>