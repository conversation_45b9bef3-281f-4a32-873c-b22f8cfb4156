<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMainEComDesignsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('main_e_com_designs', function (Blueprint $table) {
            $table->id();
            
            // Font Configuration
            $table->integer('Font_Type')->default(1);
            
            // Pagination
            $table->string('Pagination_BG_Color')->default('#ffffff');
            $table->string('Pagination_Txt_Color')->default('#495057');
            $table->string('Pagination_Active_BG_Color')->default('#3498db');
            $table->string('Pagination_Active_Txt_Color')->default('#ffffff');
            
            // Body Background
            $table->integer('Body_BG_Type')->default(1);
            $table->text('Body_BG_Image')->nullable();
            $table->string('Body_BG_Color')->default('#ffffff');
            
            // Sub Pages & Breadcrumb
            $table->string('Sub_Page_BG_Color')->default('#2c3e50');
            $table->string('Breadcumb_BG_Color')->default('#f8f9fa');
            $table->string('Breadcumb_Txt_Color')->default('#6c757d');
            
            // Modal
            $table->string('Modal_BG_Color')->default('#ffffff');
            $table->string('Modal_Txt_Color')->default('#495057');
            $table->string('Modal_Button_BG_Color')->default('#3498db');
            $table->string('Modal_Button_Txt_Color')->default('#ffffff');
            
            // Table
            $table->string('Table_Header_BG_Color')->default('#f8f9fa');
            $table->string('Table_Header_Txt_Color')->default('#495057');
            $table->string('Table_Body_BG_Color')->default('#ffffff');
            $table->string('Table_Body_Txt_Color')->default('#495057');
            $table->string('Table_Button_BG_Color')->default('#3498db');
            $table->string('Table_Button_Txt_Color')->default('#ffffff');
            
            // Copyright
            $table->string('CopyRights_Txt_Color')->default('#6c757d');
            $table->string('CopyRights_Klar_Txt_Color')->default('#3498db');
            $table->string('CopyRights_Klar_Hover_Txt_Color')->default('#2980b9');
            
            // Preloader
            $table->string('Preloader_BG_Color')->default('#ffffff');
            $table->string('Preloader_Small_Circle_Color')->default('#3498db');
            $table->string('Preloader_Large_Circle_Color')->default('#2980b9');
            
            // Footer
            $table->string('Footer_Title_Color')->default('#ffffff');
            $table->string('Footer_Txt_Color')->default('#bdc3c7');
            $table->string('Footer_Txt_Hover_Color')->default('#ffffff');
            $table->string('Footer_Social_Color')->default('#ffffff');
            $table->string('Footer_Social_BG_Color')->default('#34495e');
            $table->string('Footer_Social_Hover_BG_Color')->default('#3498db');
            $table->string('Footer_Social_Hover_Txt_Color')->default('#ffffff');
            
            // Header
            $table->string('Header_Top_BG_Color')->default('#2c3e50');
            $table->string('Header_Top_Txt_Color')->default('#ffffff');
            $table->string('Header_Top_Txt_Hover_Color')->default('#3498db');
            $table->string('Header_Middle_BG_Color')->default('#ffffff');
            $table->string('Header_Middle_Icon_Color')->default('#2c3e50');
            $table->string('Header_Middle_Icon_Hover_Color')->default('#3498db');
            $table->string('Header_SearchBar_BG_Color')->default('#f8f9fa');
            $table->string('Header_SearchBar_Txt_Color')->default('#495057');
            $table->string('Header_SearchBar_Icon_BG_Color')->default('#3498db');
            $table->string('Header_SearchBar_Icon_Txt_Color')->default('#ffffff');
            $table->string('Header_SearchBar_Icon_Hover_BG_Color')->default('#2980b9');
            $table->string('Header_SearchBar_Icon_Hover_Txt_Color')->default('#ffffff');
            
            // Navbar
            $table->string('Navbar_BG_Color')->default('#2c3e50');
            $table->string('Navbar_Txt_Color')->default('#ffffff');
            $table->string('Navbar_Txt_Hover_Color')->default('#3498db');
            $table->string('Navbar_Txt_Active_Color')->default('#3498db');
            $table->string('Navbar_Dropdown_BG_Color')->default('#34495e');
            $table->string('Navbar_Dropdown_Txt_Color')->default('#ffffff');
            $table->string('Navbar_Dropdown_Txt_Hover_Color')->default('#3498db');
            $table->string('Navbar_Dropdown_Border_Color')->default('#2c3e50');
            
            // Slider
            $table->integer('Slider_BG_Type')->default(1);
            $table->text('Slider_BG_Image')->nullable();
            $table->string('Slider_BG_Color')->default('#2c3e50');
            $table->string('Slider_Title_Color')->default('#ffffff');
            $table->string('Slider_Txt_Color')->default('#bdc3c7');
            $table->string('Slider_Button_BG_Color')->default('#3498db');
            $table->string('Slider_Button_Txt_Color')->default('#ffffff');
            $table->string('Slider_Button_Hover_BG_Color')->default('#2980b9');
            $table->string('Slider_Button_Hover_Txt_Color')->default('#ffffff');
            
            // Sections
            $table->string('Section_Title_Color')->default('#2c3e50');
            $table->string('Section_Txt_Color')->default('#555555');
            $table->string('Section_BG_Color')->default('#ffffff');
            $table->string('Section_Alt_BG_Color')->default('#f8f9fa');
            
            // Cards
            $table->string('Card_BG_Color')->default('#ffffff');
            $table->string('Card_Border_Color')->default('#e9ecef');
            $table->string('Card_Title_Color')->default('#2c3e50');
            $table->string('Card_Txt_Color')->default('#555555');
            $table->string('Card_Price_Color')->default('#e74c3c');
            $table->string('Card_Button_BG_Color')->default('#3498db');
            $table->string('Card_Button_Txt_Color')->default('#ffffff');
            $table->string('Card_Button_Hover_BG_Color')->default('#2980b9');
            $table->string('Card_Button_Hover_Txt_Color')->default('#ffffff');
            
            // Forms
            $table->string('Form_BG_Color')->default('#ffffff');
            $table->string('Form_Border_Color')->default('#e9ecef');
            $table->string('Form_Input_BG_Color')->default('#ffffff');
            $table->string('Form_Input_Border_Color')->default('#ced4da');
            $table->string('Form_Input_Txt_Color')->default('#495057');
            $table->string('Form_Label_Color')->default('#495057');
            $table->string('Form_Button_BG_Color')->default('#3498db');
            $table->string('Form_Button_Txt_Color')->default('#ffffff');
            $table->string('Form_Button_Hover_BG_Color')->default('#2980b9');
            $table->string('Form_Button_Hover_Txt_Color')->default('#ffffff');
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('main_e_com_designs');
    }
}
